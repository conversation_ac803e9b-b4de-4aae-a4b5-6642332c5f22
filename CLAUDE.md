# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

BINA is a React Native construction/project management mobile application with sophisticated offline capabilities, real-time synchronization, and document management features. The app supports iOS and Android platforms and integrates with GraphQL/REST APIs, PDF annotation, CAD drawing management, and social authentication.

## Development Setup

### Prerequisites

- Node.js and Yarn package manager
- React Native CLI
- Xcode (for iOS) with CocoaPods
- Android Studio (for Android)

### Installation

```bash
# Install dependencies
yarn setup:dependencies

# For iOS development
yarn pod-install

# For M1 Macs
yarn pod-install:m1
```

### Environment Configuration

Create a `.env` file with the following variables for staging:

```
NODE_ENV=staging
STAGING_API_URL=https://api-stg.bina.cloud
PROD_API_URL=https://api-stg.bina.cloud
DEV_API_URL=https://api-stg.bina.cloud
ANDROID_DEV_API_URL=https://api-stg.bina.cloud
GRAPHQL_SCHEMA_PATH=https://api-stg.bina.cloud/graphql
API_SCHEMA_PATH=https://api-stg.bina.cloud/api-json
```

## Essential Commands

### Development

```bash
# Start Metro bundler
yarn start
yarn start:reset  # with cache reset

# Run on iOS (M1 Mac)
yarn ios-13:m1    # iPhone 13 simulator
yarn ios-16:m1    # iPhone 16 Plus simulator
yarn ios-ipad:m1  # iPad simulator

# Run on Android
yarn android
yarn android:release  # release build

# Install pods (iOS)
yarn pod-install:m1  # for M1 Macs
```

### Code Quality

```bash
# Type checking
yarn checkTs

# Linting
yarn lint

# Code formatting
yarn prettify

# Run tests
yarn test
```

### Build & Release

```bash
# Android builds
yarn android:assemble  # APK
yarn android:bundle    # AAB

# iOS builds
yarn ios:bundle        # Archive build
```

### Dependency Management

```bash
# Install all dependencies
yarn setup:dependencies

# Clean dependencies
yarn clear:dependencies

# Reset dependencies (clean + install)
yarn reset:dependencies

# Load assets
yarn load:assets
```

### Code Generation

```bash
# Generate GraphQL types
yarn gql-codegen

# Generate REST API types
yarn codegen
```

## Architecture Overview

### Tech Stack

- **React Native 0.73.10** with TypeScript
- **WatermelonDB** for offline-first local database
- **Redux Toolkit** for global state management
- **Apollo Client** for GraphQL API integration
- **React Query** for server state management
- **React Navigation v6** for navigation
- **NativeBase** for UI components

### Database Architecture

- **WatermelonDB** with SQLite adapter
- **Offline-first** approach with bi-directional sync
- **22 core models** including Projects, Tasks, Users, Documents
- **Incremental sync** with timestamp-based conflict resolution
- **Background sync** every 5 minutes

### API Integration

- **Dual API architecture**: GraphQL (primary) + REST (specific operations)
- **JWT authentication** with project context headers
- **Presigned URLs** for efficient file uploads
- **Error handling** with maintenance mode detection

### Module Organization

Each feature is organized as a self-contained module:

```
src/modules/[FeatureName]/
├── [FeatureName]Navigator.tsx
├── components/
├── pages/
└── helpers/
```

### Key Directories

- `src/database/` - WatermelonDB models, schemas, and sync logic
- `src/modules/` - Feature modules (Authentication, Projects, Tasks, etc.)
- `src/commons/` - Reusable UI components
- `src/hooks/` - Custom React hooks
- `src/store/` - Redux store and slices
- `src/api/` - API client configuration
- `src/graphql/` - GraphQL queries and mutations
- `src/types/` - TypeScript type definitions

## Common Development Tasks

### Database Operations

- Models are located in `src/database/model/`
- Schemas are in `src/database/schema/`
- Sync logic is in `src/database/sync.ts`
- Use `withObservables` for reactive queries

### API Integration

- GraphQL queries/mutations in `src/queries/` and `src/mutation/`
- REST API services are auto-generated from Swagger specs
- File uploads use presigned URLs from the server

### Navigation

- Main routing in `src/route.tsx`
- Each module has its own navigator
- Deep linking configured for `bina://` scheme

### State Management

- Redux slices in `src/slice/`
- Use typed hooks from `src/store/`
- React Query for server state caching

### Testing

- Jest configuration in `jest.config.js`
- Test user: `<EMAIL>` / `1234`

## Platform-Specific Notes

### iOS Development

- Requires Xcode and CocoaPods
- Use M1-specific commands for Apple Silicon Macs
- PDFTron framework requires specific build settings

### Android Development

- Minimum SDK version and build tools specified in `android/build.gradle`
- Google Services configuration required for Firebase
- Proguard rules for release builds

## Important Considerations

### Offline Functionality

- App is designed offline-first with local database
- All data operations should work without network
- Sync conflicts are resolved server-wins

### Performance

- WatermelonDB provides efficient querying
- Images use FastImage for optimization
- PDF rendering uses PDFTron for performance

### Security

- JWT tokens for authentication
- Project-level data isolation
- File access via presigned URLs

## Third-Party Integrations

### Essential Services

- **Firebase**: Push notifications and analytics
- **Sentry**: Error monitoring and performance tracking
- **PDFTron**: PDF viewing and annotation
- **Social Auth**: Apple, Google, Facebook sign-in

### File Management

- Document picker and image cropping
- Video playback and thumbnail generation
- Cloud storage integration

This architecture supports a mature, enterprise-level construction management application with sophisticated offline capabilities and real-time collaboration features.
