name: Remove console and logger statements

on:
  push:
    branches: ['**']

jobs:
  remove-logs:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Remove multi-line logs
        run: |
          find . -type f \( -name "*.js" -o -name "*.ts" -o -name "*.tsx" \) \
          -exec perl -0777 -i -pe '
            s/console\.(log|debug|error)\s*\(.*?\);?\s*//gs;
            s/this\.logger\.(warn|debug|error)\s*\(.*?\);?\s*//gs;
          ' {} +
            
      - name: Remove single-line logs
        run: |
          find . -type f \( -name "*.js" -o -name "*.ts" -o -name "*.tsx" \) \
          -exec sed -i '/console\.\(log\|debug\|error\)/d;/this\.logger\.\(warn\|debug\|error\)/d' {} +

      - name: Commit changes
        env:
          GH_PAT: ${{ secrets.GH_TOKEN }}
        run: |
          git config --global user.name "Danial-dene"
          git config --global user.email "<EMAIL>"
          git add .
          git commit -m "Remove console and logger statements" || echo "No changes to commit"
          git push https://${GH_PAT}@github.com/${{ github.repository }} HEAD:${{ github.ref_name }}
