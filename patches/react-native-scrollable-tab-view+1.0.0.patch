diff --git a/node_modules/react-native-scrollable-tab-view/DefaultTabBar.js b/node_modules/react-native-scrollable-tab-view/DefaultTabBar.js
index 4cb92de..0d66ace 100644
--- a/node_modules/react-native-scrollable-tab-view/DefaultTabBar.js
+++ b/node_modules/react-native-scrollable-tab-view/DefaultTabBar.js
@@ -1,5 +1,6 @@
 const React = require('react');
-const { ViewPropTypes } = ReactNative = require('react-native');
+const ReactNative = require('react-native');
+const { ViewPropTypes } = require('deprecated-react-native-prop-types');
 const PropTypes = require('prop-types');
 const createReactClass = require('create-react-class');
 const {
@@ -18,7 +19,7 @@ const DefaultTabBar = createReactClass({
     backgroundColor: PropTypes.string,
     activeTextColor: PropTypes.string,
     inactiveTextColor: PropTypes.string,
-    textStyle: Text.propTypes.style,
+    // textStyle: Text.propTypes.style,
     tabStyle: ViewPropTypes.style,
     renderTab: PropTypes.func,
     underlineStyle: ViewPropTypes.style,
diff --git a/node_modules/react-native-scrollable-tab-view/ScrollableTabBar.js b/node_modules/react-native-scrollable-tab-view/ScrollableTabBar.js
index 5db3793..effbd6c 100644
--- a/node_modules/react-native-scrollable-tab-view/ScrollableTabBar.js
+++ b/node_modules/react-native-scrollable-tab-view/ScrollableTabBar.js
@@ -1,7 +1,4 @@
 const React = require('react');
-const { ViewPropTypes } = ReactNative = require('react-native');
-const PropTypes = require('prop-types');
-const createReactClass = require('create-react-class');
 const {
   View,
   Animated,
@@ -10,7 +7,10 @@ const {
   Text,
   Platform,
   Dimensions,
-} = ReactNative;
+} = require('react-native');
+const PropTypes = require('prop-types');
+const { ViewPropTypes } = require('deprecated-react-native-prop-types');
+const createReactClass = require('create-react-class');
 const Button = require('./Button');
 
 const WINDOW_WIDTH = Dimensions.get('window').width;
@@ -27,7 +27,7 @@ const ScrollableTabBar = createReactClass({
     style: ViewPropTypes.style,
     tabStyle: ViewPropTypes.style,
     tabsContainerStyle: ViewPropTypes.style,
-    textStyle: Text.propTypes.style,
+    // textStyle: Text.propTypes.style,
     renderTab: PropTypes.func,
     underlineStyle: ViewPropTypes.style,
     onScroll: PropTypes.func,
diff --git a/node_modules/react-native-scrollable-tab-view/index.js b/node_modules/react-native-scrollable-tab-view/index.js
index 82e53c5..1c5bcb3 100644
--- a/node_modules/react-native-scrollable-tab-view/index.js
+++ b/node_modules/react-native-scrollable-tab-view/index.js
@@ -1,6 +1,7 @@
 const React = require('react');
 const { Component } = React;
-const { ViewPropTypes } = ReactNative = require('react-native');
+const ReactNative = require('react-native');
+const { ViewPropTypes } = require('deprecated-react-native-prop-types');
 const createReactClass = require('create-react-class');
 const PropTypes = require('prop-types');
 const {
@@ -12,6 +13,7 @@ const {
   StyleSheet,
   InteractionManager,
 } = ReactNative;
+import PagerView from 'react-native-pager-view';
 
 const ViewPagerAndroid = require('@react-native-community/viewpager');
 const TimerMixin = require('react-timer-mixin');
@@ -22,7 +24,7 @@ const DefaultTabBar = require('./DefaultTabBar');
 const ScrollableTabBar = require('./ScrollableTabBar');
 
 const AnimatedViewPagerAndroid = Platform.OS === 'android' ?
-  Animated.createAnimatedComponent(ViewPager) :
+  Animated.createAnimatedComponent(PagerView) :
   undefined;
 
 const ScrollableTabView = createReactClass({
@@ -137,14 +139,14 @@ const ScrollableTabView = createReactClass({
     if (Platform.OS === 'ios') {
       const offset = pageNumber * this.state.containerWidth;
       if (this.scrollView) {
-        this.scrollView.getNode().scrollTo({x: offset, y: 0, animated: !this.props.scrollWithoutAnimation, });
+        this.scrollView.scrollTo({x: offset, y: 0, animated: !this.props.scrollWithoutAnimation, });
       }
     } else {
       if (this.scrollView) {
         if (this.props.scrollWithoutAnimation) {
-          this.scrollView.getNode().setPageWithoutAnimation(pageNumber);
+          this.scrollView.setPageWithoutAnimation(pageNumber);
         } else {
-          this.scrollView.getNode().setPage(pageNumber);
+          this.scrollView.setPage(pageNumber);
         }
       }
     }
