{"name": "BINA", "version": "0.0.1", "private": true, "scripts": {"lint": "eslint . --ext .js,.jsx,.ts,.tsx", "android": "npx react-native run-android", "android:clean": "cd android && ./gradlew clean", "android:release": "npx react-native run-android --mode release", "android:assemble": "cd android && ./gradlew assembleRelease", "android:bundle": "cd android && ./gradlew bundleRelease", "ios:bundle": "cd ios && xcodebuild archive -workspace BINA.xcworkspace -scheme BINA -sdk iphoneos -configuration Release -archivePath ~/Library/Developer/Xcode/Archives/$(date +%Y-%m-%d)/'BINA.xcarchive' && cd ..", "ios": "npx react-native run-ios --device=\"Tom's iPhone Max XS\"", "ios:sim": "npx react-native run-ios --simulator='iPhone SE (3rd generation)'", "ios-sim:m1": "arch -x86_64 npx react-native run-ios --simulator='iPhone SE (3rd generation)'", "ios-ammar:m1": "arch -x86_64 npx react-native run-ios --device=\"Ammar's Iphone\"", "ios-13:m1": "arch -x86_64 npx react-native run-ios --simulator='iPhone 13'", "ios-ipad:m1": "arch -x86_64 npx react-native run-ios --simulator='iPad'", "ios-16:m1": "arch -x86_64 npx react-native run-ios --simulator='iPhone 16 Plus'", "ios-tab:m1": "arch -x86_64 npx react-native run-ios --simulator='iPad Pro (12.9-inch) (6th generation)'", "ios-mini:m1": "arch -x86_64 npx react-native run-ios --simulator='iPad mini (6th generation)'", "pod-install": "npx pod-install ios", "pod-install:m1": "cd ios && arch -x86_64 pod install && cd ..", "pod-update:m1": "cd ios && arch -x86_64 pod repo update && cd ..", "start": "npx react-native start", "start:reset": "npx react-native start --reset-cache", "test": "jest", "prettify": "npx prettier --write \"**/*.{js,jsx,ts,tsx,json}\" \"*.{js,jsx,ts,tsx,json}\" --ignore-path .gitignore", "checkTs": "tsc --noEmit", "gql-codegen": "graphql-codegen --config codegen.yml -r dotenv/config", "codegen": "node swagger", "postinstall": "patch-package", "devtools": "react-devtools", "setup:dependencies": "yarn install && yarn pod-install", "clear:dependencies": "rm -rf node_modules && rm -rf ios/build && rm -rf ios/Pods", "reset:dependencies": "yarn clear:dependencies && yarn setup:dependencies", "load:assets": "npx react-native-asset", "rn:up": "npx react-native upgrade && yarn setup:dependencies", "android-ios": "yarn ios-16:m1 && yarn android && yarn ios-ipad:m1", "prepare": "husky"}, "dependencies": {"@apollo/client": "^3.6.9", "@invertase/react-native-apple-authentication": "^2.2.2", "@nozbe/watermelondb": "^0.27.1", "@react-native-async-storage/async-storage": "^1.22.0", "@react-native-camera-roll/camera-roll": "^5.5.0", "@react-native-community/checkbox": "^0.5.15", "@react-native-community/hooks": "^2.8.0", "@react-native-community/netinfo": "^11.3.0", "@react-native-community/push-notification-ios": "^1.10.1", "@react-native-community/segmented-control": "^2.2.2", "@react-native-firebase/app": "^16.4.1", "@react-native-firebase/messaging": "^16.4.1", "@react-native-google-signin/google-signin": "^8.0.0", "@react-native/metro-config": "^0.75.4", "@react-navigation/bottom-tabs": "^6.3.1", "@react-navigation/native": "^6.0.10", "@react-navigation/native-stack": "^6.6.2", "@react-navigation/stack": "^6.3.21", "@reduxjs/toolkit": "^1.8.2", "@sentry/react-native": "^5.33.2", "@shopify/flash-list": "^1.6.4", "@tanstack/react-query": "^5.35.1", "@types/file-saver": "^2.0.5", "@types/lodash": "^4.17.15", "@types/react-native-background-timer": "^2.0.0", "@types/react-native-snap-carousel": "^3.8.9", "@types/react-native-version-check": "^3.4.5", "@types/react-native-video": "^5.0.14", "apollo-upload-client": "^17.0.0", "async-mutex": "^0.5.0", "axios": "^0.27.2", "csvtojson": "^2.0.10", "formik": "^2.2.9", "graphql": "^16.5.0", "he": "^1.2.0", "lodash": "^4.17.21", "moment": "^2.29.3", "native-base": "^3.4.15", "numeral": "^2.0.6", "patch-package": "^6.4.7", "react": "18.2.0", "react-native": "^0.73.10", "react-native-alert-notification": "^0.3.4", "react-native-background-actions": "^4.0.1", "react-native-background-timer": "^2.4.1", "react-native-bootsplash": "^4.3.2", "react-native-calendar-picker": "^7.1.2", "react-native-calendars": "^1.1286.0", "react-native-chart-kit": "^6.12.0", "react-native-checkbox-animated": "^0.0.10", "react-native-collapsible": "^1.6.1", "react-native-collapsible-segmented-view": "^1.2.1", "react-native-confirmation-code-field": "^7.3.0", "react-native-controlled-mentions": "^2.2.5", "react-native-create-thumbnail": "2.0.0-rc.2", "react-native-date-picker": "^4.2.9", "react-native-device-info": "^10.5.1", "react-native-document-picker": "^8.1.2", "react-native-dotenv": "^3.3.1", "react-native-fast-image": "^8.5.11", "react-native-fbsdk-next": "^10.1.0", "react-native-file-viewer": "^2.1.5", "react-native-fs": "^2.20.0", "react-native-get-location": "^3.0.1", "react-native-htmlview": "^0.17.0", "react-native-image-crop-picker": "^0.41.4", "react-native-image-zoom-viewer": "^3.0.1", "react-native-mime-types": "^2.3.0", "react-native-modal": "^13.0.1", "react-native-multi-selectbox": "^1.5.0", "react-native-network-logger": "^1.17.0", "react-native-otp-textinput": "^1.1.6", "react-native-pager-view": "^6.0.0", "react-native-parsed-text": "^0.0.22", "react-native-pdf-thumbnail": "^1.2.1", "react-native-pdftron": "github:binacloudmy/pdftron-react-native-sdk", "react-native-progress": "^5.0.1", "react-native-prompt-android": "^1.1.0", "react-native-push-notification": "^8.1.1", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^4.8.2", "react-native-screens": "3.29.0", "react-native-scrollable-tab-view": "^1.0.0", "react-native-share": "^8.0.1", "react-native-snap-carousel": "4.0.0-beta.6", "react-native-super-grid": "^5.0.0", "react-native-svg": "^15.0.0", "react-native-svg-transformer": "^1.0.0", "react-native-swipe-list-view": "^3.2.9", "react-native-switch": "^1.5.1", "react-native-tab-view": "^3.1.1", "react-native-text-input-mask": "^3.1.4", "react-native-vector-icons": "^9.2.0", "react-native-version-check": "^3.4.7", "react-native-video": "^5.2.1", "react-native-webview": "^13.12.3", "react-redux": "^8.0.2", "redux": "^4.2.0", "rn-fetch-blob": "^0.12.0", "socket.io-client": "^4.7.1", "swagger-axios-codegen": "^0.13.3", "victory-native": "^36.6.11"}, "devDependencies": {"@babel/core": "^7.12.9", "@babel/plugin-proposal-decorators": "^7.23.9", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/runtime": "^7.12.5", "@graphql-codegen/cli": "2.11.6", "@graphql-codegen/typescript": "2.7.3", "@graphql-codegen/typescript-operations": "2.5.3", "@graphql-codegen/typescript-react-apollo": "3.3.3", "@react-native-community/cli": "^12.1.1", "@react-native-community/cli-platform-android": "^12.1.1", "@react-native-community/cli-platform-ios": "^12.1.1", "@react-native-community/eslint-config": "^2.0.0", "@types/apollo-upload-client": "^17.0.1", "@types/jest": "^26.0.23", "@types/numeral": "^2.0.2", "@types/react-native": "^0.69.0", "@types/react-native-calendar-picker": "^7.0.2", "@types/react-native-push-notification": "^8.1.1", "@types/react-native-scrollable-tab-view": "^0.10.3", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.29.0", "@typescript-eslint/parser": "^5.29.0", "babel-jest": "^26.6.3", "babel-plugin-module-resolver": "^4.1.0", "babel-plugin-transform-remove-console": "^6.9.4", "eslint": "^7.32.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-plugin-react-hooks": "^4.6.2", "husky": "^9.1.7", "jest": "^26.6.3", "lint-staged": "^15.4.3", "metro-react-native-babel-preset": "^0.70.3", "metro-react-native-babel-transformer": "^0.77.0", "prettier": "^3.4.2", "react-devtools": "^4.27.7", "react-test-renderer": "18.2.0", "typescript": "^4.4.4"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,css,md}": "prettier --write"}, "jest": {"moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"], "overrides": {"react-devtools-core": "~4.25.0"}}}