buildscript {
    ext {
        buildToolsVersion = '34.0.0'
        minSdkVersion = 24
        compileSdkVersion = 34
        targetSdkVersion = 34
        googlePlayServicesAuthVersion = '19.2.0'
        kotlinVersion = '1.8.0'
        ndkVersion = '25.1.8937393'

        safeExtGet = { prop, fallback -> rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback }
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath('com.android.tools.build:gradle')
        classpath('com.facebook.react:react-native-gradle-plugin')
        classpath('de.undercouch:gradle-download-task:5.0.1')
        classpath('org.jetbrains.kotlin:kotlin-gradle-plugin')
        classpath('com.google.gms:google-services:4.3.14')
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url("$rootDir/../node_modules/react-native/android")
        }
        maven {
            // Android JSC is installed from npm
            url("$rootDir/../node_modules/jsc-android/dist")
        }
        maven { url 'https://www.jitpack.io' }
    }
}

subprojects { subproject ->
    if (project.name == 'react-native-reanimated') {
        project.configurations { implementation { } } // Replace deprecated 'compile' with 'implementation'
    }
}

 apply plugin: 'com.facebook.react.rootproject'
