<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="true">
        <!-- <domain includeSubdomains="true">add your domain like www.google.com</domain> -->
        <domain includeSubdomains="true">http://192.168.100.42:3001</domain>
        <domain includeSubdomains="true">http://192.168.209.208:3001</domain>        
        <domain includeSubdomains="true">https://app.bina.cloud</domain>        
        <domain includeSubdomains="true">https://app-stg2.bina.cloud</domain>

        <trust-anchors>
            <certificates src="system" />
            <certificates src="user" />
        </trust-anchors>
    </domain-config>

    <base-config cleartextTrafficPermitted="false" />
</network-security-config>