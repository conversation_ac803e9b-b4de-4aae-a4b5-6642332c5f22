<resources>
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
    </style>
     <!-- Add the following lines (BootTheme should inherit from AppTheme) -->
    <style name="BootTheme" parent="Theme.SplashScreen">
        <!-- set the generated bootsplash.xml drawable as activity background -->
        <item name="windowSplashScreenBackground">@color/bootsplash_background</item>
        <item name="windowSplashScreenAnimatedIcon">@mipmap/bootsplash_logo</item>
        <item name="postSplashScreenTheme">@style/AppTheme</item>
    </style>

    <!-- <style name="PDFTronAppTheme" parent="PDFTronAppThemeBase">
       <item name="free_text_presets">@array/custom_free_text_presets</item>
   </style>

   <style name="CustomFreeTextPresetStyle">
       <item name="annot_color">#000000</item>
       <item name="annot_font_size">16</item>
   </style> -->

</resources>
