apply plugin: 'com.android.application'
apply plugin: 'org.jetbrains.kotlin.android'
apply plugin: 'com.facebook.react'
apply plugin: 'com.google.gms.google-services'

// Enable Hermes for better performance
project.ext.react = [
    enableHermes: true // Enable Hermes
]

/**
 * The react.gradle file registers tasks for each build variant (e.g. bundleDebugJsAndAssets
 * and bundleReleaseJsAndAssets) to build the JS bundle as part of the Android build cycle.
 */

/**
 * Run Proguard to shrink the Java bytecode in release builds.
 */
def enableProguardInReleaseBuilds = false

/**
 * The preferred build flavor of JavaScriptCore.
 * Use Hermes as the JavaScript engine if enabled.
 */
def jscFlavor = 'org.webkit:android-jsc:+'

def isNewArchitectureEnabled() {
    // To opt-in for the New Architecture, you can either:
    // - Set `newArchEnabled` to true inside the `gradle.properties` file
    // - Invoke gradle with `-newArchEnabled=true`
    // - Set an environment variable `ORG_GRADLE_PROJECT_newArchEnabled=true`
    return project.hasProperty('newArchEnabled') && project.newArchEnabled == 'true'
}

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdkVersion rootProject.ext.compileSdkVersion

    namespace 'cloud.bina.android'
    defaultConfig {
        applicationId 'cloud.bina.android'
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 378
        versionName '1.16.12'
        buildConfigField "boolean", "IS_NEW_ARCHITECTURE_ENABLED", isNewArchitectureEnabled().toString()
        multiDexEnabled true
        manifestPlaceholders = [pdftronLicenseKey: 'Bina Cloudtech sdn bhd (bina.cloud):OEM:Bina::IA:AMS(20230928):D7863732B60C5D58B999E101404F3D38DD43F51255922C4C9BD65ABAB6F5C7']

        if (isNewArchitectureEnabled()) {
            externalNativeBuild {
                ndkBuild {
                    arguments 'APP_PLATFORM=android-21',
                            'APP_STL=c++_shared',
                            'NDK_TOOLCHAIN_VERSION=clang',
                            "GENERATED_SRC_DIR=$buildDir/generated/source",
                            "PROJECT_BUILD_DIR=$buildDir",
                            "REACT_ANDROID_DIR=$rootDir/../node_modules/react-native/ReactAndroid",
                            "REACT_ANDROID_BUILD_DIR=$rootDir/../node_modules/react-native/ReactAndroid/build",
                            "NODE_MODULES_DIR=$rootDir/../node_modules"
                    cFlags '-Wall', '-Werror', '-fexceptions', '-frtti', '-DWITH_INSPECTOR=1'
                    cppFlags '-std=c++17'
                    targets 'bina_appmodules'
                }
            }
            if (!enableSeparateBuildPerCPUArchitecture) {
                ndk {
                    abiFilters(*reactNativeArchitectures())
                }
            }
        }
    }

    if (isNewArchitectureEnabled()) {
        externalNativeBuild {
            ndkBuild {
                path "$projectDir/src/main/jni/Android.mk"
            }
        }
        def reactAndroidProjectDir = project(':ReactAndroid').projectDir
        def packageReactNdkDebugLibs = tasks.register('packageReactNdkDebugLibs', Copy) {
            dependsOn(':ReactAndroid:packageReactNdkDebugLibsForBuck')
            from("$reactAndroidProjectDir/src/main/jni/prebuilt/lib")
            into("$buildDir/react-ndk/exported")
        }
        def packageReactNdkReleaseLibs = tasks.register('packageReactNdkReleaseLibs', Copy) {
            dependsOn(':ReactAndroid:packageReactNdkReleaseLibsForBuck')
            from("$reactAndroidProjectDir/src/main/jni/prebuilt/lib")
            into("$buildDir/react-ndk/exported")
        }
        afterEvaluate {
            preDebugBuild.dependsOn(packageReactNdkDebugLibs)
            preReleaseBuild.dependsOn(packageReactNdkReleaseLibs)
            configureNdkBuildRelease.dependsOn(preReleaseBuild)
            configureNdkBuildDebug.dependsOn(preDebugBuild)
            reactNativeArchitectures().each { architecture ->
                tasks.findByName("configureNdkBuildDebug[\${architecture}]")?.configure {
                    dependsOn('preDebugBuild')
                }
                tasks.findByName("configureNdkBuildRelease[\${architecture}]")?.configure {
                    dependsOn('preReleaseBuild')
                }
            }
        }
    }

    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
        release {
            if (project.hasProperty('MYAPP_UPLOAD_STORE_FILE')) {
                storeFile file(MYAPP_UPLOAD_STORE_FILE)
                storePassword MYAPP_UPLOAD_STORE_PASSWORD
                keyAlias MYAPP_UPLOAD_KEY_ALIAS
                keyPassword MYAPP_UPLOAD_KEY_PASSWORD
            }
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            signingConfig signingConfigs.release
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    // Use a specific React Native version to avoid issues with wildcard versioning
    implementation 'com.facebook.react:react-native:0.73.0'  // Replace with your specific version

    implementation 'androidx.core:core-splashscreen:1.0.0'
    debugImplementation('com.facebook.flipper:flipper:0.99.0') // Only use Flipper in debug builds
    implementation 'org.jetbrains.kotlin:kotlin-stdlib:1.8.0'

    implementation 'androidx.multidex:multidex:2.0.1'

    implementation('com.facebook.react:hermes-android')
}

if (isNewArchitectureEnabled()) {
    configurations.all {
        resolutionStrategy.dependencySubstitution {
            substitute(module('com.facebook.react:react-native'))
                    .using(project(':ReactAndroid'))
                    .because("On New Architecture we're building React Native from source")
            substitute(module('com.facebook.react:hermes-engine'))
                    .using(project(':ReactAndroid:hermes-engine'))
                    .because("On New Architecture we're building Hermes from source")
        }
    }
}

// Run this once to be able to run the application with BUCK
task copyDownloadableDepsToLibs(type: Copy) {
    from configurations.implementation
    into 'libs'
}

apply from: file('../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle'); applyNativeModulesAppBuildGradle(project)
apply from: '../../node_modules/@sentry/react-native/sentry.gradle'
