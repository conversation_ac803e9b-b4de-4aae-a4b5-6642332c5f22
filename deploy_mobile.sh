#!/bin/bash

# Exit script if any command fails
set -e

# Load environment variables from .env file if it exists
if [ -f .env ]; then
  echo "Loading environment variables from .env file..."
  source .env
fi

# Function to prompt user to select environment using a menu
choose_environment() {
  echo "Please choose the environment for the commit:"
  PS3="Select the environment (1 or 2): "
  options=("Staging" "Production")
  select opt in "${options[@]}"
  do
    case $opt in
      "Staging")
        environment="Staging"
        break
        ;;
      "Production")
        environment="Production"
        break
        ;;
      *)
        echo "Invalid option $REPLY"
        ;;
    esac
  done
}

# Ask user to choose the environment for the commit
choose_environment

# Ask user if the commit is about what
echo "What is this commit about? (Enter a brief description)"
read commitMessage

# Navigate to the Android directory and get current versionCode and versionName
cd android/app
currentAndroidVersionCode=$(grep "versionCode" build.gradle | head -1 | awk '{print $2}')
currentAndroidVersionName=$(grep "versionName" build.gradle | head -1 | awk -F "'" '{print $2}')

# Navigate back to the project root
cd ../..

# Navigate to the iOS directory and get current CURRENT_PROJECT_VERSION and MARKETING_VERSION
cd ios/BINA.xcodeproj
currentIosVersionCode=$(grep "CURRENT_PROJECT_VERSION" project.pbxproj | head -1 | awk -F '= ' '{print $2}' | tr -d ';')
currentIosVersionName=$(grep "MARKETING_VERSION" project.pbxproj | head -1 | awk -F '= ' '{print $2}' | tr -d ';')

# Navigate back to the project root
cd ../..

# Prompt for new version details and handle null inputs
echo "✨ Enter the new Android version code (current: $currentAndroidVersionCode): ✨"
read androidVersionCode
androidVersionCode=${androidVersionCode:-$currentAndroidVersionCode}

echo "✨ Enter the new Android version name (current: $currentAndroidVersionName): ✨"
read androidVersionName
androidVersionName=${androidVersionName:-$currentAndroidVersionName}

echo "✨ Enter the new iOS version code (current: $currentIosVersionCode): ✨"
read iosVersionCode
iosVersionCode=${iosVersionCode:-$currentIosVersionCode}

echo "✨ Enter the new iOS version name (current: $currentIosVersionName): ✨"
read iosVersionName
iosVersionName=${iosVersionName:-$currentIosVersionName}

# Apply changes to Android build.gradle
cd android/app
sed -i '' "s/versionCode [0-9]*/versionCode $androidVersionCode/" build.gradle
sed -i '' "s/versionName '.*'/versionName '$androidVersionName'/" build.gradle
echo "✨Updated Android versionCode to $androidVersionCode and versionName to $androidVersionName✨"
cd ../..

# Apply changes to iOS project.pbxproj
cd ios/BINA.xcodeproj
sed -i '' "s/CURRENT_PROJECT_VERSION = [0-9]*;/CURRENT_PROJECT_VERSION = $iosVersionCode;/" project.pbxproj
sed -i '' "s/MARKETING_VERSION = .*;/MARKETING_VERSION = $iosVersionName;/" project.pbxproj
echo "✨Updated iOS Version Code to $iosVersionCode and Version Name to $iosVersionName✨"
cd ../..

# Run yarn install
echo "✨ Running yarn install to update dependencies... ✨"
yarn install

# Run yarn pod-install
echo "✨ Running yarn pod-install to update iOS dependencies... ✨"
yarn pod-install:m1

# Update kotlinVersion in react-native-text-input-mask
echo "✨ Updating Kotlin version in react-native-text-input-mask... ✨"
sed -i '' "30s/kotlinVersion = '.*'/kotlinVersion = '1.6.20'/" node_modules/react-native-text-input-mask/android/build.gradle
echo "Kotlin version updated to 1.6.20."

# Run appropriate yarn command based on environment for Android build
if [ "$environment" == "Staging" ]; then
  echo "✨ Running yarn android:assemble for Staging... ✨"
  yarn android:assemble
else
  echo "✨ Running yarn android:bundle for Production... ✨"
  yarn android:assemble
  yarn android:bundle
fi

# Rename the APK file to include the version name
apkSourcePath="android/app/build/outputs/apk/release/app-release.apk"
apkTargetPath="android/app/build/outputs/apk/release/$androidVersionName.apk"

if [ -f "$apkSourcePath" ]; then
    mv "$apkSourcePath" "$apkTargetPath"
    echo "✨ APK file renamed to $androidVersionName.apk ✨"
else
    echo "❌ APK file not found at $apkSourcePath. Please ensure the build has been generated."
    exit 1
fi

# --- Begin APK Upload to OBS Section ---

# Calculate the size of the APK file in bytes (macOS/BSD syntax; use 'stat -c%s' for Linux)
apkSize=$(stat -f%z "$apkTargetPath")
echo "APK file size: $apkSize bytes"

# Define the key and MIME type for the APK file
apkKey="android-apk/$androidVersionName.apk"
mimeType="application/vnd.android.package-archive"
apkMimeType="android"  # Correct MIME type for APK

# Login to get the session key (access token)
echo "✨ Logging in to get session key... ✨"
loginResponse=$(curl -s -X POST "https://api-stg.bina.cloud/api/auth/user/sign-in" \
  -H "Content-Type: application/json" \
  -d "{\"email\": \"${AUTH_EMAIL}\", \"password\": \"${AUTH_PASSWORD}\", \"rememberMe\": true}")
accessToken=$(echo "$loginResponse" | jq -r '.accessToken')

if [ -z "$accessToken" ] || [ "$accessToken" = "null" ]; then
  echo "❌ Failed to get access token. Response: $loginResponse"
  exit 1
fi

echo "Access token received: $accessToken"

# Get the presigned URL from the API using the obtained access token
echo "✨ Requesting presigned URL for upload... ✨"
presignedResponse=$(curl -s -G "https://api-stg.bina.cloud/api/system/presigned-upload" \
  -H "Authorization: Bearer $accessToken" \
  --data-urlencode "key=$apkKey" \
  --data-urlencode "size=$apkSize" \
  --data-urlencode "mimeType=$apkMimeType")

# Extract the uploadUrl using jq (ensure jq is installed)
uploadUrl=$(echo "$presignedResponse" | jq -r '.uploadUrl.SignedUrl')

if [ -z "$uploadUrl" ] || [ "$uploadUrl" = "null" ]; then
  echo "❌ Failed to obtain presigned URL. Response: $presignedResponse"
  exit 1
fi

echo "Presigned URL received: $uploadUrl"

# Upload the APK using the presigned URL
echo "✨ Uploading APK to OBS using the presigned URL... ✨"
curl -X PUT -T "$apkTargetPath" -H "Content-Type: $mimeType" "$uploadUrl"

echo "✨ APK successfully uploaded to OBS. ✨"
# --- End APK Upload Section ---

yarn ios:bundle

# Conditionally build IPA generation only for Production
if [ "$environment" == "Production" ]; then

  # --- Begin IPA Generation Section ---
  echo "✨ Generating IPA from the iOS archive... ✨"
  DATE_FOLDER=$(date "+%Y-%m-%d")
  ARCHIVE_FOLDER="$HOME/Library/Developer/Xcode/Archives/$DATE_FOLDER"
  ARCHIVE_NAME="BINA.xcarchive"
  ARCHIVE_PATH="$ARCHIVE_FOLDER/$ARCHIVE_NAME"

  if [ ! -d "$ARCHIVE_PATH" ]; then
    echo "❌ iOS archive not found at: $ARCHIVE_PATH"
    exit 1
  fi

  # Create export options plist (adjust the method if needed)
  EXPORT_OPTIONS_PLIST="$ARCHIVE_FOLDER/ExportOptions.plist"
  cat <<EOF > "$EXPORT_OPTIONS_PLIST"
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>method</key>
	<string>release-testing</string>
	<key>signingStyle</key>
	<string>automatic</string>
	<key>stripSwiftSymbols</key>
	<true/>
	<key>teamID</key>
	<string>${APPLE_TEAM_ID}</string>
	<key>thinning</key>
	<string>&lt;none&gt;</string>
</dict>
</plist>
EOF

  # Define the export path for the IPA and create the directory if it doesn't exist
  EXPORT_PATH="$ARCHIVE_FOLDER/IPA"
  mkdir -p "$EXPORT_PATH"

  # Export the IPA from the archive
  xcodebuild -exportArchive \
    -allowProvisioningUpdates \
    -archivePath "$ARCHIVE_PATH" \
    -exportPath "$EXPORT_PATH" \
    -exportOptionsPlist "$EXPORT_OPTIONS_PLIST"

  if [ $? -eq 0 ]; then
    echo "✨ IPA created successfully in: $EXPORT_PATH"
  else
    echo "❌ Failed to export IPA."
    exit 1
  fi
  # --- End IPA Generation Section ---
else
  echo "Staging environment detected. Skipping iOS IPA generation."
fi

cd android/app
echo "✨ Committing Android $environment Version $androidVersionName ($androidVersionCode) to git... ✨"
git add build.gradle
git commit -m "✨Android $environment Version $androidVersionName ($androidVersionCode) $commitMessage ✨"
cd ../..

cd ios
echo "✨ Committing iOS $environment Version $iosVersionName ($iosVersionCode) to git... ✨"

# Always add project.pbxproj
git add BINA.xcodeproj/project.pbxproj

# Check if Podfile.lock exists before adding it
if [ -f "Podfile.lock" ]; then
    git add Podfile.lock
    echo "Podfile.lock found and added to the commit."
else
    echo "Podfile.lock not found, continuing without it."
fi

# Commit the changes with the version information
git commit -m "✨iOS $environment Version $iosVersionName ($iosVersionCode) $commitMessage ✨"
cd ..

echo "✨ Opening Android Build Directory ✨"
if [ -d "android/app/build/outputs/bundle/release" ]; then
    open android/app/build/outputs/bundle/release
else
    echo "Directory does not exist. Please ensure the build has been generated."
fi

# Send Slack notification with build details
echo "✨ Sending Slack notification... ✨"
SLACK_WEBHOOK_URL="*********************************************************************************"
PAYLOAD=$(cat <<EOF
{
  "text": "$environment - $commitMessage\niOS $iosVersionName($iosVersionCode) - Testflight\nAndroid $androidVersionName($androidVersionCode) - https://bina-dev-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/android-apk/$androidVersionName.apk"
}
EOF
)
curl -X POST -H "Content-Type: application/json" --data "$PAYLOAD" "$SLACK_WEBHOOK_URL"

echo "All operations completed successfully."
