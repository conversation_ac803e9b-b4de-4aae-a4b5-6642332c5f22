## Setup

`yarn install`
`yarn pod-install`

## Run it

If you are using MAC M1 processor (non intel base)
`yarn ios-13:m1`

## .env

For staging

```
NODE_ENV=staging
STAGING_API_URL=https://api-stg.bina.cloud
PROD_API_URL=https://api-stg.bina.cloud
DEV_API_URL=https://api-stg.bina.cloud
ANDROID_DEV_API_URL=https://api-stg.bina.cloud
GRAPHQL_SCHEMA_PATH=https://api-stg.bina.cloud/graphql
API_SCHEMA_PATH=https://api-stg.bina.cloud/api-json
```

## Test user account

```
u: <EMAIL>
p: 1234
```

## Useful commands

Install dependencies

```bash
$ yarn setup:dependencies
```

Delete all the dependencies

```bash
$ yarn clear:dependencies
```

Delete all the dependencies and install them again

```bash
$ yarn reset:dependencies
```

Install all dependancies and do pod install

```bash
$ yarn setup:dependencies
```

Load assets to both iOS and Android

```bash
$ yarn load:assets
```

Upgrade React Native

```bash
$ yarn rn:up
```

Openup Devtools - Make sure to run this before `yarn start:reset` or `yarn start`

```bash
$ yarn devtools
```

## Prettier

We use [Prettier](https://prettier.io/) to format our code. Please make sure you have the [Prettier extension](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode) installed in your IDE.

Run the following command to format your code before commiting.

```bash
$ yarn prettify
```
