# .maestro/fullWorkflow.yaml
env:
  MAESTRO_APP_ID_IOS: cloud.bina.ios
  MAESTRO_APP_ID_ANDROID: com.bina.android
  # --- Login Credentials ---
  LOGIN_EMAIL: "<EMAIL>"
  LOGIN_PASSWORD: "Asraf2001" # Consider using environment variables for sensitive data in real scenarios
  # --- Test Data ---
  TEMPLATE_NAME: "My Test Template" # Adjust to an existing template name
  GENERATED_DOC_NAME: "My Automated Test Doc ${maestro.timestamp()}" # Creates a unique name
  ASSIGNEE_USER: "<EMAIL>" # Adjust user identifier
  CC_USER: "<EMAIL>" # Adjust user identifier
  LINKED_DOC_NAME: "Some Linked Document" # Adjust document name to link
  ATTACHMENT_FILE_NAME: "dummy-attachment.pdf" # Adjust attachment name
  MEDIA_FILE_NAME: "dummy-image.jpg" # Adjust media name
appId: "${MAESTRO_PLATFORM == 'iOS' ? MAESTRO_APP_ID_IOS : MAESTRO_APP_ID_ANDROID}"
---
# Ensure app is fresh and state is cleared (including login status)
- clearState
- clearKeychain # Clears potential saved login tokens

# Start the actual workflow
- launchApp:
    stopApp: true

# --- NEW: Login ---
- assertVisible:
    id: "loginEmailInput" # Add testID to email input field
- inputText:                 # Command name
    text: "${LOGIN_EMAIL}"   # Parameter 'text' for the value
    id: "loginEmailInput"      # Parameter 'id' for the selector
- inputText:                 # Command name
    text: "${LOGIN_PASSWORD}"  # Parameter 'text' for the value
    id: "loginPasswordInput"   # Parameter 'id' for the selector
- tapOn:
    id: "loginButton" # Add testID to login button

# --- NEW: Select Project ---
- assertVisible:
    id: "allProjectsScreen" # Add testID to the main container/FlatList in AllProjects.tsx
- tapOn: # Tap on the first project in the list
    id: "projectListItem-0" # Ensure your project list items have predictable testIDs like projectListItem-<index> or projectListItem-<projectId>
    # Or use index if testIDs are not feasible/predictable:
    # index: 0
    # point: "50%,50%" # Tap center of the first item

# --- Existing Steps Start Here ---

# --- 1. Go to StandardForm ---
# (Wait for navigation to complete after project selection)
- assertVisible:
    id: "digitalFormTab" # Adjust testID for the main 'Digital Form' tab/button
- tapOn:
    id: "digitalFormTab"
- assertVisible:
    id: "standardFormTab" # Wait for sub-tab/section to appear
- tapOn:
    id: "standardFormTab" # Adjust testID for the 'Standard Form' sub-tab/section

# --- 2. Generate Document ---
- assertVisible:
    id: "templateItem-${TEMPLATE_NAME}" # Wait for the specific template item
- tapOn:
    id: "templateItem-${TEMPLATE_NAME}" # Adjust testID pattern for template list items
- assertVisible:
    id: "pdftronView" # Assume Pdftron container has a testID
# --- (Optional: Add interactions within Pdftron if needed, e.g., annotations) ---
# - tapOn: ...
- tapOn:
    id: "generateButton" # Adjust testID for the 'Generate' button in Pdftron footer
- assertVisible: # Wait for the rename prompt
    text: "Rename" # Or use prompt's title testID
- inputText:                   # Command name
    text: "${GENERATED_DOC_NAME}"  # Parameter 'text' for the value
    id: "renameInput"            # Parameter 'id' for the selector
- tapOn:
    text: "OK" # Or use prompt's OK button testID

# --- 3. Go to AllForm & Select Draft ---
# --- (App might navigate back automatically, or add explicit back navigation) ---
# - back # Example if needed
- assertVisible:
    id: "allFormTab" # Wait for All Form tab/section
- tapOn:
    id: "allFormTab" # Adjust testID for the 'All Form' sub-tab/section
- assertVisible:
    id: "draftFilterSelect"
- tapOn:
    id: "draftFilterSelect" # Adjust testID for the Status/DriveType Select component
- tapOn: # Tap on the "Draft" option (might need specific testID if text isn't unique)
    text: "Draft"
    id: "selectOption-Draft" # Example more specific testID if needed
- assertVisible: # This command will wait for the item
    id: "documentListItem-${GENERATED_DOC_NAME}" # Wait for the specific document item

# --- 4. Navigate to Detail Screen & 5. Populate Details ---
- assertVisible:
    id: "documentDetailScreen" # Add a testID to the main container of Linear/DynamicDocumentDetail
# Add Assignee
- tapOn:
    id: "addAssigneeButton" # Adjust testID
- assertVisible:
    id: "userSelectionScreen" # Adjust testID for the user selection screen/modal
- tapOn: # Select the user
    id: "userItem-${ASSIGNEE_USER}" # Adjust testID pattern for user list
- tapOn:
    id: "confirmUserSelectionButton" # Adjust testID
- assertVisible:
    id: "documentDetailScreen" # Wait to return to detail screen
# Add CC
- tapOn:
    id: "addCcButton" # Adjust testID
- assertVisible:
    id: "userSelectionScreen" # Adjust testID for the user selection screen/modal
- tapOn: # Select the user
    id: "userItem-${CC_USER}" # Adjust testID pattern for user list
- tapOn:
    id: "confirmUserSelectionButton" # Adjust testID
- assertVisible:
    id: "documentDetailScreen" # Wait to return to detail screen
# Link Document
- tapOn:
    id: "linkDocumentButton" # Adjust testID
- assertVisible:
    id: "linkedToModal" # Adjust testID for the LinkedToModal container
- tapOn: # Select the document to link
    id: "linkedDocItem-${LINKED_DOC_NAME}" # Adjust testID pattern for linkable documents
# - tapOn: # Confirm linking (if needed)
#     id: "confirmLinkButton"
- assertVisible:
    id: "documentDetailScreen" # Wait to return/modal closes
# Add Media
- tapOn:
    id: "addMediaButton" # Adjust testID
- assertVisible:
    id: "addAttachmentPhotoModal" # Adjust testID for AddAttachmentPhotoModal container
# --- (Simulate choosing/taking photo - highly dependent on UI) ---
- tapOn:
    id: "selectFromGalleryButton" # Example testID
- tapOn: # Select the media
    id: "mediaItem-${MEDIA_FILE_NAME}" # Example testID for gallery item
- tapOn:
    id: "confirmMediaSelectionButton" # Example testID
- assertVisible:
    id: "documentDetailScreen" # Wait to return/modal closes
# Add Attachment
- tapOn:
    id: "addAttachmentButton" # Adjust testID
- assertVisible:
    id: "addAttachmentPhotoModal" # Or a different modal/picker? Adjust testID
# --- (Simulate choosing file - highly dependent on UI) ---
- tapOn:
    id: "selectFileButton" # Example testID
- tapOn: # Select the file
    id: "fileItem-${ATTACHMENT_FILE_NAME}" # Example testID for file picker item
- tapOn:
    id: "confirmFileSelectionButton" # Example testID
- assertVisible:
    id: "documentDetailScreen" # Wait to return/modal closes
# Save
- tapOn:
    id: "saveDocumentButton" # Adjust testID for the 'Save' button in Document Detail

# --- 6. Request Approval ---
# --- (Navigate back to AllForm - might be automatic after save or need explicit nav) ---
- back # Example, adjust if needed
- assertVisible:
    id: "allFormScreen" # Add testID to AllForm container if needed for waiting
- assertVisible:
    id: "documentListItem-${GENERATED_DOC_NAME}" # Wait for item again
- tapOn: # Tap the three dots icon for the specific document
    id: "documentOptionsIcon-${GENERATED_DOC_NAME}" # Adjust testID pattern
- assertVisible:
    id: "documentOptionModal" # Adjust testID for the DocumentOptionModal container
- tapOn:
    id: "requestApprovalOption" # Adjust testID for the 'Request for Approval' option

# --- 7. Sync ---
- assertVisible:
    id: "allFormScreen" # Wait for modal to close / be back on AllForm
- swipe:                   # Replaced pullToRefresh with swipe
    direction: DOWN        # Swipe down to simulate pull-to-refresh
    duration: 500          # Optional: control swipe speed
    # element: # Optionally target the list if needed
    #   id: "documentList"

# --- Optional: Add assertions ---
# - assertVisible: "Status: Submitted" # Check if status updated visually

- stopApp # End the test 