module.exports = {
  env: {
    production: {
      plugins: ['transform-remove-console']
    }
  },
  presets: ['module:@react-native/babel-preset'],
  plugins: [
    [
      'babel-plugin-module-resolver',
      {
        alias: {
          '@src': './src',
          '@assets': './src/assets',
          '@commons': './src/commons',
          '@modules': './src/modules',
          '@constants': './src/constants',
          '@styles': './src/styles',
          '@store': './src/store',
          '@reducers': './src/reducers',
          '@configs': './src/configs',
          '@types': './src/types',
          '@lib': './src/lib',
          '@api': './src/api',
          '@graphql': './src/graphql',
          '@forms': './src/forms',
          '@inputs': './src/inputs'
        }
      }
    ],
    [
      'module:react-native-dotenv',
      {
        moduleName: 'react-native-dotenv',
        // moduleName: '@env',
        path: '.env',
        blacklist: null,
        whitelist: null,
        safe: false,
        allowUndefined: true
      }
    ],
    ['@babel/plugin-proposal-decorators', { legacy: true }],
    '@babel/plugin-proposal-export-namespace-from'
  ]
};
