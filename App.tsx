import React from 'react';
import App from '@src/index';
import { Provider } from 'react-redux';
import { AppRegistry, LogBox, Platform } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { INativebaseConfig, NativeBaseProvider } from 'native-base';
import { store } from '@store';
import { customTheme } from '@src/theme';
import { ApolloProvider } from '@apollo/client';
import apolloClient from '@src/lib/apollo';
import ToastLayout from '@src/commons/Toast';
import OverlayIndicator from '@src/commons/OverlayIndicator';

import * as Sentry from '@sentry/react-native';
import { httpClientIntegration } from '@sentry/integrations';
import SyncIndicator from '@src/commons/SyncIndicator';

import { QueryClientProvider } from '@tanstack/react-query';
import queryClient from '@src/configs/reactQuerySetup';

Sentry.init({
  dsn: Platform.OS === 'ios' ? process.env.SENTRY_IOS_DSN : process.env.SENTRY_AND_DSN,
  enableAutoSessionTracking: true,
  integrations: [
    httpClientIntegration({
      failedRequestStatusCodes: [[500, 505], 507],
      failedRequestTargets: [process.env.STAGING_API_URL!, process.env.PROD_API_URL!]
    })
  ],
  sendDefaultPii: true,
  tracesSampleRate: 1.0,
  debug: process.env.NODE_ENV === 'local',
  enabled: true,
  environment: process.env.NODE_ENV
});

// @ts-ignore
console.disableYellowBox = true;
LogBox.ignoreLogs(['NativeBase:']);
LogBox.ignoreAllLogs();

const config: INativebaseConfig = {
  // rest of the config keys like dependencies can go here
  strictMode: 'off'
};

export default () => (
  <SafeAreaProvider>
    <ApolloProvider client={apolloClient}>
      <QueryClientProvider client={queryClient}>
        <Provider store={store}>
          <NativeBaseProvider theme={customTheme} config={config}>
            <OverlayIndicator />
            <SyncIndicator />
            <ToastLayout>
              <App />
            </ToastLayout>
          </NativeBaseProvider>
        </Provider>
      </QueryClientProvider>
    </ApolloProvider>
  </SafeAreaProvider>
);
