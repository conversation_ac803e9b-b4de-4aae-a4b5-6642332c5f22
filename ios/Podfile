# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

platform :ios, '13.4'
install! 'cocoapods', :deterministic_uuids => false
use_modular_headers!

# If you are using a `react-native-flipper` your iOS build will fail when `NO_FLIPPER=1` is set.
# because `react-native-flipper` depends on (FlipperKit,...) that will be excluded
#
# To fix this you can also exclude `react-native-flipper` using a `react-native.config.js`
# ```js
# module.exports = {
#   dependencies: {
#     ...(process.env.NO_FLIPPER ? { 'react-native-flipper': { platforms: { ios: null } } } : {}),
# ```

target 'BINA' do
  config = use_native_modules!

  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  pod 'React-RCTText', :path => '../node_modules/react-native/Libraries/Text', :modular_headers => true
  pod 'PDFNet', podspec: 'https://www.pdftron.com/downloads/ios/cocoapods/xcframeworks/pdfnet/latest.podspec'
  pod 'Firebase', :modular_headers => true
  pod 'FirebaseCoreInternal', :modular_headers => true
  pod 'FirebaseCore', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true
  pod 'rn-fetch-blob', :path => '../node_modules/rn-fetch-blob'


  target 'BINATests' do
    inherit! :complete
    # Pods for testing
  end

  # Enables Flipper.
  #
  # Note that if you have use_frameworks! enabled, Flipper will not work and
  # you should disable the next line.
  # use_flipper!()


  # post_install do |installer|
  #   react_native_post_install(installer)
  #   __apply_Xcode_12_5_M1_post_install_workaround(installer)
  # end
  post_install do |installer|
    react_native_post_install(installer,config[:reactNativePath])
    # __apply_Xcode_12_5_M1_post_install_workaround(installer)
      installer.pods_project.targets.each do |target|
          target.build_configurations.each do |config|
            config.build_settings["ONLY_ACTIVE_ARCH"] = "NO"
          end
      end
      bitcode_strip_path = `xcrun --find bitcode_strip`.chop!
      def strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
      framework_path = File.join(Dir.pwd, framework_relative_path)
      command = "#{bitcode_strip_path} #{framework_path} -r -o #{framework_path}"
      puts "Stripping bitcode: #{command}"
      system(command)
    end
  
    # Add paths for Facebook SDK frameworks
    framework_paths = [
      "Pods/FBSDKCoreKit/XCFrameworks/FBSDKCoreKit.xcframework/ios-arm64/FBSDKCoreKit.framework/FBSDKCoreKit",
      "Pods/FBSDKCoreKit_Basics/XCFrameworks/FBSDKCoreKit_Basics.xcframework/ios-arm64/FBSDKCoreKit_Basics.framework/FBSDKCoreKit_Basics",
      "Pods/FBSDKGamingServicesKit/XCFrameworks/FBSDKGamingServicesKit.xcframework/ios-arm64/FBSDKGamingServicesKit.framework/FBSDKGamingServicesKit",
      "Pods/FBSDKLoginKit/XCFrameworks/FBSDKLoginKit.xcframework/ios-arm64/FBSDKLoginKit.framework/FBSDKLoginKit",
      "Pods/FBSDKShareKit/XCFrameworks/FBSDKShareKit.xcframework/ios-arm64/FBSDKShareKit.framework/FBSDKShareKit",
      "Pods/FBAEMKit/XCFrameworks/FBAEMKit.xcframework/ios-arm64/FBAEMKit.framework/FBAEMKit"
    ]
  
    # Strip bitcode from each framework
    framework_paths.each do |framework_relative_path|
      strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
    end
  end
  
end

# Uncomment this line if you're not using auto-linking or if auto-linking causes trouble
# pod 'WatermelonDB', path: '../node_modules/@nozbe/watermelondb'

# WatermelonDB dependency, should not be needed on modern React Native
# (please file an issue if this causes issues for you)
# pod 'React-jsi', path: '../node_modules/react-native/ReactCommon/jsi', modular_headers: true

# WatermelonDB dependency
pod 'simdjson', path: '../node_modules/@nozbe/simdjson', modular_headers: true



