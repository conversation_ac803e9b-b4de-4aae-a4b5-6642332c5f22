# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:ios)

platform :ios do

	desc 'Fetch certificates and provisioning profiles'
	lane :certificates do
	  match(
		readonly: true
	  )
	end

  desc 'Fetch code signing secrets. Build the iOS application.'
  lane :build do
    certificates
    # Creates a signed file
    gym(
      export_method: 'app-store'
    )
  end

  desc "Upload to TestFlight"
  lane :beta do
    # Using default env variable names for fastlane to pick them up. 
    app_store_connect_api_key(
      is_key_content_base64: true,
    )
    pilot
  end

end


