#!/bin/bash

# Bitcode strip commands for the specified frameworks

# 1. FBAEMKit.framework
xcrun bitcode_strip -r Pods/FBAEMKit/XCFrameworks/FBAEMKit.xcframework/ios-arm64/FBAEMKit.framework/FBAEMKit -o Pods/FBAEMKit/XCFrameworks/FBAEMKit.xcframework/ios-arm64/FBAEMKit.framework/FBAEMKit

# 2. FBSDKCoreKit.framework
xcrun bitcode_strip -r Pods/FBSDKCoreKit/XCFrameworks/FBSDKCoreKit.xcframework/ios-arm64/FBSDKCoreKit.framework/FBSDKCoreKit -o Pods/FBSDKCoreKit/XCFrameworks/FBSDKCoreKit.xcframework/ios-arm64/FBSDKCoreKit.framework/FBSDKCoreKit

# 3. FBSDKCoreKit_Basics.framework
xcrun bitcode_strip -r Pods/FBSDKCoreKit_Basics/XCFrameworks/FBSDKCoreKit_Basics.xcframework/ios-arm64/FBSDKCoreKit_Basics.framework/FBSDKCoreKit_Basics -o Pods/FBSDKCoreKit_Basics/XCFrameworks/FBSDKCoreKit_Basics.xcframework/ios-arm64/FBSDKCoreKit_Basics.framework/FBSDKCoreKit_Basics

# 4. FBSDKGamingServicesKit.framework
xcrun bitcode_strip -r Pods/FBSDKGamingServicesKit/XCFrameworks/FBSDKGamingServicesKit.xcframework/ios-arm64/FBSDKGamingServicesKit.framework/FBSDKGamingServicesKit -o Pods/FBSDKGamingServicesKit/XCFrameworks/FBSDKGamingServicesKit.xcframework/ios-arm64/FBSDKGamingServicesKit.framework/FBSDKGamingServicesKit

# 5. FBSDKLoginKit.framework
xcrun bitcode_strip -r Pods/FBSDKLoginKit/XCFrameworks/FBSDKLoginKit.xcframework/ios-arm64/FBSDKLoginKit.framework/FBSDKLoginKit -o Pods/FBSDKLoginKit/XCFrameworks/FBSDKLoginKit.xcframework/ios-arm64/FBSDKLoginKit.framework/FBSDKLoginKit

# 6. FBSDKShareKit.framework
xcrun bitcode_strip -r Pods/FBSDKShareKit/XCFrameworks/FBSDKShareKit.xcframework/ios-arm64/FBSDKShareKit.framework/FBSDKShareKit -o Pods/FBSDKShareKit/XCFrameworks/FBSDKShareKit.xcframework/ios-arm64/FBSDKShareKit.framework/FBSDKShareKit
