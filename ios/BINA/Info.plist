<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>BINA</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>bina</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>bina</string>
				<string>fb637682384561112</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.328771878135-c91sipt1htcp2ih05586ttjh0k97uhbc</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>FacebookAppID</key>
	<string>637682384561112</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>TechiesApp</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>Our app does not request this permission or utilize this functionality but it is
			included in our info.plist since our app utilizes some library, which references this
			permission in its code.</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>Our app does not request this permission or utilize this functionality but it is
			included in our info.plist since our app utilizes some library, which references this
			permission in its code.</string>
	<key>NSCameraUsageDescription</key>
	<string>This app will use camera for profile image uploads</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>This app will capture user current location for tracking purpose</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>This app will capture user current location for tracking purpose</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app will capture user current location for tracking purpose</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app will use microphone for voice message</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app will select photos from photo gallery for profile image uploads</string>
	<key>UIAppFonts</key>
	<array>
		<string>fonts/Inter-Bold.ttf</string>
		<string>fonts/Inter-SemiBold.ttf</string>
		<string>fonts/Inter-Regular.ttf</string>
		<string>fonts/Inter-Medium.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>processing</string>
		<string>remote-notification</string>
	</array>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
  	<array>
      	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
  	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
