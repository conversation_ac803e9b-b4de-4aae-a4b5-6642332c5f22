import * as Sentry from '@sentry/react-native';
import { Platform } from 'react-native';
import database from '@src/database/index.native';
import { getLastPulledAt } from '@nozbe/watermelondb/sync/impl';
import RNFS from 'react-native-fs';
import { Q } from '@nozbe/watermelondb';
import NetInfo from '@react-native-community/netinfo';
import { getUserInfo } from '@src/configs/async-storage';

export type SyncErrorContext = {
  phase: string;
  documentId?: string;
  fileSize?: number;
  retryCount?: number;
  successCount?: number;
  failureCount?: number;
  processedCount?: number;
  unsynced_changes?: Record<
    string,
    {
      created: Array<Record<string, unknown>>;
      updated: Array<Record<string, unknown>>;
      deleted: string[];
      created_count: number;
      updated_count: number;
      deleted_count: number;
    }
  >;
  user?: {
    email: string;
    name: string;
  };
  graphQlErrors?: any;
};

export class SyncErrorHandler {
  static async capture(error: Error, context: SyncErrorContext) {
    try {
      const freeStorage = await this.getFreeStorage();
      const unsyncedChanges = await this.getUnsyedChanges();
      const user = await getUserInfo();

      Sentry.withScope(async scope => {
        scope.setContext('device', {
          platform: Platform.OS,
          freeDiskStorage: `${(freeStorage / 1024 / 1024).toFixed(2)} MB`,
          appVersion: this.getAppVersion()
        });

        // Add database context
        scope.setContext('database', {
          lastPulledAt: await getLastPulledAt(database),
          databaseVersion: database.schema.version
        });

        scope.setTags({
          sync_phase: context.phase,
          environment: process.env.NODE_ENV
        });

        scope.setExtras({
          ...context,
          unsynced_changes: unsyncedChanges,
          network_type: await this.getNetworkType()
        });

        const eventId = Sentry.captureException(error);

        // await this.notifySlack(
        //   error,
        //   {
        //     ...context,
        //     unsynced_changes: unsyncedChanges,
        //     user: user as any
        //   },
        //   eventId
        // );
      });
    } catch (handlerError) {}
  }

  private static async getFreeStorage() {
    try {
      const info = await RNFS.getFSInfo();
      return info.freeSpace;
    } catch (error) {
      return -1;
    }
  }

  private static async getUnsyedChanges() {
    try {
      return await database.read(async () => {
        const collections = Object.values(database.collections.map);
        const changes: Record<
          string,
          {
            created: Array<Record<string, unknown>>;
            updated: Array<Record<string, unknown>>;
            deleted: string[];
            created_count: number;
            updated_count: number;
            deleted_count: number;
          }
        > = {};

        for (const collection of collections) {
          const tableName = collection.table;

          try {
            // Get created records with raw data
            const created = await collection.query(Q.where('_status', 'created')).fetch();

            // Get updated records with raw data
            const updated = await collection.query(Q.where('_status', 'updated')).fetch();

            // Get deleted record IDs
            const deleted = await database.adapter.getDeletedRecords(tableName);

            if (created.length === 0 && updated.length === 0 && deleted.length === 0) {
              continue;
            }

            changes[tableName] = {
              created: created.map(c => c._raw), // Serialize raw database values
              updated: updated.map(u => u._raw),
              deleted,
              created_count: created.length,
              updated_count: updated.length,
              deleted_count: deleted.length
            };
          } catch (error) {
            changes[tableName] = {
              created: [],
              updated: [],
              deleted: [],
              created_count: -1,
              updated_count: -1,
              deleted_count: -1
              // error: error.message
            };
          }
        }

        return changes;
      });
    } catch (error) {
      return { error: error.message };
    }
  }

  private static async getNetworkType() {
    try {
      const { type } = await NetInfo.fetch();
      return type;
    } catch {
      return 'unknown';
    }
  }

  // Add to SyncErrorHandler class
  private static async notifySlack(error: Error, context: SyncErrorContext, sentryEventId?: string) {
    try {
      const SLACK_WEBHOOK = process.env.SLACK_WEBHOOK_URL;
      if (!SLACK_WEBHOOK) return;

      // Safe raw record formatting
      const formatRecords = (records: Array<Record<string, unknown>>) =>
        records
          .slice(0, 5)
          .map(r => JSON.stringify(r, null, 2))
          .join('\n\n');

      const unsyncedBlocks = Object.entries(context.unsynced_changes ?? {}).map(([table, changes]) => ({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text:
            `*${table}*\n` +
            `Created (${changes.created_count}):\n\`\`\`${formatRecords(changes.created)}\`\`\`\n` +
            `Updated (${changes.updated_count}):\n\`\`\`${formatRecords(changes.updated)}\`\`\`\n` +
            `Deleted (${changes.deleted_count}): ${changes.deleted.slice(0, 5).join(', ')}`
        }
      }));

      const message = {
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `🚨 *Sync Error - ${context.phase}*`
            }
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `*User*\n
                Name: ${context.user?.name || 'N/A'}\n
                Email: ${context.user?.email || 'N/A'}`
            }
          },
          ...(context.phase === 'presigned-upload'
            ? [
                {
                  type: 'section',
                  fields: [
                    {
                      type: 'mrkdwn',
                      text: `*Error:*\n${error.message.slice(0, 100)}`
                    },
                    {
                      type: 'mrkdwn',
                      text: `*Document:*\n${context.documentId || 'N/A'}`
                    }
                  ]
                },
                {
                  type: 'divider'
                }
              ]
            : []),
          // {
          //   type: "mrkdwn",
          //   text: `*Sentry Event:*\n${
          //     sentryEventId
          //       ? `<https://sentry.io/organizations/YOUR_ACTUAL_ORG_SLUG/issues/?query=event_id:${encodeURIComponent(sentryEventId)}|View in Sentry>`
          //       : 'N/A'
          //   }`
          // },
          ...unsyncedBlocks
        ]
      };

      const response = await fetch(SLACK_WEBHOOK, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(message)
      });

      if (!response.ok) {
      }
    } catch (slackError) {}
  }

  private static getAppVersion() {
    return `${Platform.Version}-${require('../../package.json').version}`;
  }
}
