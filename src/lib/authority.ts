import { Gql } from '@src/api';

export const manageFiles = (currentRole: string, dirId?: string, driveType?: Gql.ProjectDocumentDriveType) => {
  const projectOwner = currentRole === Gql.ProjectUserRoleType.ProjectOwner;
  const cloudCoordinator = currentRole === Gql.ProjectUserRoleType.CloudCoordinator;
  const editor = currentRole === Gql.ProjectUserRoleType.CanEdit;
  const viewer = currentRole === Gql.ProjectUserRoleType.CanView;

  //**** RULES ****//
  //? 1. Project Owner and Cloud Coordinator can do everything
  //? 2. Editor can move and add files and folders inside nested folders
  //? 3. Viewer can only view files and folders
  //? 4. Users can do everything in the personal drive

  return {
    canCreate:
      projectOwner ||
      cloudCoordinator ||
      (editor && dirId) ||
      (!viewer && driveType && driveType === Gql.ProjectDocumentDriveType.Personal),
    canDelete: projectOwner || cloudCoordinator || (driveType && driveType === Gql.ProjectDocumentDriveType.Personal),
    canMove:
      projectOwner ||
      cloudCoordinator ||
      (editor && dirId) ||
      (driveType && driveType === Gql.ProjectDocumentDriveType.Personal),
    canEdit: projectOwner || cloudCoordinator || editor,
    canShare: projectOwner || cloudCoordinator
  };
};

export const manageDocuments = (
  currentRole: string,
  dirId?: string,
  driveType?: Gql.ProjectDocumentStatus,
  isAssignee: boolean = false
) => {
  const projectOwner = currentRole === Gql.ProjectUserRoleType.ProjectOwner;
  const cloudCoordinator = currentRole === Gql.ProjectUserRoleType.CloudCoordinator;
  const editor = currentRole === Gql.ProjectUserRoleType.CanEdit;
  const viewer = currentRole === Gql.ProjectUserRoleType.CanView;

  //**** RULES ****//
  //? 1. Project Owner and Cloud Coordinator can do everything
  //? 2. Editor can add files and folders
  //? 3. Viewer can only view files and folders
  //? 4. Users can do everything in the draft drive
  //? 5. Current assignee can edit documents

  return {
    canCreate: projectOwner || cloudCoordinator || editor,
    canDelete: projectOwner || cloudCoordinator || (driveType && driveType === Gql.ProjectDocumentStatus.Draft),
    canMove: projectOwner || cloudCoordinator || editor,
    canEdit: projectOwner || cloudCoordinator || isAssignee
  };
};

export const task = (currentRole?: string) => {
  const projectOwner = currentRole === Gql.ProjectUserRoleType.ProjectOwner;
  const cloudCoordinator = currentRole === Gql.ProjectUserRoleType.CloudCoordinator;
  const editor = currentRole === Gql.ProjectUserRoleType.CanEdit;
  const viewer = currentRole === Gql.ProjectUserRoleType.CanView;

  return {
    overview: {
      canCreate: projectOwner || cloudCoordinator || editor,
      canRename: projectOwner || cloudCoordinator || editor,
      canDelete: projectOwner || cloudCoordinator,
      canSearch: projectOwner || cloudCoordinator || editor || viewer,
      canCreateRoot: projectOwner || cloudCoordinator
    },
    tasks: {
      canEdit: projectOwner || cloudCoordinator,
      canAdd: projectOwner || cloudCoordinator || editor,
      canDelete: projectOwner || cloudCoordinator
    }
  };
};

export const photo = (currentRole?: string) => {
  const projectOwner = currentRole === Gql.ProjectUserRoleType.ProjectOwner;
  const cloudCoordinator = currentRole === Gql.ProjectUserRoleType.CloudCoordinator;
  const editor = currentRole === Gql.ProjectUserRoleType.CanEdit;
  const viewer = currentRole === Gql.ProjectUserRoleType.CanView;

  return {
    canCreate: projectOwner || cloudCoordinator || editor,
    canRename: projectOwner || cloudCoordinator || editor,
    canDelete: projectOwner || cloudCoordinator,
    canSearch: projectOwner || cloudCoordinator || editor || viewer,
    canDownload: projectOwner || cloudCoordinator || editor || viewer,
    canShare: projectOwner || cloudCoordinator || editor || viewer
  };
};

export const projectPerm = (currentRole?: string) => {
  const projectOwner = currentRole === Gql.ProjectUserRoleType.ProjectOwner;
  const cloudCoordinator = currentRole === Gql.ProjectUserRoleType.CloudCoordinator;
  const editor = currentRole === Gql.ProjectUserRoleType.CanEdit;
  const viewer = currentRole === Gql.ProjectUserRoleType.CanView;

  return {
    canUpdate: projectOwner || cloudCoordinator,
    canDelete: projectOwner || cloudCoordinator
  };
};

export const workspace = (currentRole?: string) => {
  const projectOwner = currentRole === Gql.ProjectUserRoleType.ProjectOwner;
  const cloudCoordinator = currentRole === Gql.ProjectUserRoleType.CloudCoordinator;
  const editor = currentRole === Gql.ProjectUserRoleType.CanEdit;
  const viewer = currentRole === Gql.ProjectUserRoleType.CanView;

  return {
    overview: {
      canCreate: projectOwner || cloudCoordinator,
      canRename: projectOwner || cloudCoordinator,
      canDelete: projectOwner || cloudCoordinator,
      canSearch: projectOwner || cloudCoordinator || editor || viewer,
      canCreateRoot: projectOwner || cloudCoordinator
    },
    documents: {
      canCreate: projectOwner || cloudCoordinator || editor,
      canDelete: projectOwner || cloudCoordinator,
      canRequestApproval: projectOwner || cloudCoordinator || editor
    },
    templates: {
      canDelete: projectOwner || cloudCoordinator
    }
  };
};
