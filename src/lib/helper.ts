import { ApolloQueryResult, QueryResult } from '@apollo/client';
import { ProjectAccess } from '@constants/subscription';
import moment from 'moment';
import * as Gql from '@api/graphql';
import { Platform } from 'react-native';
import RNFetchBlob from 'rn-fetch-blob';

/*export const isSubscriptionActive = (
  companySubscriptions: any,
  returnDate: boolean = false
) => {
  const activeSubscription = companySubscriptions?.find((subscription: any) =>
    moment(subscription.subscriptionEndDate).isAfter(moment())
  );
  return returnDate ? activeSubscription : !!activeSubscription;
};*/

export const isAllowed = (
  companySubscriptions: QueryResult<Gql.CompanySubscriptionsQuery, Gql.Exact<{}>>,
  access: ProjectAccess | ProjectAccess[] | null,
  any: boolean = false
): boolean => {
  if (!access) {
    return false;
  }
  if (Array.isArray(access)) {
    if (any) {
      return access.some((access: ProjectAccess) => isAllowed(companySubscriptions, access));
    }
    return access.every((access: ProjectAccess) => isAllowed(companySubscriptions, access));
  }
  return companySubscriptions.data?.companySubscriptions?.nodes?.[0]?.subscriptionPackage?.[access] ?? false;
};

export const isProjectFeatureAllowed = (
  companySubscriptions: QueryResult<Gql.CompanySubscriptionsQuery, Gql.Exact<{}>>,
  projectSubscriptionPackageId: number | null,
  access: ProjectAccess | ProjectAccess[] | null,
  any: boolean = false
): boolean => {
  if (!access) {
    return false;
  }

  if (Array.isArray(access)) {
    if (any) {
      return access.some((access: ProjectAccess) =>
        isProjectFeatureAllowed(companySubscriptions, projectSubscriptionPackageId, access)
      );
    }
    return access.every((access: ProjectAccess) =>
      isProjectFeatureAllowed(companySubscriptions, projectSubscriptionPackageId, access)
    );
  }

  // If no project-specific subscription package, fall back to company subscription
  if (!projectSubscriptionPackageId || !companySubscriptions.data) {
    return isAllowed(companySubscriptions, access, any);
  }

  // Find the subscription package that matches the project's subscription package ID
  const matchingSubscription = companySubscriptions.data?.companySubscriptions?.nodes?.find(
    subscription => subscription.subscriptionPackageId === projectSubscriptionPackageId.toString()
  );

  if (matchingSubscription?.subscriptionPackage) {
    return matchingSubscription.subscriptionPackage[access] ?? false;
  }

  // If specific project subscription package not found, deny access
  return false;
};

export const modules = (modules: Gql.CategoryType | undefined) => {
  switch (modules) {
    case Gql.CategoryType.StandardForm:
      return 'Templates';
    case Gql.CategoryType.ProjectDocument:
      return 'CloudDocs';
    case Gql.CategoryType.TwoDDrawings:
      return 'Drawings';
    case undefined:
      return '';
  }
};

export const getOfflinePath = (fileUrl: string) => {
  if (Platform.OS === 'ios') {
    return `${RNFetchBlob.fs.dirs.DocumentDir}/${fileUrl}`;
  } else {
    return `${RNFetchBlob.fs.dirs.DCIMDir}/${fileUrl}`;
  }
};
