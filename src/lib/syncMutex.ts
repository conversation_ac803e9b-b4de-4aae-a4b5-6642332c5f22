// mutex.ts
export class Mutex {
  private _locked = false;
  private _waiting: Array<(unlock: () => void) => void> = [];

  private _unlock() {
    if (this._waiting.length > 0) {
      // Get the next waiting resolve and call it with the unlock function.
      const nextResolve = this._waiting.shift();
      if (nextResolve) {
        nextResolve(this._unlock.bind(this));
      }
    } else {
      this._locked = false;
    }
  }

  lock(): Promise<() => void> {
    if (this._locked) {
      // If already locked, queue the resolver.
      return new Promise(resolve => {
        this._waiting.push(resolve);
      });
    } else {
      // Lock is free, lock it and immediately return the unlock function.
      this._locked = true;
      return Promise.resolve(this._unlock.bind(this));
    }
  }

  async runExclusive<T>(callback: () => Promise<T>): Promise<T> {
    const unlock = await this.lock();
    try {
      return await callback();
    } finally {
      unlock();
    }
  }
}

// Usage
export const syncMutex = new Mutex();
