import { ApolloClient, InMemoryCache, ApolloLink } from '@apollo/client'; // Import NormalizedCacheObject
import { createUploadLink } from 'apollo-upload-client';
import { setContext } from '@apollo/client/link/context';
import { getJwtToken, getProjectId, pushMessaage } from '@configs';
import { API_URL } from '@constants';
import { onError } from '@apollo/client/link/error';

const uploadLink = createUploadLink({ uri: `${API_URL}/graphql` });

const errorLink = onError(({ networkError }) => {
  if (networkError && 'statusCode' in networkError && networkError.statusCode === 503) {
    pushMessaage('System is under maintenance', 'warning');
  }
});

// Modify the authLink to check context first
const authLink = setContext(async (operation, { headers }) => {
  // Add 'operation' parameter
  const token = await getJwtToken();
  let authorization = '';

  if (token) {
    authorization = `Bearer ${token}`;
  } else {
    authorization = '';
  }

  const contextProjectId = operation.context?.projectId;
  const projectId = contextProjectId ?? (await getProjectId()) ?? '';

  return {
    headers: {
      ...headers,
      Authorization: authorization,
      'project-id': projectId
    }
  };
});

export const apolloClient = new ApolloClient({
  link: ApolloLink.from([errorLink, authLink, uploadLink as unknown as ApolloLink]),
  cache: new InMemoryCache(),
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'no-cache',
      errorPolicy: 'ignore'
    },
    query: {
      fetchPolicy: 'no-cache',
      errorPolicy: 'all'
    }
  }
});

export default apolloClient;
