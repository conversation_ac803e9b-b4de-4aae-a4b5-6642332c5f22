import { serviceOptions } from '@src/api/rest';
import { getJwtToken, getProjectId } from '@src/configs';
import { API_URL, PROJECT_NAME } from '@src/constants';
import messaging from '@react-native-firebase/messaging';
import axios from 'axios';
import { Platform } from 'react-native';
import { store } from '@src/store';

const instance = axios.create({
  baseURL: API_URL,
  timeout: 300000,
  withCredentials: true,
  headers: {
    app: PROJECT_NAME
  }
});

instance.interceptors.request.use(async (config: any) => {
  const accessToken = await getJwtToken();
  const projectId = await getProjectId();
  const state = store.getState();
  const user = state.auth.user;

  if (accessToken) {
    config.headers['Authorization'] = `Bearer ${accessToken}`;
  }

  if (projectId) {
    config.headers['project-id'] = `${projectId}`;
  }

  if (user) {
    config.user = user;
  }

  return config;
});

// Used for notifications
export const requestDeviceToken = async () => {
  const authStatus = await messaging().requestPermission();
  const enabled =
    authStatus === messaging.AuthorizationStatus.AUTHORIZED || authStatus === messaging.AuthorizationStatus.PROVISIONAL;
  if (Platform.OS === 'ios') await messaging().registerDeviceForRemoteMessages();

  if (enabled) {
    const token = await messaging().getToken();
    return token;
  }
  return null;
};

//Inject instance to swagger-code-gen services
serviceOptions.axios = instance;
