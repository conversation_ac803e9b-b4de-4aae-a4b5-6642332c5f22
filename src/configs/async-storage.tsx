import AsyncStorage from '@react-native-async-storage/async-storage';
import { Gql } from '@src/api';
import { NotificationApiService } from '@src/api/rest';
import { generateRNFile } from './utils';

export const setProjectId = async (projectId: string) => {
  try {
    await AsyncStorage.setItem('@projectId', projectId);
    return Promise.resolve(projectId);
  } catch (e) {
    return Promise.reject(e);
  }
};

export const getProjectId = async () => {
  try {
    const projectId = await AsyncStorage.getItem('@projectId');
    return Promise.resolve(projectId);
  } catch (e) {
    return Promise.reject(e);
  }
};

export const setMetTownId = async (metTownId: string) => {
  try {
    await AsyncStorage.setItem('@metTownId', metTownId);
    return Promise.resolve(metTownId);
  } catch (e) {
    return Promise.reject(e);
  }
};

export const getMetTownId = async () => {
  try {
    const metTownId = await AsyncStorage.getItem('@metTownId');
    return Promise.resolve(metTownId);
  } catch (e) {
    return Promise.reject(e);
  }
};

export const setJwtToken = async (token: string) => {
  try {
    await AsyncStorage.setItem('@token', token);
    return Promise.resolve(token);
  } catch (e) {
    return Promise.reject(e);
  }
};

export const getJwtToken = async () => {
  try {
    const token = await AsyncStorage.getItem('@token');
    return Promise.resolve(token);
  } catch (e) {
    return Promise.reject(e);
  }
};

export const removeJwtToken = async () => {
  try {
    await AsyncStorage.removeItem('@token');
    return Promise.resolve();
  } catch (e) {
    return Promise.reject(e);
  }
};

export const storeRefreshToken = async (token: string) => {
  try {
    await AsyncStorage.setItem('@refreshToken', token);
    return Promise.resolve();
  } catch (e) {
    return Promise.reject(e);
  }
};

export const getRefreshToken = async () => {
  try {
    const token = await AsyncStorage.getItem('@refreshToken');
    return Promise.resolve(token);
  } catch (e) {
    return Promise.reject(e);
  }
};

export const removeRefreshToken = async () => {
  try {
    await AsyncStorage.removeItem('@refreshToken');
    return Promise.resolve();
  } catch (e) {
    return Promise.reject(e);
  }
};

export const storeFcmToken = async (token: string) => {
  try {
    await AsyncStorage.setItem('@fcmToken', token);
    return Promise.resolve();
  } catch (e) {
    return Promise.reject(e);
  }
};

export const getUserInfo = async () => {
  try {
    const user: any = await AsyncStorage.getItem('@user');
    const userProfile = JSON.parse(user);
    return Promise.resolve(userProfile);
  } catch (e) {
    return Promise.reject(e);
  }
};

export const storeUserInfo = async (user: any) => {
  try {
    await AsyncStorage.setItem('@user', JSON.stringify(user));
    return Promise.resolve();
  } catch (e) {
    return Promise.reject(e);
  }
};

export const removeUserInfo = async () => {
  try {
    await AsyncStorage.removeItem('@user');
    return Promise.resolve();
  } catch (e) {
    return Promise.reject(e);
  }
};

export const getFcmToken = async () => {
  try {
    const token = await AsyncStorage.getItem('@fcmToken');
    return Promise.resolve(token);
  } catch (e) {
    return Promise.reject(e);
  }
};

export const removeFcmToken = async () => {
  try {
    const token = await getFcmToken();
    await NotificationApiService.removeDeviceToken({ body: { deviceToken: token as string } });
    await AsyncStorage.removeItem('@fcmToken');
    return Promise.resolve();
  } catch (e) {
    return Promise.reject(e);
  }
};

export async function getTimestamps() {
  const item = await AsyncStorage.getItem('module');
  if (!item) return null;

  return JSON.parse(item) ?? {};
}

export async function setModuleTimestamp(moduleName: Gql.AuditLogModuleType, timestamp: Date) {
  let timestamps = await getTimestamps();

  if (timestamps === null) {
    timestamps = {};
  }
  timestamps[moduleName] = timestamp;
  await AsyncStorage.setItem('module', JSON.stringify(timestamps));
}

export async function getModuleTimestamp(moduleName: Gql.AuditLogModuleType) {
  const timestamps = await getTimestamps();
  if (timestamps === null) {
    return null;
  }
  return timestamps[moduleName] || null;
}

export async function storeBinaryFile(key: string, file: any) {
  try {
    const fileString = JSON.stringify(file);
    await AsyncStorage.setItem(key, fileString);
    return Promise.resolve();
  } catch (e) {
    // return Promise.reject(e);
  }
}

export async function getBinaryFile(key: string) {
  try {
    const fileString = await AsyncStorage.getItem(key);
    if (!fileString) {
      // return null;
    }
    const fileObject = JSON.parse(fileString);
    const { uri, name, type } = fileObject;

    const newFile = generateRNFile({
      uri,
      name: name as string,
      type: type as string
    });

    return newFile;
  } catch (e) {
    return Promise.reject(e);
  }
}

export async function removeBinaryFile(key: string) {
  try {
    await AsyncStorage.removeItem(key);
    return Promise.resolve();
  } catch (e) {
    return Promise.reject(e);
  }
}

export async function setRemoveFcmTokenFlag() {
  try {
    await AsyncStorage.setItem('@removeFcmTokenOnNextConnection', 'true');
    return Promise.resolve();
  } catch (e) {
    return Promise.reject(e);
  }
}

export async function removeRemoveFcmTokenFlag() {
  try {
    await AsyncStorage.removeItem('@removeFcmTokenOnNextConnection');
    return Promise.resolve();
  } catch (e) {
    return Promise.reject(e);
  }
}
