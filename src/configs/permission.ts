import PushNotificationIOS from '@react-native-community/push-notification-ios';
import { PermissionsAndroid, Platform, Alert } from 'react-native';

// Check and request notification permission for iOS
const requestNotificationPermissionIOS = async () => {
  return new Promise(resolve => {
    PushNotificationIOS.requestPermissions().then(granted => {
      const isGranted = granted.alert || granted.badge || granted.sound;
      resolve(isGranted);
    });
  });
};

// Check if notification permission is granted for iOS
const checkNotificationPermissionIOS = async () => {
  return new Promise(resolve => {
    PushNotificationIOS.checkPermissions(permissions => {
      const isGranted = permissions.alert || permissions.badge || permissions.sound;
      resolve(isGranted);
    });
  });
};

// Check if permissions are granted for Android
const checkNotificationPermissionAndroid = async () => {
  const granted = await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS);
  return granted;
};

// Request permissions
const requestPermissions = async () => {
  if (Platform.OS === 'android') {
    const version = Platform.Version as number;

    try {
      if (version >= 33) {
        // Request notification and media permissions for Android 13+
        const granted = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES,
          PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO,
          PermissionsAndroid.PERMISSIONS.READ_MEDIA_AUDIO,
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS // New notification permission
        ]);

        const storageGranted =
          granted['android.permission.READ_MEDIA_IMAGES'] === PermissionsAndroid.RESULTS.GRANTED &&
          granted['android.permission.READ_MEDIA_VIDEO'] === PermissionsAndroid.RESULTS.GRANTED &&
          granted['android.permission.READ_MEDIA_AUDIO'] === PermissionsAndroid.RESULTS.GRANTED;

        const notificationGranted =
          granted['android.permission.POST_NOTIFICATIONS'] === PermissionsAndroid.RESULTS.GRANTED;

        if (!notificationGranted) {
          console.warn('Notification permission denied');
        }

        return storageGranted && notificationGranted;
      } else {
        // For Android 12 and below, request traditional storage permissions
        const granted = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE
        ]);
        return (
          granted['android.permission.WRITE_EXTERNAL_STORAGE'] === PermissionsAndroid.RESULTS.GRANTED &&
          granted['android.permission.READ_EXTERNAL_STORAGE'] === PermissionsAndroid.RESULTS.GRANTED
        );
      }
    } catch (error) {
      return false;
    }
  } else if (Platform.OS === 'ios') {
    // For iOS, check and request notification permission only
    const isNotificationGranted = await checkNotificationPermissionIOS();
    if (!isNotificationGranted) {
      return await requestNotificationPermissionIOS();
    }
    return true; // Permissions are already granted
  }
};

export { requestPermissions };
