import _ from 'lodash';
import { ALERT_TYPE, Toast } from 'react-native-alert-notification';

export const getErrorMsg = (e: any) => {
  const msg =
    _.get(e, 'response.data.message', null) ||
    _.get(e, 'response.data.error', null) ||
    _.get(e, 'response.data', null) ||
    _.get(e, 'networkError.result.errors[0].message', null) ||
    _.get(e, 'graphQLErrors.result.errors[0].message', null) ||
    _.get(e, 'graphQLErrors[0].message', null) ||
    _.get(e, 'message', '');

  return msg;
};

export const pushError = (e: any, title?: string) => {
  return Toast.show({
    type: ALERT_TYPE.DANGER,
    title: title ?? 'Error Encountered',
    textBody: typeof e === 'string' ? e : getErrorMsg(e)
  });
};

export const pushSuccess = (msg: string, title?: any) => {
  return Toast.show({
    type: ALERT_TYPE.SUCCESS,
    title: title ?? 'Success',
    textBody: msg
  });
};

export const pushMessaage = (msg: string, type: 'error' | 'success' | 'warning', title?: string) => {
  if (type == 'error')
    return Toast.show({
      type: ALERT_TYPE.DANGER,
      title: title ?? 'Something went wrong',
      textBody: msg
    });
  else if (type == 'success')
    return Toast.show({
      type: ALERT_TYPE.SUCCESS,
      title: title ?? 'Success',
      textBody: msg
    });
  else if (type == 'warning')
    return Toast.show({
      type: ALERT_TYPE.WARNING,
      title: title ?? 'Warning',
      textBody: msg
    });
};
