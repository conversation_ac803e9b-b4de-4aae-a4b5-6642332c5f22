import RNFetchBlob from 'rn-fetch-blob';
import { Platform } from 'react-native';

const BASE_DIR = RNFetchBlob.fs.dirs.DocumentDir;

// Utility to sanitize file names
function sanitizeFileName(name: string): string {
  return name.replace(/\/|<|>|\*|"|:|#|\?|\\|\|/g, '_'); // Replace problematic characters
}

// Utility to ensure directory exists
async function ensureDirectory(path: string): Promise<void> {
  const exists = await RNFetchBlob.fs.isDir(path);
  if (!exists) {
    await RNFetchBlob.fs.mkdir(path);
  } else {
  }
  return;
}

// Main function to save a file locally
async function storeFileLocally(url: string, subPath: string, name: string): Promise<any> {
  if (!url) throw new Error('File URL is not available');

  const sanitizedFileName = sanitizeFileName(name);
  const fullPath = `${BASE_DIR}/${subPath}`;
  const initialFilePath = `${fullPath}/${sanitizedFileName}`;

  // Ensure directories exist
  await ensureDirectory(BASE_DIR);
  await ensureDirectory(fullPath);

  // Delete existing file if it exists
  const exists = await RNFetchBlob.fs.exists(initialFilePath);
  if (exists) {
    await RNFetchBlob.fs.unlink(initialFilePath);
  }

  try {
    const response = await RNFetchBlob.config({
      fileCache: true,
      path: initialFilePath
    }).fetch('GET', url);

    const status = response?.info?.()?.status;
    if ((Platform.OS === 'ios' && status !== 200) || (Platform.OS === 'android' && !response.data)) {
      // throw new Error('Failed to save the file. Response status: ' + status);
    }

    return initialFilePath;
  } catch (error: any) {
    // throw new Error('Error in storeFileLocally: ' + error.message);
  }
}

export default storeFileLocally;
