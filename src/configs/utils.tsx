import _ from 'lodash';
import moment from 'moment';
import numeral from 'numeral';
import { ReactNativeFile } from 'apollo-upload-client';
// import Geolocation from '@react-native-community/geolocation';
// @ts-ignore
import * as mime from 'react-native-mime-types';
import { Alert } from 'react-native';
import { ProjectDocumentStatus } from '@src/api/graphql';

// ------------------------------ VALIDATION ------------------------------ //

export const composeValidators =
  (...args: any[]) =>
  (value: any) => {
    let error: any;
    _.forEach(args, (v: (arg0: any) => any) => {
      const res = v(value);
      if (res) error = res;
    });
    return error;
  };

export const required = (value: any) => (value ? undefined : 'This field is required');
export const notEmpty = (value: any) => (!_.isEmpty(value) ? undefined : 'This field is required');
export const minImages = (min: any) => (value: any) =>
  value && value.length < min ? `Must be ${min} photos or more` : undefined;
export const maxLength = (max: any) => (value: any) =>
  value && value.length > max ? `Must be ${max} characters or less` : undefined;
export const minLength = (min: any) => (value: any) =>
  value && value.length < min ? `Must be ${min} characters or more` : undefined;
export const confirmValue = (nextValue: any, message: any) => (value: any) =>
  value === nextValue ? undefined : message || 'Value does not match';
export const maxNumber = (max: any) => (value: any) => (_.toNumber(value) > max ? `Must be ${max} or less` : undefined);
export const minNumber = (min: any) => (value: any) => (_.toNumber(value) < min ? `Must be ${min} or more` : undefined);
export const email = (value: any) =>
  value && !/^[\w+-.]+@([\w-]+\.)+[\w-]{2,5}$/i.test(value) ? 'Invalid email address' : undefined;
// value && !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,5}$/i.test(value) ? 'Invalid email address' : undefined;
export const alphaNumeric = (value: any) =>
  value && /[^a-zA-Z0-9 ]/i.test(value) ? 'Only alphanumeric characters' : undefined;
export const number = (value: any) => (value && /[^0-9 ]/i.test(value) ? 'Only numeric characters' : undefined);
export const validDate = (value: any) => {
  const date = moment(value);
  return date.isValid() ? undefined : 'Invalid date';
};
export const noWhiteSpace = (value: any) => (value && /\s/.test(value) ? 'White space is not allowed' : undefined);
export const noAlphabetic = (value: any) =>
  value && /[a-z]/i.test(value) ? 'Alphabetic character is not allowed' : undefined;

export const tooOld = (value: number) => (value && value > 65 ? 'You might be too old for this' : undefined);

export const phoneNumber = (value: string) =>
  value && /^(\+?6?01)[0|1|2|3|4|6|7|8|9]-*[0-9]{7,8}$/.test(value) ? 'Invalid phone number' : undefined;

// ------------------------------ OTHERS ------------------------------ //

let isCalled = false;
let timer: any;
export const preventDoubleClick = (functionToBeCalled: () => void, interval = 400) => {
  if (!isCalled) {
    isCalled = true;
    clearTimeout(timer);
    timer = setTimeout(() => {
      isCalled = false;
    }, interval);
    return functionToBeCalled();
  }
  return null;
};

export const generateRNFile = ({
  uri,
  name,
  extension,
  type
}: {
  uri: string;
  name: string;
  extension?: string;
  type?: string;
}) => {
  let exts = extension;
  if (mime.extension(mime.lookup(uri))) {
    exts = mime.extension(mime.lookup(uri));
  } else {
    exts = extension;
  }

  return uri
    ? new ReactNativeFile({
        uri,
        type: mime.lookup(uri) || type,
        name: `${name}${exts ? `` : ''}`
      })
    : null;
};

export const parseMoney = (value: number | undefined, format?: string) => numeral(value).format(format || '0,0.00');

export const parseLiteralDateTime = (d: any) => moment(d).format('D MMM YYYY, h:mmA');

export const parseFormData = (obj: any) => {
  const form = new FormData();
  _.forOwn(obj, (value, key) => {
    if (value !== null) {
      if (_.isArray(value)) {
        form.append(key, JSON.stringify(value));
      } else {
        form.append(key, value);
      }
    }
  });
  return form;
};

export const removeSpaces = (value: string) => {
  return value.split(' ').join('');
};

export const parseCartItems = (arr: any) => {
  let result: any = [];
  const derivedArr = _.groupBy(arr, o => o.product.merchant.id);
  _.forEach(derivedArr, data => {
    result.push({
      merchant: _.get(data[0], 'product.merchant', {}),
      data
    });
  });

  return result;
};

export const validateFormPass = (values: any, items: any[]) => {
  for (let i = 0; i < _.size(items); i++) {
    if (items[i] === 'image[0]') {
      if (_.isEmpty(_.get(values, 'image[0]', {}))) {
        return false;
      }
    } else if (items[i] === 'image') {
      // if (!_.get(values['image[0]'], '', null)) {
      //   return false;
      // }
      return false;
    } else {
      if (!items[i]) {
        return true;
      }
      if (!values[items[i]]) {
        return false;
      }
    }
  }
  return true;
};

export const generateUniqueFilename = (newFile: any, selectedAttachments: string[]) => {
  // Check if the new file's name already exists in the selectedAttachments array
  if (!newFile) return;
  if (selectedAttachments.includes(newFile.name ?? '')) {
    let counter = 1;
    let newName = newFile.name;
    // Loop until a unique name is found
    while (selectedAttachments.includes(newName ?? '')) {
      // Add a number in parentheses to the end of the name, before the file extension
      newName = `${newFile.name?.split('.').slice(0, -1).join('.')} (${counter}).${
        newFile.name?.split('.').slice(-1)[0]
      }`;
      counter++;
    }

    // Modify the name of the new file
    newFile.name = newName;
  }

  return newFile;
};

export const showFileName = (module: 'Task' | 'File' | 'Folder' | 'Group', name: string) =>
  Alert.alert(`${module} Name`, name);

export const getDocumentStatus = (status: ProjectDocumentStatus) => {
  switch (status) {
    case ProjectDocumentStatus.Submitted:
      return 'Submitted';
    case ProjectDocumentStatus.Rejected:
      return 'Rejected';
    case ProjectDocumentStatus.Approved:
      return 'Approved';
    case ProjectDocumentStatus.Pending:
      return 'Pending';
    case ProjectDocumentStatus.Draft:
      return 'Draft';
    case ProjectDocumentStatus.InProgress:
      return 'In Progress';
    case ProjectDocumentStatus.InReview:
      return 'In Review';
  }
};

export const getDeleteConfirmationMessage = (offlineMode: boolean | undefined) => {
  if (offlineMode) {
    return 'Are you sure you want to delete this file from offline access?';
  } else {
    return 'Are you sure you want to delete this file?';
  }
};

export const getDeleteConfirmationTitle = (offlineMode: boolean | undefined) => {
  if (offlineMode) {
    return 'Delete';
  } else {
    return 'Delete Folder/File';
  }
};

export function filterNewEntities<T extends { id?: string }>(entities: T[] | undefined): T[] {
  return entities?.filter(entity => !entity.id) || [];
}

export const sanitizer = (rawReactions: any[]) => {
  return Array.isArray(rawReactions) ? rawReactions.map(String) : [];
};

export const parseJSON = (json: string | null, defaultValue: any[] = []): any[] => {
  try {
    if (!json) return defaultValue;
    const parsed = JSON.parse(json);
    if (!Array.isArray(parsed)) throw new Error('Expected an array');
    return parsed;
  } catch (error) {
    console.warn(`Invalid JSON: ${json}`, error);
    return defaultValue;
  }
};

export const createLookupMap = (array: any[], key: string): Map<string, any> =>
  new Map(array.map(item => [item[key], item]));

// export const openBrowser = async (url: string) => {
//   try {
//     if (await InAppBrowser.isAvailable()) {
//       await InAppBrowser.open(url, {
//         // iOS Properties
//         ...BROWSER_CONFIG
//       });
//     } else Linking.openURL(url);
//   } catch (error) {
//     pushError(error);
//   }
// };

// export const getCurrentLocation = () => {
//   return new Promise((resolve, reject) => {
//     Geolocation.getCurrentPosition(
//       res => {
//         resolve(res);
//       },
//       e => {
//         reject(e);
//       },
//       {
//         timeout: 10000,
//         enableHighAccuracy: true,
//         maximumAge: 10,
//         distanceFilter: 10
//       }
//     );
//   });
// };

// export const useKeyboard = () => {
//   const [isKeyboardVisible, setKeyboardVisible] = useState<boolean>(false);

//   useEffect(() => {
//     const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
//       setKeyboardVisible(true); // or some other action
//     });
//     const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
//       setKeyboardVisible(false); // or some other action
//     });

//     return () => {
//       keyboardDidHideListener.remove();
//       keyboardDidShowListener.remove();
//     };
//   }, []);

//   return isKeyboardVisible;
// };
