import React, { useEffect } from 'react';
import messaging from '@react-native-firebase/messaging';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import { Linking, Platform } from 'react-native';
import _ from 'lodash';
import { useDispatch } from '@src/store';
import { ProjectActions } from '@src/slice/project.slice';
import { Gql } from '@src/api';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootNavigatorParams } from '@src/types';
import { pushError } from './helpers';

const ForegroundHandler = () => {
  const dispatch = useDispatch();
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();

  const { data: user } = Gql.useGetUserMeQuery({});

  const [switchCompany] = Gql.useSwitchCompanyMutation({});

  useEffect(() => {
    const type = 'notification';
    const unSubscribe = messaging()?.onMessage(async remoteMessage => {
      if (Platform.OS === 'ios') {
        PushNotificationIOS.addEventListener(type, onRemoteNotification);
        PushNotificationIOS.presentLocalNotification({
          alertTitle: remoteMessage.notification?.title,
          alertBody: remoteMessage.notification?.body ?? '',
          userInfo: remoteMessage.data
        });
      }
    });

    messaging()?.onNotificationOpenedApp(async remoteMessage => {
      const deepLink = _.get(remoteMessage, 'data.link', null);
      const projectId = _.get(remoteMessage, 'data.projectId', null);
      const companyId = _.get(remoteMessage, 'data.companyId', null);
      const sanitizedDeepLink = deepLink?.replace(/:/g, '');
      let deepLinkWithProjectId = sanitizedDeepLink?.replace(/\/0\//g, '/1/');

      if (deepLinkWithProjectId?.includes('digital-form/1/')) {
        const pattern = /digital-form\/1\/(\d+)\/(.+)/;
        const match = deepLinkWithProjectId?.match(pattern);
        if (match && match[1] && match[2]) {
          deepLinkWithProjectId = `documentDetail/${match[1]}/${match[2]}`;
        } else {
          return;
        }
      }

      if (deepLinkWithProjectId?.startsWith('task/1/')) {
        // Extract the ID and redirect path
        const pattern = /task\/1\/(\d+)\/(.+)/;
        const match = deepLinkWithProjectId?.match(pattern);

        if (match && match[1] && match[2]) {
          deepLinkWithProjectId = `EditTask/${match[1]}/${match[2]}`;
        } else {
          return pushError('Invalid deep link');
        }
      }

      if (user?.getUserMe?.company?.id !== companyId) {
        if (!companyId) return;
        await switchCompany({
          variables: {
            companyId: parseInt(companyId)
          }
        })
          .then(() => {
            navigation.push('ProjectsNav', { screen: 'AllProjects' });
          })
          .finally(() => {
            if (!projectId) return;
            dispatch(ProjectActions.updateProjectId(projectId));

            if (deepLinkWithProjectId) return Linking.openURL(`bina://${deepLinkWithProjectId}`);
          });
      }

      if (!projectId) return;
      dispatch(ProjectActions.updateProjectId(projectId));

      if (deepLink) Linking.openURL(`bina://${deepLink}`);
    });

    // * Open from quit state
    // messaging()
    //   .getInitialNotification()
    //   .then(async remoteMessage => {

    //     const deepLink = _.get(remoteMessage, 'data.link', null);
    //     const projectId = _.get(remoteMessage, 'data.projectId', null);
    //     const companyId = _.get(remoteMessage, 'data.companyId', null);
    //     const sanitizedDeepLink = deepLink?.replace(/:/g, '');
    //     let deepLinkWithProjectId = sanitizedDeepLink?.replace(/\/0\//g, '/1/');

    //     if (deepLinkWithProjectId?.includes('digital-form/1/')) {
    //       const pattern = /digital-form\/1\/(\d+)\/(.+)/;
    //       const match = deepLinkWithProjectId?.match(pattern);
    //       //       if (match && match[1] && match[2]) {
    //         deepLinkWithProjectId = `documentDetail/${match[1]}/${match[2]}`
    //       } else {
    //         return //       }
    //     }

    //     if (deepLinkWithProjectId?.startsWith('task/1/')) {
    //       // Extract the ID and redirect path
    //       const pattern = /task\/1\/(\d+)\/(.+)/;
    //       const match = deepLinkWithProjectId?.match(pattern);

    //       if (match && match[1] && match[2]) {
    //         deepLinkWithProjectId = `EditTask/${match[1]}/${match[2]}`;
    //       } else {
    //         return pushError('Invalid deep link');
    //       }
    //     }

    //     if (user?.getUserMe?.company?.id !== companyId) {
    //       if (!companyId) return;
    //       await switchCompany({
    //         variables: {
    //           companyId: parseInt(companyId)
    //         }
    //       })
    //         .then(() => {
    //           navigation.push('ProjectsNav', { screen: 'AllProjects' });
    //         })
    //         .finally(() => {
    //           if (!projectId) return;
    //           dispatch(ProjectActions.updateProjectId(projectId));

    //           if (deepLinkWithProjectId) return Linking.openURL(`bina://${deepLinkWithProjectId}`);
    //         });
    //     }

    //     if (!projectId) return;
    //     dispatch(ProjectActions.updateProjectId(projectId));

    //     if (deepLink) Linking.openURL(`bina://${deepLink}`);
    //   });

    return () => {
      unSubscribe();
      PushNotificationIOS.removeEventListener(type);
    };
  }, []);

  const onRemoteNotification = (notification: any) => {
    const result = PushNotificationIOS.FetchResult;
    notification.finish(result);
  };

  return null;
};

export default ForegroundHandler;
