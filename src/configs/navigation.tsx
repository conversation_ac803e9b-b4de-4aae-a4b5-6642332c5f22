import { createNavigationContainerRef } from '@react-navigation/native';

export const navigationRef = createNavigationContainerRef();

export const navigate = (name: any, params?: any) => {
  if (navigationRef.isReady()) {
    navigationRef.navigate(name as never, params as never);
  }
};

export const resetNavigation = (props: any) => {
  if (navigationRef.isReady()) {
    navigationRef.reset(props);
  }
};
