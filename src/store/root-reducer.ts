import { combineReducers } from '@reduxjs/toolkit';
import { appReducer } from '@src/slice/app.slice';
import { authReducer } from '@src/slice/auth.slice';
import { documentWorkspaceReducer } from '@src/slice/document-workspace.slice';
import { drawingsReducer } from '@src/slice/drawings.slice';
import { photosReducer } from '@src/slice/photos.slice';
import { projectReducer } from '@src/slice/project.slice';
import { taskReducer } from '@src/slice/tasks.slice';
import { notificationReducer } from '@src/slice/notification.slice';
import { correspondenceReducer } from '@src/slice/corrrespondence';

export const rootReducer = combineReducers({
  auth: authReducer,
  app: appReducer,
  project: projectReducer,
  documentWorkspace: documentWorkspaceReducer,
  photos: photosReducer,
  tasks: taskReducer,
  drawings: drawingsReducer,
  notifications: notificationReducer,
  correspondence: correspondenceReducer
});
