import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useDispatch, useSelector } from '@store';
import { RootNavigatorParams } from '@types';
import React, { useEffect, useRef, useState } from 'react';
import RNBootSplash from 'react-native-bootsplash';
import messaging from '@react-native-firebase/messaging';

// Components
import Tab from '@modules/App/TabNavigator';
import Authentication from '@modules/Authentication/AuthenticationNavigator';
import { Alert, AppState, Linking, Platform } from 'react-native';
import AccountNavigator from './modules/Account/AccountNavigator';
import CloudDocNavigator from './modules/CloudDocs/CloudDocsNavigator';
import ContactNavigator from './modules/Contacts/ContactNavigator';
import DashboardNavigator from './modules/Dashboard/DashboardNavigator';
import MemberNavigator from './modules/Members/MemberNavigator';
import NotificationsNavigator from './modules/Notification/NotificationsNavigator';
import OverviewNavigator from './modules/Overview/OverviewNavigator';
import PhotosNavigator from './modules/Photos/PhotosNavigator';
import ProjectNavigator from './modules/Projects/ProjectNavigator';
import TaskNavigator from './modules/Task/TaskNavigator';
import { AuthActions } from './slice/auth.slice';
import DigitalFormNavigator from './modules/DigitalForm/DigitalFormNavigator';
import DrawingNavigator from './modules/Drawing/DrawingNavigator';
import PushNotification, { Importance } from 'react-native-push-notification';
import { PushNotificationController } from './commons/PushNotificationController';
import ForegroundHandler from './configs/ForegroundHandler';
import PdftronNavigator from './modules/Pdftron/PdftronNavigator';
import VersionCheck from 'react-native-version-check';
import { Gql } from './api';
import VideoNavigator from './modules/Videos/VideoNavigator';
import socket from './lib/socket';
import { notificationActions } from './slice/notification.slice';
import NetInfo from '@react-native-community/netinfo';
import OfflineNotice from './commons/OfflineNotice';
import CorrespondenceNavigator from './modules/Correspondence/CorrespondenceNavigator';

PushNotification.createChannel(
  {
    channelId: 'local-channel',
    channelName: 'Local channel',
    channelDescription: 'A channel to categorise your local notifications',
    importance: Importance.HIGH,
    vibrate: true
  },
  _created => {
    // } // (optional) callback returns whether the channel was created, false means it already existed.
  }
);

const RootStack = createNativeStackNavigator<RootNavigatorParams>();
const Route: React.FC<any> = () => {
  const dispatch = useDispatch();
  const auth = useSelector(state => state.auth);
  const project = useSelector(state => state.project);
  const appState = useRef(AppState.currentState);
  const [getVersions] = Gql.useGetMobileVersionsLazyQuery();

  useEffect(() => {
    AppState.addEventListener('change', handleAppStateChange);
  }, []);

  useEffect(() => {
    if (auth.isInitialized) {
      RNBootSplash.hide({ fade: true });
      return;
    }

    handleAppStateChange(appState.current);
    dispatch(AuthActions.initialize());
  }, [auth.isInitialized]);

  useEffect(() => {
    if (auth.user?.id && project?.projectId) {
      const roomName = `project-user-room-${project?.projectId}-${auth?.user?.id}`; // Replace userId with the actual user ID
      const notificationRoomName = `notification-room-${auth?.user?.id}`; // Replace userId with the actual user ID
      socket.emit('joinRoom', roomName);
      socket.emit('joinRoom', notificationRoomName);

      socket.on('event:project-user-updated', () => {
        dispatch(AuthActions.initialize());
      });

      socket.on('event:project-user-updated', () => {
        dispatch(AuthActions.initialize());
      });

      socket.on('event:notification', () => {
        dispatch(notificationActions.getUnreadCount());
      });

      return () => {
        socket.off('event:project-user-updated');
        socket.off('event:notification');
      };
    }
  }, [auth?.user?.id, project?.projectId]);

  const linking: any = {
    prefixes: ['https://bina://', 'bina://'],
    config: {
      screens: {
        Authentication: {
          screens: {
            Onboarding: 'onboarding',
            ResetPassword: 'reset-password'
          }
        },
        TasksNav: {
          screens: {
            EditTask: 'EditTask/:id/:redirect',
            AddTask: 'detail-task/:id'
          }
        },
        Tab: {
          screens: {
            Tasks: 'task/:pageIndex/:taskId',
            CloudDoc: 'cloud-doc/:pageIndex',
            DigitalForm: 'digital-form/:pageIndex/:documentId',
            Drawings: 'drawings/:pageIndex/:documentId'
          }
        },
        DigitalFormNav: {
          screens: {
            DigitalFormPdfTron: 'digital-form',
            DocumentDetail: 'documentDetail/:id/:redirect',
            DynamicDocumentDetail: 'dynamicDocumentDetail/:id/:redirect'
          }
        },
        Photos: {
          screens: {
            Photos: 'photo'
          }
        },
        PdftronNav: {
          screens: {
            Pdftron: 'pdftron/:id/:path/:screen'
          }
        },
        DashboardNav: {
          screens: {
            Dashboard: 'dashboard/:redirect'
          }
        },
        DrawingNav: {
          screens: {
            DrawingRevision: 'drawing-revision/:id'
          }
        },
        CorrespondenceNav: {
          screens: {
            Content: 'correspondence/:id'
          }
        }
      }
    }
  };

  const handleAppStateChange = (nextAppState: any) => {
    if (nextAppState === 'active') {
      // App has come to the foreground!
      // revokeAuthentication(false);
      // getVersions().then(data => {
      //   const latestAppVersion = data.data?.mobileVersions.nodes.find(x => x.platformName === Platform.OS)?.versionCode;
      //   if (latestAppVersion == undefined) return;
      //   if (Platform.OS == 'ios')
      //     VersionCheck.needUpdate({
      //       depth: 3,
      //       currentVersion: VersionCheck.getCurrentVersion(),
      //       latestVersion: data.data?.mobileVersions.nodes.find(x => x.platformName === 'ios')?.versionCode,
      //       packageName: 'cloud.bina.ios',
      //       provider: 'appStore'
      //     }).then(res => {
      //       if (res.isNeeded) {
      //         Alert.alert('App Needs An Update', 'This app requires a latest update to be functional, please update now.', [
      //           {
      //             text: 'Update',
      //             style: 'default',
      //             onPress: () => {
      //               Linking.openURL('https://apps.apple.com/us/app/bina/id6444920215');  // open store if update is needed.
      //             }
      //           }
      //         ]);
      //       }
      //     });
      //   else if (Platform.OS == 'android')
      //     VersionCheck.needUpdate({
      //       depth: 3,
      //       currentVersion: VersionCheck.getCurrentVersion(),
      //       latestVersion: data.data?.mobileVersions.nodes.find(x => x.platformName === 'android')?.versionCode,
      //       packageName: 'cloud.bina.android',
      //       provider: 'playStore',
      //     }).then(res => {
      //       if (res.isNeeded) {
      //         Alert.alert('App Needs An Update', 'This app requires a latest update to be functional, please update now.', [
      //           {
      //             text: 'Update',
      //             style: 'default',
      //             onPress: () => {
      //               Linking.openURL('https://play.google.com/store/apps/details?id=cloud.bina.android');  // open store if update is needed.
      //             }
      //           }
      //         ]);
      //       }
      //     });
      // })
    } else if (nextAppState === 'background') {
      // App has come to the background!
      // revokeAuthentication(true);
    }
    appState.current = nextAppState;
  };

  const routeName = auth.isAuthenticated ? 'ProjectsNav' : 'Authentication';
  return (
    <>
      <NavigationContainer linking={linking}>
        {auth.isInitialized && (
          <RootStack.Navigator screenOptions={{ headerShown: false }} initialRouteName={routeName}>
            {!auth.isAuthenticated ? (
              <RootStack.Screen name="Authentication" component={Authentication} />
            ) : (
              <>
                <RootStack.Screen name="ProjectsNav" component={ProjectNavigator} />
                <RootStack.Screen name="Tab" component={Tab} />
                <RootStack.Screen name="TasksNav" component={TaskNavigator} />
                <RootStack.Screen name="NotificationsNav" component={NotificationsNavigator} />
                <RootStack.Screen name="AccountNav" component={AccountNavigator} />
                <RootStack.Screen name="MembersNav" component={MemberNavigator} />
                <RootStack.Screen name="ContactsNav" component={ContactNavigator} />
                <RootStack.Screen name="OverviewNav" component={OverviewNavigator} />
                <RootStack.Screen name="DashboardNav" component={DashboardNavigator} />
                <RootStack.Screen name="PhotosNav" component={PhotosNavigator} />
                <RootStack.Screen name="CloudDocsNav" component={CloudDocNavigator} />
                <RootStack.Screen name="DigitalFormNav" component={DigitalFormNavigator} />
                <RootStack.Screen name="DrawingNav" component={DrawingNavigator} />
                <RootStack.Screen name="PdftronNav" component={PdftronNavigator} />
                <RootStack.Screen name="VideoNav" component={VideoNavigator} />
                <RootStack.Screen name="CorrespondenceNav" component={CorrespondenceNavigator} />
              </>
            )}
          </RootStack.Navigator>
        )}
        {/* <OfflineNotice /> */}
        <PushNotificationController />
        <ForegroundHandler />
      </NavigationContainer>
    </>
  );
};

export default Route;
