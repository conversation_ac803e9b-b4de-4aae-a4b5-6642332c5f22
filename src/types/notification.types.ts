import {
  ReceivedNotification,
  PushNotificationPermissions,
  PushNotificationObject
} from 'react-native-push-notification';

export interface NotificationData {
  link?: string;
  projectId?: string;
  [key: string]: any;
}

// Base notification interface for consistent message handling
export interface BaseNotification {
  title?: string;
  message: string | object;
  channelId?: string;
  data?: NotificationData;
  userInteraction?: boolean;
  foreground?: boolean;
  finish?: (fetchResult: string) => void;
}

// Combine base notification with ReceivedNotification
export type PushNotificationPayload = BaseNotification & Partial<ReceivedNotification>;

export type NotificationPriority = 'max' | 'high' | 'low' | 'min' | 'default';
export type NotificationVisibility = 'public' | 'private' | 'secret';

export interface LocalNotificationConfig extends Omit<PushNotificationObject, 'priority'> {
  autoCancel: boolean;
  subText: string;
  title: string;
  message: string;
  vibrate: boolean;
  vibration: number;
  playSound: boolean;
  soundName: string;
  priority: NotificationPriority;
  ignoreInForeground: boolean;
  channelId: string;
  smallIcon: string;
  allowWhileIdle: boolean;
  userInfo: any;
}

export interface DeepLinkConfig {
  pattern: RegExp;
  transform: (match: RegExpMatchArray) => string | null;
}

export interface SyncConfig {
  showSyncModal: boolean;
  syncMutateOptions: {
    dispatchStatus: boolean;
  };
  offlineDownloadOptions: {
    dispatchStatus: boolean;
    id: string;
  };
}

export interface Notification {
  id: string;
  actionName: string;
  actionType: string;
  avatar: string | null;
  body?: string;
  bodyColon?: string;
  color?: string;
  createdAt: {
    raw: string;
    formatted: string;
  };
  deepLink: string;
  header?: string;
  headColon?: string;
  link?: {
    web?: string;
    redirect?: string;
    mobile?: string;
  };
  name?: string;
  projectId?: string;
  read: boolean;
  tail?: string;
  title?: string;
  updatedAt?: string;
  userId?: string;
}
