import { email } from '@configs';
import { NavigatorScreenParams } from '@react-navigation/native';
import { Gql } from '@src/api';
import { ProjectDocumentDriveType } from '@src/api/graphql';
import DrawingRevisionModel from '@src/database/model/drawing-revision.model';
import { UserDetail } from 'project-user';

export type RootNavigatorParams = {
  Authentication: NavigatorScreenParams<AuthenticationNavigatorParams>;
  Tab: NavigatorScreenParams<TabNavigatorParams>;
  TasksNav: NavigatorScreenParams<TaskNavigatorParams>;
  NotificationsNav: NavigatorScreenParams<NotificationsNavigatorParams>;
  AccountNav: NavigatorScreenParams<AccountNavigatorParams>;
  ProjectsNav: NavigatorScreenParams<ProjectNavigatorParams>;
  MembersNav: NavigatorScreenParams<MemberNavigatorParams>;
  ContactsNav: NavigatorScreenParams<ContactNavigatorParams>;
  OverviewNav: NavigatorScreenParams<OverviewNavigatorParams>;
  DashboardNav: NavigatorScreenParams<DashboardNavigatorParams>;
  PhotosNav: NavigatorScreenParams<PhotosNavigatorParams>;
  CloudDocsNav: NavigatorScreenParams<CloudDocNavigatorParams>;
  DigitalFormNav: NavigatorScreenParams<DigitalFormNavigatorParams>;
  DrawingNav: NavigatorScreenParams<DrawingNavigatorParams>;
  PdftronNav: NavigatorScreenParams<PdftronNavigatorParams>;
  VideoNav: NavigatorScreenParams<VideoNavigatorParams>;
  CorrespondenceNav: NavigatorScreenParams<CorrespondenceNavigatorParams>;
};

export type TabNavigatorParams = {
  Task: { pageIndex: number };
  Tasks: { pageIndex: number };
  TasksOverview: undefined;
  CloudDoc: { pageIndex: number };
  DigitalForm: { pageIndex: number };
  Drawings: { pageIndex: number };
  More: undefined;
};

export type AuthenticationNavigatorParams = {
  AuthMenu: undefined;
  LoginProcessing: undefined;
  Login:
    | undefined
    | {
        onSuccess?: () => void;
      };
  SignUp: undefined;
  ForgotPassword: undefined;
  ResetPassword: {
    newPassword: string;
    token: string;
  };
  Tab: undefined;
  ResetPasswordSuccessful: undefined;
  Onboarding: {
    token: string;
  };
  VerifyOTP: {
    email: string;
    tokens: any;
  };
};

export type TaskNavigatorParams = {
  Task: { pageIndex: number };
  Tasks: undefined;
  TasksOverview: undefined;
  AddAssignee: {
    ass: UserDetail[];
    onPressBack?: (() => void) | undefined;
  };
  AddCc: {
    ass: UserDetail[];
    onPressBack?: (() => void) | undefined;
  };
  AddTask: {
    refetch: () => void;
  };
  EditTask: {
    id: string;
    refetch?: () => void;
  };
  AttachPdftron: {
    id: string;
  };
  PhotoPdftron: {
    id: string;
  };
  CreateMemo: {
    id: string;
  };
};

export type NotificationsNavigatorParams = {
  Notifications: undefined;
  NovuNotificationDrawer: undefined;
};

export type AccountNavigatorParams = {
  Account: undefined;
  Dashboard: undefined;
  Overview: undefined;
  Contacts: undefined;
  Members: undefined;
  Photos: undefined;
  FieldReport: undefined;
  ActivityLog: undefined;
  LiveChat: undefined;
  Settings: undefined;
  Correspondence: undefined;
  Logout: undefined;
  FormCategories: {
    id?: string;
    refetch: () => void;
  };
};

export type ProjectNavigatorParams = {
  AllProjects: undefined;
  CreateProject: {
    refetch?: () => void;
    allProjectRefetch?: () => void;
  };
};

export type MemberNavigatorParams = {
  Members: undefined;
  InviteMembers: undefined;
  MembersDetails: {
    id: string;
    refetch?: () => void;
    refetchViewer?: () => void;
  };
};

export type ContactNavigatorParams = {
  Contacts: undefined;
  AddContact: {
    refetch: () => void;
  };
  ContactDetails: {
    id: string;
    refetch: () => void;
  };
  ReviewImportedContact: {
    convertedCsv: any;
    refetch: () => void;
  };
};

export type OverviewNavigatorParams = {
  Overview: undefined;
  EditOverviewProjectDetails: {
    id: string;
    refetch: () => void;
  };
};

export type DashboardNavigatorParams = {
  Dashboard: undefined;
  Event: {
    id?: string;
    refetch: () => void;
  };
};

export type PhotosNavigatorParams = {
  Photos: {
    isRealod?: boolean;
  };
  Folder: {
    category?: string;
    id?: string;
    pageIndex?: number;
    projectDocumentId?: string;
    refetch?: () => void;
  };
  DirFolder: {
    id: string;
    name: string;
    refetch: () => void;
  };
  PhotoImageViewer: {
    id: string;
  };
  MovingFile: {
    id: string;
  };
};

export type CloudDocNavigatorParams = {
  CloudDoc: { pageIndex: number };
  CloudDocSubFolder: {
    id: string;
    name: string;
    category: string;
    refetch?: () => void;
    role: string;
    driveType?: ProjectDocumentDriveType;
    fileUrl?: string;
  };
  CloudDocFolder: {
    category: string;
    id?: string;
    pageIndex?: number;
    projectDocumentId?: string;
    refetch: () => void;
    driveType?: Gql.ProjectDocumentDriveType;
  };
  PdfTron: {
    id: string;
  };
  CloudDocImageViewer: {
    id: string;
  };
  CloudDocWebView: {
    id?: string;
    projectId: string;
    category: string;
  };
  CloudDocMovingFile: {
    selectedDocumentId: string;
  };
};

export type DigitalFormNavigatorParams = {
  DigitalForm: { pageIndex: number };
  DigitalFormSubFolder: {
    id: string;
    name: string;
    category: string;
    refetch: () => void;
  };
  DigitalFormFolder: {
    category: string;
    id?: string;
    pageIndex?: number;
    projectDocumentId?: string;
    refetch: () => void;
  };
  RequestSignature: {
    documentId: string;
  };
  DigitalFormPdfTron: {
    id: string;
    role: string;
    assigneeIds: any;
    addedBy: string;
  };
  StandardFormPdfTron: {
    id: string;
    role: string;
    assigneeIds: any;
  };
  AddAssignee: {
    type?: 'linear' | 'dynamic';
    permittedAss: any;
    documentId: string;
    ass: string[];
    onPressBack?: (() => void) | undefined;
  };
  AddAssigneeApproveProceed: {
    currentAssigneeId: string;
    permittedAss?: any;
    documentId: string;
    groupName?: string;
    ass: string[];
    onPressBack?: (() => void) | undefined;
  };
  AddCc: {
    documentId: string;
    ccs?: any;
  };
  AddCcDocumentDetails: {
    documentId: string;
    ccs: string[];
  };
  MovingFile: { docId: number; category: string };
  CreateFolder: {
    category?: string;
    id?: number;
    pageIndex?: number;
    projectDocumentId?: string;
    refetch?: () => void;
    driveType?: Gql.ProjectDocumentDriveType;
  };
  DigitalFormAttachedPdftron: {
    id: string;
    onPressBackPdftron?: (() => void) | undefined;
  };
  DigitalFormPhotosPdftron: {
    id: string;
    onPressBackPdftron?: (() => void) | undefined;
  };
  DocumentDetail: {
    refetch: () => void;
  };
  DynamicDocumentDetail: {
    refetch: () => void;
  };
};

export type DrawingNavigatorParams = {
  Drawings: {
    pageIndex: number;
  };
  DrawingDetails: {
    id: string;
  };
  DrawingFolder: {
    category: string;
    id?: string;
    pageIndex?: number;
    projectDocumentId?: string;
    localRemoteId?: string;
  };
  DrawingSubFolder: {
    id: number;
    name: string;
    category: string;
    refetch: () => void;
    role: string;
    localRemoteId?: string;
  };
  DrawingPdfTron: {
    id: string;
    role: string;
  };
  DrawingWebView: {
    id: string;
  };
  DrawingMovingFile: {
    docId: number;
    category: string;
    refetch: () => void;
  };
  DrawingRevision: {
    id?: string;
    data?: any;
    refetch?: () => void;
  };
};

export type PdftronNavigatorParams = {
  Pdftron: {
    id: string;
    data?: any;
    role?: string;
    revisionData?: DrawingRevisionModel[] | [];
    isHigherVersion?: boolean;
    assigneeIds?: any;
    addedBy?: number;
    modules: string;
    driveType?: string;
    status?: Gql.ProjectDocumentStatus;
    onPressBack?: (() => void) | undefined;
    currentUserStatus?: string;
    path?: string;
    screen?: string;
    projectDocumentId?: string | number;
    isCurrentAssignee?: boolean;
    workflow?: any;
    fileUrl?: string;
    isPreview?: boolean;
    localRemoteId?: string;
    localId?: string;
    xfdf?: string;
    changed?: string;
    version?: number;
  };
};

export type VideoNavigatorParams = {
  VideoViewer: {
    id: string;
    type: string;
    fileUrl: string;
    name: string;
    onPressBack?: (() => void) | undefined;
  };
};

export type CorrespondenceNavigatorParams = {
  Overview: undefined;
  Content: {
    id: string;
  };
};
