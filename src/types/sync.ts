import { Gql } from '@src/api';
import { EnumSyncInputDtoModule } from '@src/api/rest';

export interface SyncInputDto {
  lastPulledAt: Date;
  lastPushedAt?: Date;
  module?: EnumSyncInputDtoModule; // Using the generated enum type
  parentIds?: number[];
  offset?: number;
  limit?: number;
}

export interface PaginationMetadata {
  offset: number;
  limit: number;
  total: number;
  hasMore: boolean;
}

export interface SyncResponseDto {
  changes: { [tableName: string]: TableChangesDto };
  timestamp: number;
  pagination?: PaginationMetadata;
}

export interface TableChangesDto {
  created: any[];
  updated: any[];
  deleted: number[];
}
