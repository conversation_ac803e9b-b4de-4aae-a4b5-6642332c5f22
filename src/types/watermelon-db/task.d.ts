import ProjectUserModel from '@src/database/model/project-user.model';

declare module 'task' {
  import { BaseRawModel } from './index';

  interface TaskRaw extends BaseRawModel {
    projectId: string;
    ownerId: number;
    groupId?: number;
    taskCode: number;
    title: string;
    annotId?: string;
    spriteId?: string;
    elementId?: string;
    pos3D?: string;
    bimId?: string;
    imageURL?: string;
    description?: string;
    dueDate?: string;
    status: string;
    isSentBeforeOneDay: boolean;
    isSentBeforeOneHour: boolean;
    permanentlyDeleted: boolean;
    proposedStatus?: string;
    isUrgent?: boolean;
    memoUrl?: string;
    previewMemoUrl?: string;
    isMemoReceive?: boolean;
    issuedById: number;
    assignees?: string;
    copies?: string;
    documents?: string;
  }

  interface TaskAttachmentRaw extends BaseRawModel {
    fileUrl: string;
    name: string;
    taskId: number;
    type: string;
    userId: number;
  }

  interface TaskMediaRaw extends BaseRawModel {
    fileUrl: string;
    name: string;
    taskId: number;
    type: string;
    userId: number;
  }

  interface TaskCommentRaw extends BaseRawModel {
    message: string;
    taskId: number;
  }

  interface TaskDetail {
    group: Group;
    assignees: UserDetail[];
    copies: UserDetail[];
    attachments: Attachment[];
    media: Media[];
    owner: UserDetail;
    comments: Comment[];
    documents: Document[];
  }

  interface TaskRaw {
    id: string;
    assignees: string;
  }

  interface MergedTask extends Omit<TaskRaw, 'assignees'> {
    id: string;
    assignees: ProjectUserModel[];
  }

  interface AssigneeDetail {
    id: number;
    url: string;
  }

  interface ProjectUser {
    id: number;
    userId: number;
    projectId: string;
    role: string;
    name: string;
    email: string;
    avatarUrl: string;
    remoteId: number;
  }

  interface CreateTaskInput {
    title: string;
    description?: string;
    dueDate?: string;
    status?: string;
    attachments?: any;
    medias?: any[];
    groupId?: number;
    documents?: number[];
    localId?: string;
    assignees?: number[];
    copies?: number[];
    linkedTo?: number[];
    projectId: number;
    status: string;
    ownerId: number;
    documents?: number[];
  }
}
