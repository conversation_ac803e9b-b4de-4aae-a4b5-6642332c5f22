import { Gql } from '@src/api';

declare module 'project-user' {
  import { BaseRawModel } from 'watermelon-db';
  interface UserDetail extends BaseRawModel {
    userId: number;
    projectId: string;
    role: string;
    name: string;
    email: string;
    avatarUrl: string;
  }

  interface SearchProjectUserViewProps {
    searchedUsers: UserDetail[];
  }

  type Assignee = {
    id?: string;
    ownerId: number;
    signById: number;
    projectDocumentId?: number | null;
    user: {
      id: string;
      name: string;
      email: string;
      avatarUrl: string;
      userId?: number;
    };
    assigneeNo?: number;
    status: Gql.RequestForSignatureStatus;
    localProjectDocumentId: string;
  };

  type ProjectUser = {
    id: string;
    name: string;
    email: string;
    avatarUrl: string;
    userId: number;
  };

  type AddAssigneeScreenParams = {
    type: Gql.WorkflowType;
    ass: Assignee[];
    onPressBack?: () => void;
  };

  interface ProjectUserResponse {
    data: {
      pages: Array<{
        items: ProjectUser[];
        hasNextPage?: boolean;
      }>;
    };
    fetchNextPage: () => void;
    hasNextPage: boolean;
    isLoading: boolean;
    error: unknown;
    refetch: () => void;
  }
}
