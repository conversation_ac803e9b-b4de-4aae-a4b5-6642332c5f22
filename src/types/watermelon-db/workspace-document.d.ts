import { Gql } from '@src/api';
import { WorkspaceAttachment } from '@src/api/graphql';
import ProjectDocumentModel from '@src/database/model/project-document.model';
import WorkspaceAttachmentModel from '@src/database/model/workspace-attachments.model';
import WorkspaceCcModel from '@src/database/model/workspace_ccs.model';
import { BaseRawModel } from 'watermelon-db';

declare global {
  interface DocumentDetails {
    id: string;
    name?: string;
    description?: string;
    group?: { id?: string; remoteId?: any };
    selectedAssigneeIds?: any;
    selectedCcs?: any;
    attachment?: any;
    photos?: any;
    linkedDocuments?: any;
  }

  interface OriginalDetails {
    document: {
      name?: string;
      description?: string;
      group?: { id?: string; remoteId?: any };
      assignees?: any;
      ccs?: any;
      attachments?: WorkspaceAttachment[];
      media?: any;
      linkedDocuments?: any;
      workspaceGroupId?: number;
    };
  }

  interface UpdateWorkspaceDocumentInput extends BaseRawModel {
    id: string;
    name?: string;
    description?: string;
    workflow?: Gql.WorkflowType;
    assignees?: RequestForSignaturesModel[];
    ccs?: WorkspaceCcModel[];
    status?: Gql.ProjectDocumentStatus;
    attachments?: WorkspaceAttachmentModel[];
    media?: WorkspacePhotosModel[];
    linkedDocuments?: WorkspaceDocumentModel[];
    xfdf?: string;
    submittedAt?: string;
    currentUserId?: number;
    localCurrentUserId?: string;
    workspaceGroupId?: number;
    fileUrl?: string;
    fileKey?: string | null;
    localFileUrl?: string | null;
    isQueued?: boolean;
  }
}
