import ProjectDocumentModel from '@src/database/model/project-document.model';
import WorkspaceGroupModel from '@src/database/model/workspace-group.model';

declare global {
  interface ChildGroup extends WorkspaceGroupModel {
    documentsByStatus: Record<string, ProjectDocumentModel[]>;
    totalCount: number;
  }

  interface WorkspaceGroup extends WorkspaceGroupModel {
    children: ChildGroup[];
    totalCount: number;
  }

  interface UpdateWorkspaceGroup {
    name: string;
    workspaceGroupId: string;
    code?: string;
  }

  interface CreateWorkspaceGroup {
    name: string;
    projectId: number;
    workspaceGroupId?: number | null;
    localGroupId?: string | null;
    code?: string;
  }
}
