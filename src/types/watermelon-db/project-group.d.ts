import ProjectGroupModel from '@src/database/model/project-group.model';
import TaskModel from '@src/database/model/task.model';

declare global {
  interface ChildGroup extends ProjectGroupModel {
    tasksByStatus: Record<string, TaskModel[]>;
    totalCount: number;
  }

  interface ProjectGroup extends ProjectGroupModel {
    children: ChildGroup[];
    totalCount: number;
  }

  interface CreateProjectGroup {
    title: string;
    projectId: number;
    projectGroupId?: number | null;
    localGroupId?: string | null;
  }

  interface UpdateProjectGroup {
    title: string;
    projectGroupId: string;
  }
}
