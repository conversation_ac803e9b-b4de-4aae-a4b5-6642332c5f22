import ProjectDocumentModel from '@src/database/model/project-document.model';
import { BaseRawModel } from 'watermelon-db';

declare global {
  interface CreateProjectDocumentInput extends BaseRawModel {
    name: string;
    projectId: number;
    projectDocumentId?: number | null;
    localRemoteId?: string;
    addedBy?: number;
    fileSystemType?: string;
    driveType?: string;
    fileUrl?: string;
    obsUrl?: string;
    obsFileSize?: number;
    obsFileType?: string;
    type?: string;
    fileSize?: number;
    category?: string;
    fileChannel?: string;
    videoThumbnail?: string;
    type?: string;
    xfdf?: string;
    status?: string | null;
    workspaceGroupId?: number | null;
    recordSource?: string;
    updatedBy?: string | number;
  }
  interface UpdateProjectDocumentInput extends BaseRawModel {
    name?: string;
    projectDocumentId?: number;
    localFileUrl?: string;
    status?: string;
    xfdf?: string;
    description?: string;
    currentUserId?: number | null;
    localCurrentUserId?: string;
    category?: string | null;
    tableName?: string | null;
  }
}
