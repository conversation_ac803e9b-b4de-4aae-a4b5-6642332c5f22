declare module 'task-attachment' {
  import { BaseRawModel } from 'watermelon-db';
  interface TaskAttachment extends BaseRawModel {
    taskId: number;
    userId: number;
    name: string;
    fileUrl?: string | null;
    type?: string | null;
    file?: string | null;
  }

  type CreateTaskAttachmentInput = {
    taskId: number;
    userId: number;
    name: string;
    uri: string;
    type?: string | null;
    localTaskId?: string | null;
    remoteId?: string | null;
    id?: string | undefined;
  };

  interface CreateTasksAttachmentInputDTO {
    id?: number;
    userId?: number;
    taskId?: number;
    fileUrl?: any;
    localId?: string;
  }
}
