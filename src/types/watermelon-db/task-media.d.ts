declare module 'task-media' {
  import { BaseRawModel } from 'watermelon-db';
  interface TaskMedia extends BaseRawModel {
    taskId: number;
    userId: number;
    name: string;
    fileUrl?: string | null;
    type?: string | null;
    localTaskId?: string | null;
  }

  interface CreateTaskMediaInput {
    taskId: number;
    userId: number;
    name: string;
    uri?: string | null;
    type?: string | null;
    localTaskId?: string | null;
  }
}
