import React from 'react';
import { Box } from 'native-base';
import { Field } from '@commons';
import { required, composeValidators, email } from '@configs/utils';
import { PhoneInput, TextInput } from '@inputs';

const SignUpForm: React.FC<any> = () => {
  return (
    <Box>
      <Field name="name" autoFocus placeholder="eg. <PERSON>" label="Full name" component={TextInput} />
      <Field
        name="phone"
        placeholder="12 345 6789"
        label="Mobile phone"
        validate={composeValidators(required)}
        component={PhoneInput}
      />
      <Field
        name="email"
        placeholder="eg. <EMAIL>"
        label="Email"
        component={TextInput}
        validate={composeValidators(required, email)}
      />
      <Field
        name="password"
        label="Password"
        component={TextInput}
        secureTextEntry
        validate={composeValidators(required)}
      />
    </Box>
  );
};

export { SignUpForm };
export default SignUpForm;
