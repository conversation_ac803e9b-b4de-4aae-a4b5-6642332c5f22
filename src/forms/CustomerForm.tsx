import React from 'react';
import { Box } from 'native-base';
import { Field } from 'formik';
import { required, composeValidators } from '@configs/utils';
import { PhoneInputCustomer, TextInput, SelectInput } from '@inputs';

const CustomerForm: React.FC<any> = () => {
  return (
    <Box>
      <Field
        name="name"
        label="Company Name"
        placeholder=""
        validate={composeValidators(required)}
        component={TextInput}
      />
      <Field
        name="picName"
        label="Name of PIC"
        placeholder=""
        validate={composeValidators(required)}
        component={TextInput}
      />
      <Field
        name="picPhone"
        label="Contact number"
        placeholder="12 345 6789"
        validate={composeValidators(required)}
        component={PhoneInputCustomer}
      />
      <Field
        name="isActive"
        label="Status"
        placeholder=""
        options={[
          {
            label: 'Active',
            value: true
          },
          {
            label: 'Inactive',
            value: false
          }
        ]}
        component={SelectInput}
      />
    </Box>
  );
};

export { CustomerForm };
export default CustomerForm;
