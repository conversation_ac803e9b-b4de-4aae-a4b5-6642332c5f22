import database from '@src/database/index.native';
import { Model, Q } from '@nozbe/watermelondb';
import RNFS from 'react-native-fs';
import { SystemService } from '@src/api/rest';
import { store } from '@src/store';
import { AppActions } from '@src/slice/app.slice';
import { <PERSON><PERSON><PERSON> } from 'buffer';
import { exponentialBackoff } from '@src/lib/network';
import { SyncErrorHandler } from '@src/lib/errorHandler';

// Define minimal AbortSignal interface for compatibility
interface AbortSignalLike {
  aborted: boolean;
}

// Configuration constants
const BATCH_SIZE = 5;
const UPLOAD_LIMIT = 20;
const UPLOAD_TIMEOUT = 60000; // 1 minute per file
const SYNC_TIMEOUT = 300000; // 5 minutes total
const MAX_RETRIES = 3;

interface BaseDocument extends Model {
  id: string;
  fileUrl: string | null;
  fileKey: string | null;
  fileName?: string | undefined; // Original filename with extension
  fileSize?: number;
  type: string;
  retryCount?: number;
  _status: string;
  errorMessage?: string;
  _changed: string;
}

const DOCUMENT_TABLES = {
  PROJECT_DOCUMENTS: 'project_documents',
  DRAWING_REVISIONS: 'drawing_revisions',
  PROJECT_CAROUSELS: 'project_carousels',
  WORKSPACE_ATTACHMENTS: 'workspace_attachments',
  WORKSPACE_PHOTOS: 'workspace_photos'
} as const;

class UploadError extends Error {
  constructor(
    message: string,
    readonly isNetworkError: boolean,
    readonly isPermanent: boolean,
    readonly documentId: string
  ) {
    super(message);
    this.name = 'UploadError';
  }
}

/**
 * Process documents for a specific table.
 * For project documents, extra query conditions are applied.
 */
const processDocumentsForTable = async (
  tableName: string,
  signal?: AbortSignalLike
): Promise<{ successCount: number; failureCount: number }> => {
  // Build the base query conditions.
  const queryConditions = [
    Q.where('_status', Q.notEq('synced')),
    Q.or(Q.where('fileKey', Q.eq('')), Q.where('fileKey', Q.eq(null)))
  ];

  // For project documents, add additional conditions.
  if (tableName === DOCUMENT_TABLES.PROJECT_DOCUMENTS) {
    queryConditions.push(
      Q.where('fileSystemType', Q.notEq('Folder')),
      Q.or(Q.where('localFileUrl', Q.eq('')), Q.where('localFileUrl', Q.eq(null)))
    );
  }

  const collection = database.collections.get<BaseDocument>(tableName);
  const documents = await collection.query(...queryConditions).fetch();

  let successCount = 0;
  let failureCount = 0;

  // Process in batches.
  for (let i = 0; i < documents.length; i += BATCH_SIZE) {
    if (signal?.aborted) break;
    const batch = documents.slice(i, i + BATCH_SIZE);
    const batchResults = await processDocumentBatch(batch, signal);
    successCount += batchResults.successCount;
    failureCount += batchResults.failureCount;
  }

  return { successCount, failureCount };
};

const uploadDocument = async (doc: BaseDocument, signal?: AbortSignalLike): Promise<void> => {
  let retryCount = 0;

  const executeUpload = async (): Promise<void> => {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), UPLOAD_TIMEOUT);

    try {
      if (!doc.fileUrl) throw new Error('File URL not found');

      const decodedUri = decodeURIComponent(doc.fileUrl);
      const filePath = decodedUri.replace(/^file:\/\//, '');

      const fileExists = await RNFS.exists(filePath);
      if (!fileExists) {
        throw new Error('File not found on device');
      }

      // Determine file extension from MIME type or original filename.
      const getFileExtension = (mimeType: string, originalName?: string): string => {
        if (originalName?.includes('.')) {
          return originalName.split('.').pop() || '';
        }
        const mimeToExt: Record<string, string> = {
          'image/jpeg': 'jpg',
          'image/png': 'png',
          jpeg: 'jpg',
          png: 'png',
          jpg: 'jpg',
          heic: 'png',
          heif: 'png',
          'image/gif': 'gif',
          pdf: 'pdf'
        };
        return mimeToExt[mimeType] || 'pdf';
      };

      const sanitizedFileName = doc.fileName?.replace(/[^a-zA-Z0-9-_.]/g, '_') || 'document';
      const fileExt = getFileExtension(doc.type, doc.fileName);

      const fileName = `${doc.collection._cache.tableName}/${doc.id}-${sanitizedFileName}${
        fileExt ? `.${fileExt}` : ''
      }`;
      const fileStats = await RNFS.stat(filePath);

      const { uploadUrl } = await SystemService.presignedUpload({
        key: fileName,
        size: fileStats.size,
        mimeType: doc.type
      });

      const fileContent = await RNFS.readFile(filePath, 'base64');
      const response = await fetch(uploadUrl.SignedUrl, {
        method: 'PUT',
        headers: uploadUrl.ActualSignedRequestHeaders,
        body: Buffer.from(fileContent, 'base64')
        // signal: controller.signal
      });

      if (!response.ok) {
        throw new Error(`Upload failed with status ${response.status}`);
      }

      return await markDocumentAsSynced(doc, fileName);
    } finally {
      clearTimeout(timeoutId);
    }
  };

  while (retryCount <= MAX_RETRIES) {
    try {
      await executeUpload();
      return;
    } catch (error: unknown) {
      if (signal?.aborted) throw new Error('Upload aborted');

      const errorObj = error as Error;
      const isNetworkError = errorObj.message?.includes('Network request failed') || errorObj.name === 'AbortError';
      const apiError = error as { response?: { status?: number } };
      const statusCode = apiError.response?.status;
      const isPermanent = !isNetworkError && statusCode !== undefined && statusCode >= 400 && statusCode < 500;

      await database.write(async () => {
        await doc.update(updatedDoc => {
          updatedDoc.retryCount = (updatedDoc.retryCount || 0) + 1;
          // Optionally set an error message: updatedDoc.errorMessage = errorObj.message;
        });
      });

      if (isPermanent || retryCount >= MAX_RETRIES) {
        throw new UploadError(errorObj.message || 'Unknown error', isNetworkError, isPermanent, doc.id);
      }

      await exponentialBackoff(retryCount);
      retryCount++;
    }
  }
};

const processDocumentBatch = async (
  documents: BaseDocument[],
  signal?: AbortSignalLike
): Promise<{ successCount: number; failureCount: number }> => {
  const results = [];

  for (const doc of documents) {
    try {
      await uploadDocument(doc, signal);
      results.push('fulfilled');
    } catch (error: unknown) {
      results.push('rejected');
    }
  }

  return {
    successCount: results.filter(r => r === 'fulfilled').length,
    failureCount: results.filter(r => r === 'rejected').length
  };
};

/**
 * Marks a document as synced in the database.
 *
 * This function updates the specified document's status to 'synced' in the database,
 * sets the file key, resets the retry count, and clears the file URL.
 *
 * @param doc - The document to be marked as synced.
 * @param fileName - The file key to be set for the document.
 */
async function markDocumentAsSynced(doc: BaseDocument, fileName: string): Promise<void> {
  const maxRetries = 3;
  const retryDelay = 500; // 500ms

  for (let i = 0; i < maxRetries; i++) {
    try {
      const document = (await doc.collection.find(doc.id)) as BaseDocument;

      // Verify document is ready for update
      if (!document || !document.update) {
        throw new Error('Document not properly initialized');
      }

      return await database.write(async () => {
        await document.update((updatedDoc: BaseDocument) => {
          updatedDoc.fileKey = fileName;
          updatedDoc.retryCount = 0;
          updatedDoc.fileUrl = null;
          updatedDoc._changed = '';
        });
      });
    } catch (error) {
      if (i === maxRetries - 1) {
        throw error;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, i)));
    }
  }
}

/**
 * Starts the synchronization process by processing documents table by table.
 *
 * 1. Process project documents first (with additional query conditions).
 * 2. Upload the document via presigned URL.
 * 3. Update the document's fileKey upon successful upload.
 * 4. Then process the remaining tables.
 */
export const startSync = async (signal: AbortSignalLike): Promise<void> => {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), SYNC_TIMEOUT);

  let successCount = 0;
  let failureCount = 0;

  try {
    store.dispatch(AppActions.setSyncStatus('upload'));

    // Step 1 & 2: Process Project Documents first.
    const projectResult = await processDocumentsForTable(DOCUMENT_TABLES.PROJECT_DOCUMENTS, controller.signal);
    successCount += projectResult.successCount;
    failureCount += projectResult.failureCount;

    // Step 5: Process the other tables one by one.
    for (const tableName of Object.values(DOCUMENT_TABLES)) {
      if (tableName === DOCUMENT_TABLES.PROJECT_DOCUMENTS) continue;
      const tableResult = await processDocumentsForTable(tableName, controller.signal);
      successCount += tableResult.successCount;
      failureCount += tableResult.failureCount;
    }

    if (failureCount > 0) {
      throw new Error(`${failureCount} documents failed to sync`);
    }
  } catch (error: unknown) {
    if (!controller.signal.aborted) {
      store.dispatch(AppActions.setSyncStatus('failed'));
      SyncErrorHandler.capture(error instanceof Error ? error : new Error(String(error)), {
        phase: 'presigned-upload',
        successCount,
        failureCount
      });
    }
  } finally {
    clearTimeout(timeoutId);
    if (!controller.signal.aborted) {
      store.dispatch(AppActions.setSyncProgress(100));
    }
  }
};
