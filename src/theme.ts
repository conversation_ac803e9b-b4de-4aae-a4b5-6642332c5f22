import { extendTheme } from 'native-base';
import { COLORS, FONTS } from '@constants';

export const customTheme = extendTheme({
  space: {},
  components: {
    Button: {
      baseStyle: {
        _text: {
          color: '#FFF',
          variant: 'buttonText'
        },
        _disabled: {
          bg: 'neutrals.gray40',
          opacity: 1,
          _text: { color: '#000', variant: 'buttonText' }
        }
      },
      defaultProps: {},
      variants: {
        primary: {
          bg: 'primary.1',
          borderRadius: 6,
          _text: {
            color: '#FFF',
            variant: 'buttonText'
          },
          _disabled: {
            bg: 'neutrals.gray5',
            _text: { color: 'neutrals.gray25' }
          },
          _pressed: {
            // bg: '#222222'
            opacity: 0.6
          },
          _loading: {
            bg: '#222222',
            opacity: 1
          }
        },
        light: {
          bg: '#FFF',
          borderRadius: 6,
          borderWidth: 1,
          borderColor: 'neutrals.gray40',
          _text: {
            color: 'primary.1',
            variant: 'buttonText'
          },
          _disabled: {
            bg: 'neutrals.gray5',
            _text: { color: 'neutrals.gray25' }
          },
          _pressed: {
            // bg: '#222222'
            opacity: 0.6
          },
          _loading: {
            bg: '#222222',
            opacity: 1
          }
        },
        outlineGray: {
          bg: 'transparent',
          borderRadius: 4,
          borderWidth: 1,
          borderColor: 'neutrals.gray50',
          _text: {
            color: 'neutrals.gray50',
            variant: 'buttonText'
          },
          _pressed: {
            bg: 'transparent',
            opacity: 0.4
          },
          _loading: {
            bg: 'transparent',
            opacity: 0.4
          },
          _disabled: {
            color: 'neutrals.gray60',
            bg: 'neutrals.gray10'
          }
        },
        outline: {
          bg: 'transparent',
          borderRadius: 4,
          borderWidth: 1,
          borderColor: 'primary.1',
          _text: {
            color: 'primary.1',
            variant: 'buttonText'
          },
          _pressed: {
            bg: 'transparent',
            opacity: 0.4
          },
          _loading: {
            bg: 'transparent',
            opacity: 0.4
          },
          _disabled: {
            bg: 'transparent',
            opacity: 0.5
          }
        },
        outlineWhite: {
          bg: 'transparent',
          borderRadius: 4,
          borderWidth: 1,
          borderColor: '#FFF',
          _text: {
            color: '#FFF',
            variant: 'buttonText'
          },
          _pressed: {
            bg: 'transparent',
            opacity: 0.4
          },
          _loading: {
            bg: 'transparent',
            opacity: 0.4
          },
          _disabled: {
            bg: '#E6E6E6'
          }
        },
        primaryLink: {
          bg: 'transparent',
          paddingRight: '0',
          paddingLeft: '0',
          paddingTop: '0',
          paddingBottom: '0',
          _text: {
            fontFamily: 'Inter',
            color: 'primary.1',
            fontSize: 16,
            fontWeight: 700
          },
          _pressed: {
            bg: 'transparent',
            opacity: 0.4
          },
          _loading: {
            bg: 'transparent',
            opacity: 0.4
          },
          _disabled: {
            bg: '#E6E6E6'
          }
        },
        delete: {
          bg: 'transparent',
          paddingRight: '0',
          paddingLeft: '0',
          paddingTop: '0',
          paddingBottom: '0',
          borderWidth: 1,
          borderColor: '#E8E8E8',
          borderRadius: '8px',
          _text: {
            fontFamily: 'Inter',
            color: '#FF2020',
            fontSize: 16,
            fontWeight: 700
          },
          _pressed: {
            bg: 'transparent',
            opacity: 0.4
          },
          _loading: {
            bg: 'transparent',
            opacity: 0.4
          },
          _disabled: {
            bg: '#E6E6E6'
          }
        }
      },
      sizes: {}
    },
    Input: {
      baseStyle: {
        height: '40px'
      },
      variants: {
        underlined: {
          _focused: {
            borderColor: 'primary.1'
          }
        }
      },
      _disabled: {},
      _hover: {},
      _invalid: {},
      _focus: {}
    },
    Text: {
      baseStyle: {
        fontFamily: 'Inter',
        fontWeight: 500,
        fontSize: 14,
        lineHeight: 22,
        color: 'neutrals.black'
      },
      variants: {
        largeTitle: {
          fontWeight: 700,
          fontSize: 32,
          lineHeight: 40,
          color: 'neutrals.black'
        },
        h1: {
          // fontWeight: 800,
          fontSize: 28,
          lineHeight: 36,
          color: 'neutrals.black'
        },
        h2: {
          fontWeight: 600,
          fontSize: 24,
          lineHeight: 32,
          color: 'neutrals.black'
        },
        h3: {
          fontWeight: 600,
          fontSize: 20,
          lineHeight: 30,
          color: 'neutrals.black'
        },
        headline: {
          fontWeight: 700,
          fontSize: 16,
          lineHeight: 24,
          color: 'neutrals.black'
        },
        headlineSm: {
          fontWeight: 700,
          fontSize: 14,
          lineHeight: 22,
          color: 'neutrals.black'
        },
        headlineMuted: {
          fontWeight: 500,
          fontSize: 14,
          lineHeight: 22,
          color: 'neutrals.gray90'
        },
        bodySemiBold: {
          fontWeight: 600,
          fontSize: 16,
          lineHeight: 24,
          color: 'neutrals.black'
        },
        bodyReg: {
          fontWeight: 400,
          fontSize: 14,
          lineHeight: 22,
          color: 'neutrals.gray90'
        },
        caption: {
          fontWeight: 500,
          fontSize: 12,
          lineHeight: 18,
          color: 'neutrals.black'
        },
        buttonText: {
          fontFamily: 'Inter',
          color: '#FFFFFF',
          fontWeight: 600,
          fontSize: 16,
          lineHeight: 18
        }
      },
      sizes: {}
    },
    Checkbox: {
      baseStyle: {
        justifyContent: 'flex-start',
        flexDirection: 'row',
        borderWidth: 1,
        width: '22px',
        height: '22px',
        _icon: {
          size: 4
        },
        _checked: {
          borderColor: 'primary.1',
          bg: 'primary.1'
        }
      }
    },
    Divider: {
      baseStyle: {
        borderColor: 'neutrals.gray40'
      }
    },
    Skeleton: {
      baseStyle: {
        endColor: 'neutrals.gray40'
      }
    },
    SkeletonText: {
      baseStyle: {
        endColor: 'neutrals.gray40'
      }
    }
  },
  colors: COLORS,
  fontConfig: {
    Inter: {
      300: {
        normal: FONTS.inter
      },
      500: {
        normal: FONTS.interMedium
      },
      600: {
        normal: FONTS.interSemiBold
      },
      700: {
        normal: FONTS.interBold
      }
    }
  },

  // Make sure values below matches any of the keys in `fontConfig`
  fonts: {
    Inter: 'Inter'
  },

  breakpoints: {
    base: 0,
    sm: 480,
    md: 768,
    lg: 992,
    xl: 1280
  }
});

// 2. Get the type of the CustomTheme
type CustomThemeType = typeof customTheme;

// 3. Extend the internal NativeBase Theme
declare module 'native-base' {
  interface ICustomTheme extends CustomThemeType {}
}
