import { Platform, Dimensions } from 'react-native';
import { NODE_ENV, STAGING_API_URL, PROD_API_URL, DEV_API_URL, ANDROID_DEV_API_URL } from 'react-native-dotenv';

export const PROJECT_NAME = 'React Native App';

export const APP_PRIMARY_COLOUR = '#4D818C';
let url = '';

if (NODE_ENV === 'production') {
  url = `${PROD_API_URL}`;
} else if (NODE_ENV === 'staging') {
  url = `${STAGING_API_URL}`;
} else if (Platform.OS === 'ios') {
  url = `${DEV_API_URL}`;
} else {
  url = `${ANDROID_DEV_API_URL}`;
}

export const API_URL = url;

// Alert.alert(NODE_ENV, API_URL);

export const DIMENS = {
  screenHeight: Dimensions.get('window').height,
  screenWidth: Dimensions.get('window').width
};

export const LINKS = {
  TermsAndCondition: 'https://bina.cloud/terms-and-conditions',
  PrivacyAndPolicy: 'https://bina.cloud/privacy_policy'
};

export const ImageFormat = ['png', 'jpg', 'jpeg', 'PNG', 'JPG', 'JPEG'];
export const verifyLoginTokenAPI = API_URL + '/api/auth/phone/login';
