export const BROWSER_CONFIG = {
  dismissButtonStyle: 'cancel',
  preferredBarTintColor: '#FFFFFF',
  // preferredControlTintColor: '#000000',
  readerMode: false,
  animated: true,
  modalPresentationStyle: 'fullScreen',
  modalTransitionStyle: 'coverVertical',
  modalEnabled: true,
  enableBarCollapsing: false,
  // Android Properties
  showTitle: true,
  toolbarColor: '#FFFFFF',
  // secondaryToolbarColor: '#000000',
  enableUrlBarHiding: true,
  forceCloseOnRedirection: false,
  animations: {
    startEnter: 'slide_in_right',
    startExit: 'slide_out_left',
    endEnter: 'slide_in_left',
    endExit: 'slide_out_right'
  }
};
