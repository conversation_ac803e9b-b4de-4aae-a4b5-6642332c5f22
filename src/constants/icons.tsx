import ActivityLog from '@assets/icons/activity-log.svg';
import AddFile from '@assets/icons/add-file.svg';
import Add from '@assets/icons/add.svg';
import Apple from '@assets/icons/apple.svg';
import Approved from '@assets/icons/approved.svg';
import Assignee from '@assets/icons/assignee.svg';
import Assignees from '@assets/icons/assignees.svg';
import Attached from '@assets/icons/attached.svg';
import BellNotificationDot from '@assets/icons/bell-notification-dot.svg';
import BellNotification from '@assets/icons/bell-notification.svg';
import Calendar from '@assets/icons/calendar.svg';
import CalendarGrey from '@assets/icons/calendar-grey.svg';
import Camera from '@assets/icons/camera.svg';
import Cancel from '@assets/icons/cancel.svg';
import CheckSquare from '@assets/icons/check-square.svg';
import ChevronDown from '@assets/icons/chevron-down.svg';
import ChevronDownGrey from '@assets/icons/chevron-down-grey.svg';
import ChevronLeft from '@assets/icons/chevron-left.svg';
import ChevronRight from '@assets/icons/chevron-right.svg';
import Clock from '@assets/icons/clock.svg';
import CloudDocs from '@assets/icons/cloud-docs.svg';
import ColorList from '@assets/icons/color-list.svg';
import ColorTableView from '@assets/icons/color-table-view.svg';
import Contacts from '@assets/icons/contacts.svg';
import Dashboard from '@assets/icons/dashboard.svg';
import Delete from '@assets/icons/delete.svg';
import DigitalForms from '@assets/icons/digital-forms.svg';
import Draft from '@assets/icons/draft.svg';
import Drawings from '@assets/icons/drawings.svg';
import Ellipse48 from '@assets/icons/ellipse-48.svg';
import Ellipse49 from '@assets/icons/ellipse-49.svg';
import Ellipse50 from '@assets/icons/ellipse-50.svg';
import EmptyProject from '@assets/icons/empty-project.svg';
import Facebook from '@assets/icons/facebook.svg';
import FieldReport from '@assets/icons/field-report.svg';
import FileTypeDocs from '@assets/icons/filetype-word.svg';
import FileTypeDRW from '@assets/icons/filetype-drw.svg';
import FileTypeDWG from '@assets/icons/filetype-dwg.svg';
import FileTypeExcel from '@assets/icons/filetype-excel.svg';
import FileTypeFolder from '@assets/icons/filetype-folder.svg';
import FileTypeImage from '@assets/icons/filetype-image.svg';
import FileTypePdf from '@assets/icons/filetype-pdf.svg';
import FileTypeProject from '@assets/icons/filetype-project.svg';
import FileTypeWord from '@assets/icons/filetype-word.svg';
import Filter from '@assets/icons/filter.svg';
import Filtering from '@assets/icons/filtering.svg';
import Flag from '@assets/icons/flag.svg';
import FolderBlue from '@assets/icons/folder-blue.svg';
import FolderOutline from '@assets/icons/folder-outline.svg';
import Folder from '@assets/icons/folder.svg';
import Google from '@assets/icons/google.svg';
import Group from '@assets/icons/group.svg';
import Image from '@assets/icons/image.svg';
import InReview from '@assets/icons/in-review.svg';
import InProgressDynamic from '@assets/icons/dynamic-in-progress.svg';
import Pending from '@assets/icons/pending.svg';
import LinkedTo from '@assets/icons/linked-to.svg';
import LiveChat from '@assets/icons/live-chat.svg';
import LogOut from '@assets/icons/logout.svg';
import Malaysia from '@assets/icons/malaysia.svg';
import Members from '@assets/icons/members.svg';
import More from '@assets/icons/more.svg';
import OptionDot from '@assets/icons/option-dot.svg';
import Overview from '@assets/icons/overview.svg';
import PasswordHidden from '@assets/icons/password-hidden.svg';
import PasswordResetSuccess from '@assets/icons/password-reset.svg';
import PasswordVisible from '@assets/icons/password-visible.svg';
import Photos from '@assets/icons/photos.svg';
import Plus from '@assets/icons/plus.svg';
import Reject from '@assets/icons/reject.svg';
import Search from '@assets/icons/search.svg';
import Settings from '@assets/icons/settings.svg';
import Submit from '@assets/icons/submit.svg';
import TableView from '@assets/icons/table-view.svg';
import TakePhoto from '@assets/icons/take-photo.svg';
import TaskCheckMarkDone from '@assets/icons/task-checkmark-done.svg';
import TaskCheckMarkUndone from '@assets/icons/task-checkmark-undone.svg';
import Tasks from '@assets/icons/tasks.svg';
import ThreeDots from '@assets/icons/three-dots.svg';
import Tick from '@assets/icons/tick.svg';
import TransparentList from '@assets/icons/transparent-list.svg';
import TreeView from '@assets/icons/tree-view.svg';
import Upload from '@assets/icons/upload.svg';
import UploadFile from '@assets/icons/upload-file.svg';
import MoreMenu from '@assets/icons/more-menu.svg';
import ThreeDotsVertical from '@assets/icons/three-dots-v.svg';
import UseCamera from '@assets/icons/use-camera.svg';
import XCircle from '@assets/icons/x-circle.svg';
import InProgress from '@assets/icons/in-progress.svg';
import ChevronRightGreyIcon from '@assets/icons/chevron-right-grey.svg';
import FilteringGreyIcon from '@assets/icons/filtering-grey.svg';
import GarbageIcon from '@assets/icons/garbage.svg';
import MoreHeader from '@assets/icons/more-header.svg';
import AddContact from '@assets/icons/add-contact.svg';
import LinkDocument from '@assets/icons/link-document.svg';
import DigitalForm from '@assets/icons/digital-form.svg';
import Drawing from '@assets/icons/drw.svg';
import Video from '@assets/icons/video icon.svg';
import PlayButton from '@assets/icons/play_button.svg';
import Sunny from '@assets/icons/sunny.svg';
import Rainy from '@assets/icons/rain.svg';
import Thunderstorm from '@assets/icons/thunderstorm.svg';
import Amend from '@assets/icons/amend.svg';
import Night from '@assets/icons/night.svg';
import ChevronDownPrimary from '@assets/icons/chevron-down-primary.svg';
import ChevronUpPrimary from '@assets/icons/chevron-up-primary.svg';
import BinaLogo from '@assets/icons/bina-logo.svg';
import DeleteBin from '@assets/icons/delete-bin.svg';
import ArrowRight from '@assets/icons/arrow-right.svg';
import ArrowDown from '@assets/icons/arrow-down.svg';
import Id from '@assets/icons/id.svg';
import GroupCode from '@assets/icons/group-code.svg';
import StoreDocument from '@assets/icons/store-document.svg';
import ChevronDownPrimaryLight from '@assets/icons/chevron-down-primary-light.svg';
import ChevronRightPrimaryLight from '@assets/icons/chevron-right-primary-light.svg';
import DynamicWorkspace from '@assets/icons/dynamic-workspace.svg';
import LinearWorkspace from '@assets/icons/linear-workspace.svg';
import OfflineCloud from '@assets/icons/offline-cloud.svg';
import Download from '@assets/icons/download.svg';
import DownloadGrey from '@assets/icons/download-grey.svg';
import Memo from '@assets/icons/memo.svg';
import UrgentFlag from '@assets/icons/urgent-flag.svg';
import ProposedOnHold from '@assets/icons/propose-onhold.svg';
import ProposedClosed from '@assets/icons/propose-closed.svg';
import TaskFolder from '@assets/icons/task-folder.svg';
import Xlsx from '@assets/icons/xlsx.svg';
import Pptx from '@assets/icons/pptx.svg';
import Sync from '@assets/icons/sync.svg';
import Unsync from '@assets/icons/unsync.svg';
import EmptyScurve from '@assets/icons/empty-scurve.svg';
import SiteDiary from '@assets/icons/site-diary.svg';
import UngroupDocument from '@assets/icons/ungroup-document.svg';
import Rvt from '@assets/icons/filetype-rvt.svg';
import Nwd from '@assets/icons/filetype-nwd.svg';
import Fbx from '@assets/icons/filetype-fbx.svg';
import PLError from '@assets/icons/pl-error.svg';
import FourSquare from '@assets/icons/foursquare.svg';
import ThreeList from '@assets/icons/threelist.svg';
import Lock from '@assets/icons/lock.svg';
import Subgroup from '@assets/icons/subgroup.svg';
import TwoBlueMen from '@assets/icons/twobluemen.svg';
import BlueLock from '@assets/icons/bluelock.svg';
import WhiteLock from '@assets/icons/lock-white.svg';
import TQ from '@assets/icons/tq.svg';
import NCR from '@assets/icons/ncr.svg';
import Offline from '@assets/icons/offline.svg';
import Correspondence from '@assets/icons/correspondence.svg';
import DeleteBinTwo from '@assets/icons/delete-bin-2.svg';
import ThreeLine from '@assets/icons/three-lines.svg';

type ICON_PROPS = {
  name: string;
  component: any;
};
const ICONS: ICON_PROPS[] = [
  { name: 'activity-log', component: ActivityLog },
  { name: 'add-file', component: AddFile },
  { name: 'add', component: Add },
  { name: 'apple', component: Apple },
  { name: 'approved', component: Approved },
  { name: 'assignee', component: Assignee },
  { name: 'assignees', component: Assignees },
  { name: 'attached', component: Attached },
  { name: 'bell-notification-dot', component: BellNotificationDot },
  { name: 'bell-notification', component: BellNotification },
  { name: 'calendar', component: Calendar },
  { name: 'calendar-grey', component: CalendarGrey },
  { name: 'camera', component: Camera },
  { name: 'cancel', component: Cancel },
  { name: 'check-square', component: CheckSquare },
  { name: 'chevron-down', component: ChevronDown },
  { name: 'chevron-down-grey', component: ChevronDownGrey },
  { name: 'chevron-left', component: ChevronLeft },
  { name: 'chevron-right', component: ChevronRight },
  { name: 'clock', component: Clock },
  { name: 'cloud-docs', component: CloudDocs },
  { name: 'color-list', component: ColorList },
  { name: 'color-table-view', component: ColorTableView },
  { name: 'contacts', component: Contacts },
  { name: 'dashboard', component: Dashboard },
  { name: 'delete', component: Delete },
  { name: 'digital-forms', component: DigitalForms },
  { name: 'draft', component: Draft },
  { name: 'drawings', component: Drawings },
  { name: 'ellipse-48', component: Ellipse48 },
  { name: 'ellipse-49', component: Ellipse49 },
  { name: 'ellipse-50', component: Ellipse50 },
  { name: 'empty-project', component: EmptyProject },
  { name: 'facebook', component: Facebook },
  { name: 'field-report', component: FieldReport },
  { name: 'filetype-docs', component: FileTypeDocs },
  { name: 'filetype-drw', component: FileTypeDRW },
  { name: 'filetype-dwg', component: FileTypeDWG },
  { name: 'filetype-excel', component: FileTypeExcel },
  { name: 'filetype-folder', component: FileTypeFolder },
  { name: 'filetype-image', component: FileTypeImage },
  { name: 'filetype-pdf', component: FileTypePdf },
  { name: 'filetype-project', component: FileTypeProject },
  { name: 'filetype-word', component: FileTypeWord },
  { name: 'filter', component: Filter },
  { name: 'filtering', component: Filtering },
  { name: 'flag', component: Flag },
  { name: 'folder-blue', component: FolderBlue },
  { name: 'folder-outline', component: FolderOutline },
  { name: 'folder', component: Folder },
  { name: 'google', component: Google },
  { name: 'group', component: Group },
  { name: 'image', component: Image },
  { name: 'in-review', component: InReview },
  { name: 'linked-to', component: LinkedTo },
  { name: 'live-chat', component: LiveChat },
  { name: 'logout', component: LogOut },
  { name: 'calendar', component: Calendar },
  { name: 'malaysia', component: Malaysia },
  { name: 'members', component: Members },
  { name: 'more', component: More },
  { name: 'option-dot', component: OptionDot },
  { name: 'overview', component: Overview },
  { name: 'password-hidden', component: PasswordHidden },
  { name: 'password-reset-success', component: PasswordResetSuccess },
  { name: 'password-visible', component: PasswordVisible },
  { name: 'photos', component: Photos },
  { name: 'plus', component: Plus },
  { name: 'reject', component: Reject },
  { name: 'search', component: Search },
  { name: 'settings', component: Settings },
  { name: 'submit', component: Submit },
  { name: 'table-view', component: TableView },
  { name: 'take-photo', component: TakePhoto },
  { name: 'task-checkmark-done', component: TaskCheckMarkDone },
  { name: 'task-checkmark-undone', component: TaskCheckMarkUndone },
  { name: 'tasks', component: Tasks },
  { name: 'three-dots', component: ThreeDots },
  { name: 'tick', component: Tick },
  { name: 'transparent-list', component: TransparentList },
  { name: 'tree-view', component: TreeView },
  { name: 'upload', component: Upload },
  { name: 'upload-file', component: UploadFile },
  { name: 'more-menu', component: MoreMenu },
  { name: 'three-dots-v', component: ThreeDotsVertical },
  { name: 'use-camera', component: UseCamera },
  { name: 'x-circle', component: XCircle },
  { name: 'in-progress', component: InProgress },
  { name: 'chevron-right-grey', component: ChevronRightGreyIcon },
  { name: 'filtering-grey', component: FilteringGreyIcon },
  { name: 'garbage', component: GarbageIcon },
  { name: 'more-header', component: MoreHeader },
  { name: 'add-contact', component: AddContact },
  { name: 'link-document', component: LinkDocument },
  { name: 'digital-form', component: DigitalForm },
  { name: 'drw', component: Drawing },
  { name: 'video', component: Video },
  { name: 'play-button', component: PlayButton },
  { name: 'sunny', component: Sunny },
  { name: 'rainy', component: Rainy },
  { name: 'night', component: Night },
  { name: 'thunderstorm', component: Thunderstorm },
  { name: 'chevron-down-primary', component: ChevronDownPrimary },
  { name: 'chevron-up-primary', component: ChevronUpPrimary },
  { name: 'bina-logo', component: BinaLogo },
  { name: 'delete-bin', component: DeleteBin },
  { name: 'arrow-right', component: ArrowRight },
  { name: 'arrow-down', component: ArrowDown },
  { name: 'id', component: Id },
  { name: 'group-code', component: GroupCode },
  { name: 'store-document', component: StoreDocument },
  { name: 'chevron-down-primary-light', component: ChevronDownPrimaryLight },
  { name: 'pending', component: Pending },
  { name: 'dynamic-in-progress', component: InProgressDynamic },
  { name: 'dynamic-workspace', component: DynamicWorkspace },
  { name: 'linear-workspace', component: LinearWorkspace },
  { name: 'offline-cloud', component: OfflineCloud },
  { name: 'download', component: Download },
  { name: 'download-grey', component: DownloadGrey },
  { name: 'chevron-right-primary-light', component: ChevronRightPrimaryLight },
  { name: 'memo', component: Memo },
  { name: 'urgent-flag', component: UrgentFlag },
  { name: 'propose-onHold', component: ProposedOnHold },
  { name: 'propose-closed', component: ProposedClosed },
  { name: 'task-folder', component: TaskFolder },
  { name: 'xlsx', component: Xlsx },
  { name: 'pptx', component: Pptx },
  { name: 'amend', component: Amend },
  { name: 'sync', component: Sync },
  { name: 'unsync', component: Unsync },
  { name: 'empty-scurve', component: EmptyScurve },
  { name: 'site-diary', component: SiteDiary },
  { name: 'ungroup-document', component: UngroupDocument },
  { name: 'filetype-rvt', component: Rvt },
  { name: 'filetype-nwd', component: Nwd },
  { name: 'filetype-fbx', component: Fbx },
  { name: 'pl-error', component: PLError },
  { name: 'foursquare', component: FourSquare },
  { name: 'three-list', component: ThreeList },
  { name: 'lock', component: Lock },
  { name: 'subgroup', component: Subgroup },
  { name: 'twobluemen', component: TwoBlueMen },
  { name: 'bluelock', component: BlueLock },
  { name: 'lock-white', component: WhiteLock },
  { name: 'tq', component: TQ },
  { name: 'ncr', component: NCR },
  { name: 'offline', component: Offline },
  { name: 'correspondence', component: Correspondence },
  { name: 'delete-bin-two', component: DeleteBinTwo },
  { name: 'three-lines', component: ThreeLine }
];

export type ICON_NAMES =
  | 'activity-log'
  | 'add-file'
  | 'add'
  | 'apple'
  | 'approved'
  | 'assignee'
  | 'assignees'
  | 'attached'
  | 'bell-notification-dot'
  | 'bell-notification'
  | 'calendar'
  | 'calendar-grey'
  | 'camera'
  | 'cancel'
  | 'check-square'
  | 'chevron-down'
  | 'chevron-down-grey'
  | 'chevron-left'
  | 'chevron-right'
  | 'clock'
  | 'cloud-docs'
  | 'color-list'
  | 'color-table-view'
  | 'contacts'
  | 'dashboard'
  | 'delete'
  | 'digital-forms'
  | 'draft'
  | 'drawings'
  | 'ellipse-48'
  | 'ellipse-49'
  | 'ellipse-50'
  | 'empty-project'
  | 'facebook'
  | 'field-report'
  | 'filetype-docs'
  | 'filetype-drw'
  | 'filetype-dwg'
  | 'filetype-excel'
  | 'filetype-folder'
  | 'filetype-image'
  | 'filetype-pdf'
  | 'filetype-project'
  | 'filetype-word'
  | 'filter'
  | 'flag'
  | 'filtering'
  | 'folder-blue'
  | 'folder-outline'
  | 'folder'
  | 'google'
  | 'group'
  | 'image'
  | 'in-review'
  | 'linked-to'
  | 'live-chat'
  | 'logout'
  | 'malaysia'
  | 'members'
  | 'more'
  | 'option-dot'
  | 'overview'
  | 'password-hidden'
  | 'password-reset-success'
  | 'password-visible'
  | 'photos'
  | 'plus'
  | 'reject'
  | 'search'
  | 'table-view'
  | 'take-photo'
  | 'settings'
  | 'submit'
  | 'task-checkmark-done'
  | 'task-checkmark-undone'
  | 'tasks'
  | 'three-dots'
  | 'tick'
  | 'transparent-list'
  | 'tree-view'
  | 'upload-file'
  | 'more-menu'
  | 'three-dots-v'
  | 'use-camera'
  | 'x-circle'
  | 'in-progress'
  | 'upload'
  | 'chevron-right-grey'
  | 'garbage'
  | 'filtering-grey'
  | 'more-header'
  | 'add-contact'
  | 'link-document'
  | 'digital-form'
  | 'drw'
  | 'video'
  | 'sunny'
  | 'rainy'
  | 'night'
  | 'thunderstorm'
  | 'play-button'
  | 'chevron-down-primary'
  | 'chevron-up-primary'
  | 'bina-logo'
  | 'delete-bin'
  | 'arrow-right'
  | 'arrow-down'
  | 'id'
  | 'group-code'
  | 'store-document'
  | 'chevron-down-primary-light'
  | 'pending'
  | 'dynamic-in-progress'
  | 'dynamic-workspace'
  | 'linear-workspace'
  | 'offline-cloud'
  | 'download'
  | 'download-grey'
  | 'chevron-right-primary-light'
  | 'memo'
  | 'flag'
  | 'urgent-flag'
  | 'propose-onHold'
  | 'propose-closed'
  | 'task-folder'
  | 'xlsx'
  | 'pptx'
  | 'amend'
  | 'sync'
  | 'unsync'
  | 'empty-scurve'
  | 'site-diary'
  | 'ungroup-document'
  | 'filetype-rvt'
  | 'filetype-nwd'
  | 'filetype-fbx'
  | 'pl-error'
  | 'foursquare'
  | 'three-list'
  | 'lock'
  | 'subgroup'
  | 'twobluemen'
  | 'bluelock'
  | 'lock-white'
  | 'tq'
  | 'ncr'
  | 'offline'
  | 'correspondence'
  | 'delete-bin-two'
  | 'three-lines';

export { ICONS };
