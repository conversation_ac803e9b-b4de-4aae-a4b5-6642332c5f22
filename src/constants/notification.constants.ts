import { DeepLinkConfig } from '@src/types/notification.types';

export const NOTIFICATION_CHANNEL = {
  DEFAULT: 'local-channel',
  ICON: 'ic_notification'
};

export const NOTIFICATION_SOUND = {
  DEFAULT: 'default'
};

export const NOTIFICATION_CONFIG = {
  autoCancel: true,
  subText: 'message',
  vibrate: true,
  vibration: 300,
  playSound: true,
  priority: 'high',
  ignoreInForeground: false,
  allowWhileIdle: true,
  foreground: true, // Allow notifications in foreground
  userInteraction: false, // Don't require user interaction
  visibility: 'public' as const, // Show on lock screen
  presentAlert: true, // iOS: Show alert in foreground
  presentBadge: true, // iOS: Update badge count
  presentSound: true // iOS: Play sound
};

export const DEEP_LINK_CONFIGS: Record<string, DeepLinkConfig> = {
  digitalForm: {
    pattern: /digital-form\/1\/(\d+)\/(.+)/,
    transform: (match: RegExpMatchArray) =>
      match && match[1] && match[2] ? `documentDetail/${match[1]}/${match[2]}` : null
  },
  task: {
    pattern: /task\/1\/(\d+)\/(.+)/,
    transform: (match: RegExpMatchArray) => (match && match[1] && match[2] ? `EditTask/${match[1]}/${match[2]}` : null)
  }
};

export const DEEP_LINK_PREFIX = 'bina://';
