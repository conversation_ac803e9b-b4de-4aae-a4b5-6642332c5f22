import { synchronize, SyncPullResult } from '@nozbe/watermelondb/sync';
import database from './index.native';
import { Gql } from '@src/api';
import { SyncApiService } from '@src/api/rest';
import { applyChanges, TableChanges } from './utils/pullSync';
import { TableChangesDto } from '@src/types/sync';
import { mainSyncFunction } from './utils/pushSyncV3';
import { store, useDispatch } from '@src/store';
import { AppActions } from '@src/slice/app.slice';
import SyncLogger from '@nozbe/watermelondb/sync/SyncLogger';
import { hasUnsyncedChanges } from '@nozbe/watermelondb/sync';
import { pullEntities } from './utils/tableName';

type SyncModuleType = Gql.AuditLogModuleType;

function convertToWatermelonChanges(changes: { [key: string]: TableChangesDto }): {
  [tableName: string]: TableChanges;
} {
  const result: { [tableName: string]: TableChanges } = {};

  for (const [tableName, tableChanges] of Object.entries(changes)) {
    const converted: TableChanges = {
      created: tableChanges.created.map(record => ({
        ...record,
        id: record.id.toString(),
        _status: 'synced',
        _changed: '',
        updated_at: Date.now()
      })),
      updated: tableChanges.updated.map(record => ({
        ...record,
        id: record.id.toString(),
        _status: 'synced',
        _changed: '',
        updated_at: Date.now()
      })),
      deleted: tableChanges.deleted
    };
    result[tableName] = converted;
  }

  return result;
}

export async function sync(moduleName: SyncModuleType | null) {
  const logger = new SyncLogger(10);
  let completedUnits = 0;
  const totalUnitsOfWork = pullEntities.reduce((total, entity) => total + 1 + entity.children.length, 0);

  await synchronize({
    database,
    log: logger.newLog(),
    pullChanges: async ({ lastPulledAt }): Promise<SyncPullResult> => {
      const lastPulledAtDate = lastPulledAt ? new Date(lastPulledAt) : null;
      let timestamp;

      if (!lastPulledAt) {
        store.dispatch(AppActions.setShowSyncModal(true));
        store.dispatch(AppActions.setSyncProgress(0));
        store.dispatch(AppActions.setSyncStatus('pending'));
      }

      let pullChangesResult: { [tableName: string]: TableChanges } = {};

      for (const { tableName, children } of pullEntities) {
        const { timestamp: newTimestamp, changes } = await syncTable(tableName, lastPulledAtDate, moduleName);
        timestamp = newTimestamp;

        const changesTableName = tableName === 'project_documents_users' ? 'project_documents' : tableName;

        if (changes && changes[changesTableName]) {
          const watermelonChanges = convertToWatermelonChanges({ [changesTableName]: changes[changesTableName] });
          await applyChanges(watermelonChanges);
          pullChangesResult = { ...pullChangesResult, ...watermelonChanges };
        }

        // Always increment completed units for the parent table, regardless of changes
        completedUnits++;
        store.dispatch(AppActions.setSyncProgress(Math.floor((completedUnits / totalUnitsOfWork) * 100)));

        // Check if there are created or updated parent items that might have children
        if (
          children.length > 0 &&
          changes &&
          changes[tableName] &&
          (changes[tableName]?.created?.length || changes[tableName]?.updated?.length)
        ) {
          // Combine created and updated items to get all relevant parent IDs
          const parentItems = [...(changes[tableName].created || []), ...(changes[tableName].updated || [])];
          const parentIds = parentItems.map(item => item.id);
          // Fetch and apply changes for children tables using the combined parent IDs
          for (const childTableName of children) {
            const childChanges = await syncTable(childTableName, lastPulledAtDate, moduleName, parentIds);
            if (childChanges.changes && childChanges.changes[childTableName]) {
              const childWatermelonChanges = convertToWatermelonChanges({
                [childTableName]: childChanges.changes[childTableName]
              });
              await applyChanges(childWatermelonChanges);
              pullChangesResult = { ...pullChangesResult, ...childWatermelonChanges };
            }

            completedUnits++;
            store.dispatch(AppActions.setSyncProgress(Math.floor((completedUnits / totalUnitsOfWork) * 100)));
          }
        } else {
          // If no parent changes but there are children, still increment for each child
          for (let i = 0; i < children.length; i++) {
            completedUnits++;
            store.dispatch(AppActions.setSyncProgress(Math.floor((completedUnits / totalUnitsOfWork) * 100)));
          }
        }
      }

      // check for completed sync
      if (completedUnits === totalUnitsOfWork && !lastPulledAt) {
        store.dispatch(AppActions.setSyncProgress(100));
        store.dispatch(AppActions.setSyncStatus('idle'));
        store.dispatch(AppActions.setShowSyncModal(false));
      }

      return {
        timestamp: timestamp || Date.now(),
        changes: {}
      };
    },
    pushChanges: async ({ changes, lastPulledAt }: any) => {
      const hasChanges = await checkUnsyncedChanges();

      if (!hasChanges) {
        return;
      }

      if (changes) {
        const success = await mainSyncFunction({
          changes,
          lastPulledAt: lastPulledAt
        });

        if (!success) {
          throw new Error('Failed to push changes');
        }

        return {};
      }
    },
    sendCreatedAsUpdated: true
  });
}

function mergeTableChanges(
  existing: TableChangesDto | undefined,
  incoming: TableChangesDto | undefined
): TableChangesDto {
  const emptyChanges: TableChangesDto = { created: [], updated: [], deleted: [] };
  if (!incoming) return existing || emptyChanges;
  if (!existing) return incoming;

  return {
    created: [...(existing.created || []), ...(incoming.created || [])],
    updated: [...(existing.updated || []), ...(incoming.updated || [])],
    deleted: [...(existing.deleted || []), ...(incoming.deleted || [])]
  };
}

function mergeChanges(
  existing: { [tableName: string]: TableChangesDto } = {},
  incoming: { [tableName: string]: TableChangesDto } = {}
): { [tableName: string]: TableChangesDto } {
  if (!incoming) return existing;
  if (!existing) return incoming;

  const result: { [tableName: string]: TableChangesDto } = { ...existing };

  for (const tableName in incoming) {
    result[tableName] = mergeTableChanges(existing[tableName], incoming[tableName]);
  }

  return result;
}

async function syncTable(tableName: string, lastPulledAtDate: any, moduleName: any, parentIds: number[] = []) {
  try {
    let hasMore = true;
    let offset = 0;
    const limit = 500;
    let allChanges: { [tableName: string]: TableChangesDto } = {
      [tableName]: { created: [], updated: [], deleted: [] }
    };
    let latestTimestamp = 0;

    while (hasMore) {
      const response = await SyncApiService.sync({
        tableName: tableName,
        body: {
          lastPulledAt: lastPulledAtDate,
          module: moduleName,
          parentIds: parentIds.length > 0 ? parentIds : undefined,
          offset,
          limit
        }
      });

      // Update timestamp to the latest one
      latestTimestamp = Math.max(latestTimestamp, response.timestamp);

      // Merge changes from this batch
      allChanges = mergeChanges(allChanges, response.changes);

      // Check if there are more records to fetch
      hasMore = response.pagination?.hasMore ?? false;
      if (hasMore) {
        offset += limit;
      }
    }

    return {
      timestamp: latestTimestamp,
      changes: allChanges
    };
  } catch (error) {
    return {
      timestamp: lastPulledAtDate,
      changes: { [tableName]: { created: [], updated: [], deleted: [] } }
    };
  }
}

async function checkUnsyncedChanges() {
  return await hasUnsyncedChanges({
    database
  });
}
