import { appSchema } from '@nozbe/watermelondb';
import projectGroupSchema from './schema/project-group.schema';
import taskSchema from './schema/task.schema';
import projectUsersSchema from './schema/project-user.schema';
import tasksAttachmentsSchema from './schema/task-attachment.schema';
import tasksCommentsSchema from './schema/task-comment.schema';
import tasksMediasSchema from './schema/task-media.schema';
import projectDocumentsSchema from './schema/project-document.schema';
import { DrawingRevisionSchema } from './schema/drawing-revision.schema';
import projectSchema from './schema/project.schema';
import { projectCarouselSchema } from './schema/project-carousel.schema';
import workspaceGroupSchema from './schema/workspace-group.schema';
import requestForSignaturesSchema from './schema/request-for-signatures.schema';
import { workspacePhotoSchema } from './schema/workspace-photos.schema';
import { workspaceAttachmentSchema } from './schema/workspace-attachments.schema';
import { workspaceDocumentSchema } from './schema/workspace-document.schema';
import workspaceCommentSchema from './schema/workspace-comment.schema';
import { workspaceCcSchema } from './schema/workspace-ccs.schema';
import { OfflineDownloadSchema } from './schema/offline-download.schema';
import usersSchema from './schema/user.schema';
import eventSchema from './schema/event.schema';
import workspaceGroupUserSchema from './schema/workspace-group-users.schema';
import subscriptionPackageSchema from './schema/subscription-package.schema';

export default appSchema({
  version: 2,
  tables: [
    projectGroupSchema,
    taskSchema,
    projectUsersSchema,
    tasksAttachmentsSchema,
    tasksCommentsSchema,
    tasksMediasSchema,
    projectDocumentsSchema,
    DrawingRevisionSchema,
    projectSchema,
    projectCarouselSchema,
    workspaceGroupSchema,
    requestForSignaturesSchema,
    workspacePhotoSchema,
    workspaceAttachmentSchema,
    workspaceDocumentSchema,
    workspaceCommentSchema,
    workspaceCcSchema,
    OfflineDownloadSchema,
    usersSchema,
    eventSchema,
    workspaceGroupUserSchema,
    subscriptionPackageSchema
  ]
});
