import { getBinaryFile } from '@src/configs';
import { projectDocumentChildEntities } from './constant';

interface Sanitizable {
  [key: string]: any;
}

// Base exclusion keys
const BASE_EXCLUDED_KEYS = [
  '_status',
  'remote_id',
  'deleted_at',
  'server_updated_at',
  'retry_count',
  'server_created_at',
  'download_retry'
];

// Entity-specific exclusion mappings
const ENTITY_SPECIFIC_EXCLUSIONS: Record<string, string[]> = {
  tasks_attachments: ['id', 'file', 'type', 'name', 'fileUrl'],
  tasks_medias: ['id', 'file', 'type', 'name', 'fileUrl'],
  user: [],
  task: [
    'id',
    'isSentBeforeOneDay',
    'isSentBeforeOneHour',
    'permanentlyDeleted',
    'isMemoReceive',
    'issuedById',
    'createdAt',
    'deletedAt',
    'serverCreatedAt',
    'serverUpdatedAt',
    'assignees',
    'copies',
    'documents',
    'isUrgent',
    'taskCode',
    'proposedStatus',
    'memoUrl',
    'previewMemoUrl'
  ],
  task_comments: ['server_updated_at', 'localTaskId', 'id', 'updatedBy'],
  project_groups: ['projectGroupId', 'id'],
  project_documents: [
    'id',
    'file',
    'fileUrl',
    'addedBy',
    'formCategoryId',
    'fileChannel',
    'allFormCode',
    'watermarkId',
    'groupCode',
    'updatedBy',
    'localFileUrl',
    'isQueued',
    'assigneeIds',
    'autosavedAt'
  ],
  drawing_revisions: ['id', 'fileUrl', 'version', 'status', 'category', 'updatedBy', 'localFileUrl'],
  projects: [
    'id',
    'fileUrlProgress',
    'fileUrlCover',
    'ganttChartUrl',
    'fileUrlProgressFinance',
    'updatedBy',
    'server_updated_at',
    'fileProgressKey',
    'ganttChartKey',
    'fileProgressFinanceKey',
    'createdBy',
    'status',
    'managedBy',
    'deputySuperintendent',
    'client',
    'contractor',
    'startDate',
    'completionDate',
    'cnsConsultant',
    'mneConsultant',
    'qsConsultant',
    'environmentConsultant',
    'metTownId',
    'contractValue',
    'isRemindSiteDiary',
    'isNotify2DUploaded',
    'issuesAndProblem',
    'solution',
    'drawing_sync'
  ],
  project_carousels: ['id', 'localProjectId', 'fileUrl', 'updated_at', 'isDownloaded'],
  workspace_groups: ['id', 'projectDocumentId'],
  request_for_signatures: ['id', 'updatedBy', 'projectDocumentId'],
  workspace_ccs: ['id', 'status', 'updatedBy', 'projectDocumentId'],
  workspace_attachments: ['id', 'updatedBy', 'fileUrl', 'localFileUrl'],
  workspace_photos: ['id', 'updatedBy', 'fileUrl', 'localFileUrl'],
  workspace_document: ['id', 'updatedBy'],
  project_document_comments: ['id', 'updatedBy']
};

// Utility: Key exclusion for objects
const excludeKeys = (obj: Sanitizable, keys: string[]): Sanitizable => {
  return Object.fromEntries(Object.entries(obj).filter(([key]) => !keys.includes(key)));
};

// Service: Generate exclusion list for entity type
const getExclusionKeys = (entityType: string, action: 'create' | 'update'): string[] => {
  const specificExclusions = ENTITY_SPECIFIC_EXCLUSIONS[entityType] || [];
  const exclusions = [...BASE_EXCLUDED_KEYS, ...specificExclusions];

  if (action === 'create') {
    exclusions.push('remoteId', '_changed');
  }

  if (action === 'update' && entityType === 'tasks_attachments') {
    exclusions.push('fileUrl');
  }

  return exclusions;
};

// Service: Fetch binary file content
const getFileContent = async (fileUrl: string, fileName: string): Promise<any> => {
  if (!fileUrl) return null;

  try {
    return await getBinaryFile(fileName);
  } catch (error) {
    return null;
  }
};

// Main: Sanitize data for generic entities
export const sanitizeData = async (
  entity: Sanitizable,
  entityType: keyof typeof ENTITY_SPECIFIC_EXCLUSIONS,
  action: 'create' | 'update'
): Promise<Sanitizable> => {
  const exclusions = getExclusionKeys(entityType, action);

  const fileContent =
    entityType === 'drawing_revisions' ||
    entityType === 'workspace_document' ||
    (entityType === 'tasks_attachments' && action === 'update')
      ? null
      : await getFileContent(entity.fileUrl, entity.name);

  if (entity.hasOwnProperty('driveType')) {
    entity.driveType = entity.driveType !== '' ? entity.driveType : null;
  }

  if (projectDocumentChildEntities.includes(entityType)) {
    entity.projectDocumentId = parseInt(entity.projectDocumentId);
  }

  if (entity.recordSource === null) {
    entity.recordSource = 'OfflineApp';
  }

  return {
    localId: entity.id,
    ...excludeKeys(entity, exclusions),
    ...(fileContent ? { fileUrl: fileContent } : {})
  };
};

// Main: Sanitize task data specifically
export const sanitizedTask = async (task: Sanitizable, action: 'create' | 'update'): Promise<Sanitizable> => {
  const exclusions = getExclusionKeys('task', action);

  return {
    localId: task.id,
    assignees: safeParseJSON(task.assignees, []).map(Number),
    copies: safeParseJSON(task.copies, []).map(Number),
    documents: safeParseJSON(task.documents, []).map((id: string) => ({ id: parseInt(id) })),
    isUrgent: !!task.isUrgent,
    ...excludeKeys(task, exclusions)
  };
};

// Utility: Safely parse JSON
const safeParseJSON = (json: string, defaultValue: any): any => {
  try {
    return JSON.parse(json);
  } catch (error) {
    return defaultValue;
  }
};
