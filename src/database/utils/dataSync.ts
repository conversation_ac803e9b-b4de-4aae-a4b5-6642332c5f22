import NetInfo from '@react-native-community/netinfo';
import { sync } from '../sync';

export async function attemptImmediateSync(): Promise<void> {
  try {
    const networkState = await NetInfo.fetch();

    if (networkState.isConnected) {
      return await sync(null);
    } else {
    }
  } catch (error) {}
}

// async function updateRecordWithRemoteId(tableName: string, localId: string, remoteId: string, action: action) {
//   try {
//     await database.write(async () => {
//       const collection = database.get(tableName);
//       const record = await collection.find(localId);

//       await record.update((r: any) => {
//         if (action === 'create') r.remoteId = remoteId;
//         r._raw._status = 'synced';
//       });

//       //     });
//   } catch (error) {
//     //   }
// }
