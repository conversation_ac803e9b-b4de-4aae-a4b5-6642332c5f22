import { Alert } from 'react-native';
import RNFS from 'react-native-fs';

export async function copyPdfFile(name: string, path: string, projectId: number) {
  const sourcePath = path;
  const destinationDir = `${RNFS.DocumentDirectoryPath}/${projectId}/StandardForm`;
  let destinationPath = `${destinationDir}/${name}.pdf`;
  let counter = 1;

  try {
    // Verify the source file exists
    if (!(await RNFS.exists(sourcePath))) {
      Alert.alert('File Error', 'Source file does not exist.');
      return;
    }

    // Create the destination directory if it doesn't exist
    if (!(await RNFS.exists(destinationDir))) {
      await RNFS.mkdir(destinationDir);
    }

    // Generate a unique destination path if a file with the same name exists
    while (await RNFS.exists(destinationPath)) {
      destinationPath = `${destinationDir}/${name} (${counter}).pdf`;
      counter++;
    }

    // Copy the file to the destination
    await RNFS.copyFile(sourcePath, destinationPath);

    return destinationPath;
  } catch (error) {
    Alert.alert('Error', 'An error occurred while copying the file.');
  }
}
