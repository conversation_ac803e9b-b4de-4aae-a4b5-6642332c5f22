import { Q } from '@nozbe/watermelondb';
import database from '../index.native';
import { getLastPulledAt } from '@nozbe/watermelondb/sync/impl';

let isSyncing = false;

interface UpdateChange {
  remoteId: string;
  server_updated_at: number;
  [key: string]: any;
}

export interface TableChanges {
  updated?: UpdateChange[];
  deleted?: number[];
  created?: any[];
}

interface SyncChanges {
  [tableName: string]: TableChanges;
}

export async function applyChanges(changes: SyncChanges) {
  if (isSyncing) {
    return;
  }

  isSyncing = true;
  try {
    const chunkSize = 200;
    for (const tableName of Object.keys(changes)) {
      const tableChanges: TableChanges = changes[tableName];

      if (tableChanges.created && tableChanges.created.length > 0) {
        for (let i = 0; i < tableChanges.created.length; i += chunkSize) {
          const chunk = tableChanges.created.slice(i, i + chunkSize);
          await applyCreates(tableName, chunk);
        }
      }

      if (tableChanges.updated && tableChanges.updated.length > 0) {
        for (let i = 0; i < tableChanges.updated.length; i += chunkSize) {
          const chunk = tableChanges.updated.slice(i, i + chunkSize);
          await applyUpdates(tableName, chunk);
        }
      }

      const deletions = tableChanges.deleted;
      if (deletions && deletions.length > 0) {
        for (let i = 0; i < deletions.length; i += chunkSize) {
          const chunk = deletions.slice(i, i + chunkSize);
          await applyDeletions(tableName, chunk);
        }
      }
    }
  } catch (error) {
    // Error handling
  } finally {
    isSyncing = false;
  }
}

async function applyCreates(tableName: string, createdRecords: any[]) {
  const collection = database.collections.get(tableName);

  const existingRemoteIds = new Set(
    (
      await collection
        .query(Q.where('remoteId', Q.oneOf(createdRecords.map(record => (record as any).remoteId))))
        .fetch()
    ).map(record => (record as any).remoteId)
  );

  const batchedCreates: any[] = [];
  const firstTimeSync = (await getLastPulledAt(database)) === null;

  await database.write(async () => {
    for (const recordData of createdRecords) {
      try {
        if (!firstTimeSync) {
          const exists = await collection.query(Q.where('remoteId', recordData.remoteId)).fetchCount();
          if (exists > 0) continue;
        }

        if (existingRemoteIds.has(recordData.remoteId)) {
          continue;
        }
        const newRecord = collection.prepareCreate(record => {
          Object.keys(recordData).forEach(key => {
            if (isKeyAllowed(key)) {
              (record as any)[key] = recordData[key];
            }
          });
          record._raw._status = 'synced';
        });

        batchedCreates.push(newRecord);
      } catch (innerError: any) {
        // Error handling
      }
    }

    if (batchedCreates.length > 0) {
      await database.batch(batchedCreates);
    }
  });
}

export async function applyUpdates(tableName: string, updates: UpdateChange[]) {
  if (!updates || updates.length === 0) {
    return; // Early return if no updates
  }

  const collection = database.collections.get(tableName);
  const recordsToUpdate = updates.map(u => u.remoteId).filter(Boolean);

  if (recordsToUpdate.length === 0) {
    // No valid remoteIds found
    return;
  }

  // Find existing records by remoteId
  const existingRecords = await collection.query(Q.where('remoteId', Q.oneOf(recordsToUpdate))).fetch();

  // Create a map of existing records by remoteId for quick lookup
  const existingRecordsMap = new Map();
  existingRecords.forEach(record => {
    existingRecordsMap.set((record as any).remoteId, record);
  });

  // Separate updates into records to update and records to create
  const recordsToCreate: UpdateChange[] = [];
  const recordsToUpdateFiltered: UpdateChange[] = [];

  updates.forEach(update => {
    if (!update.remoteId) {
      // Skipping update with missing remoteId
      return;
    }

    if (existingRecordsMap.has(update.remoteId)) {
      recordsToUpdateFiltered.push(update);
    } else {
      recordsToCreate.push(update);
    }
  });

  // Log the upsert operation
  if (recordsToCreate.length > 0) {
    // Creating new records
  }

  await database.write(async () => {
    // Handle updates for existing records
    const batchedUpdates = existingRecords
      .map(record => {
        const update = updates.find(u => u.remoteId === (record as any).remoteId);

        if (!update) return null;

        // Check if the server update is newer than our local record
        const serverTime = new Date(update.server_updated_at || 0).getTime();
        const localTime = (record._raw as any).updated_at || 0;

        if (serverTime > localTime) {
          return record.prepareUpdate(r => {
            // Apply all fields from the update data
            (Object.keys(update) as (keyof typeof update)[]).forEach(key => {
              if (isKeyAllowed(key as string)) {
                if (key === 'remoteId' && update[key]) {
                  (r as any)[key] = typeof update[key] === 'string' ? parseInt(update[key], 10) : update[key];
                } else {
                  (r as any)[key] = update[key];
                }
              }
            });

            // Update the record status
            r._raw._status = 'synced';
            r._raw._changed = '';
          });
        }

        return null;
      })
      .filter(Boolean);

    // Handle creates for non-existing records (upsert functionality)
    const batchedCreates = recordsToCreate
      .map(recordData => {
        try {
          // Ensure we have all required fields for a new record
          const now = Date.now();

          return collection.prepareCreate(record => {
            // Set default values for required fields
            (record as any).created_at = now;
            (record as any).updated_at = now;

            // Apply all fields from the update data
            Object.keys(recordData).forEach(key => {
              if (isKeyAllowed(key)) {
                if (key === 'remoteId' && recordData[key]) {
                  (record as any)[key] =
                    typeof recordData[key] === 'string' ? parseInt(recordData[key], 10) : recordData[key];
                } else {
                  (record as any)[key] = recordData[key];
                }
              }
            });

            // Set record status to synced
            record._raw._status = 'synced';
            record._raw._changed = '';
          });
        } catch (innerError: any) {
          return null;
        }
      })
      .filter(Boolean);

    // Combine all operations and execute in a single batch
    const allOperations = [...batchedUpdates, ...batchedCreates];
    if (allOperations.length > 0) {
      await database.batch(allOperations as any);
    }
  });
}

export async function applyDeletions(tableName: string, deletions: number[]) {
  try {
    const collection = database.collections.get(tableName);
    await database.write(async () => {
      const recordsToDelete = await collection.query(Q.where('remoteId', Q.oneOf(deletions))).fetch();

      if (recordsToDelete.length > 0) {
        await database.batch(recordsToDelete.map(r => r.prepareDestroyPermanently()));
      }
    });
  } catch (error) {
    throw error;
  }
}

function isKeyAllowed(key: string): boolean {
  return !['id', '_status', 'createdAt', 'updatedAt', 'deletedAt', 'deletedBy', 'updatedBy'].includes(key);
}
