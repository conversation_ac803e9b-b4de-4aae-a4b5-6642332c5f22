/**
 * Database Synchronization Utility
 *
 * This module handles the synchronization of local database changes with the server.
 * It processes changes from WatermelonDB, structures them for the server API,
 * and updates local records with remote IDs after successful sync.
 *
 * The sync process handles parent-child relationships, ensuring proper nesting
 * of related records and maintaining data integrity across the system.
 */

import apolloClient from '@src/lib/apollo';
import { Gql } from '@src/api';
import { sanitizeData, sanitizedTask } from './sanitizedInput';
import database from '../index.native';
import { TableName, tableRelations, projectDocumentChildEntities, taskChildEntities, unpushedTables } from './constant';
import { Q } from '@nozbe/watermelondb';
import { store } from '@src/store';
import { AppActions } from '@src/slice/app.slice';
import * as Sentry from '@sentry/react-native';
import { SyncErrorHandler } from '@src/lib/errorHandler';
import ProjectDocument from '../model/project-document';
/**
 * Interface representing the response from the server after sync
 */
interface SyncResponseType {
  syncData: {
    [key: string]: {
      created?: Array<{
        id: string;
        remoteId?: string | number;
        projectDocumentId?: string | number;
        documentStatus?: string;
        [key: string]: any;
      }>;
      updated?: Array<{
        id: string;
        remoteId?: string | number;
        projectDocumentId?: string | number;
        documentStatus?: string;
        [key: string]: any;
      }>;
      failed?: Array<{
        id: string;
        error: string;
      }>;
    };
  };
}
// Import WatermelonDB types
import '@nozbe/watermelondb';

/**
 * Generic interface for any record/item with dynamic properties
 */
interface Item {
  [key: string]: any;
}

/**
 * Interface representing changes coming from WatermelonDB
 */
interface WatermelonTableChanges {
  created: Item[];
  updated: Item[];
  /** WatermelonDB provides string IDs for deletions */
  deleted: string[];
}

/**
 * Type representing all changes from WatermelonDB, organized by table name
 */
interface WatermelonChangesType {
  [key: string]: WatermelonTableChanges;
}

/**
 * Interface representing the structured changes before sending to the server
 */
interface StructuredTableChanges {
  created: Item[];
  updated: Item[];
  /** Remote IDs can be string or number */
  deleted: (string | number)[];
}

/**
 * Type representing all structured changes, organized by table name
 */
interface StructuredChangesType {
  [key: string]: StructuredTableChanges;
}

/**
 * Gets remote IDs for deleted records
 * @param {string} tableName - The name of the table containing deleted records
 * @param {string[]} deletedIds - Local string IDs of deleted records
 * @returns {Promise<(string | number)[]>} - Remote IDs (string or number) for the deleted records
 */
async function getRemoteIdsForDeletedRecords(tableName: string, deletedIds: string[]): Promise<(string | number)[]> {
  if (!deletedIds || deletedIds.length === 0) {
    return [];
  }

  try {
    const validRemoteIds: (string | number)[] = [];
    const collection = database.collections.get(tableName);

    for (const localId of deletedIds) {
      try {
        const record = await collection.find(localId);

        if (record && record._raw) {
          const rawRecord = record._raw as any;
          if (rawRecord.remoteId !== null && rawRecord.remoteId !== undefined) {
            validRemoteIds.push(rawRecord.remoteId);
          }
        }
      } catch (error) {
        // Record might not exist anymore or other error
      }
    }

    if (validRemoteIds.length !== deletedIds.length) {
      // Some remote IDs could not be found
    }

    return validRemoteIds;
  } catch (error) {
    Sentry.captureException(error, { extra: { tableName, deletedIds } });
    return [];
  }
}

/**
 * Set of fields that are safe to update from server response
 * These fields won't cause data corruption when updated locally
 */
const safeUpdateFields = new Set([
  'remoteId',
  'fileUrl',
  'taskCode',
  'projectDocumentId',
  'workspaceGroupId',
  'allFormCode',
  'groupCode',
  'server_created_at',
  'currentUserId',
  'projectGroupId',
  'workspaceGroupId',
  'documentStatus'
]);

/**
 * Main synchronization function that processes changes and syncs with the server
 * @param {Object} params - The parameters object
 * @param {WatermelonChangesType} params.changes - Changes from WatermelonDB to be synced
 * @param {number} params.lastPulledAt - Timestamp of the last successful pull
 * @returns {Promise<boolean>} - True if sync was successful, false otherwise
 */
export async function mainSyncFunction({
  changes,
  lastPulledAt
}: {
  changes: WatermelonChangesType;
  lastPulledAt: number;
}): Promise<boolean> {
  try {
    const structuredChanges: StructuredChangesType = await buildParentChildStructure(changes);

    const syncResult = await syncWithServer(structuredChanges, lastPulledAt);
    await updateLocalRecordsWithRemoteIds(syncResult);
    return true;
  } catch (error) {
    await SyncErrorHandler.capture(error as Error, {
      phase: 'mainSyncFunction'
    });
    store.dispatch(AppActions.setSyncStatus('failed'));
    return false;
  }
}

/**
 * Checks if a table has any changes (created, updated, or deleted remote IDs)
 * Operates on the structured changes before sending to the server
 * @param {StructuredTableChanges} tableChanges - The changes for a specific table
 * @returns {boolean} - True if there are any changes, false otherwise
 */
function hasChanges(tableChanges: StructuredTableChanges): boolean {
  return (
    (tableChanges.created && tableChanges.created.length > 0) ||
    (tableChanges.updated && tableChanges.updated.length > 0) ||
    (tableChanges.deleted && tableChanges.deleted.length > 0)
  );
}

/**
 * Takes WatermelonDB changes and returns structured changes with remote IDs for deletion
 * Organizes parent-child relationships and prepares data for server sync
 * @param {WatermelonChangesType} changes - The original changes from WatermelonDB
 * @returns {Promise<StructuredChangesType>} - Structured changes ready for server sync
 */
async function buildParentChildStructure(changes: WatermelonChangesType): Promise<StructuredChangesType> {
  const structuredChanges: StructuredChangesType = {};

  const parentTables = ['project_documents', 'tasks', 'projects'];

  // Create a deep copy of changes to avoid modifying the original object
  const workingChanges: WatermelonChangesType = JSON.parse(JSON.stringify(changes));

  // Track all nested child IDs to filter them out later
  const allNestedChildIds: string[] = [];

  // First pass: Process parent tables and nest children
  for (const tableName of parentTables) {
    if (workingChanges[tableName] && hasChanges(workingChanges[tableName])) {
      const deletedRemoteIds = await getRemoteIdsForDeletedRecords(tableName, workingChanges[tableName].deleted || []);

      structuredChanges[tableName] = {
        created: [],
        updated: [],
        deleted: deletedRemoteIds
      };

      const nestedIds = await processParentTable(tableName, workingChanges, structuredChanges);
      allNestedChildIds.push(...nestedIds);

      if (!hasChanges(structuredChanges[tableName])) {
        delete structuredChanges[tableName];
      }
    }
  }

  // Track nested child IDs for filtering

  // Second pass: Process remaining tables, excluding nested children
  for (const tableName in workingChanges) {
    // Skip parent tables (already processed), unpushed tables, and tables with no changes
    if (
      parentTables.includes(tableName) ||
      unpushedTables.includes(tableName) ||
      !workingChanges[tableName] ||
      !hasChanges(workingChanges[tableName])
    ) {
      continue;
    }

    // Filter out any records that were nested as children
    const created = workingChanges[tableName].created.filter(child => !allNestedChildIds.includes(child.id));
    const updated = workingChanges[tableName].updated;

    if (created.length !== workingChanges[tableName].created.length) {
      // Filtered out nested children
    }

    const deletedRemoteIds = await getRemoteIdsForDeletedRecords(tableName, workingChanges[tableName].deleted || []);

    if (created.length > 0 || updated.length > 0 || deletedRemoteIds.length > 0) {
      structuredChanges[tableName] = {
        created,
        updated,
        deleted: deletedRemoteIds
      };
    }
  }

  // Special handling for project_documents table when it's both parent and child
  if (structuredChanges['project_documents']) {
    // Make sure we're not including any nested children at the root level
    structuredChanges['project_documents'].created = structuredChanges['project_documents'].created.filter(
      record => !allNestedChildIds.includes(record.id)
    );

    // If there are no changes left after filtering, remove the table entry
    if (!hasChanges(structuredChanges['project_documents'])) {
      delete structuredChanges['project_documents'];
    }
  }

  return structuredChanges;
}

/**
 * Determines if a table is a child table (belongs to project documents or tasks)
 * @param {string} tableName - The name of the table to check
 * @returns {boolean} - True if it's a child table, false otherwise
 */
function isChildTable(tableName: string): boolean {
  return projectDocumentChildEntities.includes(tableName) || taskChildEntities.includes(tableName);
}

/**
 * Processes a parent table and its child records
 * @param {string} parentTable - The name of the parent table
 * @param {WatermelonChangesType} originalChanges - Original changes from WatermelonDB
 * @param {StructuredChangesType} structuredChanges - Structured changes being built
 * @returns {Promise<string[]>} - Array of nested child IDs that were processed
 */
async function processParentTable(
  parentTable: string,
  originalChanges: WatermelonChangesType,
  structuredChanges: StructuredChangesType
): Promise<string[]> {
  const parentChanges = originalChanges[parentTable];
  if (!parentChanges) return [];

  const allNestedChildIds: string[] = [];

  // Created parents → nest children
  for (const parentRecord of parentChanges.created) {
    const enrichedParentRecord = { ...parentRecord };
    const nestedIds = await addChildRecords(parentTable, enrichedParentRecord, originalChanges);
    allNestedChildIds.push(...nestedIds);
    structuredChanges[parentTable].created.push(enrichedParentRecord);
  }

  // Updated parents → no child nesting (as per requirement)
  for (const parentRecord of parentChanges.updated) {
    // For updates, we don't include child records
    structuredChanges[parentTable].updated.push({ ...parentRecord });
  }

  return allNestedChildIds;
}

/**
 * Gets the foreign key field name used to link child records to their parent
 * @param {string} parentTable - The name of the parent table
 * @returns {string} - The foreign key field name used in child records
 */
function getParentIdField(parentTable: string): string {
  switch (parentTable) {
    case 'project_documents':
      return 'localProjectDocumentId'; // important!
    case 'tasks':
      return 'localTaskId';
    default:
      return '';
  }
}

/**
 * Adds child records to a parent record for nested creation
 * @param {string} parentTable - The name of the parent table
 * @param {any} parentRecord - The parent record to add children to
 * @param {WatermelonChangesType} changes - Original changes from WatermelonDB
 * @returns {Promise<string[]>} - Array of child IDs that were nested under the parent
 */
async function addChildRecords(
  parentTable: string,
  parentRecord: any,
  changes: WatermelonChangesType
): Promise<string[]> {
  const relations = tableRelations[parentTable as TableName];
  if (!relations) return [];

  const parentIdField = getParentIdField(parentTable);
  if (!parentIdField) return [];

  const nestedChildIds: string[] = [];

  for (const relation of relations) {
    const { tableName: childTable, foreignKey } = relation;
    try {
      if (!changes[childTable]?.created) continue;

      // Find child records that are related to this parent by foreign key
      const childRecordsToNest = changes[childTable].created.filter(child => child[foreignKey] === parentRecord.id);

      if (childRecordsToNest.length > 0) {
        const sanitizedChildren = await Promise.all(
          childRecordsToNest.map(async childRecord => {
            try {
              if (!childRecord || typeof childRecord !== 'object') {
                throw new Error(`Invalid child record format for ${childTable}`);
              }

              return await sanitizeData(childRecord, childTable as TableName, 'create');
            } catch (error) {
              Sentry.captureException(error, {
                extra: { childRecord, childTable }
              });
              return null;
            }
          })
        );

        const validChildren = sanitizedChildren.filter(child => child !== null);
        parentRecord[childTable] = validChildren;

        // Store the IDs of all nested children to filter them out later
        const childIds = childRecordsToNest.map(c => c.id);
        nestedChildIds.push(...childIds);

        // Remove nested children from original changes
        const originalLength = changes[childTable].created.length;
        changes[childTable].created = changes[childTable].created.filter(
          child => child[foreignKey] !== parentRecord.id
        );
      }
    } catch (error) {
      Sentry.captureException(error);
      parentRecord[childTable] = [];
    }
  }

  return nestedChildIds;
}

/**
 * Takes structured changes, sanitizes them, and sends to server
 * @param {StructuredChangesType} changes - Structured changes with remote IDs for deletion
 * @param {number} lastPulledAt - Timestamp of the last successful pull
 * @returns {Promise<SyncResponseType>} - Server response with sync results
 */
async function syncWithServer(changes: StructuredChangesType, lastPulledAt: number): Promise<SyncResponseType> {
  try {
    const apiPayloadChanges: { [key: string]: { created: Item[]; updated: Item[]; deleted: string[] } } = {};

    for (const tableName in changes) {
      const deletedIdsAsString = changes[tableName].deleted.map(id => String(id));

      const sanitizedCreated = await sanitizeRecords(changes[tableName].created, tableName as TableName, 'created');
      const sanitizedUpdated = await sanitizeRecords(changes[tableName].updated, tableName as TableName, 'updated');

      const apiTablePayload = {
        created: sanitizedCreated,
        updated: sanitizedUpdated,
        deleted: deletedIdsAsString
      };

      if (
        (apiTablePayload.created && apiTablePayload.created.length > 0) ||
        (apiTablePayload.updated && apiTablePayload.updated.length > 0) ||
        (apiTablePayload.deleted && apiTablePayload.deleted.length > 0)
      ) {
        apiPayloadChanges[tableName] = apiTablePayload;
      }
    }

    // If no changes to sync after processing, return early
    if (Object.keys(apiPayloadChanges).length === 0) {
      return { syncData: {} };
    }

    const response = await apolloClient.mutate({
      mutation: Gql.SyncDataDocument,
      variables: {
        input: {
          changes: apiPayloadChanges, // Send the payload with string IDs for deletion
          lastPulledAt
        }
      },
      errorPolicy: 'all',
      context: {
        timeout: 30000
      }
    });

    if (response.errors?.length) {
      throw new Error(`GraphQL errors: ${response.errors.map(e => e.message).join(', ')}`);
    }

    return response.data;
  } catch (error) {
    await SyncErrorHandler.capture(error as Error, {
      phase: 'syncWithServer'
    });
    throw error;
  }
}

/**
 * Sanitizes records for sending to the server
 * @param {Item[]} records - Records to sanitize
 * @param {TableName} tableName - The name of the table the records belong to
 * @param {'created' | 'updated'} operation - Whether the records are being created or updated
 * @returns {Promise<Item[]>} - Sanitized records ready for server sync
 *
 * This function handles foreign key relationships for both created and updated records.
 * For child records (like request_for_signatures), it looks up the parent's remoteId
 * and sets the appropriate foreign key field (like projectDocumentId) to maintain
 * the relationship on the server.
 */
async function sanitizeRecords(
  records: Item[],
  tableName: TableName,
  operation: 'created' | 'updated'
): Promise<Item[]> {
  if (!records || records.length === 0) return [];

  const sanitizedRecords = [];
  for (const record of records) {
    try {
      let sanitizedRecord;
      if (tableName === 'tasks') {
        sanitizedRecord = await sanitizedTask(record, operation === 'created' ? 'create' : 'update');
      } else {
        sanitizedRecord = await sanitizeData(record, tableName, operation === 'created' ? 'create' : 'update');
      }

      try {
        if (isChildTable(tableName)) {
          // For project document children
          if (record.localProjectDocumentId) {
            try {
              const projectDoc = (await database.collections
                .get('project_documents')
                .find(record.localProjectDocumentId)) as ProjectDocument;

              if (projectDoc?.remoteId) {
                sanitizedRecord.projectDocumentId = projectDoc.remoteId;
              }
            } catch (error) {}
          }
          // For task children
          else if (record.localTaskId) {
            try {
              const task = await database.collections.get('tasks').find(record.localTaskId);
              if (task && task._raw) {
                const rawTask = task._raw as any;
                if (rawTask.remoteId) {
                  sanitizedRecord.taskId = rawTask.remoteId;
                }
              }
            } catch (error) {}
          }
        }

        // Only include child records for created parent records
        if (operation === 'created') {
          for (const childTable of [...projectDocumentChildEntities, ...taskChildEntities]) {
            if (record[childTable] && Array.isArray(record[childTable])) {
              sanitizedRecord[childTable] = record[childTable];
            }
          }
        }
      } catch (error) {
        Sentry.captureException(error, {
          extra: { tableName, record }
        });
      }

      sanitizedRecords.push(sanitizedRecord);
    } catch (error) {
      Sentry.captureException(error, {
        extra: { record, tableName, operation }
      });
    }
  }

  return sanitizedRecords;
}

/**
 * Updates local records with remote IDs and other data returned from the server
 * @param {SyncResponseType} syncResponse - The response from the server
 * @returns {Promise<void>}
 */
async function updateLocalRecordsWithRemoteIds(syncResponse: SyncResponseType): Promise<void> {
  try {
    await database.write(async () => {
      for (const [tableName, tableData] of Object.entries(syncResponse.syncData)) {
        if (!tableData) continue;

        const { created = [], updated = [] } = tableData;
        const allChanges = [...created, ...updated];

        // Cache the collection for the current table.
        const collection = database.collections.get(tableName);

        for (const change of allChanges) {
          try {
            const records = await collection.query(Q.where('id', change.id)).fetch();
            if (records.length === 0) {
              continue;
            }
            const record = records[0];

            await record.update((rec: any) => {
              Object.keys(change).forEach(key => {
                if (!safeUpdateFields.has(key) || change[key] === null) return;

                switch (key) {
                  case 'groupCode':
                    rec[key] = String(change[key]);
                    break;
                  case 'remoteId':
                    rec[key] = parseInt(change[key] as string, 10);
                    break;
                  case 'taskCode':
                  case 'projectDocumentId':
                  case 'workspaceGroupId':
                  case 'allFormCode':
                  case 'currentUserId':
                    // Skip updating currentUserId if it's an empty string
                    if (change[key] !== '') {
                      rec[key] = parseInt(change[key] as string, 10);
                    }
                    break;
                  case 'projectGroupId':
                    rec[key] = parseInt(change[key] as string, 10);
                    break;
                  case 'server_created_at':
                    rec[key] = change[key]; // Assuming ISO string is acceptable
                    break;
                  default:
                    break;
                }
              });

              rec._raw._status = 'synced';
              rec._raw._changed = '';
            });

            if (tableName === 'project_documents' && change.documentStatus) {
              const projectDocumentId = change.projectDocumentId?.toString();
              if (!projectDocumentId) {
              } else {
                const docRecords = await database.collections
                  .get('project_documents')
                  .query(Q.where('remoteId', parseInt(projectDocumentId, 10)))
                  .fetch();
                if (docRecords.length > 0) {
                  const docRecord = docRecords[0];
                  if (docRecord._raw._status !== 'updated') {
                    await docRecord.update((rec: any) => {
                      rec.status = change.documentStatus;
                      rec._raw._status = 'synced';
                      rec._raw._changed = '';
                    });
                  }
                }
              }
            }
          } catch (error) {
            await SyncErrorHandler.capture(error as Error, {
              phase: 'updateLocalRecordsWithRemoteIds',
              documentId: change.id
            });
          }
        }
      }
    });
  } catch (error) {
    await SyncErrorHandler.capture(error as Error, {
      phase: 'updateLocalRecordsWithRemoteIds'
    });
    throw error;
  }
}
