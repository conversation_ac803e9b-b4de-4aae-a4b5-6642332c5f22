export interface TableRelation {
  tableName: string;
  foreignKey: string;
  fieldToUpdate: string;
}

export type TableName =
  | 'tasks_attachments'
  | 'tasks_medias'
  | 'project_groups'
  | 'task_comments'
  | 'tasks'
  | 'project_documents'
  | 'drawing_revisions'
  | 'project_users'
  | 'projects'
  | 'project_carousels'
  | 'workspace_groups'
  | 'request_for_signatures'
  | 'workspace_ccs'
  | 'workspace_attachments'
  | 'workspace_photos'
  | 'workspace_document'
  | 'project_document_comments';

export type TableRelations = {
  [key in TableName]?: TableRelation[];
};

export const tableRelations: TableRelations = {
  tasks: [
    { tableName: 'tasks_medias', foreignKey: 'localTaskId', fieldToUpdate: 'taskId' },
    { tableName: 'tasks_attachments', foreignKey: 'localTaskId', fieldToUpdate: 'taskId' },
    { tableName: 'task_comments', foreignKey: 'localTaskId', fieldToUpdate: 'taskId' }
  ],
  project_documents: [
    { tableName: 'project_documents', foreignKey: 'localRemoteId', fieldToUpdate: 'projectDocumentId' },
    { tableName: 'request_for_signatures', foreignKey: 'localProjectDocumentId', fieldToUpdate: 'projectDocumentId' },
    { tableName: 'workspace_ccs', foreignKey: 'localProjectDocumentId', fieldToUpdate: 'projectDocumentId' },
    { tableName: 'workspace_groups', foreignKey: 'localGroupId', fieldToUpdate: 'workspaceGroupId' },
    { tableName: 'workspace_attachments', foreignKey: 'localProjectDocumentId', fieldToUpdate: 'projectDocumentId' },
    { tableName: 'workspace_photos', foreignKey: 'localProjectDocumentId', fieldToUpdate: 'projectDocumentId' },
    { tableName: 'workspace_document', foreignKey: 'localProjectDocumentId', fieldToUpdate: 'projectDocumentId' },
    { tableName: 'project_document_comments', foreignKey: 'localProjectDocumentId', fieldToUpdate: 'projectDocumentId' }
  ],
  projects: [{ tableName: 'project_carousels', foreignKey: 'localProjectId', fieldToUpdate: 'projectId' }]
};

export const FIXTURES_GROUP_TASKS = ['Ungroup Tasks'];
export const FIXTURES_GROUP_DOCUMENT = ['Ungroup Document', 'Site Diary'];

export const taskChildEntities = ['tasks_medias', 'tasks_attachments', 'task_comments'];
export const projectDocumentChildEntities = [
  'workspace_ccs',
  'workspace_groups',
  'workspace_attachments',
  'workspace_photos',
  'workspace_document',
  'project_document_comments',
  'request_for_signatures'
];

export const tableWithoutUpdate = [
  'tasks_attachments',
  'tasks_medias',
  'task_comments',
  'workspace_ccs',
  'workspace_attachments',
  'workspace_photos',
  'workspace_document',
  'project_document_comments'
];

export const unpushedTables = ['offline_download'];
