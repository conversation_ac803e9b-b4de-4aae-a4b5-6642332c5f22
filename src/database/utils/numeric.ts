/**
 * Determines if the given projectDocumentId is numeric and returns an appropriate object.
 *
 * @param projectDocumentId - The ID to be checked and parsed.
 * @returns An object with either `projectDocumentId` as a number or `localRemoteId` as a string.
 */
export const getLinkId = (id: any) => {
  const isNumeric = /^\d+$/.test(id);

  return isNumeric
    ? {
        projectDocumentId: parseInt(id ?? '0', 10)
      }
    : {
        localRemoteId: id
      };
};

/**
 * Generates a key-value pair for a foreign key based on the input type, along with a specified field name for querying.
 * If the input ID is numeric, it assumes it to be a 'remoteId' with a default field name, otherwise uses the provided returnKey.
 *
 * @param {number | string} id - The ID that could either be a local or remote identifier.
 * @param {string} returnKey - The key to return if the ID is not numeric.
 * @param {string} defaultFieldName - The default field name for numeric remote ID queries.
 * @returns {{keyValue: Record<string, number|string>, fieldName: string}}
 * An object containing the key-value pair and the field name for querying.
 */
export const getForeignKey = (
  id: number | string,
  returnKey: string,
  defaultFieldName: string = 'remoteId'
): { keyValue: Record<string, number | string>; fieldName: string } => {
  const isNumeric = /^-?\d+$/.test(id.toString());

  if (isNumeric) {
    return {
      keyValue: { [defaultFieldName]: parseInt(id.toString(), 10) },
      fieldName: defaultFieldName // Use the default field name for numeric IDs
    };
  } else {
    return {
      keyValue: { [returnKey]: id },
      fieldName: returnKey // Use the return key as the field name for non-numeric IDs
    };
  }
};
