import apolloClient from '@src/lib/apollo';
import { Gql } from '@src/api';
import { sanitizeData, sanitizedTask } from './sanitizedInput';
import database from '../index.native';
import { projectDocumentChildEntities, tableRelations, tableWithoutUpdate, taskChildEntities } from './constant';
import { Q } from '@nozbe/watermelondb';
import { store } from '@src/store';
import { AppActions } from '@src/slice/app.slice';
import * as Sentry from '@sentry/react-native';
import BaseModel from '../model/base-model';
import { SyncErrorContext, SyncErrorHandler } from '@src/lib/errorHandler';
import { SyncResponseType } from '@src/api/graphql';

import { TableName } from './constant';
import '@nozbe/watermelondb';

import { Model, Collection } from '@nozbe/watermelondb';

type ChangeType = 'created' | 'updated' | 'deleted';

interface Item {
  [key: string]: any;
}

interface TableChanges {
  created: Item[];
  updated: Item[];
  deleted: Item[];
}

interface ChangesType {
  [key: string]: TableChanges;
}

type Handler = (item: Item) => Promise<Item>;
interface Handlers {
  [key: string]: Handler;
}

let globalChanges: ChangesType = {};

export async function mainSyncFunction({
  changes,
  lastPulledAt
}: {
  changes: ChangesType;
  lastPulledAt: number;
}): Promise<boolean> {
  return pushWithRetry(changes, lastPulledAt);
}

export async function pushWithRetry(changes: ChangesType, lastPulledAt: number): Promise<boolean> {
  globalChanges = changes;

  const groupedTables = Object.keys(changes).filter(
    tableName =>
      ['project_documents', 'request_for_signatures'].includes(tableName) &&
      hasTableChanges(changes[tableName], tableName as TableName)
  );

  const individualTables = Object.keys(changes).filter(
    tableName =>
      [
        'drawing_revisions',
        'project_groups',
        'tasks',
        'task_comments',
        'tasks_attachments',
        'tasks_medias',
        'projects',
        'project_carousels',
        'workspace_groups',
        'workspace_ccs',
        'workspace_attachments',
        'workspace_photos',
        'workspace_document',
        'project_document_comments'
      ].includes(tableName) && hasTableChanges(changes[tableName], tableName as TableName)
  );

  try {
    const groupedChanges: { [key: string]: TableChanges } = {};
    for (const tableName of groupedTables) {
      if (globalChanges[tableName]) {
        groupedChanges[tableName] = globalChanges[tableName];
      }
    }

    if (Object.keys(groupedChanges).length > 0) {
      const groupedData = await syncGroupedTables(groupedChanges, lastPulledAt);
      for (const tableName of groupedTables) {
        if (groupedData.syncData[tableName]?.failed && groupedData.syncData[tableName]?.failed.length > 0) {
          await increaseRetryCount(groupedData.syncData[tableName].failed, tableName as TableName);
        }

        if (groupedData) {
          await updateLocalRecordsWithRemoteIds(tableName as TableName, groupedData);
        }
      }
    }

    for (const tableName of individualTables) {
      if (globalChanges[tableName]) {
        const data: any = await syncTableWithServer(globalChanges[tableName], tableName as TableName, lastPulledAt, {
          phase: 'table_sync'
        });
        if (data?.syncData?.[tableName]?.failed && data.syncData[tableName].failed.length > 0) {
          await increaseRetryCount(data.syncData[tableName].failed, tableName as TableName);
        }
        await updateLocalRecordsWithRemoteIds(tableName as TableName, data);
      }
    }

    return true;
  } catch (error) {
    await SyncErrorHandler.capture(error as Error, {
      phase: 'pushWithRetry'
    });
    return false;
  }
}

async function handleTableChanges(tableName: TableName, changeType: ChangeType, changes: Item[]): Promise<Item[]> {
  try {
    if (!changes.length) return [];

    const handlers: Handlers = {
      tasks: item => sanitizedTask(item, changeType === 'created' ? 'create' : 'update'),
      project_groups: item => sanitizeData(item, 'project_groups', changeType === 'created' ? 'create' : 'update'),
      tasks_attachments: item =>
        sanitizeData(item, 'tasks_attachments', changeType === 'created' ? 'create' : 'update'),
      task_comments: item => sanitizeData(item, 'task_comments', changeType === 'created' ? 'create' : 'update'),
      tasks_medias: item => sanitizeData(item, 'tasks_medias', changeType === 'created' ? 'create' : 'update'),
      project_documents: item =>
        sanitizeData(item, 'project_documents', changeType === 'created' ? 'create' : 'update'),
      drawing_revisions: item =>
        sanitizeData(item, 'drawing_revisions', changeType === 'created' ? 'create' : 'update'),
      projects: item => sanitizeData(item, 'projects', changeType === 'created' ? 'create' : 'update'),
      project_carousels: item =>
        sanitizeData(item, 'project_carousels', changeType === 'created' ? 'create' : 'update'),
      workspace_groups: item => sanitizeData(item, 'workspace_groups', changeType === 'created' ? 'create' : 'update'),
      request_for_signatures: item =>
        sanitizeData(item, 'request_for_signatures', changeType === 'created' ? 'create' : 'update'),
      workspace_ccs: item => sanitizeData(item, 'workspace_ccs', changeType === 'created' ? 'create' : 'update'),
      workspace_attachments: item =>
        sanitizeData(item, 'workspace_attachments', changeType === 'created' ? 'create' : 'update'),
      workspace_photos: item => sanitizeData(item, 'workspace_photos', changeType === 'created' ? 'create' : 'update'),
      workspace_document: item =>
        sanitizeData(item, 'workspace_document', changeType === 'created' ? 'create' : 'update'),
      project_document_comments: item =>
        sanitizeData(item, 'project_document_comments', changeType === 'created' ? 'create' : 'update')
    };

    if (projectDocumentChildEntities.includes(tableName)) {
      changes = changes.filter(item => item.projectDocumentId !== 0);
    }

    if (taskChildEntities.includes(tableName)) {
      changes = changes.filter(item => item.taskId !== 0);
    }

    const handler = handlers[tableName] || (item => Promise.resolve(item));
    return Promise.all(changes.map(handler));
  } catch (error) {
    await SyncErrorHandler.capture(error as Error, {
      phase: 'handleTableChanges',
      documentId: tableName
    });
    throw error;
  }
}

async function syncTableWithServer(
  tableChanges: TableChanges,
  tableName: TableName,
  lastPulledAt: number,
  context: SyncErrorContext
): Promise<SyncResponseType> {
  const tableContext: SyncErrorContext = {
    ...context,
    phase: `table_sync_${tableName}`,
    documentId: undefined // Reset document ID for table-level sync
  };

  try {
    const filteredChanges = filterTableChanges(tableChanges, tableName);
    const remoteDeletedIds = await fetchRemoteIdsForDeletedItems(filteredChanges.deleted as any, tableName);

    const formattedChanges = {
      created: await handleTableChanges(tableName, 'created', filteredChanges.created),
      updated: await handleTableChanges(tableName, 'updated', filteredChanges.updated),
      deleted: remoteDeletedIds
    };

    const response = await apolloClient.mutate({
      mutation: Gql.SyncDataDocument,
      variables: {
        input: { changes: { [tableName]: formattedChanges }, lastPulledAt }
      },
      errorPolicy: 'all',
      context: { timeout: 30000 }
    });

    if (response.errors) {
      const error = new Error(`GraphQL errors: ${response.errors.map(e => e.message).join(', ')}`);
      await SyncErrorHandler.capture(error as Error, {
        ...tableContext,
        graphQlErrors: response.errors
      });
    }

    return response.data;
  } catch (error) {
    await SyncErrorHandler.capture(error instanceof Error ? error : new Error(String(error)), tableContext);
    throw error;
  }
}

async function syncGroupedTables(
  groupedChanges: { [key: string]: TableChanges },
  lastPulledAt: number
): Promise<{ [key: string]: any }> {
  try {
    const projectDocumentsChanges = groupedChanges['project_documents'] || { created: [], updated: [], deleted: [] };
    const requestForSignaturesChanges = groupedChanges['request_for_signatures'] || {
      created: [],
      updated: [],
      deleted: []
    };

    projectDocumentsChanges.created = projectDocumentsChanges.created.filter(
      item => item.fileKey || item.type === 'folder'
    );

    let projectDocumentDeletedRemoteIds: any[] = [];
    let requestForSignaturesDeletedRemoteIds: any[] = [];

    try {
      projectDocumentDeletedRemoteIds = await Promise.all(
        projectDocumentsChanges.deleted.map((item: any) => fetchRemoteIdForDeletedItem(item, 'project_documents'))
      );
    } catch (error) {
      Sentry.captureException(error, {
        level: 'error',
        tags: { module: 'project_documents', operation: 'fetchRemoteIdForDeletedItem' },
        extra: { deletedItems: projectDocumentsChanges.deleted }
      });
      throw new Error('Failed to process deleted project documents.');
    }

    try {
      requestForSignaturesDeletedRemoteIds = await Promise.all(
        requestForSignaturesChanges.deleted.map((item: any) =>
          fetchRemoteIdForDeletedItem(item, 'request_for_signatures')
        )
      );
    } catch (error) {
      Sentry.captureException(error, {
        level: 'error',
        tags: { module: 'request_for_signatures', operation: 'fetchRemoteIdForDeletedItem' },
        extra: { deletedItems: requestForSignaturesChanges.deleted }
      });
      throw new Error('Failed to process deleted request for signatures.');
    }

    const validProjectDocumentDeletedIds = projectDocumentDeletedRemoteIds.filter(id => id != null);
    const validRequestForSignaturesDeletedIds = requestForSignaturesDeletedRemoteIds.filter(id => id != null);

    const formattedChanges = {
      project_documents: {
        created: await handleTableChanges('project_documents', 'created', projectDocumentsChanges.created),
        updated: await handleTableChanges('project_documents', 'updated', projectDocumentsChanges.updated),
        deleted: validProjectDocumentDeletedIds
      },
      request_for_signatures: {
        created: await handleTableChanges('request_for_signatures', 'created', requestForSignaturesChanges.created),
        updated: await handleTableChanges('request_for_signatures', 'updated', requestForSignaturesChanges.updated),
        deleted: validRequestForSignaturesDeletedIds
      }
    };

    try {
      const response = await apolloClient.mutate({
        mutation: Gql.SyncDataDocument,
        variables: {
          input: {
            changes: formattedChanges,
            lastPulledAt
          }
        },
        errorPolicy: 'all',
        context: {
          timeout: 30000
        }
      });

      if (response.errors?.length) {
        const errorMessages = response.errors.map(err => err.message).join(', ');
        Sentry.captureException(new Error(`GraphQL errors occurred: ${errorMessages}`), {
          level: 'error',
          tags: { operation: 'graphql_mutation' },
          extra: { errors: response.errors }
        });
        store.dispatch(AppActions.setSyncStatus('failed'));
        throw new Error(`GraphQL errors occurred: ${errorMessages}`);
      }

      return response.data;
    } catch (mutationError) {
      Sentry.captureException(mutationError, {
        level: 'error',
        tags: { module: 'apolloClient', operation: 'mutation' },
        extra: { formattedChanges }
      });
      store.dispatch(AppActions.setSyncStatus('failed'));
      throw mutationError;
    }
  } catch (generalError) {
    await SyncErrorHandler.capture(generalError as Error, {
      phase: 'syncGroupedTables'
    });
    store.dispatch(AppActions.setSyncStatus('failed'));
    throw generalError;
  }
}

async function updateLocalRecordsWithRemoteIds(
  tableName: TableName,
  syncResponse: Gql.SyncResponseType
): Promise<void> {
  try {
    //@ts-ignore
    const tableData = syncResponse.syncData[tableName];

    if (!tableData) {
      return;
    }

    const { created, updated } = tableData;
    const allChanges = [...(created || []), ...(updated || [])];

    if (allChanges.length === 0) return;

    const safeUpdateFields = new Set([
      'remoteId',
      'fileUrl',
      'taskCode',
      'projectDocumentId',
      'workspaceGroupId',
      'allFormCode',
      'groupCode',
      'server_created_at',
      'currentUserId',
      'projectGroupId',
      'workspaceGroupId',
      'documentStatus'
    ]);

    const globalTableChanges = globalChanges[tableName] || { created: [], updated: [], deleted: [] };
    const changesToUpdate: any = findItemsToUpdate(globalTableChanges, allChanges, safeUpdateFields);

    if (changesToUpdate.length === 0) {
      return;
    }

    try {
      await database.write(async () => {
        const collection: any = database.collections.get(tableName);
        const batchedUpdates = [];

        for (const change of changesToUpdate) {
          //This part is to update status for project_documents linear logic
          if (change.documentStatus && change.documentStatus !== null) {
            // Prepare update document status for project documents
            const records = await database.collections
              .get('project_documents')
              .query(Q.where('remoteId', parseInt(change.projectDocumentId, 10)))
              .fetch();

            if (records.length === 0) {
              continue; // Skip to next change if no matching records
            } else {
              const record = records[0];
              if (record._raw._status === 'updated') {
                continue; // Skip to next change if record is not synced
              }
              const updateOp1 = record.prepareUpdate((record: any) => {
                record.status = change.documentStatus;
                record._raw._status = 'synced';
                record._raw._changed = '';
              });
              batchedUpdates.push(updateOp1);
            }
          }
          try {
            const record: any = await collection.find(change.id);

            try {
              const updateOp = record.prepareUpdate((record: any) => {
                Object.keys(change).forEach(key => {
                  if (!safeUpdateFields.has(key)) {
                    return;
                  }
                  // Skip update if change[key] is null
                  if (change[key] === null) {
                    return;
                  }
                  if (key === 'groupCode') {
                    record[key] = change[key]?.toString();
                  } else if (
                    [
                      'remoteId',
                      'taskCode',
                      'projectDocumentId',
                      'workspaceGroupId',
                      'allFormCode',
                      'currentUserId',
                      'projectGroupId',
                      'workspaceGroupId'
                    ].includes(key)
                  ) {
                    record[key] = parseInt(change[key], 10);
                  } else {
                    record[key] = change[key];
                  }
                });

                record._raw._status = 'synced';
                record._raw._changed = '';
              });

              batchedUpdates.push(updateOp);
            } catch (error) {
              Sentry.captureException(error, {
                level: 'error',
                tags: { module: 'updateLocalRecordsWithRemoteIds', operation: 'prepareUpdate' },
                extra: { tableName, changeId: change.id }
              });
            }
          } catch (error) {
            Sentry.captureException(error, {
              level: 'error',
              tags: { module: 'updateLocalRecordsWithRemoteIds', operation: 'findRecord' },
              extra: { tableName, changeId: change.id }
            });
          }
        }

        if (batchedUpdates.length > 0) {
          try {
            await database.batch(...batchedUpdates);
          } catch (error) {
            Sentry.captureException(error, {
              level: 'error',
              tags: { module: 'updateLocalRecordsWithRemoteIds', operation: 'batchUpdate' },
              extra: { tableName, updateCount: batchedUpdates.length }
            });
            throw error;
          }
        }
      });
    } catch (error) {
      Sentry.captureException(error, {
        level: 'error',
        tags: { module: 'updateLocalRecordsWithRemoteIds', operation: 'databaseWrite' },
        extra: { tableName }
      });
      throw error;
    }

    return await updateRelatedTablesAndChanges(tableName, changesToUpdate);
  } catch (error) {
    Sentry.captureException(error, {
      level: 'error',
      tags: { module: 'updateLocalRecordsWithRemoteIds' },
      extra: { tableName }
    });
    throw error;
  }
}

async function updateRelatedTablesAndChanges(
  parentTableName: TableName,
  parentChanges: Array<{ id: string; remoteId: string }>
): Promise<void> {
  try {
    const relations = tableRelations[parentTableName];
    if (!relations || relations.length === 0) {
      return;
    }

    await database.write(async () => {
      for (const relation of relations) {
        const { tableName, foreignKey, fieldToUpdate } = relation;
        const childTable = database.collections.get(tableName);
        if (!childTable) {
          continue;
        }
        const childUpdates = [];

        for (const parentChange of parentChanges) {
          // Validate remoteId is numeric
          const remoteId = parseInt(parentChange.remoteId);
          if (isNaN(remoteId)) {
            continue;
          }
          try {
            const children = await (childTable as Collection<Model>)
              .query(Q.where(foreignKey, parentChange.id))
              .fetch();
            for (const child of children) {
              const updateOp = (child as Model).prepareUpdate((record: any) => {
                (record as any)[fieldToUpdate] = remoteId;
              });
              childUpdates.push(updateOp);

              // Safely update globalChanges
              if (globalChanges?.[tableName]) {
                const { created = [], updated = [] } = globalChanges[tableName];
                let childChange = created.find(c => c.id === child.id);

                if (childChange) {
                  childChange[fieldToUpdate] = remoteId;
                } else {
                  childChange = { id: child.id, [fieldToUpdate]: remoteId };
                  updated.push(childChange);
                }
              }
            }
          } catch (error) {
            await SyncErrorHandler.capture(error as Error, {
              phase: 'updateRelatedTablesAndChanges',
              documentId: parentChange.id
            });
          }
        }

        if (childUpdates.length > 0) {
          try {
            await database.batch(...childUpdates);
          } catch (error) {
            await SyncErrorHandler.capture(error as Error, {
              phase: 'updateRelatedTablesAndChanges_batch',
              documentId: tableName
            });
          }
        }
      }
    });
  } catch (error) {
    await SyncErrorHandler.capture(error as Error, {
      phase: 'updateRelatedTablesAndChanges',
      documentId: parentTableName
    });
    throw error;
  }
}

async function fetchRemoteIdForDeletedItem(localId: string, tableName: TableName) {
  const record: any = await database.get(tableName).find(localId);

  if (!record) {
    return null;
  }

  if (record.remoteId) {
    return record.remoteId;
  }

  return null;
}

async function increaseRetryCount(failedChanges: Gql.FailedRecord[], tableName: TableName): Promise<void> {
  if (!failedChanges || failedChanges.length === 0) {
    return;
  }

  await database.write(async () => {
    const collection = database.collections.get<BaseModel>(tableName);
    const batchedOperations = [];

    for (const change of failedChanges) {
      try {
        const record = await collection.find(change.id);
        const currentRetryCount = record.retryCount ?? 0;
        const nextRetryCount = currentRetryCount + 1;

        if (nextRetryCount >= 3) {
          batchedOperations.push(record.prepareDestroyPermanently());
        } else {
          const updateOp = record.prepareUpdate(r => {
            r.retryCount = nextRetryCount;
          });
          batchedOperations.push(updateOp);
        }
      } catch (error) {
        // Error handling
      }
    }

    if (batchedOperations.length > 0) {
      await database.batch(...batchedOperations);
    }
  });
}

function hasTableChanges(tableChanges: TableChanges, tableName: TableName): boolean {
  const hasCreatedChanges = tableChanges.created.length > 0;
  const hasDeletedChanges = tableChanges.deleted.length > 0;

  const hasUpdatedChanges =
    tableChanges.updated.length > 0 &&
    !tableWithoutUpdate.includes(tableName) &&
    tableChanges.updated.some(item => {
      // If no _changed field or empty, skip this item
      if (!item._changed) return false;

      // Split the _changed string into an array of field names
      const changedFields = item._changed.split(',');

      // Return true only if there are fields other than just updated_at
      return changedFields.some((field: string) => field && field !== 'updated_at');
    });

  return hasCreatedChanges || hasUpdatedChanges || hasDeletedChanges;
}

function findItemsToUpdate(globalTableChanges: TableChanges, allChanges: Item[], safeUpdateFields: Set<string>) {
  const { created, updated } = globalTableChanges;

  // Process "created" records: Always need updates
  const createdItemsToUpdate = created
    .filter(item => item._status === 'created')
    .map(createdItem => allChanges.find(change => change.id === createdItem.id))
    .filter(Boolean); // Remove null/undefined if no match found

  // Process "updated" records: Check if `_changed` contains safe fields or if documentStatus exists
  const hasDocumentStatus = allChanges.some(change => change.documentStatus);
  const updatedItemsToUpdate = updated
    .filter(item => {
      if (item._status !== 'updated' || !item._changed) return false;
      const changedFields = item._changed ? new Set(item._changed.split(',')) : new Set();
      // Check for intersection with safe fields
      const hasSafeField = Array.from(safeUpdateFields).some(field => changedFields.has(field));
      // Include item if documentStatus exists
      return hasSafeField || hasDocumentStatus;
    })
    .map(updatedItem => allChanges.find(change => change.id === updatedItem.id))
    .filter(Boolean); // Remove null/undefined if no match found

  // Combine created and filtered updated items
  const result = [...createdItemsToUpdate, ...updatedItemsToUpdate];
  return result;
}

function filterTableChanges(changes: TableChanges, tableName: TableName): TableChanges {
  // Special handling for file-based tables
  if (
    ['project_documents', 'tasks_medias', 'drawing_revisions', 'project_carousels', 'workspace_attachments'].includes(
      tableName
    )
  ) {
    return {
      created: changes.created.filter(
        item => item.fileKey || (tableName === 'project_documents' && item.type === 'folder')
      ),
      updated: changes.updated.filter(
        item => item.fileKey || (tableName === 'project_documents' && item.type === 'folder')
      ),
      deleted: changes.deleted
    };
  }

  // Default case - return original changes
  return changes;
}

async function fetchRemoteIdsForDeletedItems(localIds: string[], tableName: TableName): Promise<string[]> {
  const validIds: string[] = [];

  await database.read(async () => {
    const collection = database.collections.get<BaseModel & { remoteId: string }>(tableName);

    for (const localId of localIds) {
      try {
        const record = await collection.find(localId);
        if (record.remoteId) {
          validIds.push(record.remoteId.toString());
        }
      } catch (error) {
        await SyncErrorHandler.capture(error as Error, {
          phase: 'fetch_deleted_remote_id',
          documentId: localId
        });
      }
    }
  });

  return validIds;
}
