import { createTable, schemaMigrations } from '@nozbe/watermelondb/Schema/migrations';

export default schemaMigrations({
  migrations: [
    {
      toVersion: 2,
      steps: [
        createTable({
          name: 'project_groups',
          columns: [
            { name: 'remoteId', type: 'number', isOptional: true, isIndexed: true },
            { name: 'projectId', type: 'number', isIndexed: true },
            { name: 'updated_at', type: 'number' },
            { name: 'deleted_at', type: 'number' },
            { name: 'title', type: 'string' },
            { name: 'projectGroupId', type: 'string', isOptional: true, isIndexed: true },
            { name: 'retry_count', type: 'number' },
            { name: 'recordSource', type: 'string', isOptional: true }
          ]
        }),
        createTable({
          name: 'tasks',
          columns: [
            { name: 'remoteId', type: 'number', isIndexed: true },
            { name: 'projectId', type: 'number' },
            { name: 'ownerId', type: 'number', isIndexed: true },
            { name: 'groupId', type: 'number', isIndexed: true },
            { name: 'taskCode', type: 'number', isIndexed: true },
            { name: 'title', type: 'string' },
            { name: 'annotId', type: 'string', isOptional: true },
            { name: 'spriteId', type: 'string', isOptional: true },
            { name: 'elementId', type: 'string', isOptional: true },
            { name: 'pos3D', type: 'string', isOptional: true },
            { name: 'bimId', type: 'string', isOptional: true },
            { name: 'imageURL', type: 'string', isOptional: true },
            { name: 'description', type: 'string', isOptional: true },
            { name: 'dueDate', type: 'string', isOptional: true },
            { name: 'status', type: 'string' },
            { name: 'isSentBeforeOneDay', type: 'boolean', isIndexed: true },
            { name: 'isSentBeforeOneHour', type: 'boolean', isIndexed: true },
            { name: 'permanentlyDeleted', type: 'boolean', isIndexed: true },
            { name: 'proposedStatus', type: 'string', isOptional: true },
            { name: 'isUrgent', type: 'boolean', isOptional: true },
            { name: 'memoUrl', type: 'string', isOptional: true },
            { name: 'previewMemoUrl', type: 'string', isOptional: true },
            { name: 'isMemoReceive', type: 'boolean', isOptional: true },
            { name: 'issuedById', type: 'number', isIndexed: true },
            { name: 'assignees', type: 'string', isOptional: true },
            { name: 'copies', type: 'string', isOptional: true },
            { name: 'documents', type: 'string', isOptional: true },
            { name: 'updated_at', type: 'number' },
            { name: 'deleted_at', type: 'number' },
            { name: 'localMemoUrl', type: 'string', isOptional: true },
            { name: 'retry_count', type: 'number' },
            { name: 'recordSource', type: 'string', isOptional: true }
          ]
        }),
        createTable({
          name: 'project_users',
          columns: [
            { name: 'remoteId', type: 'number', isOptional: true, isIndexed: true },
            { name: 'userId', type: 'number', isIndexed: true },
            { name: 'projectId', type: 'number', isIndexed: true },
            { name: 'role', type: 'string' },
            { name: 'name', type: 'string' },
            { name: 'email', type: 'string' },
            { name: 'avatarUrl', type: 'string', isOptional: true },
            { name: 'updated_at', type: 'number' },
            { name: 'deleted_at', type: 'number' },
            { name: 'retry_count', type: 'number' },
            { name: 'recordSource', type: 'string', isOptional: true }
          ]
        }),
        createTable({
          name: 'tasks_attachments',
          columns: [
            { name: 'remoteId', type: 'number', isIndexed: true },
            { name: 'taskId', type: 'number', isIndexed: true },
            { name: 'userId', type: 'number', isIndexed: true },
            { name: 'name', type: 'string' },
            { name: 'fileUrl', type: 'string', isOptional: true },
            { name: 'type', type: 'string' },
            { name: 'updated_at', type: 'number' },
            { name: 'deleted_at', type: 'number' },
            { name: 'file', type: 'string', isOptional: true },
            { name: 'retry_count', type: 'number' },
            { name: 'recordSource', type: 'string', isOptional: true }
          ]
        }),
        createTable({
          name: 'tasks_comments',
          columns: [
            { name: 'remoteId', type: 'number', isIndexed: true },
            { name: 'taskId', type: 'number', isIndexed: true },
            { name: 'userId', type: 'number', isIndexed: true },
            { name: 'message', type: 'string' },
            { name: 'updated_at', type: 'number' },
            { name: 'deleted_at', type: 'number' },
            { name: 'server_created_at', type: 'string', isOptional: true },
            { name: 'server_updated_at', type: 'string', isOptional: true },
            { name: 'retry_count', type: 'number' },
            { name: 'recordSource', type: 'string', isOptional: true }
          ]
        }),
        createTable({
          name: 'tasks_medias',
          columns: [
            { name: 'remoteId', type: 'number', isIndexed: true },
            { name: 'taskId', type: 'number', isIndexed: true },
            { name: 'userId', type: 'number', isIndexed: true },
            { name: 'name', type: 'string' },
            { name: 'fileUrl', type: 'string' },
            { name: 'type', type: 'string' },
            { name: 'updated_at', type: 'number' },
            { name: 'deleted_at', type: 'number' },
            { name: 'retry_count', type: 'number' },
            { name: 'recordSource', type: 'string', isOptional: true }
          ]
        }),
        createTable({
          name: 'project_documents',
          columns: [
            { name: 'remoteId', type: 'number', isIndexed: true },
            { name: 'localRemoteId', type: 'string', isOptional: true },
            { name: 'projectId', type: 'number' },
            { name: 'projectDocumentId', type: 'number', isOptional: true },
            { name: 'addedBy', type: 'number', isOptional: true },
            { name: 'formCategoryId', type: 'number', isOptional: true },
            { name: 'workspaceGroupId', type: 'number', isOptional: true },
            { name: 'name', type: 'string' },
            { name: 'description', type: 'string', isOptional: true },
            { name: 'fileSystemType', type: 'string', isOptional: true },
            { name: 'driveType', type: 'string' },
            { name: 'fileUrl', type: 'string', isOptional: true },
            { name: 'obsUrl', type: 'string', isOptional: true },
            { name: 'obsFileSize', type: 'number', isOptional: true },
            { name: 'obsFileType', type: 'string', isOptional: true },
            { name: 'type', type: 'string', isOptional: true },
            { name: 'fileSize', type: 'number', isOptional: true },
            { name: 'status', type: 'string', isOptional: true },
            { name: 'category', type: 'string', isOptional: true },
            { name: 'fileChannel', type: 'string' },
            { name: 'xfdf', type: 'string', isOptional: true },
            { name: 'notes', type: 'string', isOptional: true },
            { name: 'versionName', type: 'string', isOptional: true },
            { name: 'allFormCode', type: 'number', isOptional: true },
            { name: 'watermarkId', type: 'string', isOptional: true },
            { name: 'uploadLatitude', type: 'string', isOptional: true },
            { name: 'uploadLongitude', type: 'string', isOptional: true },
            { name: 'uploadAddress', type: 'string', isOptional: true },
            { name: 'videoThumbnail', type: 'string', isOptional: true },
            { name: 'isDocsStored', type: 'boolean' },
            { name: 'submittedAt', type: 'number', isOptional: true },
            { name: 'groupCode', type: 'string', isOptional: true },
            { name: 'currentUserId', type: 'number', isOptional: true },
            { name: 'workflow', type: 'string', isOptional: true },
            { name: 'updated_at', type: 'number' },
            { name: 'deleted_at', type: 'number' },
            { name: 'updatedBy', type: 'number', isOptional: true },
            { name: 'localFileUrl', type: 'string', isOptional: true },
            { name: 'fileKey', type: 'string', isOptional: true },
            { name: 'server_created_at', type: 'number', isOptional: true },
            { name: 'server_updated_at', type: 'number', isOptional: true },
            { name: 'localCurrentUserId', type: 'string', isOptional: true },
            { name: 'isQueued', type: 'boolean', isOptional: true },
            { name: 'retry_count', type: 'number' },
            { name: 'recordSource', type: 'string', isOptional: true },
            { name: 'download_retry', type: 'number' },
            { name: 'assigneeIds', type: 'string', isOptional: true },
            { name: 'autosavedAt', type: 'number', isOptional: true }
          ]
        }),
        createTable({
          name: 'drawing_revisions',
          columns: [
            { name: 'fileName', type: 'string', isOptional: true },
            { name: 'fileUrl', type: 'string' },
            { name: 'localFileUrl', type: 'string', isOptional: true },
            { name: 'fileKey', type: 'string' },
            { name: 'projectDocumentId', type: 'number', isOptional: true },
            { name: 'version', type: 'number', isOptional: true },
            { name: 'versionName', type: 'string', isOptional: true },
            { name: 'notes', type: 'string', isOptional: true },
            { name: 'xfdf', type: 'string', isOptional: true },
            { name: 'status', type: 'string', isOptional: true },
            { name: 'category', type: 'string', isOptional: true },
            { name: 'remoteId', type: 'number', isIndexed: true, isOptional: true },
            { name: 'localProjectDocumentId', type: 'string', isOptional: true },
            { name: 'retry_count', type: 'number' },
            { name: 'recordSource', type: 'string', isOptional: true }
          ]
        }),
        createTable({
          name: 'workspace_groups',
          columns: [
            { name: 'name', type: 'string' },
            { name: 'projectId', type: 'number' },
            { name: 'workspaceGroupId', type: 'number', isOptional: true },
            { name: 'remoteId', type: 'number', isOptional: true, isIndexed: true },
            { name: 'created_at', type: 'number' },
            { name: 'updated_at', type: 'number' },
            { name: 'deleted_at', type: 'number' },
            { name: 'updatedBy', type: 'number', isOptional: true },
            { name: 'localGroupId', type: 'string', isOptional: true },
            // { name: 'runningId', type: 'string', isOptional: true },
            { name: 'code', type: 'string', isOptional: true },
            { name: 'retry_count', type: 'number' },
            { name: 'recordSource', type: 'string', isOptional: true }
          ]
        }),
        createTable({
          name: 'projects',
          columns: [
            { name: 'companyId', type: 'number' },
            { name: 'userId', type: 'number' },
            { name: 'refNo', type: 'string', isOptional: true },
            { name: 'title', type: 'string' },
            { name: 'description', type: 'string', isOptional: true },
            { name: 'status', type: 'string' },
            { name: 'managedBy', type: 'string', isOptional: true },
            { name: 'deputySuperintendent', type: 'string', isOptional: true },
            { name: 'client', type: 'string', isOptional: true },
            { name: 'contractor', type: 'string', isOptional: true },
            { name: 'startDate', type: 'number', isOptional: true },
            { name: 'completionDate', type: 'number', isOptional: true },
            { name: 'cnsConsultant', type: 'string', isOptional: true },
            { name: 'mneConsultant', type: 'string', isOptional: true },
            { name: 'qsConsultant', type: 'string', isOptional: true },
            { name: 'environmentConsultant', type: 'string', isOptional: true },
            { name: 'fileUrlCover', type: 'string', isOptional: true },
            { name: 'metTownId', type: 'string', isOptional: true },
            { name: 'fileUrlProgress', type: 'string', isOptional: true },
            { name: 'fileUrlProgressFinance', type: 'string', isOptional: true },
            { name: 'contractValue', type: 'string', isOptional: true },
            { name: 'ganttChartUrl', type: 'string', isOptional: true },
            { name: 'isRemindSiteDiary', type: 'boolean' },
            { name: 'isNotify2DUploaded', type: 'boolean' },
            { name: 'issuesAndProblem', type: 'string', isOptional: true },
            { name: 'solution', type: 'string', isOptional: true },
            { name: 'fileProgressKey', type: 'string', isOptional: true },
            { name: 'ganttChartKey', type: 'string', isOptional: true },
            { name: 'fileProgressFinanceKey', type: 'string', isOptional: true },
            { name: 'createdBy', type: 'number', isOptional: true },
            { name: 'remoteId', type: 'number', isOptional: true },
            { name: 'server_created_at', type: 'number', isOptional: true },
            { name: 'server_updated_at', type: 'number', isOptional: true },
            { name: 'drawing_sync', type: 'boolean', isOptional: true },
            { name: 'fileUrlProgressJson', type: 'string', isOptional: true },
            { name: 'fileUrlProgressFinanceJson', type: 'string', isOptional: true },
            { name: 'created_at', type: 'number' },
            { name: 'updated_at', type: 'number' },
            { name: 'deleted_at', type: 'number' },
            { name: 'retry_count', type: 'number' },
            { name: 'recordSource', type: 'string', isOptional: true }
          ]
        }),
        createTable({
          name: 'project_carousels',
          columns: [
            { name: 'projectId', type: 'number', isOptional: true, isIndexed: true },
            { name: 'name', type: 'string', isOptional: true },
            { name: 'fileUrl', type: 'string', isOptional: true },
            { name: 'type', type: 'string', isOptional: true },
            { name: 'fileKey', type: 'string', isOptional: true },
            { name: 'remoteId', type: 'number', isOptional: true },
            { name: 'localProjectId', type: 'string', isOptional: true },
            { name: 'retry_count', type: 'number' },
            { name: 'recordSource', type: 'string', isOptional: true },
            { name: 'isDownloaded', type: 'boolean', isOptional: true }
          ]
        }),
        createTable({
          name: 'request_for_signatures',
          columns: [
            { name: 'projectDocumentId', type: 'number', isIndexed: true },
            { name: 'ownerId', type: 'number', isIndexed: true },
            { name: 'signById', type: 'number', isIndexed: true },
            { name: 'status', type: 'string' },
            { name: 'assigneeNo', type: 'number', isOptional: true },
            { name: 'remoteId', type: 'number', isOptional: true, isIndexed: true },
            { name: 'created_at', type: 'number' },
            { name: 'updated_at', type: 'number' },
            { name: 'deleted_at', type: 'number' },
            { name: 'server_created_at', type: 'number', isOptional: true },
            { name: 'server_updated_at', type: 'number', isOptional: true },
            { name: 'updatedBy', type: 'number', isOptional: true },
            { name: 'localProjectDocumentId', type: 'string', isOptional: true },
            { name: 'retry_count', type: 'number' },
            { name: 'recordSource', type: 'string', isOptional: true }
          ]
        }),
        createTable({
          name: 'workspace_photos',
          columns: [
            { name: 'remoteId', type: 'number', isOptional: true, isIndexed: true },
            { name: 'projectDocumentId', type: 'number' },
            { name: 'userId', type: 'number' },
            { name: 'name', type: 'string' },
            { name: 'fileUrl', type: 'string', isOptional: true },
            { name: 'type', type: 'string', isOptional: true },
            { name: 'localProjectDocumentId', type: 'string', isOptional: true },
            { name: 'created_at', type: 'number' },
            { name: 'updated_at', type: 'number' },
            { name: 'deleted_at', type: 'number' },
            { name: 'updatedBy', type: 'number', isOptional: true },
            { name: 'server_created_at', type: 'number', isOptional: true },
            { name: 'server_updated_at', type: 'number', isOptional: true },
            { name: 'fileKey', type: 'string', isOptional: true },
            { name: 'retry_count', type: 'number' },
            { name: 'recordSource', type: 'string', isOptional: true }
          ]
        }),
        createTable({
          name: 'workspace_attachments',
          columns: [
            { name: 'remoteId', type: 'number', isOptional: true, isIndexed: true },
            { name: 'projectDocumentId', type: 'number' },
            { name: 'userId', type: 'number' },
            { name: 'name', type: 'string' },
            { name: 'fileUrl', type: 'string', isOptional: true },
            { name: 'type', type: 'string', isOptional: true },
            { name: 'localProjectDocumentId', type: 'string', isOptional: true },
            { name: 'created_at', type: 'number' },
            { name: 'updated_at', type: 'number' },
            { name: 'deleted_at', type: 'number' },
            { name: 'updatedBy', type: 'number', isOptional: true },
            { name: 'server_created_at', type: 'number', isOptional: true },
            { name: 'server_updated_at', type: 'number', isOptional: true },
            { name: 'fileKey', type: 'string', isOptional: true },
            { name: 'retry_count', type: 'number' },
            { name: 'recordSource', type: 'string', isOptional: true }
          ]
        }),
        createTable({
          name: 'workspace_document',
          columns: [
            { name: 'projectDocumentId', type: 'number', isOptional: true },
            { name: 'name', type: 'string', isOptional: true },
            { name: 'fileUrl', type: 'string', isOptional: true },
            { name: 'type', type: 'string', isOptional: true },
            { name: 'category', type: 'string', isOptional: true },
            { name: 'documentId', type: 'number', isOptional: true },
            { name: 'remoteId', type: 'number', isOptional: true },
            { name: 'localProjectDocumentId', type: 'string', isOptional: true },
            { name: 'created_at', type: 'number' },
            { name: 'updated_at', type: 'number' },
            { name: 'deleted_at', type: 'number' },
            { name: 'updatedBy', type: 'number', isOptional: true },
            { name: 'server_created_at', type: 'number', isOptional: true },
            { name: 'server_updated_at', type: 'number', isOptional: true },
            { name: 'retry_count', type: 'number' },
            { name: 'recordSource', type: 'string', isOptional: true }
          ]
        }),
        createTable({
          name: 'workspace_ccs',
          columns: [
            { name: 'projectDocumentId', type: 'string', isOptional: true },
            { name: 'name', type: 'string', isOptional: true },
            { name: 'file_url', type: 'string', isOptional: true },
            { name: 'type', type: 'string', isOptional: true },
            { name: 'category', type: 'string', isOptional: true },
            { name: 'document_id', type: 'string', isOptional: true },
            { name: 'remoteId', type: 'number', isOptional: true },
            { name: 'localProjectDocumentId', type: 'string', isOptional: true },
            { name: 'created_at', type: 'number' },
            { name: 'updated_at', type: 'number' },
            { name: 'deleted_at', type: 'number' },
            { name: 'updatedBy', type: 'number', isOptional: true },
            { name: 'server_created_at', type: 'number', isOptional: true },
            { name: 'server_updated_at', type: 'number', isOptional: true },
            { name: 'retry_count', type: 'number' },
            { name: 'recordSource', type: 'string', isOptional: true }
          ]
        }),
        createTable({
          name: 'offline_download',
          columns: [
            { name: 'created_at', type: 'number' },
            { name: 'updated_at', type: 'number' },
            { name: 'deleted_at', type: 'number' },
            { name: 'localId', type: 'string' },
            { name: 'fileUrl', type: 'string' },
            { name: 'isDownloaded', type: 'boolean' },
            { name: 'retry_count', type: 'number' },
            { name: 'recordSource', type: 'string', isOptional: true }
          ]
        }),
        createTable({
          name: 'users',
          columns: [
            { name: 'remoteId', type: 'number', isOptional: true, isIndexed: true },
            { name: 'updated_at', type: 'number' },
            { name: 'deleted_at', type: 'number' },
            { name: 'updatedBy', type: 'number', isOptional: true },
            { name: 'created_at', type: 'number' },
            { name: 'name', type: 'string', isOptional: false },
            { name: 'phoneNo', type: 'string', isOptional: true },
            { name: 'stampUrl', type: 'string', isOptional: true },
            { name: 'isReadChangeLogMobile', type: 'boolean', isOptional: true },
            { name: 'signUrl', type: 'string', isOptional: true },
            { name: 'fontSize', type: 'number', isOptional: true },
            { name: 'stampAndSignUrl', type: 'string', isOptional: true },
            { name: 'stampKey', type: 'string', isOptional: true },
            { name: 'signKey', type: 'string', isOptional: true },
            { name: 'stampAndSignKey', type: 'string', isOptional: true },
            { name: 'drawing_sync', type: 'boolean', isOptional: true },
            { name: 'recordSource', type: 'string', isOptional: true }
          ]
        }),
        createTable({
          name: 'events',
          columns: [
            { name: 'userId', type: 'number', isOptional: true },
            { name: 'title', type: 'string', isOptional: true },
            { name: 'startAt', type: 'number', isOptional: true },
            { name: 'endAt', type: 'number', isOptional: true },
            { name: 'startTime', type: 'string', isOptional: true },
            { name: 'endTime', type: 'string', isOptional: true },
            { name: 'isAllDay', type: 'boolean', isOptional: true },
            { name: 'projectId', type: 'number', isOptional: true },
            { name: 'scheduleForAll', type: 'boolean', isOptional: true },
            { name: 'remoteId', type: 'number', isOptional: true },
            { name: 'updated_at', type: 'number' },
            { name: 'deleted_at', type: 'number' },
            { name: 'created_at', type: 'number' },
            { name: 'retry_count', type: 'number' },
            { name: 'recordSource', type: 'string', isOptional: true }
          ]
        }),
        createTable({
          name: 'project_document_comments',
          columns: [
            { name: 'remoteId', type: 'number', isOptional: true, isIndexed: true },
            { name: 'projectDocumentId', type: 'number' },
            { name: 'userId', type: 'number' },
            { name: 'message', type: 'string', isOptional: true },
            { name: 'localProjectDocumentId', type: 'string', isOptional: true },
            { name: 'updated_at', type: 'number' },
            { name: 'deleted_at', type: 'number' },
            { name: 'created_at', type: 'number' },
            { name: 'server_created_at', type: 'number' },
            { name: 'server_updated_at', type: 'number' },
            { name: 'updatedBy', type: 'number', isOptional: true },
            { name: 'retry_count', type: 'number' },
            { name: 'recordSource', type: 'string', isOptional: true },
            { name: 'commentType', type: 'string', isOptional: true }
          ]
        }),
        createTable({
          name: 'workspace_group_users',
          columns: [
            { name: 'remoteId', type: 'number', isIndexed: true },
            { name: 'created_at', type: 'number' },
            { name: 'updated_at', type: 'number' },
            { name: 'deleted_at', type: 'number' },
            { name: 'updatedBy', type: 'number', isOptional: true },
            { name: 'userId', type: 'number', isOptional: true },
            { name: 'workspaceGroupId', type: 'number', isOptional: true },
            { name: 'server_created_at', type: 'number' },
            { name: 'server_updated_at', type: 'number' },
            { name: 'retry_count', type: 'number' },
            { name: 'recordSource', type: 'string', isOptional: true }
          ]
        }),
        createTable({
          name: 'subscription_packages',
          columns: [
            { name: 'remoteId', type: 'number', isOptional: true },
            { name: 'title', type: 'string' },
            { name: 'description', type: 'string' },
            { name: 'amount', type: 'number' },
            { name: 'availableDuration', type: 'number' },
            { name: 'totalProjects', type: 'number' },
            { name: 'totalUsers', type: 'number' },
            { name: 'allowTask', type: 'boolean' },
            { name: 'allowProjectDocument', type: 'boolean' },
            { name: 'allowWorkProgramme', type: 'boolean' },
            { name: 'allowCorrespondence', type: 'boolean' },
            { name: 'allowWorkspaceDocument', type: 'boolean' },
            { name: 'allowWorkspaceTemplate', type: 'boolean' },
            { name: 'allowDrawing', type: 'boolean' },
            { name: 'allowBimModel', type: 'boolean' },
            { name: 'allowPhoto', type: 'boolean' },
            { name: 'allowScheduleChart', type: 'boolean' },
            { name: 'allowScheduleActivity', type: 'boolean' },
            { name: 'allowDashboard', type: 'boolean' },
            { name: 'allowEmailCorrespondence', type: 'boolean' },
            { name: 'created_at', type: 'number' },
            { name: 'updated_at', type: 'number' },
            { name: 'deleted_at', type: 'number', isOptional: true },
            { name: 'updatedBy', type: 'number', isOptional: true },
            { name: 'recordSource', type: 'string', isOptional: true },
            { name: 'retry_count', type: 'number', isOptional: true },
            { name: 'download_retry', type: 'number', isOptional: true },
            { name: 'server_created_at', type: 'number', isOptional: true },
            { name: 'server_updated_at', type: 'number', isOptional: true }
          ]
        })
      ]
    }
  ]
});
