import { Database } from '@nozbe/watermelondb';
import SQLiteAdapter from '@nozbe/watermelondb/adapters/sqlite';

import schema from './schema';
import migrations from './migrations';
import ProjectGroupModel from './model/project-group.model';
import TaskModel from './model/task.model';
import ProjectUserModel from './model/project-user.model';
import TasksAttachmentModel from './model/task-attachment.model';
import TasksCommentComment from './model/task-comment.model';
import TasksMediaModel from './model/task-media.model';
import DrawingRevisionModel from './model/drawing-revision.model';
import ProjectModel from './model/project.model';
import ProjectCarouselModel from './model/project-carousel.model';
import WorkspaceGroupModel from './model/workspace-group.model';
import RequestForSignaturesModel from './model/request-for-signatures.model';
import WorkspacePhotoModel from './model/workspace-photos.model';
import WorkspaceAttachmentModel from './model/workspace-attachments.model';
import WorkspaceDocumentModel from './model/workspace-document.model';
import WorkspaceCommentModel from './model/workspace-comment.model';
import WorkspaceCcModel from './model/workspace_ccs.model';
import OfflineDownloadModel from './model/offline-download.model';
import UserModel from './model/user.model';
import EventModel from './model/event.model';
import WorkspaceGroupUserModel from './model/workspace-group-users.model';
import ProjectDocumentModel from './model/project-document';
import SubscriptionPackageModel from './model/subscription-package.model';

const adapter = new SQLiteAdapter({
  schema,
  migrations,
  jsi: true,
  onSetUpError: error => {},
  dbName: 'bina-db'
});

const database: Database = new Database({
  adapter,
  modelClasses: [
    ProjectGroupModel,
    TaskModel,
    ProjectUserModel,
    TasksAttachmentModel,
    TasksCommentComment,
    TasksMediaModel,
    ProjectDocumentModel,
    DrawingRevisionModel,
    ProjectModel,
    ProjectCarouselModel,
    WorkspaceGroupModel,
    RequestForSignaturesModel,
    WorkspacePhotoModel,
    WorkspaceAttachmentModel,
    WorkspaceDocumentModel,
    WorkspaceCommentModel,
    WorkspaceCcModel,
    OfflineDownloadModel,
    UserModel,
    EventModel,
    WorkspaceGroupUserModel,
    SubscriptionPackageModel
  ]
});

export default database;
