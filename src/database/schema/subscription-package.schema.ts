import { tableSchema } from '@nozbe/watermelondb/Schema';

const subscriptionPackageSchema = tableSchema({
  name: 'subscription_packages',
  columns: [
    { name: 'remoteId', type: 'number', isOptional: true },
    { name: 'title', type: 'string' },
    { name: 'description', type: 'string' },
    { name: 'amount', type: 'number' },
    { name: 'availableDuration', type: 'number' },
    { name: 'totalProjects', type: 'number' },
    { name: 'totalUsers', type: 'number' },
    { name: 'allowTask', type: 'boolean' },
    { name: 'allowProjectDocument', type: 'boolean' },
    { name: 'allowWorkProgramme', type: 'boolean' },
    { name: 'allowCorrespondence', type: 'boolean' },
    { name: 'allowWorkspaceDocument', type: 'boolean' },
    { name: 'allowWorkspaceTemplate', type: 'boolean' },
    { name: 'allowDrawing', type: 'boolean' },
    { name: 'allowBimModel', type: 'boolean' },
    { name: 'allowPhoto', type: 'boolean' },
    { name: 'allowScheduleChart', type: 'boolean' },
    { name: 'allowScheduleActivity', type: 'boolean' },
    { name: 'allowDashboard', type: 'boolean' },
    { name: 'allowEmailCorrespondence', type: 'boolean' },
    { name: 'created_at', type: 'number' },
    { name: 'updated_at', type: 'number' },
    { name: 'deleted_at', type: 'number', isOptional: true },
    { name: 'updatedBy', type: 'number', isOptional: true },
    { name: 'recordSource', type: 'string', isOptional: true },
    { name: 'retry_count', type: 'number', isOptional: true },
    { name: 'download_retry', type: 'number', isOptional: true },
    { name: 'server_created_at', type: 'number', isOptional: true },
    { name: 'server_updated_at', type: 'number', isOptional: true }
  ]
});

export default subscriptionPackageSchema;
