import { tableSchema } from '@nozbe/watermelondb';

const projectGroupSchema = tableSchema({
  name: 'project_groups',
  columns: [
    { name: 'title', type: 'string' },
    { name: 'projectId', type: 'number' },
    { name: 'projectGroupId', type: 'number', isOptional: true },
    { name: 'remoteId', type: 'number', isOptional: true, isIndexed: true },
    { name: 'created_at', type: 'number' },
    { name: 'updated_at', type: 'number' },
    { name: 'deleted_at', type: 'number' },
    { name: 'updatedBy', type: 'number', isOptional: true },
    { name: 'localGroupId', type: 'string', isOptional: true },
    { name: 'updatedBy', type: 'number', isOptional: true },
    { name: 'retry_count', type: 'number' },
    { name: 'recordSource', type: 'string', isOptional: true }
  ]
});

export default projectGroupSchema;
