import { tableSchema } from '@nozbe/watermelondb';

export const workspaceDocumentSchema = tableSchema({
  name: 'workspace_document',
  columns: [
    { name: 'projectDocumentId', type: 'number', isOptional: true },
    { name: 'name', type: 'string', isOptional: true },
    { name: 'fileUrl', type: 'string', isOptional: true },
    { name: 'type', type: 'string', isOptional: true },
    { name: 'category', type: 'string', isOptional: true },
    { name: 'documentId', type: 'number', isOptional: true },
    { name: 'remoteId', type: 'number', isOptional: true, isIndexed: true },
    { name: 'localProjectDocumentId', type: 'string', isOptional: true },
    { name: 'created_at', type: 'number' },
    { name: 'updated_at', type: 'number' },
    { name: 'deleted_at', type: 'number' },
    { name: 'updatedBy', type: 'number', isOptional: true },
    { name: 'server_created_at', type: 'number', isOptional: true },
    { name: 'server_updated_at', type: 'number', isOptional: true },
    { name: 'retry_count', type: 'number' },
    { name: 'recordSource', type: 'string', isOptional: true }
  ]
});
