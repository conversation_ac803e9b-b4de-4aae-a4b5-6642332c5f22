import { tableSchema } from '@nozbe/watermelondb';

const workspaceCommentSchema = tableSchema({
  name: 'project_document_comments',
  columns: [
    { name: 'remoteId', type: 'number', isOptional: true, isIndexed: true },
    { name: 'projectDocumentId', type: 'number' },
    { name: 'userId', type: 'number' },
    { name: 'message', type: 'string', isOptional: true },
    { name: 'localProjectDocumentId', type: 'string', isOptional: true },
    { name: 'updated_at', type: 'number' },
    { name: 'deleted_at', type: 'number' },
    { name: 'created_at', type: 'number' },
    { name: 'server_created_at', type: 'number' },
    { name: 'server_updated_at', type: 'number' },
    { name: 'updatedBy', type: 'number', isOptional: true },
    { name: 'retry_count', type: 'number' },
    { name: 'recordSource', type: 'string', isOptional: true },
    { name: 'commentType', type: 'string', isOptional: true }
  ]
});

export default workspaceCommentSchema;
