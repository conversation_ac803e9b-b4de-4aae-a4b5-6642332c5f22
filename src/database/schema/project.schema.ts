import { tableSchema } from '@nozbe/watermelondb/Schema';

const projectSchema = tableSchema({
  name: 'projects',
  columns: [
    { name: 'companyId', type: 'number' },
    { name: 'userId', type: 'number' },
    { name: 'refNo', type: 'string', isOptional: true },
    { name: 'title', type: 'string' },
    { name: 'description', type: 'string', isOptional: true },
    { name: 'status', type: 'string' },
    { name: 'managedBy', type: 'string', isOptional: true },
    { name: 'deputySuperintendent', type: 'string', isOptional: true },
    { name: 'client', type: 'string', isOptional: true },
    { name: 'contractor', type: 'string', isOptional: true },
    { name: 'startDate', type: 'number', isOptional: true },
    { name: 'completionDate', type: 'number', isOptional: true },
    { name: 'cnsConsultant', type: 'string', isOptional: true },
    { name: 'mneConsultant', type: 'string', isOptional: true },
    { name: 'qsConsultant', type: 'string', isOptional: true },
    { name: 'environmentConsultant', type: 'string', isOptional: true },
    { name: 'fileUrlCover', type: 'string', isOptional: true },
    { name: 'metTownId', type: 'string', isOptional: true },
    { name: 'fileUrlProgress', type: 'string', isOptional: true },
    { name: 'fileUrlProgressFinance', type: 'string', isOptional: true },
    { name: 'contractValue', type: 'string', isOptional: true },
    { name: 'ganttChartUrl', type: 'string', isOptional: true },
    { name: 'isRemindSiteDiary', type: 'boolean' },
    { name: 'isNotify2DUploaded', type: 'boolean' },
    { name: 'issuesAndProblem', type: 'string', isOptional: true },
    { name: 'solution', type: 'string', isOptional: true },
    { name: 'fileProgressKey', type: 'string', isOptional: true },
    { name: 'ganttChartKey', type: 'string', isOptional: true },
    { name: 'fileProgressFinanceKey', type: 'string', isOptional: true },
    { name: 'remoteId', type: 'number', isOptional: true },
    { name: 'created_at', type: 'number' },
    { name: 'updated_at', type: 'number' },
    { name: 'createdBy', type: 'number', isOptional: true },
    { name: 'updatedBy', type: 'number', isOptional: true },
    { name: 'server_updated_at', type: 'number', isOptional: true },
    { name: 'drawing_sync', type: 'boolean', isOptional: true },
    { name: 'fileUrlProgressJson', type: 'string', isOptional: true },
    { name: 'fileUrlProgressFinanceJson', type: 'string', isOptional: true },
    { name: 'subscriptionPackageId', type: 'number', isOptional: true },
    { name: 'retry_count', type: 'number' },
    { name: 'recordSource', type: 'string', isOptional: true }
  ]
});

export default projectSchema;
