import { tableSchema } from '@nozbe/watermelondb';

const workspaceGroupSchema = tableSchema({
  name: 'workspace_groups',
  columns: [
    { name: 'name', type: 'string' },
    { name: 'projectId', type: 'number' },
    { name: 'workspaceGroupId', type: 'number', isOptional: true },
    { name: 'remoteId', type: 'number', isOptional: true, isIndexed: true },
    { name: 'created_at', type: 'number' },
    { name: 'updated_at', type: 'number' },
    { name: 'deleted_at', type: 'number' },
    { name: 'updatedBy', type: 'number', isOptional: true },
    { name: 'localGroupId', type: 'string', isOptional: true },
    // { name: 'runningId', type: 'string', isOptional: true },
    { name: 'code', type: 'string', isOptional: true },
    { name: 'retry_count', type: 'number' },
    { name: 'recordSource', type: 'string', isOptional: true }
  ]
});

export default workspaceGroupSchema;
