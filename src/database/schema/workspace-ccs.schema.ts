import { tableSchema } from '@nozbe/watermelondb';

export const workspaceCcSchema = tableSchema({
  name: 'workspace_ccs',
  columns: [
    { name: 'projectDocumentId', type: 'number', isOptional: true },
    { name: 'ownerId', type: 'number', isOptional: true },
    { name: 'ccId', type: 'number', isOptional: true },
    { name: 'status', type: 'string', isOptional: true },
    { name: 'localProjectDocumentId', type: 'string', isOptional: true },
    { name: 'remoteId', type: 'number', isOptional: true },
    { name: 'created_at', type: 'number' },
    { name: 'updated_at', type: 'number' },
    { name: 'deleted_at', type: 'number' },
    { name: 'updatedBy', type: 'number', isOptional: true },
    { name: 'server_created_at', type: 'number', isOptional: true },
    { name: 'server_updated_at', type: 'number', isOptional: true },
    { name: 'retry_count', type: 'number' },
    { name: 'recordSource', type: 'string', isOptional: true }
  ]
});
