import { tableSchema } from '@nozbe/watermelondb';

const eventSchema = tableSchema({
  name: 'events',
  columns: [
    { name: 'userId', type: 'number', isOptional: true },
    { name: 'title', type: 'string', isOptional: true },
    { name: 'startAt', type: 'number', isOptional: true },
    { name: 'endAt', type: 'number', isOptional: true },
    { name: 'startTime', type: 'string', isOptional: true },
    { name: 'endTime', type: 'string', isOptional: true },
    { name: 'isAllDay', type: 'boolean', isOptional: true },
    { name: 'projectId', type: 'number', isOptional: true, isIndexed: true },
    { name: 'scheduleForAll', type: 'boolean', isOptional: true },
    { name: 'remoteId', type: 'number', isOptional: true },
    { name: 'recordSource', type: 'string', isOptional: true },
    { name: 'created_at', type: 'number' },
    { name: 'updated_at', type: 'number' },
    { name: 'retry_count', type: 'number' }
  ]
});

export default eventSchema;
