import { tableSchema } from '@nozbe/watermelondb/Schema';

const requestForSignaturesSchema = tableSchema({
  name: 'request_for_signatures',
  columns: [
    { name: 'projectDocumentId', type: 'number', isIndexed: true },
    { name: 'ownerId', type: 'number', isIndexed: true },
    { name: 'signById', type: 'number', isIndexed: true },
    { name: 'status', type: 'string', isOptional: true },
    { name: 'assigneeNo', type: 'number', isOptional: true },
    { name: 'remoteId', type: 'number', isOptional: true, isIndexed: true },
    { name: 'server_created_at', type: 'number', isOptional: true },
    { name: 'server_updated_at', type: 'number', isOptional: true },
    { name: 'updatedBy', type: 'number', isOptional: true },
    { name: 'created_at', type: 'number' },
    { name: 'updated_at', type: 'number' },
    { name: 'deleted_at', type: 'number', isOptional: true },
    { name: 'localProjectDocumentId', type: 'string', isOptional: true },
    { name: 'retry_count', type: 'number' },
    { name: 'recordSource', type: 'string', isOptional: true }
  ]
});

export default requestForSignaturesSchema;
