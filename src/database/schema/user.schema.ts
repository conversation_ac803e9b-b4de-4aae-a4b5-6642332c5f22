import { tableSchema } from '@nozbe/watermelondb/Schema';

const usersSchema = tableSchema({
  name: 'users',
  columns: [
    { name: 'remoteId', type: 'number', isOptional: true, isIndexed: true },
    { name: 'updated_at', type: 'number' },
    { name: 'deleted_at', type: 'number' },
    { name: 'updatedBy', type: 'number', isOptional: true },
    { name: 'created_at', type: 'number' },
    { name: 'name', type: 'string', isOptional: false },
    { name: 'phoneNo', type: 'string', isOptional: true },
    { name: 'stampUrl', type: 'string', isOptional: true },
    { name: 'isReadChangeLogMobile', type: 'boolean', isOptional: true },
    { name: 'signUrl', type: 'string', isOptional: true },
    { name: 'fontSize', type: 'number', isOptional: true },
    { name: 'stampAndSignUrl', type: 'string', isOptional: true },
    { name: 'stampKey', type: 'string', isOptional: true },
    { name: 'signKey', type: 'string', isOptional: true },
    { name: 'stampAndSignKey', type: 'string', isOptional: true },
    { name: 'drawing_sync', type: 'boolean', isOptional: true },
    { name: 'recordSource', type: 'string', isOptional: true }
  ]
});

export default usersSchema;
