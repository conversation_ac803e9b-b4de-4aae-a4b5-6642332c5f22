import { tableSchema } from '@nozbe/watermelondb';

const projectDocumentsSchema = tableSchema({
  name: 'project_documents',
  columns: [
    { name: 'remoteId', type: 'number', isIndexed: true },
    { name: 'localRemoteId', type: 'string', isOptional: true },
    { name: 'projectId', type: 'number' },
    { name: 'projectDocumentId', type: 'number', isOptional: true },
    { name: 'addedBy', type: 'number', isOptional: true },
    { name: 'formCategoryId', type: 'number', isOptional: true },
    { name: 'workspaceGroupId', type: 'number', isOptional: true },
    { name: 'name', type: 'string' },
    { name: 'description', type: 'string', isOptional: true },
    { name: 'fileSystemType', type: 'string', isOptional: true },
    { name: 'driveType', type: 'string' },
    { name: 'fileUrl', type: 'string', isOptional: true },
    { name: 'obsUrl', type: 'string', isOptional: true },
    { name: 'obsFileSize', type: 'number', isOptional: true },
    { name: 'obsFileType', type: 'string', isOptional: true },
    { name: 'type', type: 'string', isOptional: true },
    { name: 'fileSize', type: 'number', isOptional: true },
    { name: 'status', type: 'string', isOptional: true },
    { name: 'category', type: 'string', isOptional: true },
    { name: 'fileChannel', type: 'string' },
    { name: 'xfdf', type: 'string', isOptional: true },
    { name: 'notes', type: 'string', isOptional: true },
    { name: 'versionName', type: 'string', isOptional: true },
    { name: 'allFormCode', type: 'number', isOptional: true },
    { name: 'watermarkId', type: 'string', isOptional: true },
    { name: 'uploadLatitude', type: 'string', isOptional: true },
    { name: 'uploadLongitude', type: 'string', isOptional: true },
    { name: 'uploadAddress', type: 'string', isOptional: true },
    { name: 'videoThumbnail', type: 'string', isOptional: true },
    { name: 'isDocsStored', type: 'boolean' },
    { name: 'submittedAt', type: 'number', isOptional: true, isIndexed: true },
    { name: 'groupCode', type: 'string', isOptional: true },
    { name: 'currentUserId', type: 'number', isOptional: true },
    { name: 'workflow', type: 'string', isOptional: true },
    { name: 'updated_at', type: 'number' },
    { name: 'deleted_at', type: 'number' },
    { name: 'updatedBy', type: 'number', isOptional: true },
    { name: 'localFileUrl', type: 'string', isOptional: true },
    { name: 'fileKey', type: 'string' },
    { name: 'server_created_at', type: 'number', isOptional: true },
    { name: 'server_updated_at', type: 'number', isOptional: true },
    { name: 'created_at', type: 'number' },
    { name: 'localCurrentUserId', type: 'string', isOptional: true },
    { name: 'retry_count', type: 'number' },
    { name: 'isQueued', type: 'boolean', isOptional: true },
    { name: 'recordSource', type: 'string', isOptional: true },
    { name: 'download_retry', type: 'number' },
    { name: 'assigneeIds', type: 'string', isOptional: true },
    { name: 'autosavedAt', type: 'number', isOptional: true }
  ]
});

export default projectDocumentsSchema;
