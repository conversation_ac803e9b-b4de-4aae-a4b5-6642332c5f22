import { tableSchema } from '@nozbe/watermelondb';

const tasksMediasSchema = tableSchema({
  name: 'tasks_medias',
  columns: [
    { name: 'remoteId', type: 'number', isIndexed: true },
    { name: 'taskId', type: 'number', isIndexed: true },
    { name: 'userId', type: 'number', isIndexed: true },
    { name: 'name', type: 'string' },
    { name: 'fileUrl', type: 'string' },
    { name: 'type', type: 'string' },
    { name: 'updated_at', type: 'number' },
    { name: 'deleted_at', type: 'number' },
    { name: 'localTaskId', type: 'string', isOptional: true },
    { name: 'updatedBy', type: 'number', isOptional: true },
    { name: 'created_at', type: 'number' },
    { name: 'retry_count', type: 'number' },
    { name: 'recordSource', type: 'string', isOptional: true }
  ]
});

export default tasksMediasSchema;
