import { tableSchema } from '@nozbe/watermelondb';

const tasksCommentsSchema = tableSchema({
  name: 'task_comments',
  columns: [
    { name: 'remoteId', type: 'number', isIndexed: true },
    { name: 'taskId', type: 'number', isIndexed: true },
    { name: 'userId', type: 'number', isIndexed: true },
    { name: 'message', type: 'string' },
    { name: 'updated_at', type: 'number' },
    { name: 'deleted_at', type: 'number' },
    { name: 'created_at', type: 'number' },
    { name: 'server_created_at', type: 'string', isOptional: true },
    { name: 'server_updated_at', type: 'string', isOptional: true },
    { name: 'localTaskId', type: 'string', isOptional: true },
    { name: 'updatedBy', type: 'number', isOptional: true },
    { name: 'retry_count', type: 'number' },
    { name: 'recordSource', type: 'string', isOptional: true }
  ]
});

export default tasksCommentsSchema;
