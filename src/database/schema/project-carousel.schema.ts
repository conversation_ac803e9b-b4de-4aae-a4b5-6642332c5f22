import { tableSchema } from '@nozbe/watermelondb';

const projectCarouselSchema = tableSchema({
  name: 'project_carousels',
  columns: [
    { name: 'projectId', type: 'number', isOptional: true, isIndexed: true },
    { name: 'name', type: 'string', isOptional: true },
    { name: 'fileUrl', type: 'string', isOptional: true },
    { name: 'type', type: 'string', isOptional: true },
    { name: 'fileKey', type: 'string', isOptional: true },
    { name: 'updatedBy', type: 'number', isOptional: true },
    { name: 'remoteId', type: 'number', isOptional: true },
    { name: 'localProjectId', type: 'string', isOptional: true },
    { name: 'recordSource', type: 'string', isOptional: true },
    { name: 'created_at', type: 'number' },
    { name: 'updated_at', type: 'number' },
    { name: 'retry_count', type: 'number' },
    { name: 'isDownloaded', type: 'boolean', isOptional: true }
  ]
});

export { projectCarouselSchema };
