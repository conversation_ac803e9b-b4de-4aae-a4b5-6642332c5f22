import { tableSchema } from '@nozbe/watermelondb/Schema';

export const OfflineDownloadSchema = tableSchema({
  name: 'offline_download',
  columns: [
    { name: 'created_at', type: 'number' },
    { name: 'updated_at', type: 'number' },
    { name: 'deleted_at', type: 'number' },
    { name: 'localId', type: 'string' },
    { name: 'fileUrl', type: 'string' },
    { name: 'isDownloaded', type: 'boolean' },
    { name: 'retry_count', type: 'number' },
    { name: 'recordSource', type: 'string', isOptional: true }
  ]
});
