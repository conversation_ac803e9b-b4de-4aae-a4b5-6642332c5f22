import { tableSchema } from '@nozbe/watermelondb/Schema';

export const DrawingRevisionSchema = tableSchema({
  name: 'drawing_revisions',
  columns: [
    { name: 'fileName', type: 'string', isOptional: true },
    { name: 'fileUrl', type: 'string' },
    { name: 'localFileUrl', type: 'string', isOptional: true },
    { name: 'fileKey', type: 'string' },
    { name: 'projectDocumentId', type: 'number', isOptional: true },
    { name: 'version', type: 'number', isOptional: true },
    { name: 'versionName', type: 'string', isOptional: true },
    { name: 'notes', type: 'string', isOptional: true },
    { name: 'xfdf', type: 'string', isOptional: true },
    { name: 'status', type: 'string', isOptional: true },
    { name: 'category', type: 'string', isOptional: true },
    { name: 'updated_at', type: 'number' },
    { name: 'deleted_at', type: 'number' },
    { name: 'updatedBy', type: 'number', isOptional: true },
    { name: 'remoteId', type: 'number', isOptional: true, isIndexed: true },
    { name: 'localProjectDocumentId', type: 'string', isOptional: true },
    { name: 'created_at', type: 'number' },
    { name: 'retry_count', type: 'number' },
    { name: 'recordSource', type: 'string', isOptional: true }
  ]
});
