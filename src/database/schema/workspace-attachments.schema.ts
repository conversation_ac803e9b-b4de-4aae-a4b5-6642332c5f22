import { tableSchema } from '@nozbe/watermelondb';

export const workspaceAttachmentSchema = tableSchema({
  name: 'workspace_attachments',
  columns: [
    { name: 'remoteId', type: 'number', isOptional: true, isIndexed: true },
    { name: 'projectDocumentId', type: 'number' },
    { name: 'userId', type: 'number' },
    { name: 'name', type: 'string' },
    { name: 'fileUrl', type: 'string', isOptional: true },
    { name: 'type', type: 'string', isOptional: true },
    { name: 'localProjectDocumentId', type: 'string', isOptional: true },
    { name: 'created_at', type: 'number' },
    { name: 'updated_at', type: 'number' },
    { name: 'deleted_at', type: 'number' },
    { name: 'updatedBy', type: 'number', isOptional: true },
    { name: 'server_created_at', type: 'number', isOptional: true },
    { name: 'server_updated_at', type: 'number', isOptional: true },
    { name: 'fileKey', type: 'string', isOptional: true },
    { name: 'retry_count', type: 'number' },
    { name: 'recordSource', type: 'string', isOptional: true }
  ]
});
