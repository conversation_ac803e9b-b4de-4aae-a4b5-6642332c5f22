import { tableSchema } from '@nozbe/watermelondb';

const taskSchema = tableSchema({
  name: 'tasks',
  columns: [
    { name: 'remoteId', type: 'number', isOptional: true, isIndexed: true },
    { name: 'projectId', type: 'number' },
    { name: 'ownerId', type: 'number', isIndexed: true },
    { name: 'groupId', type: 'number', isIndexed: true, isOptional: true },
    { name: 'taskCode', type: 'number', isIndexed: true },
    { name: 'title', type: 'string' },
    { name: 'annotId', type: 'string', isOptional: true },
    { name: 'spriteId', type: 'string', isOptional: true },
    { name: 'elementId', type: 'string', isOptional: true },
    { name: 'pos3D', type: 'string', isOptional: true },
    { name: 'bimId', type: 'string', isOptional: true },
    { name: 'imageURL', type: 'string', isOptional: true },
    { name: 'description', type: 'string', isOptional: true },
    { name: 'dueDate', type: 'string', isOptional: true },
    { name: 'status', type: 'string' },
    { name: 'isSentBeforeOneDay', type: 'boolean', isIndexed: true },
    { name: 'isSentBeforeOneHour', type: 'boolean', isIndexed: true },
    { name: 'permanentlyDeleted', type: 'boolean', isIndexed: true },
    { name: 'proposedStatus', type: 'string', isOptional: true },
    { name: 'isUrgent', type: 'boolean', isOptional: true },
    { name: 'memoUrl', type: 'string', isOptional: true },
    { name: 'previewMemoUrl', type: 'string', isOptional: true },
    { name: 'isMemoReceive', type: 'boolean', isOptional: true },
    { name: 'issuedById', type: 'number', isIndexed: true },
    { name: 'createdAt', type: 'number' },
    { name: 'updatedAt', type: 'number' },
    { name: 'deletedAt', type: 'number', isOptional: true },
    { name: 'serverCreatedAt', type: 'number', isOptional: true },
    { name: 'serverUpdatedAt', type: 'number', isOptional: true },
    { name: 'assignees', type: 'string', isOptional: true },
    { name: 'copies', type: 'string', isOptional: true },
    { name: 'documents', type: 'string', isOptional: true },
    { name: 'created_at', type: 'number' },
    { name: 'updated_at', type: 'number' },
    { name: 'deleted_at', type: 'number' },
    { name: 'updatedBy', type: 'number', isOptional: true },
    { name: 'localMemoUrl', type: 'string', isOptional: true },
    { name: 'retry_count', type: 'number' },
    { name: 'recordSource', type: 'string', isOptional: true }
  ]
});

export default taskSchema;
