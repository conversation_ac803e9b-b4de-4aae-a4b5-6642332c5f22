import { tableSchema } from '@nozbe/watermelondb';

const workspaceGroupUserSchema = tableSchema({
  name: 'workspace_group_users',
  columns: [
    { name: 'created_at', type: 'number' },
    { name: 'updated_at', type: 'number' },
    { name: 'deleted_at', type: 'number' },
    { name: 'updatedBy', type: 'number', isOptional: true },
    { name: 'userId', type: 'number', isOptional: true },
    { name: 'workspaceGroupId', type: 'number', isOptional: true },
    { name: 'remoteId', type: 'number', isIndexed: true },
    { name: 'server_created_at', type: 'number', isOptional: true },
    { name: 'server_updated_at', type: 'number', isOptional: true },
    { name: 'recordSource', type: 'string', isOptional: true }
  ]
});

export default workspaceGroupUserSchema;
