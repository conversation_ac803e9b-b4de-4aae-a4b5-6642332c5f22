import { tableSchema } from '@nozbe/watermelondb/Schema';

const projectUsersSchema = tableSchema({
  name: 'project_users',
  columns: [
    { name: 'remoteId', type: 'number', isOptional: true, isIndexed: true },
    { name: 'userId', type: 'number', isOptional: false, isIndexed: true },
    { name: 'projectId', type: 'number', isOptional: false, isIndexed: true },
    { name: 'role', type: 'string', isOptional: false },
    { name: 'name', type: 'string', isOptional: false },
    { name: 'email', type: 'string', isOptional: false },
    { name: 'avatarUrl', type: 'string', isOptional: true },
    { name: 'updated_at', type: 'number' },
    { name: 'deleted_at', type: 'number' },
    { name: 'updatedBy', type: 'number', isOptional: true },
    { name: 'created_at', type: 'number' },
    { name: 'retry_count', type: 'number' },
    { name: 'recordSource', type: 'string', isOptional: true }
  ]
});

export default projectUsersSchema;
