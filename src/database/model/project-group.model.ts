import { field, text, writer } from '@nozbe/watermelondb/decorators';
import BaseModel from './base-model';
import TaskModel from './task.model';
import { Q } from '@nozbe/watermelondb';
import database from '../index.native';

class ProjectGroupModel extends BaseModel {
  static table = 'project_groups';

  @text('title') title!: string;
  @field('projectId') projectId!: number;
  @field('projectGroupId') projectGroupId?: number | null;
  @field('localGroupId') localGroupId?: string | null;

  @field('remoteId') remoteId?: number | null;

  static associations: any = {
    tasks: { type: 'has_many', foreignKey: 'groupId' }
  };
  name: any;

  static async getProjectGroups(
    page: number = 1,
    limit: number = 10,
    projectId: string,
    filteredValue: string
  ): Promise<{
    items: ProjectGroup[];
    nextPage?: number;
  }> {
    const offset = (page - 1) * limit;

    let ungroupedTasksGroup: any = [];

    if (page === 1) {
      ungroupedTasksGroup = await database.collections
        .get<ProjectGroupModel>('project_groups')
        .query(
          Q.where('projectId', Q.eq(parseInt(projectId, 10))),
          Q.where('projectGroupId', Q.eq(null)),
          Q.where('title', Q.eq('Ungroup Tasks'))
        )
        .fetch();
    }

    const queryConditions = [
      Q.where('projectId', Q.eq(parseInt(projectId, 10))),
      Q.and(Q.where('projectGroupId', Q.eq(null)), Q.where('localGroupId', Q.eq(null))),
      Q.sortBy('title', Q.asc),
      Q.where('title', Q.like(`%${Q.sanitizeLikeString(filteredValue ?? '')}%`)),
      Q.where('title', Q.notEq('Ungroup Tasks'))
    ];

    const otherGroups = await database.collections
      .get<ProjectGroupModel>('project_groups')
      .query(...queryConditions, Q.skip(offset), Q.take(limit))
      .fetch();

    const projectGroups = [...ungroupedTasksGroup, ...otherGroups];

    const projectGroupsWithChildrenCount = await Promise.all(
      projectGroups.map(async group => {
        const taskIdValue = group.remoteId ? group.remoteId : group.id;
        const taskJoinField = group.remoteId ? 'projectGroupId' : 'localGroupId';

        const children = await database.collections
          .get<ProjectGroupModel>('project_groups')
          .query(Q.where(taskJoinField, Q.eq(taskIdValue)))
          .fetch();

        const childrenWithTasks = await Promise.all(
          children.map(async child => {
            if (!child.remoteId) return child;

            const tasks = await database.collections
              .get<TaskModel>('tasks')
              .query(Q.where('groupId', Q.eq(child.remoteId)))
              .fetch();

            const tasksByStatus = tasks.reduce(
              (acc, task) => {
                if (!acc[task.status]) acc[task.status] = [];
                acc[task.status].push(task);
                return acc;
              },
              {} as Record<string, TaskModel[]>
            );

            return {
              ...child._raw,
              tasksByStatus,
              totalCount: tasks.length,
              openCount: tasksByStatus['Open']?.length ?? 0,
              inProgressCount: tasksByStatus['InProgress']?.length ?? 0,
              holdCount: tasksByStatus['Hold']?.length ?? 0,
              closedCount: tasksByStatus['Completed']?.length ?? 0
            } as unknown as ChildGroup;
          })
        );

        //@ts-ignore
        const totalTaskCount = childrenWithTasks.reduce((acc, child) => acc + (child.totalCount || 0), 0);

        const tasks = await database.collections
          .get<TaskModel>('tasks')
          .query(Q.where('groupId', Q.eq(group.remoteId)))
          .fetch();

        const tasksByStatus = tasks.reduce(
          (acc, task) => {
            if (!acc[task.status]) acc[task.status] = [];
            acc[task.status].push(task);
            return acc;
          },
          {} as Record<string, TaskModel[]>
        );

        return {
          ...group._raw,
          children: childrenWithTasks,
          totalCount: group.title === 'Ungroup Tasks' ? tasks.length : totalTaskCount,
          openCount: tasksByStatus['Open']?.length ?? 0,
          inProgressCount: tasksByStatus['InProgress']?.length ?? 0,
          holdCount: tasksByStatus['Hold']?.length ?? 0,
          closedCount: tasksByStatus['Completed']?.length ?? 0
        } as unknown as ProjectGroup;
      })
    );

    const nextPage = otherGroups.length === limit ? page + 1 : undefined;

    return {
      //@ts-ignore
      items: projectGroupsWithChildrenCount,
      nextPage
    };
  }

  static async createTaskGroup(
    title: string,
    projectId: number,
    projectGroupId?: number | null,
    localGroupId?: string | null
  ) {
    return database.write(async () => {
      return database.collections.get<ProjectGroupModel>('project_groups').create(projectGroup => {
        projectGroup.title = title;
        projectGroup.projectId = projectId;
        projectGroup.projectGroupId = projectGroupId || null;
        projectGroup.localGroupId = localGroupId || null;
        projectGroup.recordSource = 'OfflineApp';
      });
    });
  }

  static async updateProjectGroup(id: string, title: string) {
    return database.write(async () => {
      const projectGroup = await database.collections.get<ProjectGroupModel>('project_groups').find(id);
      return projectGroup.update(p => {
        p.title = title;
      });
    });
  }

  static async deleteProjectGroup(id: string) {
    return database.write(async () => {
      const collection = database.collections.get<ProjectGroupModel>('project_groups');
      const record = await collection.find(id);

      return await record.markAsDeleted();
    });
  }
}

export default ProjectGroupModel;
