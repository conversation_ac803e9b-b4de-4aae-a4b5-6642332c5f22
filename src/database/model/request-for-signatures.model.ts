import { field, writer } from '@nozbe/watermelondb/decorators';
import BaseModel from './base-model';
import ProjectUserModel from './project-user.model';
import database from '../index.native';
import { Q } from '@nozbe/watermelondb';
import { Gql } from '@src/api';
import { getForeignKey } from '../utils/numeric';
import ProjectDocumentModel from '@src/database/model/project-document';

class RequestForSignaturesModel extends BaseModel {
  static table = 'request_for_signatures';

  @field('projectDocumentId') projectDocumentId?: number | null;
  @field('ownerId') ownerId!: number;
  @field('signById') signById!: number;
  @field('status') status!: string;
  @field('assigneeNo') assigneeNo?: number | null;
  @field('remoteId') remoteId?: number | null;
  @field('localProjectDocumentId') localProjectDocumentId?: string | null;

  static async getRequestForSignatures(remoteId: number | string): Promise<any[]> {
    try {
      // Determine the correct key and field name to use for the query
      const foreignKeyInfo = getForeignKey(remoteId, 'localProjectDocumentId', 'projectDocumentId');

      // Perform the query using the determined field and key
      const requests = await database.collections
        .get('request_for_signatures')
        .query(
          Q.where(foreignKeyInfo.fieldName, Q.eq(foreignKeyInfo.keyValue[foreignKeyInfo.fieldName])),
          Q.sortBy('assigneeNo', Q.asc)
        )
        .fetch();
      if (requests.length === 0) {
        return [];
      }
      const userIds = requests.map((request: { signById: any }) => request.signById);
      const userDetails = await ProjectUserModel.getUsers(userIds);

      const userMap = new Map<number, any>(userDetails?.map((user: any) => [user.userId, user._raw]) ?? []);

      const result = requests.map(request => ({
        ...request._raw,
        user: userMap.get(request.signById) || null
      }));
      return result;
    } catch (error) {
      return [];
    }
  }

  static async isRequestForSignatureUnsynced(localProjectDocumentId: string): Promise<boolean> {
    try {
      const assingeeCollection = database.collections.get('request_for_signatures');
      const unsyncedQuery = assingeeCollection.query(
        Q.where('localProjectDocumentId', Q.eq(localProjectDocumentId)),
        Q.where('_status', Q.notEq('synced'))
      );
      const unsyncedAssignee = await unsyncedQuery.fetch();

      return unsyncedAssignee.length > 0;
    } catch (error) {
      return true; // Assume unsynced on error
    }
  }

  @writer
  async createRequestForSignature(updatedRequest: RequestForSignaturesModel[]): Promise<void> {
    try {
      const newRequestedSignatures = updatedRequest.map(request => {
        return this.collections.get('request_for_signatures').prepareCreate((req: any) => {
          req.projectDocumentId = request.projectDocumentId;
          req.ownerId = request.ownerId;
          req.signById = request.signById;
          req.status = request.status;
          req.assigneeNo = request.assigneeNo;
          req.remoteId = request.remoteId;
          req.localProjectDocumentId = request.localProjectDocumentId;
          req.recordSource = 'OfflineApp';
        });
      });

      const newRequestedUser = await this.database.batch(...newRequestedSignatures);

      return newRequestedUser;
    } catch (error) {}
  }

  static async dynamicWorkspaceValidation(
    documentId: string,
    requestForSignatureId: string,
    type: 'Approve Close' | 'Approve Proceed' | 'Amend' | 'Reject' | 'Work In Progress' | 'In Review' | 'Resubmit',
    newRequestedSignatureId?: ProjectUserModel
  ) {
    try {
      const requestForSignature = await database.collections.get('request_for_signatures').find(requestForSignatureId);
      const sanitizedRequestForSignature: any = requestForSignature?._raw;
      const projectDocument = await database.collections.get('project_documents').find(documentId);
      const sanitizedProjectDocument: any = projectDocument?._raw;

      switch (type) {
        case 'Approve Close': {
          await this.updateRequestForSignature({
            ...sanitizedRequestForSignature,
            status: Gql.RequestForSignatureStatus.Approved
          } as any);

          await ProjectDocumentModel.updateProjectDocs({
            id: documentId,
            status: Gql.ProjectDocumentStatus.Approved
          });

          break;
        }

        case 'Approve Proceed': {
          if (sanitizedRequestForSignature?.status === 'Approved') {
            return false;
          }

          const result = await database.write(async () => {
            // Check if the status is "Amend"
            if (sanitizedRequestForSignature?.status === 'Amend') {
              // Fetch the next assignee
              const nextAssignee: any = await database.collections
                .get('request_for_signatures')
                .query(
                  Q.where('projectDocumentId', Q.eq(sanitizedProjectDocument?.remoteId)),
                  Q.where('assigneeNo', Q.eq(sanitizedRequestForSignature?.assigneeNo + 1))
                )
                .fetch();

              if (nextAssignee.length > 0 && nextAssignee[0]?._raw) {
                // Update next assignee's status to "Proceed"
                await nextAssignee[0].update((assignee: any) => {
                  assignee.status = Gql.RequestForSignatureStatus.Proceed;
                });

                // Update the project document
                const projectDocument = await database.get('project_documents').find(documentId);

                await projectDocument.update((doc: any) => {
                  doc.currentUserId = nextAssignee[0]._raw.remoteId;
                });

                return nextAssignee[0];
              } else {
                throw new Error('Next assignee is not valid.');
              }
            } else {
              // Processing a new request
              if (!newRequestedSignatureId?.userId) {
                throw new Error('Invalid newRequestedSignatureId. UserId is required.');
              }

              const newRequestedSignatureParams = {
                projectDocumentId: sanitizedProjectDocument?.remoteId,
                signById: newRequestedSignatureId?.userId,
                status: Gql.RequestForSignatureStatus.Sent,
                assigneeNo: sanitizedRequestForSignature?.assigneeNo + 1,
                localProjectDocumentId: sanitizedProjectDocument?.id
              };

              // Step 1: Create the new request for signature
              const createdRequestForSignature = await database
                .get('request_for_signatures')
                .create((requestForSignature: any) => {
                  requestForSignature.projectDocumentId = sanitizedRequestForSignature?.projectDocumentId;
                  requestForSignature.ownerId = sanitizedProjectDocument.addedBy;
                  requestForSignature.signById = newRequestedSignatureParams.signById;
                  requestForSignature.status = newRequestedSignatureParams.status;
                  requestForSignature.assigneeNo = newRequestedSignatureParams.assigneeNo;
                  requestForSignature.recordSource = 'OfflineApp';
                  requestForSignature.localProjectDocumentId = newRequestedSignatureParams.localProjectDocumentId;
                });

              if (!createdRequestForSignature?.id) {
                throw new Error('Failed to create new request for signature.');
              }

              // Step 2: Update the project document
              const projectDocument = await database.get('project_documents').find(documentId);

              await projectDocument.update((doc: any) => {
                doc.status = Gql.ProjectDocumentStatus.Submitted;
                doc.localCurrentUserId = createdRequestForSignature.id;
                doc.currentUserId = null;
              });

              // Step 3: Update the current request for signature
              const currentRequestForSignature = await database
                .get('request_for_signatures')
                .find(sanitizedRequestForSignature.id);

              await currentRequestForSignature.update((request: any) => {
                request.status = Gql.RequestForSignatureStatus.Proceed;
              });

              return createdRequestForSignature;
            }
          });

          return result;

          break;
        }
        case 'Amend': {
          // Check if the request is already approved
          if (sanitizedRequestForSignature?.status === 'Approved') {
            return false;
          }

          try {
            // Step 1: Update the current request for signature to "Amend"
            await this.updateRequestForSignature({
              ...sanitizedRequestForSignature,
              status: Gql.RequestForSignatureStatus.Amend
            } as RequestForSignaturesModel);
            // Step 2: Fetch the previous assignee
            const previousAssignee: any = await database.collections
              .get('request_for_signatures')
              .query(
                Q.where('projectDocumentId', Q.eq(sanitizedProjectDocument?.remoteId)),
                Q.where('assigneeNo', Q.eq(sanitizedRequestForSignature?.assigneeNo - 1))
              )
              .fetch();

            if (previousAssignee.length === 0) {
              console.warn('No previous assignee found. Skipping assignee update.');
              break;
            }

            // Step 3: Update the previous assignee's status to "Amend"
            const updatedPreviousAssginee = await this.updateRequestForSignature({
              id: previousAssignee[0]._raw.id,
              status: Gql.RequestForSignatureStatus.Amend
            } as RequestForSignaturesModel);
            // Step 4: Update the project document directly using `database.get`
            const projectDocument = await database.collections.get('project_documents').find(documentId);

            await database.write(async () => {
              await projectDocument.update((doc: any) => {
                doc.status = Gql.ProjectDocumentStatus.Amend;
                if (previousAssignee[0]._raw?.remoteId) {
                  doc.currentUserId = previousAssignee[0]._raw.remoteId;
                } else {
                  doc.currentUserId = null;
                  doc.localCurrentUserId = previousAssignee[0]._raw.id;
                }
              });
            });
          } catch (error) {
            throw error; // Re-throw for upper-level error handling
          }

          break;
        }

        case 'Reject': {
          if (sanitizedRequestForSignature?.status === 'Approved') {
            return false;
          }

          // rules for rejecting a document
          // 1. update the current request for signature status to rejected
          // 2. update the project document's status to rejected

          await this.updateRequestForSignature({
            ...sanitizedRequestForSignature,
            status: Gql.RequestForSignatureStatus.Rejected
          } as RequestForSignaturesModel);

          await ProjectDocumentModel.updateProjectDocs({
            id: documentId,
            status: Gql.ProjectDocumentStatus.Rejected
          });

          break;
        }
        case 'Work In Progress': {
          if (sanitizedRequestForSignature?.status === 'Approved') {
            return false;
          }

          // rules for work in progress
          // 1. update the current request for signature status to work in progress
          // 2. update the project document's status to work in progress
          // 3. get the first assignee number and update the project document's current user assignee to the first assignee

          const updatedSignature = await this.updateRequestForSignature({
            ...sanitizedRequestForSignature,
            status: Gql.RequestForSignatureStatus.InProgress
          } as RequestForSignaturesModel);

          const firstAssignee: any = await database.collections
            .get('request_for_signatures')
            .query(
              Q.where('projectDocumentId', Q.eq(sanitizedProjectDocument?.remoteId)),
              Q.where('assigneeNo', Q.eq(1))
            )
            .fetch();

          const newRequestedSignature = await database.write(async () => {
            const requestForSignature = await database
              .get('request_for_signatures')
              .create((requestForSignature: any) => {
                requestForSignature.projectDocumentId = sanitizedRequestForSignature?.projectDocumentId;
                requestForSignature.ownerId = sanitizedProjectDocument.addedBy;
                requestForSignature.signById = firstAssignee[0]?._raw.signById;
                requestForSignature.status = Gql.RequestForSignatureStatus.Sent;
                requestForSignature.assigneeNo = sanitizedRequestForSignature?.assigneeNo + 1;
              });
            return requestForSignature;
          });

          await ProjectDocumentModel.updateProjectDocs({
            id: documentId,
            currentUserId: null,
            localCurrentUserId: newRequestedSignature?.id,
            status: Gql.ProjectDocumentStatus.InProgress
          });

          break;
        }

        case 'In Review': {
          if (sanitizedProjectDocument?.status === 'Approved') {
            return false;
          }

          await ProjectDocumentModel.updateProjectDocs({
            id: documentId,
            status: Gql.ProjectDocumentStatus.InReview
          });

          break;
        }

        case 'Resubmit': {
          if (sanitizedRequestForSignature?.status === 'Approved') {
            return false;
          }

          // Step 1: Update the current request for signature to "Proceed"
          await this.updateRequestForSignature({
            ...sanitizedRequestForSignature,
            status: Gql.RequestForSignatureStatus.Proceed
          } as RequestForSignaturesModel);

          // Step 2: Fetch the next request for signature
          const nextRequestForSignature = await database.collections
            .get('request_for_signatures')
            .query(
              Q.where('projectDocumentId', Q.eq(sanitizedProjectDocument?.remoteId)),
              Q.where('assigneeNo', Q.eq(sanitizedRequestForSignature?.assigneeNo + 1))
            )
            .fetch();

          if (!nextRequestForSignature.length) {
            throw new Error('Next request for signature not found.');
          }

          const sanitizedNextRequestForSignature: any = nextRequestForSignature[0]?._raw;

          // Step 3: Fetch the latest assignee number
          const latestAssignee = await database.collections
            .get('request_for_signatures')
            .query(Q.where('projectDocumentId', Q.eq(sanitizedProjectDocument?.remoteId)))
            .fetch();

          const maxAssigneeNo = Math.max(...latestAssignee.map((r: any) => r._raw.assigneeNo));

          // Step 4: Determine the status of the next request for signature
          const nextStatus =
            maxAssigneeNo - 1 === sanitizedRequestForSignature.assigneeNo
              ? Gql.RequestForSignatureStatus.Sent
              : Gql.RequestForSignatureStatus.Amend;

          await this.updateRequestForSignature({
            // ...sanitizedNextRequestForSignature,
            id: sanitizedNextRequestForSignature.id,
            status: nextStatus
          } as RequestForSignaturesModel);

          // Step 5: Update the project document's current user and status
          const projectDocument = await database.collections.get('project_documents').find(documentId);

          await database.write(async () => {
            await projectDocument.update((doc: any) => {
              doc.currentUserId = null;
              doc.localCurrentUserId = sanitizedNextRequestForSignature.id;
              doc.status = Gql.ProjectDocumentStatus.Pending;
            });
          });

          break;
        }

        default: {
          return true;
        }
      }
    } catch (error) {
      // Error handling
    }
  }

  static async LinearWorkspaceValidation(
    documentId: string,
    requestForSignatureId: string,
    type: 'In Review' | 'Approve' | 'Reject'
  ) {
    const requestForSignature = await database.collections.get('request_for_signatures').find(requestForSignatureId);
    const sanitizedRequestForSignature: any = requestForSignature?._raw;
    const projectDocument = await database.collections.get('project_documents').find(documentId);
    const sanitizedProjectDocument: any = projectDocument?._raw;

    switch (type) {
      case 'Approve': {
        await this.updateRequestForSignature({
          ...sanitizedRequestForSignature,
          status: Gql.RequestForSignatureStatus.Approved
        } as any);

        //get all requestForSignature for the document
        const allRequestForSignature = await database.collections
          .get('request_for_signatures')
          .query(Q.where('projectDocumentId', Q.eq(sanitizedProjectDocument?.remoteId)))
          .fetch();

        //Check if all requestForSignature are approved
        const isAllApproved = allRequestForSignature.every((req: any) => {
          return req._raw.status === 'Approved';
        });

        //if all requestForSignature are approved, update the project document status to approved
        if (isAllApproved) {
          await ProjectDocumentModel.updateProjectDocs({
            id: documentId,
            status: Gql.ProjectDocumentStatus.Approved
          });
        }

        break;
      }
      case 'In Review': {
        if (sanitizedProjectDocument?.status === 'Approved') {
          return false;
        }

        await ProjectDocumentModel.updateProjectDocs({
          id: documentId,
          status: Gql.ProjectDocumentStatus.InReview
        });

        break;
      }

      case 'Reject': {
        if (sanitizedRequestForSignature?.status === 'Approved') {
          return false;
        }

        // rules for rejecting a document
        // 1. update the current request for signature status to rejected
        // 2. update the project document's status to rejected

        await this.updateRequestForSignature({
          ...sanitizedRequestForSignature,
          status: Gql.RequestForSignatureStatus.Rejected
        } as RequestForSignaturesModel);

        await ProjectDocumentModel.updateProjectDocs({
          id: documentId,
          status: Gql.ProjectDocumentStatus.Rejected
        });

        break;
      }
    }
  }

  static async updateRequestForSignature(updatedRequest: RequestForSignaturesModel): Promise<void> {
    try {
      await database.write(async () => {
        const requestForSignature = await database.collections.get('request_for_signatures').find(updatedRequest.id);

        if (!requestForSignature) {
          throw new Error('Request for signature not found.');
        }

        return await requestForSignature.update((req: any) => {
          Object.entries(updatedRequest).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
              (req as any)[key] = value;
            }
          });
        });
      });
    } catch (error) {
      // Error handling
    }
  }

  static async deleteRequestForSignature(ids: string[]) {
    try {
      return await database.write(async () => {
        const requestForSignature = await database.collections
          .get('request_for_signatures')
          .query(Q.where('id', Q.oneOf(ids)))
          .fetch();
        return await Promise.all(
          requestForSignature.map(async (req: any) => {
            await req.markAsDeleted();
          })
        );
      });
    } catch (error) {
      // Error handling
      throw error;
    }
  }
}

export default RequestForSignaturesModel;
