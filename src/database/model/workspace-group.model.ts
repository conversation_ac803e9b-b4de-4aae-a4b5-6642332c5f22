import { field, text } from '@nozbe/watermelondb/decorators';
import BaseModel from './base-model';
import database from '../index.native';
import { Q } from '@nozbe/watermelondb';
import ProjectDocumentModel from './project-document.model';
import WorkspaceGroupUsersModel from './workspace-group-users.model';
import { getForeignKey } from '../utils/numeric';

class WorkspaceGroupModel extends BaseModel {
  static table = 'workspace_groups';

  @text('name') name!: string;
  @field('projectId') projectId!: number;
  @field('workspaceGroupId') workspaceGroupId?: number | null;
  //   @field('runningId') runningId?: string | null;
  @field('code') code?: string | null;
  @field('remoteId') remoteId?: number | null;
  @field('localGroupId') localGroupId?: string | null;

  static async findDefaultGroup(projectId: number) {
    const group = await database.collections
      .get<WorkspaceGroupModel>('workspace_groups')
      .query(Q.where('projectId', Q.eq(projectId)), Q.where('name', Q.eq('Ungroup Documents')))
      .unsafeFetchRaw();

    return group[0];
  }

  static async getWorkspaceGroup(remoteId: number): Promise<any> {
    try {
      const foreignKeyInfo = getForeignKey(remoteId, 'localGroupId', 'workspaceGroupId');

      const groups = await database.collections
        .get<WorkspaceGroupModel>('workspace_groups')
        .query(Q.where(foreignKeyInfo.fieldName, foreignKeyInfo.keyValue[foreignKeyInfo.fieldName]))
        .fetch();

      if (groups.length === 0) {
        // check if the group is a default group
        const defaultGroup = await database.collections
          .get<WorkspaceGroupModel>('workspace_groups')
          .query(Q.where('remoteId', Q.eq(remoteId)))
          .fetch();

        if (defaultGroup.length === 0) {
          return null;
        }

        return {
          ...defaultGroup[0]._raw,
          parent: null
        };
      }

      const group = groups[0];

      const joinValueParent = group.remoteId ? group.remoteId : group.id;
      const foreignKeyInfoParent = getForeignKey(joinValueParent, 'localGroupId', 'workspaceGroupId');

      const parentGroups = await database.collections
        .get<WorkspaceGroupModel>('workspace_groups')
        .query(Q.where(foreignKeyInfoParent.fieldName, foreignKeyInfoParent.keyValue[foreignKeyInfoParent.fieldName]))
        .fetch();

      const parentGroup = parentGroups.length > 0 ? parentGroups[0]._raw : null;

      return {
        ...group._raw,
        parent: parentGroup
      };
    } catch (error) {
      throw error;
    }
  }

  static async getWorkspaceGroups(
    page: number = 1,
    limit: number = 10,
    projectId: string,
    filteredValue: string,
    userId: string
  ): Promise<{ items: WorkspaceGroup[]; nextPage?: number }> {
    const offset = (page - 1) * limit;
    const topGroupNames = ['Ungroup Documents', 'Site Diary', 'Non Conformance Report', 'Request For Information'];

    try {
      const accessibleWorkspaceGroupIds = await WorkspaceGroupUsersModel.getAccessibleWorkspaceGroupIds(
        parseInt(userId),
        projectId
      );
      // Helper function to fetch children and documents
      const fetchChildrenWithDocuments = async (group: any) => {
        const workspaceIdValue = group.remoteId ? group.remoteId : group.id;
        const workspaceJoinField = group.remoteId ? 'workspaceGroupId' : 'localGroupId';

        const children = await database.collections
          .get<WorkspaceGroupModel>('workspace_groups')
          .query(Q.where(workspaceJoinField, Q.eq(workspaceIdValue)))
          .fetch();

        return Promise.all(
          children.map(async child => {
            if (!child.remoteId) return child;

            const documents = await database.collections
              .get<ProjectDocumentModel>('project_documents')
              .query(Q.where('workspaceGroupId', Q.eq(child.remoteId)))
              .fetch();

            const totalDocumentsCountWithoutDraft = documents.filter((doc: any) => doc.status !== 'Draft').length;
            const documentsByStatus = documents.reduce((acc: any, doc: any) => {
              if (!acc[doc.status]) acc[doc.status] = [];
              acc[doc.status].push(doc);
              return acc;
            }, {});

            return {
              ...child._raw,
              parentRemoteId: group.remoteId,
              documentsByStatus,
              totalCount: totalDocumentsCountWithoutDraft,
              submittedCount: documentsByStatus['Submitted']?.length || 0,
              inReviewCount: documentsByStatus['InReview']?.length || 0,
              amendCount: documentsByStatus['Amend']?.length || 0,
              pendingCount: documentsByStatus['Pending']?.length || 0,
              inProgressCount: documentsByStatus['InProgress']?.length || 0,
              approvedCount: documentsByStatus['Approved']?.length || 0,
              rejectedCount: documentsByStatus['Rejected']?.length || 0
            };
          })
        );
      };

      // Fetch top groups if page is 1
      const topGroups =
        page === 1
          ? await database.collections
              .get<WorkspaceGroupModel>('workspace_groups')
              .query(Q.where('projectId', Q.eq(parseInt(projectId, 10))), Q.where('name', Q.oneOf(topGroupNames)))
              .fetch()
          : [];

      let workspaceGroups: any[] = [];
      if (filteredValue.length > 0) {
        // Step 1: Fetch top groups matching the filter
        const topGroups = await database.collections
          .get<WorkspaceGroupModel>('workspace_groups')
          .query(
            Q.where('projectId', Q.eq(parseInt(projectId, 10))),
            Q.where('remoteId', Q.notIn(accessibleWorkspaceGroupIds)),
            Q.where('name', Q.like(`%${Q.sanitizeLikeString(filteredValue)}%`)),
            Q.where(
              'name',
              Q.oneOf(['Ungroup Documents', 'Site Diary', 'Non Conformance Report', 'Request For Information'])
            )
          )
          .fetch();

        // Step 2: Fetch child groups matching the filter
        const childGroups = await database.collections
          .get<WorkspaceGroupModel>('workspace_groups')
          .query(
            Q.where('projectId', Q.eq(parseInt(projectId, 10))),
            Q.where('name', Q.like(`%${Q.sanitizeLikeString(filteredValue)}%`)),
            Q.where(
              'name',
              Q.notIn(['Ungroup Documents', 'Site Diary', 'Non Conformance Report', 'Request For Information'])
            ) // Exclude top groups
          )
          .fetch();

        // Step 3: Fetch parent groups of the matched children
        const parentIds: any = childGroups
          .filter(group => group.workspaceGroupId || group.localGroupId) // Groups with parent references
          .map(group => group.workspaceGroupId || group.localGroupId); // Extract parent IDs

        const parentGroups = parentIds.length
          ? await database.collections
              .get<WorkspaceGroupModel>('workspace_groups')
              .query(Q.where('remoteId', Q.oneOf(parentIds)))
              .fetch()
          : [];

        // Step 4: Combine top groups, parent groups, and child groups
        workspaceGroups = [...topGroups, ...new Set([...parentGroups, ...childGroups])];
      } else {
        // Fetch non-top groups for unfiltered search
        const queryConditions = [
          Q.where('projectId', Q.eq(parseInt(projectId, 10))),
          Q.and(Q.where('workspaceGroupId', Q.eq(null)), Q.where('localGroupId', Q.eq(null))),
          Q.where('name', Q.notIn(topGroupNames)),
          Q.where('remoteId', Q.notIn(accessibleWorkspaceGroupIds)),
          Q.sortBy('name', Q.asc)
        ];

        const otherGroups = await database.collections
          .get<WorkspaceGroupModel>('workspace_groups')
          .query(...queryConditions, Q.skip(offset), Q.take(limit))
          .fetch();

        workspaceGroups = [...topGroups, ...otherGroups];
      }

      // Fetch children and documents for each group
      const workspaceGroupsWithChildrenCount = await Promise.all(
        workspaceGroups.map(async group => {
          const childrenWithDocuments = await fetchChildrenWithDocuments(group);

          const docs = await database.collections
            .get<ProjectDocumentModel>('project_documents')
            .query(Q.where('workspaceGroupId', Q.eq(group.remoteId ?? '')))
            .fetch();

          const workspaceGroupUsers = await database.collections
            .get<WorkspaceGroupUsersModel>('workspace_group_users')
            .query(Q.where('workspaceGroupId', Q.eq(group.remoteId ?? '')))
            .fetch();

          const totalDocumentsCountWithoutDraft = docs.filter((doc: any) => doc.status !== 'Draft').length;
          const docsByStatus = docs.reduce((acc: any, doc: any) => {
            if (!acc[doc.status]) acc[doc.status] = [];
            acc[doc.status].push(doc);
            return acc;
          }, {});

          return {
            ...group._raw,
            children: childrenWithDocuments,
            workspaceGroupUsers,
            totalCount:
              group.name === 'Ungroup Documents' || group.name === 'Site Diary'
                ? totalDocumentsCountWithoutDraft
                : // @ts-ignore
                  childrenWithDocuments.reduce((acc, child) => acc + (child?.totalCount || 0), 0),
            submittedCount: docsByStatus['Submitted']?.length || 0,
            inReviewCount: docsByStatus['InReview']?.length || 0,
            amendCount: docsByStatus['Amend']?.length || 0,
            pendingCount: docsByStatus['Pending']?.length || 0,
            inProgressCount: docsByStatus['InProgress']?.length || 0,
            approvedCount: docsByStatus['Approved']?.length || 0,
            rejectedCount: docsByStatus['Rejected']?.length || 0
          };
        })
      );

      const nextPage =
        workspaceGroups.filter(group => !topGroupNames.includes(group.name)).length === limit ? page + 1 : undefined;

      return {
        items: workspaceGroupsWithChildrenCount,
        nextPage
      };
    } catch (error) {
      throw error;
    }
  }

  static async createWorkspaceGroup(
    title: string,
    projectId: number,
    workspaceGroupId?: number | null,
    localGroupId?: string | null,
    code?: string | null
  ) {
    try {
      return database.write(async () => {
        // Determine the group code
        const groupCode = await this.getGroupCode(workspaceGroupId, localGroupId, code);

        // Create the workspace group
        return database.collections.get<WorkspaceGroupModel>('workspace_groups').create(workspaceGroup => {
          workspaceGroup.name = title;
          workspaceGroup.projectId = projectId;
          workspaceGroup.workspaceGroupId = workspaceGroupId || null;
          workspaceGroup.localGroupId = localGroupId || null;
          workspaceGroup.recordSource = 'OfflineApp';
          workspaceGroup.code = groupCode;
        });
      });
    } catch (error) {
      throw error;
    }
  }

  static async updateWorkspaceGroup(id: string, title: string, code?: string) {
    return database.write(async () => {
      const workspaceGroup = await database.collections.get<WorkspaceGroupModel>('workspace_groups').find(id);
      return workspaceGroup.update(p => {
        p.name = title;
        p.code = code;
      });
    });
  }

  static async deleteWorkspaceGroup(id: string) {
    return database.write(async () => {
      const collection = database.collections.get<WorkspaceGroupModel>('workspace_groups');
      const record = await collection.find(id);

      return await record.markAsDeleted();
    });
  }

  // ============================== HELPER FUNCTIONS ==============================
  // Helper method to fetch the group code
  private static async getGroupCode(
    workspaceGroupId?: number | null,
    localGroupId?: string | null,
    fallbackCode?: string | null
  ): Promise<string | null> {
    if (!workspaceGroupId && !localGroupId) {
      return fallbackCode || null;
    }

    const query = this.buildGroupQuery(workspaceGroupId, localGroupId);
    const parentGroups = await database.collections
      .get<WorkspaceGroupModel>('workspace_groups')
      .query(...query)
      .unsafeFetchRaw();

    return parentGroups?.[0]?.code || fallbackCode || null;
  }

  // Helper method to build the query
  private static buildGroupQuery(workspaceGroupId?: number | null, localGroupId?: string | null): any[] {
    if (workspaceGroupId) {
      return [Q.where('remoteId', Q.eq(workspaceGroupId))];
    } else if (localGroupId) {
      return [Q.where('id', Q.eq(localGroupId))];
    }
    return [];
  }
}
export default WorkspaceGroupModel;
