import { Model, Q } from '@nozbe/watermelondb';
import { field, text, writer } from '@nozbe/watermelondb/decorators';
import database from '../index.native';
import { getForeignKey } from '../utils/numeric';
import BaseModel from './base-model';

class WorkspaceDocumentModel extends BaseModel {
  static table = 'workspace_document';

  @field('projectDocumentId') projectDocumentId?: number;
  @field('name') name?: string;
  @field('fileUrl') fileUrl?: string;
  @field('type') type?: string;
  @field('category') category?: string;
  @field('documentId') documentId?: number | null;
  @field('remoteId') remoteId?: number | null;
  @field('localProjectDocumentId') localProjectDocumentId?: string | null;

  static async getWorkspaceDocument(remoteId: number | string): Promise<any[]> {
    try {
      const foreignKeyInfo = getForeignKey(remoteId, 'localProjectDocumentId', 'projectDocumentId');

      const documents = await database.collections
        .get('workspace_document')
        .query(Q.where(foreignKeyInfo.fieldName, Q.eq(foreignKeyInfo.keyValue[foreignKeyInfo.fieldName])))
        .fetch();

      if (documents.length === 0) {
        return [];
      }

      return documents.map(document => document._raw);
    } catch (error) {
      return [];
    }
  }

  static async isDocumentUnsynced(localProjectDocumentId: string): Promise<boolean> {
    try {
      const documentCollection = database.collections.get('workspace_document');
      const unsyncedQuery = documentCollection.query(
        Q.where('localProjectDocumentId', Q.eq(localProjectDocumentId)),
        Q.where('_status', Q.notEq('synced'))
      );
      const unsyncedDocument = await unsyncedQuery.fetch();

      return unsyncedDocument.length > 0;
    } catch (error) {
      return true;
    }
  }

  @writer
  async createWorkspaceDocument(updatedDocument: WorkspaceDocumentModel[]): Promise<void> {
    try {
      const newWorkspaceDocuments = updatedDocument.map((document: WorkspaceDocumentModel) => {
        return database.collections.get('workspace_document').prepareCreate((doc: any) => {
          doc.projectDocumentId = document.projectDocumentId;
          doc.name = document.name;
          doc.fileUrl = document.fileUrl;
          doc.type = document.type;
          doc.category = document.category;
          doc.documentId = document.documentId;
          doc.remoteId = document.remoteId;
          doc.localProjectDocumentId = document.localProjectDocumentId;
          doc.recordSource = 'OfflineApp';
        });
      });

      return await database.batch(...newWorkspaceDocuments);
    } catch (error) {
      throw new Error(`Error creating workspace documents: ${error}`);
    }
  }

  static async deleteWorkspaceDocument(documentId: string) {
    try {
      return await database.write(async () => {
        await database.adapter.unsafeExecute({
          sqls: [['UPDATE workspace_document SET _status = ? WHERE id = ?', ['deleted', documentId]]]
        });
      });
    } catch (error) {
      throw error;
    }
  }
}

export default WorkspaceDocumentModel;
