import { field, writer } from '@nozbe/watermelondb/decorators';
import database from '../index.native';
import { Q } from '@nozbe/watermelondb';
import { getForeignKey } from '../utils/numeric';
import { storeBinaryFile } from '@src/configs';
import BaseModel from './base-model';

class WorkspacePhotoModel extends BaseModel {
  static table = 'workspace_photos';

  @field('remoteId') remoteId?: number | null;
  @field('projectDocumentId') projectDocumentId!: number;
  @field('userId') userId!: number;
  @field('name') name!: string;
  @field('fileUrl') fileUrl?: string | null;
  @field('type') type?: string | null;
  @field('localProjectDocumentId') localProjectDocumentId?: string | null;
  @field('fileKey') fileKey?: string | null;
  @field('localFileUrl') localFileUrl?: string | null;

  static async getWorkspacePhotos(remoteId: number | string): Promise<any[]> {
    try {
      const foreignKeyInfo = getForeignKey(remoteId, 'localProjectDocumentId', 'projectDocumentId');

      const photos = await database.collections
        .get('workspace_photos')
        .query(Q.where(foreignKeyInfo.fieldName, Q.eq(foreignKeyInfo.keyValue[foreignKeyInfo.fieldName])))
        .fetch();

      if (photos.length === 0) {
        return [];
      }

      return photos.map(photo => photo._raw);
    } catch (error) {
      return [];
    }
  }

  static async isPhotosUnsynced(localProjectDocumentId: string): Promise<boolean> {
    try {
      const attachmentCollection = database.collections.get('workspace_photos');
      const unsyncedQuery = attachmentCollection.query(
        Q.where('localProjectDocumentId', Q.eq(localProjectDocumentId)),
        Q.where('_status', Q.notEq('synced'))
      );
      const unsyncedPhotos = await unsyncedQuery.fetch();

      return unsyncedPhotos.length > 0;
    } catch (error) {
      return true; // Assume unsynced on error
    }
  }

  @writer
  async createWorkspacePhotos(updatedPhotos: WorkspacePhotoModel[]): Promise<void> {
    try {
      updatedPhotos.forEach(async (attachment: WorkspacePhotoModel) => {
        await storeBinaryFile(attachment.name, attachment);
      });

      const newWorkspacePhotos = updatedPhotos.map((photo: WorkspacePhotoModel) => {
        return database.collections.get('workspace_photos').prepareCreate((ph: any) => {
          ph.remoteId = photo.remoteId;
          ph.projectDocumentId = photo.projectDocumentId;
          ph.userId = photo.userId;
          ph.name = photo.name;
          ph.fileUrl = photo.fileUrl;
          ph.type = photo.type;
          ph.localProjectDocumentId = photo.localProjectDocumentId;
          ph.recordSource = 'OfflineApp';
        });
      });

      return await database.batch(...newWorkspacePhotos);
    } catch (error) {
      throw new Error(`Error creating workspace photos: ${error}`);
    }
  }

  static async deleteMedia(mediaId: string) {
    try {
      await database.write(async () => {
        await database.adapter.unsafeExecute({
          sqls: [['UPDATE workspace_photos SET _status = ? WHERE id = ?', ['deleted', mediaId]]]
        });
      });
    } catch (error) {
      throw error;
    }
  }
}

export default WorkspacePhotoModel;
