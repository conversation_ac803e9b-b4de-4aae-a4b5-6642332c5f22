import { field, writer } from '@nozbe/watermelondb/decorators';
import BaseModel from './base-model';
import { Q } from '@nozbe/watermelondb';
import database from '../index.native';
import { ProjectDocumentEntity } from './project-document/entities/ProjectDocument.entity';

export default class DrawingRevisionModel extends BaseModel {
  static table = 'drawing_revisions';

  @field('fileName') fileName?: string;
  @field('fileUrl') fileUrl?: string;
  @field('localFileUrl') localFileUrl?: string;
  @field('fileKey') fileKey?: string;
  @field('projectDocumentId') projectDocumentId?: number;
  @field('version') version?: number;
  @field('versionName') versionName?: string;
  @field('notes') notes?: string;
  @field('xfdf') xfdf?: string | null;
  @field('status') status?: string;
  @field('category') category?: string;
  @field('remoteId') remoteId?: number;
  @field('localProjectDocumentId') localProjectDocumentId?: string;

  static async getDrawingRevisions(
    projectDocumentId: number | string,
    page: number,
    pageSize: number
  ): Promise<{
    drawingRevisions: DrawingRevisionModel[];
    nextPage?: number;
  }> {
    const relation = typeof projectDocumentId === 'number' ? 'projectDocumentId' : 'localProjectDocumentId';

    const drawingRevisions = await database.collections
      .get<DrawingRevisionModel>('drawing_revisions')
      .query(
        Q.where(relation, Q.eq(projectDocumentId)),
        Q.take(pageSize),
        Q.skip((page - 1) * pageSize),
        Q.sortBy('version', 'desc')
      )
      .fetch();

    if (drawingRevisions.length > 0) {
      const highestVersion = Math.max(...drawingRevisions.map(revision => revision.version || 0));

      drawingRevisions.forEach(revision => {
        revision.isHigherVersion = revision.version === highestVersion;
      });
    }

    return {
      drawingRevisions,
      nextPage: drawingRevisions.length === pageSize ? page + 1 : undefined
    };
  }

  static async getNextDoc(
    projectDocumentId: number,
    version: number,
    type: 'next' | 'previous'
  ): Promise<DrawingRevisionModel | undefined> {
    const drawingRevision = await database.collections
      .get<DrawingRevisionModel>('drawing_revisions')
      .query(
        Q.where('projectDocumentId', Q.eq(projectDocumentId)),
        Q.where('version', type === 'next' ? Q.gt(version) : Q.lt(version)),
        Q.sortBy('version', type === 'next' ? Q.asc : Q.desc),
        Q.take(1)
      )
      .fetch();

    return (drawingRevision[0]?._raw as unknown as DrawingRevisionModel) ?? undefined;
  }

  static async isRevisionUnsyced(ProjectDocumentLocalId: string): Promise<boolean> {
    const drawingRevision = await database.collections
      .get<DrawingRevisionModel>('drawing_revisions')
      .query(Q.where('localProjectDocumentId', Q.eq(ProjectDocumentLocalId)))
      .fetch();

    return drawingRevision.some(revision => revision.remoteId === null);
  }

  // ============================================= MUTATIONS =============================================

  @writer async createOneDrawingRevision(data: Partial<DrawingRevisionModel>): Promise<DrawingRevisionModel> {
    if (!data) {
      throw new Error('No Drawing Revision data provided');
    }

    const joinField: any = data.projectDocumentId ? 'projectDocumentId' : 'localProjectDocumentId';
    const joinParent: any = data.projectDocumentId ? 'remoteId' : 'localRemoteId';
    const joinValue: any = data.projectDocumentId ? data.projectDocumentId : data.localProjectDocumentId;

    // calculate the next version
    const lastRevision = await database.collections
      .get<DrawingRevisionModel>('drawing_revisions')
      .query(Q.where(joinField, Q.eq(joinValue)), Q.sortBy('version', Q.desc))
      .fetch();

    data.version = (lastRevision?.[0]?.version ?? 0) + 1;

    const drawingRevision = this.collections
      .get<DrawingRevisionModel>('drawing_revisions')
      .prepareCreate(newDrawingRevision => {
        newDrawingRevision.fileName = data.fileName;
        newDrawingRevision.fileUrl = data.fileUrl;
        newDrawingRevision.fileKey = data.fileKey;
        newDrawingRevision.projectDocumentId = data.projectDocumentId;
        newDrawingRevision.version = data.version;
        newDrawingRevision.versionName = data.versionName;
        newDrawingRevision.notes = data.notes;
        newDrawingRevision.xfdf = data.xfdf;
        newDrawingRevision.status = data.status;
        newDrawingRevision.category = data.category;
        newDrawingRevision.remoteId = data.remoteId;
        newDrawingRevision.recordSource = 'OfflineApp';
        newDrawingRevision.localProjectDocumentId = data.id;
      });

    await database.batch([drawingRevision]);

    //get projectDocument with the joinfield and joinvalue
    const projectDocument = await database.collections
      .get('project_documents')
      .query(Q.where(joinParent, Q.eq(joinValue)))
      .fetch();

    //update the projectDocument with the new drawingRevision, only the name, fileURL and fileKey
    await projectDocument[0].update(projectDocument => {
      projectDocument.name = data.fileName;
      projectDocument.fileUrl = data.fileUrl;
      projectDocument.fileKey = data.fileKey;
    });

    return drawingRevision as DrawingRevisionModel;
  }

  @writer async updateDrawingRevision(id: string, data: Partial<DrawingRevisionModel>): Promise<void> {
    if (!id || !data) {
      throw new Error('Invalid ID or data provided');
    }

    const drawingRevision = await database.collections.get<DrawingRevisionModel>('drawing_revisions').find(id);

    await drawingRevision.update(revision => {
      if (data.fileName !== undefined) revision.fileName = data.fileName;
      if (data.fileUrl !== undefined) revision.fileUrl = data.fileUrl;
      if (data.localFileUrl !== undefined) revision.localFileUrl = data.localFileUrl;
      if (data.fileKey !== undefined) revision.fileKey = data.fileKey;
      if (data.projectDocumentId !== undefined) revision.projectDocumentId = data.projectDocumentId;
      if (data.version !== undefined) revision.version = data.version;
      if (data.versionName !== undefined) revision.versionName = data.versionName;
      if (data.notes !== undefined) revision.notes = data.notes;
      if (data.xfdf !== undefined) revision.xfdf = data.xfdf;
      if (data.status !== undefined) revision.status = data.status;
      if (data.category !== undefined) revision.category = data.category;
      if (data.remoteId !== undefined) revision.remoteId = data.remoteId;
      if (data.localProjectDocumentId !== undefined) revision.localProjectDocumentId = data.localProjectDocumentId;
    });

    // Check if the updated revision is the latest version
    const projectDocumentId = drawingRevision.projectDocumentId;
    const highestVersionRevision = await database.collections
      .get<DrawingRevisionModel>('drawing_revisions')
      .query(Q.where('projectDocumentId', Q.eq(projectDocumentId)), Q.sortBy('version', 'desc'), Q.take(1))
      .fetch();

    if (highestVersionRevision.length > 0) {
      const projectDocument = await database.collections
        .get('project_documents')
        .query(Q.where('remoteId', Q.eq(projectDocumentId)))
        .fetch();

      if (projectDocument.length > 0) {
        await projectDocument[0].update(doc => {
          doc.name = data.fileName;
        });
      }
    }
  }

  static async deleteDrawingRevision(id: string): Promise<void> {
    if (!id) {
      throw new Error('Invalid ID provided');
    }

    await database.write(async () => {
      const drawingRevision = await database.collections.get<DrawingRevisionModel>('drawing_revisions').find(id);

      // Store project document reference before deleting
      const projectDocumentId = drawingRevision.projectDocumentId;
      const localProjectDocumentId = drawingRevision.localProjectDocumentId;

      // Delete the drawing revision
      await drawingRevision.markAsDeleted();

      // Find the parent project document
      let projectDocuments = [];
      if (projectDocumentId) {
        projectDocuments = await database.collections
          .get<ProjectDocumentEntity>('project_documents')
          .query(Q.where('remoteId', projectDocumentId))
          .fetch();
      } else if (localProjectDocumentId) {
        projectDocuments = await database.collections
          .get<ProjectDocumentEntity>('project_documents')
          .query(Q.where('localRemoteId', localProjectDocumentId))
          .fetch();
      }

      if (projectDocuments.length > 0) {
        const projectDocument = projectDocuments[0];

        // Find the highest version revision that's not deleted and not the one we just deleted
        let remainingRevisions = [];
        if (projectDocumentId) {
          remainingRevisions = await database.collections
            .get<DrawingRevisionModel>('drawing_revisions')
            .query(
              Q.where('projectDocumentId', projectDocumentId),
              Q.where('id', Q.notEq(id)),
              Q.sortBy('version', Q.desc),
              Q.take(1)
            )
            .fetch();
        } else if (localProjectDocumentId) {
          remainingRevisions = await database.collections
            .get<DrawingRevisionModel>('drawing_revisions')
            .query(
              Q.where('localProjectDocumentId', localProjectDocumentId),
              Q.where('id', Q.notEq(id)),
              Q.sortBy('version', Q.desc),
              Q.take(1)
            )
            .fetch();
        }

        if (remainingRevisions.length > 0) {
          // Update project document with the highest remaining revision
          const latestRevision = remainingRevisions[0];
          await projectDocument.update(doc => {
            if (latestRevision.fileName) doc.name = latestRevision.fileName;
            if (latestRevision.fileUrl) doc.fileUrl = latestRevision.fileUrl;
            if (latestRevision.fileKey) doc.fileKey = latestRevision.fileKey;
            if (latestRevision.versionName) doc.versionName = latestRevision.versionName;
            if (latestRevision.notes) doc.notes = latestRevision.notes;
            if (latestRevision.xfdf) doc.xfdf = latestRevision.xfdf;
          });
        }
      }
    });
  }
}
