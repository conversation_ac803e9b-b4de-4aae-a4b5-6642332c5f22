import { field } from '@nozbe/watermelondb/decorators';
import BaseModel from './base-model';
import database from '../index.native';
import apolloClient from '@src/lib/apollo';
import { Gql } from '@src/api';
import { Q } from '@nozbe/watermelondb';
import ProjectDocumentModel from './project-document';

class OfflineDownloadModel extends BaseModel {
  static table = 'offline_download';

  @field('isDownloaded') isDownloaded?: boolean;
  @field('fileUrl') fileUrl?: string;
  @field('localId') localId?: string;

  static async queueOfflineDownloads(ids: string[]) {
    for (const id of ids) {
      const item = await ProjectDocumentModel.getProjectDocumentById(id);
      database.write(async () => {
        item?.update(doc => {
          doc.isQueued = true;
          doc._raw._status = 'synced';
        });
      });

      const existingDocument = await OfflineDownloadModel.checkIfDocumentExists(id);

      if (existingDocument) {
        continue;
      }

      database.write(async () => {
        database.collections.get<OfflineDownloadModel>('offline_download').create(docs => {
          docs.localId = item?.id;
          docs.isDownloaded = false;
          docs.fileUrl = item?.fileUrl as string;
        });
      });
    }
  }

  static async handleOfflineDownload(documents: any[], projectId: number) {
    try {
      if (documents?.length === 0 || documents === null) {
        return;
      }
      const writes = documents.map(document => this.fetchDocumentUrl(document, projectId));
      await Promise.all(writes);

      return await database.write(async () => {
        await database.collections
          .get(OfflineDownloadModel.table)
          .query(Q.where('localId', documents[0]?.id))
          .markAllAsDeleted();
      });
    } catch (error) {
      throw error; // Rethrowing the error to allow handling higher up the call stack
    }
  }

  static async fetchAllOfflineDownloads() {
    const offlineDoc = await database.collections.get<OfflineDownloadModel>('offline_download').query().fetch();
    const ids = offlineDoc.map((item: any) => item.localId);
    return ids;
  }

  // do getOfflineDocumentsByLocalId
  static async getOfflineDocumentByLocalId(localId: string) {
    const offlineDoc = await database.collections
      .get<OfflineDownloadModel>('offline_download')
      .query(Q.where('localId', localId))
      .fetch();
    return offlineDoc;
  }

  static async checkIfDocumentExists(localId: string): Promise<boolean> {
    const existingDocument = await database.collections
      .get<OfflineDownloadModel>('offline_download')
      .query(Q.where('localId', localId), Q.where('isDownloaded', false))
      .fetch();
    return existingDocument.length > 0;
  }

  static async fetchDocumentUrl(document: any, projectId: number) {
    try {
      let response, fileUrl;
      const tableName = document.collection._cache.tableName;
      const remoteId = document.remoteId || document?._raw?.remoteId;

      response = await this.queryForDocument(tableName, remoteId);
      fileUrl = response.fileUrl;

      if (!response.data) {
        throw new Error('Document not found for id ' + tableName + ' ' + document?.remoteId);
      }

      const updatedItem = {
        ...document?._raw,
        fileUrl,
        tableName,
        projectId
      };

      // Save the updated project document
      await ProjectDocumentModel?.saveProjectDocument(updatedItem);
    } catch (error) {}
  }

  static async queryForDocument(tableName: string, documentId: number) {
    if (tableName === 'project_documents') {
      const data = await apolloClient.query({
        query: Gql.ProjectDocumentDocument,
        variables: {
          id: documentId
        },
        errorPolicy: 'ignore'
      });

      return { data, fileUrl: data.data.projectDocument.fileUrl };
    } else if (tableName === 'drawing_revisions') {
      const data = await apolloClient.query({
        query: Gql.GetDrawingRevisionDocument,
        variables: {
          id: documentId
        },
        errorPolicy: 'ignore'
      });
      return { data, fileUrl: data.data.drawingRevision.fileUrl };
    } else if (tableName === 'workspace_attachments') {
      const data = await apolloClient.query({
        query: Gql.WorkspaceAttachmentDocument,
        variables: {
          id: documentId
        },
        errorPolicy: 'ignore'
      });
      return { data, fileUrl: data.data.workspaceAttachment.fileUrl };
    } else if (tableName === 'workspace_photos') {
      const data = await apolloClient.query({
        query: Gql.WorkspacePhotoDocument,
        variables: {
          id: documentId
        },
        errorPolicy: 'ignore'
      });
      return { data, fileUrl: data.data.workspacePhoto.fileUrl };
    } else {
      throw new Error('Invalid table name');
    }
  }
}

export default OfflineDownloadModel;
