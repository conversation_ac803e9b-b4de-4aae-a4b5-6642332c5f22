import { Model, Q } from '@nozbe/watermelondb';
import { field, writer } from '@nozbe/watermelondb/decorators';
import BaseModel from './base-model';
import database from '../index.native';
import ProjectUserModel from './project-user.model';
import { getForeignKey } from '../utils/numeric';

class WorkspaceCcModel extends BaseModel {
  static table = 'workspace_ccs';

  @field('projectDocumentId') projectDocumentId?: number;
  @field('ownerId') ownerId?: number;
  @field('ccId') ccId?: number;
  @field('status') status?: string;
  @field('localProjectDocumentId') localProjectDocumentId?: string | null;
  @field('remoteId') remoteId?: number | null;

  static async getWorkspaceCcs(remoteId: number): Promise<any[]> {
    try {
      const foreignKeyInfo = getForeignKey(remoteId, 'localProjectDocumentId', 'projectDocumentId');

      const ccs = await database.collections
        .get<WorkspaceCcModel>('workspace_ccs')
        .query(Q.where(foreignKeyInfo.fieldName, Q.eq(foreignKeyInfo.keyValue[foreignKeyInfo.fieldName])))
        .fetch();

      const uniqueUserIds = [...new Set(ccs.map(cc => cc.ccId))];

      const filteredUserIds = uniqueUserIds.filter(id => id !== undefined);
      const userDetails = filteredUserIds.length > 0 ? await ProjectUserModel.getUsers(filteredUserIds) : [];

      const userMap = new Map(userDetails.map((user: { userId: any; _raw: any }) => [user.userId, user._raw]));

      const result = ccs.map(cc => ({
        ...cc._raw,
        user: userMap.get(cc.ccId)
      }));

      return result;
    } catch (error) {
      return [];
    }
  }

  static async isWorkspaceCcUnsynced(localProjectDocumentId: string): Promise<boolean> {
    try {
      const workspaceCCCollection = database.collections.get('workspace_ccs');
      const unsyncedQuery = workspaceCCCollection.query(
        Q.where('localProjectDocumentId', Q.eq(localProjectDocumentId)),
        Q.where('_status', Q.notEq('synced'))
      );
      const unsyncedComment = await unsyncedQuery.fetch();

      return unsyncedComment.length > 0;
    } catch (error) {
      return true; // Assume unsynced on error
    }
  }

  // static async createWorkspaceCcs(updatedRequest: WorkspaceCcModel[]): Promise<void> {
  //   try {
  //     return await database.action(async () => {
  //       const newWorkspaceCcs = updatedRequest.map((request: WorkspaceCcModel) => {
  //         return database.collections.get('workspace_ccs').prepareCreate((req: any) => {
  //           req.projectDocumentId = request.projectDocumentId;
  //           req.ownerId = request.ownerId;
  //           req.ccId = request.ccId;
  //           req.status = request.status;
  //           req.localProjectDocumentId = request.localProjectDocumentId;
  //           req.remoteId = request.remoteId;
  //         });
  //       });

  //       return await database.batch(...newWorkspaceCcs);
  //     });
  //   } catch (error) {
  //     throw new Error(`Error creating workspace ccs: ${error}`);
  //   }
  // }

  @writer
  async createWorkspaceCcs(updatedRequest: WorkspaceCcModel[]): Promise<void> {
    try {
      const newWorkspaceCcs = updatedRequest.map((request: WorkspaceCcModel) => {
        return database.collections.get('workspace_ccs').prepareCreate((req: any) => {
          req.projectDocumentId = request.projectDocumentId;
          req.ownerId = request.ownerId;
          req.ccId = request.ccId;
          req.status = request.status;
          req.localProjectDocumentId = request.localProjectDocumentId;
          req.remoteId = request.remoteId;
          req.localProjectDocumentId = request.localProjectDocumentId;
          req.recordSource = 'OfflineApp';
        });
      });

      const workspaceCc = await database.batch(...newWorkspaceCcs);

      return workspaceCc;
    } catch (error) {
      throw new Error(`Error creating workspace ccs: ${error}`);
    }
  }

  static async deleteWorkspaceCc(ids: string[]) {
    try {
      return await database.write(async () => {
        const workspaceCc = await database.collections
          .get('workspace_ccs')
          .query(Q.where('id', Q.oneOf(ids)))
          .fetch();
        return await Promise.all(
          workspaceCc.map(async (req: any) => {
            await req.markAsDeleted();
          })
        );
      });
    } catch (error) {
      throw error;
    }
  }
}

export default WorkspaceCcModel;
