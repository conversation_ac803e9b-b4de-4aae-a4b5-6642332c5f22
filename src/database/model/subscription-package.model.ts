import { field, date } from '@nozbe/watermelondb/decorators';
import BaseModel from './base-model';

class SubscriptionPackage extends BaseModel {
  static table = 'subscription_packages';

  @field('remoteId') remoteId!: number;
  @field('title') title!: string;
  @field('description') description!: string;
  @field('amount') amount!: number;
  @field('availableDuration') availableDuration!: number;
  @field('totalProjects') totalProjects!: number;
  @field('totalUsers') totalUsers!: number;
  @field('allowTask') allowTask!: boolean;
  @field('allowProjectDocument') allowProjectDocument!: boolean;
  @field('allowWorkProgramme') allowWorkProgramme!: boolean;
  @field('allowCorrespondence') allowCorrespondence!: boolean;
  @field('allowWorkspaceDocument') allowWorkspaceDocument!: boolean;
  @field('allowWorkspaceTemplate') allowWorkspaceTemplate!: boolean;
  @field('allowDrawing') allowDrawing!: boolean;
  @field('allowBimModel') allowBimModel!: boolean;
  @field('allowPhoto') allowPhoto!: boolean;
  @field('allowScheduleChart') allowScheduleChart!: boolean;
  @field('allowScheduleActivity') allowScheduleActivity!: boolean;
  @field('allowDashboard') allowDashboard!: boolean;
  @field('allowEmailCorrespondence') allowEmailCorrespondence!: boolean;

  static columns = {
    ...BaseModel.columns,
    remoteId: 'remoteId',
    title: 'title',
    description: 'description',
    amount: 'amount',
    availableDuration: 'availableDuration',
    totalProjects: 'totalProjects',
    totalUsers: 'totalUsers',
    allowTask: 'allowTask',
    allowProjectDocument: 'allowProjectDocument',
    allowWorkProgramme: 'allowWorkProgramme',
    allowCorrespondence: 'allowCorrespondence',
    allowWorkspaceDocument: 'allowWorkspaceDocument',
    allowWorkspaceTemplate: 'allowWorkspaceTemplate',
    allowDrawing: 'allowDrawing',
    allowBimModel: 'allowBimModel',
    allowPhoto: 'allowPhoto',
    allowScheduleChart: 'allowScheduleChart',
    allowScheduleActivity: 'allowScheduleActivity',
    allowDashboard: 'allowDashboard',
    allowEmailCorrespondence: 'allowEmailCorrespondence'
  };
}

export default SubscriptionPackage;
