import { date, field } from '@nozbe/watermelondb/decorators';
import BaseModel from './base-model';
import database from '../index.native';
import { Q } from '@nozbe/watermelondb';

type Event = {
  remoteId?: number;
  userId?: number;
  title?: string;
  startAt?: number;
  endAt?: number;
  startTime?: string;
  endTime?: string;
  isAllDay?: boolean;
  projectId?: number;
  scheduleForAll?: boolean;
};

export default class EventModel extends BaseModel {
  static table = 'events';

  @field('remoteId') remoteId?: number;
  @field('userId') userId?: number;
  @field('title') title?: string;
  @date('startAt') startAt?: Date;
  @date('endAt') endAt?: Date;
  @field('startTime') startTime?: string;
  @field('endTime') endTime?: string;
  @field('isAllDay') isAllDay?: boolean;
  @field('projectId') projectId?: number;
  @field('scheduleForAll') scheduleForAll?: boolean;

  static async fetchEvents(
    userId: string,
    projectId: string,
    page = 1,
    pageSize = 10
  ): Promise<{ MergedEvents: Event[]; nextPage?: number }> {
    try {
      const startOfToday = new Date();
      startOfToday.setHours(0, 0, 0, 0);
      const farFuture = new Date();
      farFuture.setFullYear(farFuture.getFullYear() + 1000);

      const projectUsers = await database.collections
        .get('project_users')
        .query(Q.where('userId', Q.eq(parseInt(userId))), Q.where('projectId', Q.eq(parseInt(projectId))))
        .fetch();

      if (!projectUsers.length) {
        return { MergedEvents: [] };
      }
      const projectUserId = projectUsers[0]._raw.remoteId;

      const events = await database.collections
        .get('events')
        .query(
          Q.where('projectId', Q.eq(parseInt(projectId))),
          Q.where('startAt', Q.between(startOfToday.getTime(), farFuture.getTime()))
        )
        .fetch();

      const tasks = await database.collections
        .get('tasks')
        .query(
          Q.where('projectId', Q.eq(parseInt(projectId))),
          Q.where('dueDate', Q.between(startOfToday.getTime(), farFuture.getTime())),
          Q.where('status', Q.notEq('completed')),
          Q.where('assignees', Q.includes(projectUserId.toString()))
        )
        .fetch();

      const combinedData: Event[] = [...events, ...tasks].map(item => ({
        ...item._raw,
        startAt: item.startAt ? new Date(item.startAt).getTime() : undefined,
        dueDate: item.dueDate ? new Date(item.dueDate).getTime() : undefined
      }));

      combinedData.sort((a, b) => (a.startAt || a.dueDate) - (b.startAt || b.dueDate));

      const startIndex = (page - 1) * pageSize;
      const paginatedData = combinedData.slice(startIndex, startIndex + pageSize);

      return {
        MergedEvents: paginatedData,
        nextPage: startIndex + pageSize < combinedData.length ? page + 1 : undefined
      };
    } catch (error) {
      return { MergedEvents: [] };
    }
  }
}
