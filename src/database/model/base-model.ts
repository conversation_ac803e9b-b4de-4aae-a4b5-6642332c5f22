import { Model } from '@nozbe/watermelondb';
import { field, date, readonly } from '@nozbe/watermelondb/decorators';

class BaseModel extends Model {
  static table: string;

  @readonly @date('created_at') createdAt!: number;
  @date('updated_at') updatedAt!: number;

  @readonly @date('deleted_at') deletedAt!: number;
  @field('updatedBy') updatedBy?: number | null;

  @field('recordSource') recordSource!: 'OfflineApp' | 'Web' | null;

  @field('retry_count') retryCount?: number;
  @field('download_retry') downloadRetry!: number;

  static columns = {
    id: 'id',
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at',
    server_created_at: 'server_created_at',
    server_updated_at: 'server_updated_at',
    updatedBy: 'updatedBy',
    recordSource: 'recordSource',
    retryCount: 'retry_count',
    downloadRetry: 'download_retry'
  };
}

export default BaseModel;
