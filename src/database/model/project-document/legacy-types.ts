import { Model } from '@nozbe/watermelondb';
import { _RawRecord } from '@nozbe/watermelondb/RawRecord';

interface ProjectRawRecord extends _RawRecord {
  drawing_sync: boolean | null;
}

interface DocumentRawRecord extends _RawRecord {
  remoteId?: number;
}

export interface ProjectDocumentLegacy extends Model {
  id: string;
  remoteId?: number;
  _raw: DocumentRawRecord;
}

export interface ProjectLegacy extends Model {
  _raw: ProjectRawRecord;
}

export interface ProjectDocumentId {
  id: string;
}

export interface ProjectDocumentStatic {
  table: string;
  getLocalId(remoteId: number): Promise<string | null>;
  getAllDrawings(projectId: number): Promise<string[]>;
  getAllTemplates(projectId: number): Promise<ProjectDocumentLegacy[]>;
}
