import { Filter } from '@src/slice/document-workspace.slice';
import { Gql } from '@src/api';

interface SqlQueryResult {
  sql: string;
  params: any[];
}

export const buildSqlQuery = (
  data: any,
  filteredValue: string,
  sorting?: string,
  page?: number,
  limit?: number,
  filterItems?: Filter
): SqlQueryResult => {
  // Start with conditions that filter out deleted documents
  let conditions: string[] = ['1=1', '_status != "deleted"'];
  let params: any[] = [];
  let orderBy: string[] = [];

  // Project ID condition
  if (data.projectId) {
    const projectId = parseInt(data.projectId, 10);
    conditions.push('projectId = ?');
    params.push(projectId);
  }

  // ID condition
  if (data.id) {
    conditions.push('id = ?');
    params.push(data.id);
  }

  // Remote ID condition
  if (data.remoteId) {
    const remoteId = parseInt(data.remoteId, 10);
    conditions.push('remoteId = ?');
    params.push(remoteId);
  }

  // Drive type condition
  if (data.driveType) {
    conditions.push('driveType = ?');
    params.push(data.driveType);
  }

  // Category condition
  if (data.category) {
    conditions.push('category = ?');
    params.push(data.category);
  }

  // Status conditions
  if (data.status === '') {
    conditions.push('status != ?');
    params.push('Draft');
  } else if (data.status === 'Draft') {
    conditions.push('addedBy = ? AND status = ?');
    params.push(data.userId, 'Draft');
  }

  // Name filter condition
  if (filteredValue) {
    conditions.push('name LIKE ?');
    params.push(`%${filteredValue}%`);
  } else {
    handleSpecialFilters(conditions, params, data);
  }

  // Template sorting condition
  if (sorting === 'template') {
    conditions.push('autosavedAt IS NULL');
  }

  // Filter items conditions
  if (filterItems) {
    const sanitizedFilterItems = Object.entries(filterItems).reduce((acc, [key, value]) => {
      if (value !== undefined && value !== null) {
        acc[key as keyof Filter] = value;
      }
      return acc;
    }, {} as Partial<Filter>);

    if (sanitizedFilterItems.groupId) {
      conditions.push('workspaceGroupId = ?');
      params.push(parseInt(sanitizedFilterItems.groupId));
    }

    if (sanitizedFilterItems.status) {
      const placeholders = sanitizedFilterItems.status.map(() => '?').join(',');
      conditions.push(`status IN (${placeholders})`);
      params.push(...sanitizedFilterItems.status);
    }

    if (sanitizedFilterItems.assigneeId) {
      conditions.push('assigneeIds LIKE ?');
      params.push(`%${sanitizedFilterItems.assigneeId}%`);
    }

    if (sanitizedFilterItems.groupCode) {
      conditions.push('groupCode LIKE ?');
      params.push(`%${sanitizedFilterItems.groupCode}%`);
    }

    if (sanitizedFilterItems.allFormCode) {
      conditions.push('allFormCode = ?');
      params.push(sanitizedFilterItems.allFormCode);
    }
  }

  // Handle document exclusions
  handleExclusions(conditions, params, data);

  // Add sorting conditions
  if (sorting) {
    switch (sorting) {
      case 'AllForm':
        orderBy.push('groupCode DESC');
        break;

      case 'server_created_at':
        orderBy.push('fileSystemType DESC', '_status ASC', 'server_created_at DESC');
        break;

      case 'submittedAt':
        orderBy.push('submittedAt DESC');
        break;

      case 'Draft':
        orderBy.push('created_at DESC', 'server_created_at DESC');
        break;

      case 'template':
        orderBy.push('fileSystemType DESC', 'created_at ASC');
        break;

      default:
        // Custom sort using GLOB patterns
        orderBy.push(
          // (A) Category sorting
          `CASE
        WHEN (
          (name GLOB '[0-9]*.[0-9]*' AND name NOT GLOB '*[^0-9.]*')
          OR (name NOT GLOB '*[^0-9]*')
        )
        THEN 0  -- purely numeric or decimal
        WHEN name GLOB '[^a-zA-Z0-9]*' THEN 1
        WHEN name GLOB '[0-9]*' THEN 2  -- starts digit + text
        WHEN name NOT GLOB '*[^a-zA-Z]*' THEN 3
        WHEN name GLOB '[a-zA-Z]*[^a-zA-Z0-9]*'
             AND name NOT GLOB '*[0-9]*'
        THEN 4
        ELSE 5
      END`,

          // (B) Numeric or numeric-prefix sorting
          `CASE
        WHEN (
          (name GLOB '[0-9]*.[0-9]*' AND name NOT GLOB '*[^0-9.]*')
          OR (name NOT GLOB '*[^0-9]*')
        )
        THEN CAST(name AS FLOAT)
        WHEN name GLOB '[0-9]*'
        THEN CAST(
          SUBSTR(
            name,
            1,
            CASE
              WHEN INSTR(name, ' ') > 0 THEN INSTR(name, ' ') - 1
              WHEN INSTR(name, '.') > 0 THEN
                CASE
                  WHEN SUBSTR(name, INSTR(name, '.') + 1, 1) GLOB '[0-9]'
                    THEN LENGTH(name)
                  ELSE INSTR(name, '.') - 1
                END
              ELSE LENGTH(name)
            END
          ) AS FLOAT
        )
        ELSE NULL
      END`,

          // (C) Final tie-break: Just the raw name in ASCII order
          'name'
        );
    }
  } else {
    // Default order if 'sorting' is not specified
    orderBy.push(
      'fileSystemType DESC',
      // (A) Category sorting
      `CASE
       WHEN (
         (name GLOB '[0-9]*.[0-9]*' AND name NOT GLOB '*[^0-9.]*')
         OR (name NOT GLOB '*[^0-9]*')
       )
       THEN 0  -- purely numeric or decimal
       WHEN name GLOB '[^a-zA-Z0-9]*' THEN 1
       WHEN name GLOB '[0-9]*' THEN 2  -- starts digit + text
       WHEN name NOT GLOB '*[^a-zA-Z]*' THEN 3
       WHEN name GLOB '[a-zA-Z]*[^a-zA-Z0-9]*'
            AND name NOT GLOB '*[0-9]*'
       THEN 4
       ELSE 5
     END`,
      // (B) Numeric or numeric-prefix sorting
      `CASE
       WHEN (
         (name GLOB '[0-9]*.[0-9]*' AND name NOT GLOB '*[^0-9.]*')
         OR (name NOT GLOB '*[^0-9]*')
       )
       THEN CAST(name AS FLOAT)
       WHEN name GLOB '[0-9]*'
       THEN CAST(
         SUBSTR(
           name,
           1,
           CASE
             WHEN INSTR(name, ' ') > 0 THEN INSTR(name, ' ') - 1
             WHEN INSTR(name, '.') > 0 THEN
               CASE
                 WHEN SUBSTR(name, INSTR(name, '.') + 1, 1) GLOB '[0-9]'
                   THEN LENGTH(name)
                 ELSE INSTR(name, '.') - 1
               END
             ELSE LENGTH(name)
           END
         ) AS FLOAT
       )
       ELSE NULL
     END`,
      // (C) Final tie-break: Just the raw name in ASCII order
      'name'
    );
  }

  // Build the SQL query
  let sql = 'SELECT * FROM project_documents WHERE ' + conditions.join(' AND ');

  if (orderBy.length > 0) {
    sql += ' ORDER BY ' + orderBy.join(', ');
  }

  // Add pagination
  if (page && limit) {
    sql += ' LIMIT ? OFFSET ?';
    params.push(limit, (page - 1) * limit);
  }

  return { sql, params };
};

const handleSpecialFilters = (conditions: string[], params: any[], data: any): void => {
  if (data.projectDocumentId === null && data.category !== Gql.CategoryType.AllForm) {
    conditions.push('(projectDocumentId IS NULL AND localRemoteId IS NULL)');
  } else if (data.projectDocumentId) {
    conditions.push('projectDocumentId = ?');
    params.push(data.projectDocumentId);
  }

  if (data.localRemoteId || data.localRemoteId === null) {
    conditions.push('localRemoteId IS ' + (data.localRemoteId === null ? 'NULL' : '?'));
    if (data.localRemoteId !== null) {
      params.push(data.localRemoteId);
    }
  }
};

const handleExclusions = (conditions: string[], params: any[], data: any): void => {
  if (data.exclude && data.exclude.length > 0) {
    data.exclude.forEach((exclusion: string) => {
      const [key, value] = exclusion.split(':');
      if (key && value) {
        conditions.push(`${key} != ?`);
        params.push(value);
      }
    });
  }
};
