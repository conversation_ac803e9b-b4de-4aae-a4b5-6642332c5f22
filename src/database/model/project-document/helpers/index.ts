import { Q } from '@nozbe/watermelondb';
export * from './buildQueryConditions.helper';

// Common database operation helper
export const fetchDocuments = async (
  database: any,
  collectionName: string,
  queryConditions: string,
  params: any[]
): Promise<any[]> => {
  try {
    return await database.get(collectionName).query(Q.unsafeSqlQuery(queryConditions, params)).unsafeFetchRaw();
  } catch (error) {
    throw error;
  }
};
