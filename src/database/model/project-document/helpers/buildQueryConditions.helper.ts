import { Filter } from '@src/slice/document-workspace.slice';
import { buildSqlQuery } from './buildSqlQuery.helper';

export const buildQueryConditions = (
  data: any,
  filteredValue: string,
  sorting?: string,
  page?: number,
  limit?: number,
  filterItems?: Filter
): { sql: string; params: any[] } => {
  try {
    // Generate SQL query
    const { sql, params } = buildSqlQuery(data, filteredValue, sorting, page, limit, filterItems);
    return { sql, params };
  } catch (error) {
    throw error;
  }
};
