import database from '../../../index.native';
import WorkspaceGroupModel from '../../workspace-group.model';
import RequestForSignaturesModel from '../../request-for-signatures.model';
import WorkspaceCcModel from '../../workspace_ccs.model';
import WorkspaceAttachmentModel from '../../workspace-attachments.model';
import WorkspacePhotoModel from '../../workspace-photos.model';
import WorkspaceDocumentModel from '../../workspace-document.model';
import { Gql } from '@src/api';
import storeFileLocally from '@src/configs/saveOffline';
import { ProjectDocument } from '../types';
import { Q } from '@nozbe/watermelondb';

interface CreateProjectDocumentInput {
  projectId: number;
  category: string;
  workspaceGroupId?: number;
  recordSource?: string;
  [key: string]: any;
}

interface UpdateProjectDocumentInput {
  id: string;
  tableName?: string;
  [key: string]: any;
}

interface UpdateWorkspaceDocumentInput {
  id: string;
  remoteId?: string;
  name?: string;
  description?: string;
  workspaceGroupId?: number;
  workflow?: string;
  status?: string;
  localFileUrl?: string;
  fileUrl?: string;
  xfdf?: string;
  fileKey?: string;
  assignees?: any[];
  ccs?: any[];
  attachments?: any[];
  media?: any[];
  linkedDocuments?: any[];
}

// ============= Basic CRUD Operations =============

/**
 * Creates multiple project documents in the database using batch processing.
 */
export const createProjectDocs = async (newDocs: CreateProjectDocumentInput[]): Promise<ProjectDocument[]> => {
  if (!Array.isArray(newDocs) || newDocs.length === 0) {
    throw new Error('Input must be a non-empty array of documents');
  }

  try {
    const createdDocs = await database.write(async () => {
      const projectDocsCollection = database.collections.get<ProjectDocument>('project_documents');
      const preparedCreates = [];

      for (const newDoc of newDocs) {
        if (newDoc.category === Gql.CategoryType.AllForm) {
          const defaultGroup = await WorkspaceGroupModel.findDefaultGroup(newDoc.projectId);
          if (defaultGroup) {
            newDoc.workspaceGroupId = defaultGroup.remoteId;
          } else {
            console.warn(`No default group found for project ID: ${newDoc.projectId}`);
          }
        }

        const docWithDefaults = {
          ...newDoc,
          recordSource: 'OfflineApp'
        };

        const preparedDoc = projectDocsCollection.prepareCreate((doc: ProjectDocument) => {
          Object.entries(docWithDefaults).forEach(([key, value]) => {
            (doc as any)[key] = value;
          });
        });

        preparedCreates.push(preparedDoc);
      }

      await database.batch(...preparedCreates);
      return preparedCreates as ProjectDocument[];
    });

    return createdDocs;
  } catch (error) {
    throw new Error(`Error creating project documents: ${error instanceof Error ? error.message : String(error)}`);
  }
};

export const updateProjectDocs = async (data: UpdateProjectDocumentInput) => {
  try {
    if (!data.id) {
      throw new Error('Document ID is required for update');
    }

    return await database.write(async () => {
      const projectDocument = (await database.collections
        .get('project_documents')
        .find(data.id ?? '')) as ProjectDocument;
      return await projectDocument.update((p: any) => {
        (Object.keys(data) as (keyof UpdateProjectDocumentInput)[]).forEach(key => {
          if (data[key] !== undefined && key !== 'id') {
            p[key] = data[key];
          }
        });
      });
    });
  } catch (error) {
    throw error;
  }
};

export const deleteDocument = async (id: string) => {
  try {
    return await database.write(async () => {
      const recordToDelete = (await database.collections.get('project_documents').find(id)) as ProjectDocument;
      return await recordToDelete.markAsDeleted();
    });
  } catch (error) {
    throw error;
  }
};

// ============= Extended CRUD Operations =============

export const updateDownloadedDocs = async (data: UpdateProjectDocumentInput) => {
  try {
    return database.write(async () => {
      const tableName = data.tableName || 'project_documents';
      const collection = database.collections.get(tableName);
      const document = (await collection.find(data.id as any)) as ProjectDocument;

      const updatedDocument = await document.update((p: any) => {
        Object.keys(data).forEach(key => {
          if (data[key] !== undefined && !['id', 'tableName', 'updated_at'].includes(key)) {
            p[key] = data[key];
          }
        });
        p._raw._status = 'synced';
        p._raw._changed = '';
      });

      return updatedDocument;
    });
  } catch (error) {
    throw error;
  }
};

interface SyncAssociatedRecordsParams {
  collectionName: string;
  documentId: string;
  remoteId?: string;
  newRecords?: Array<RequestForSignaturesModel>;
  Model: any;
  createMethod: string;
  deleteMethod: string;
}

export const updateWorkspaceDocument = async (data: UpdateWorkspaceDocumentInput) => {
  if (
    data?.name ||
    data?.description ||
    data?.workspaceGroupId ||
    data?.workflow ||
    data?.status ||
    data?.localFileUrl ||
    data.fileUrl ||
    data?.xfdf ||
    data?.fileKey
  ) {
    await updateProjectDocs(data as any);
  }

  if (data.status === Gql.ProjectDocumentStatus.Draft) {
    // Sync request_for_signatures
    await syncAssociatedRecords({
      collectionName: 'request_for_signatures',
      documentId: data.id,
      remoteId: data.remoteId,
      newRecords: data.assignees,
      Model: RequestForSignaturesModel,
      createMethod: 'createRequestForSignature',
      deleteMethod: 'deleteRequestForSignature'
    });

    // Sync workspace_ccs
    await syncAssociatedRecords({
      collectionName: 'workspace_ccs',
      documentId: data.id,
      remoteId: data.remoteId,
      newRecords: data.ccs,
      Model: WorkspaceCcModel,
      createMethod: 'createWorkspaceCcs',
      deleteMethod: 'deleteWorkspaceCc'
    });
  }

  // Handle attachments, media, and linkedDocuments
  if (data?.attachments && data?.attachments?.length > 0) {
    await new WorkspaceAttachmentModel(database.get('workspace_attachments'), {} as any).createWorkspaceAttachment(
      data.attachments
    );
  }

  if (data?.media && data?.media?.length > 0) {
    await new WorkspacePhotoModel(database.get('workspace_photos'), {} as any).createWorkspacePhotos(data.media);
  }

  if (data?.linkedDocuments && data?.linkedDocuments?.length > 0) {
    await new WorkspaceDocumentModel(database.get('workspace_document'), {} as any).createWorkspaceDocument(
      data.linkedDocuments
    );
  }
};

export const saveProjectDocument = async (
  projectDocument: Partial<ProjectDocument> & { id: string; fileUrl: string }
) => {
  try {
    if (!projectDocument.fileUrl) {
      throw new Error('File URL is not available');
    }

    const fileName = projectDocument.name || projectDocument.fileName || 'untitled';
    const category = projectDocument.category || 'files';
    const tableName = projectDocument.tableName || 'project_documents';

    const subPath = `${projectDocument.projectId}/${category}/${tableName}`;
    const fileUrl = await storeFileLocally(projectDocument.fileUrl, subPath, fileName);
    return await updateDownloadedDocs({
      id: projectDocument.id,
      localFileUrl: fileUrl,
      tableName
    }).then(async (updatedProjectDocument: ProjectDocument) => {
      updatedProjectDocument._raw.localFileUrl = fileUrl;
      updatedProjectDocument._raw._changed = '';
      updatedProjectDocument.syncStatus = 'synced';
      return updatedProjectDocument;
    });
  } catch (error) {
    throw error;
  }
};

export const incrementDownloadRetry = async (projectDocumentId: string): Promise<void> => {
  await database.write(async () => {
    const templateRecord = (await database.collections
      .get('project_documents')
      .find(projectDocumentId)) as ProjectDocument;
    await templateRecord.update(record => {
      record.download_retry = (record.download_retry || 0) + 1;
    });
  });
};

const syncAssociatedRecords = async ({
  collectionName,
  documentId,
  remoteId,
  newRecords,
  Model,
  createMethod,
  deleteMethod
}: SyncAssociatedRecordsParams) => {
  if (!newRecords) return;

  const conditions = [Q.where('localProjectDocumentId', documentId)];
  if (remoteId) {
    conditions.push(Q.where('projectDocumentId', remoteId));
  }
  const queryCondition = conditions.length > 1 ? Q.or(...conditions) : conditions[0];

  const existingRecords = await database.collections.get(collectionName).query(queryCondition).fetch();

  const newRecordIds = newRecords.map(r => r.id);
  const existingRecordIds = existingRecords.map((r: any) => r.id);

  const toAdd = newRecords.filter(r => !existingRecordIds.includes(r.id));
  const toDelete = existingRecords.filter((r: any) => !newRecordIds.includes(r.id));

  if (toAdd.length > 0) {
    await new Model(database.get(collectionName), {} as any)[createMethod](toAdd);
  }

  if (toDelete.length > 0) {
    const deleteIds = toDelete.map(r => r.id);
    await Model[deleteMethod](deleteIds);
  }
};
