import { Q } from '@nozbe/watermelondb';
import { ProjectDocumentEntity } from './entities';
import database from '@src/database/index.native';
import { buildQueryConditions, fetchDocuments } from './helpers';
import { Gql } from '@src/api';
import DrawingRevisionModel from '../drawing-revision.model';
import apolloClient from '@src/lib/apollo';
import { Filter } from '@src/slice/document-workspace.slice';
import { DocumentWithSync, PaginatedResults, RequestForSignature, WorkspaceGroup, WorkspaceGroupUser } from './types';
import { ProjectDocumentLegacy, ProjectLegacy } from './legacy-types';
import ProjectUserModel from '../project-user.model';
import WorkspaceCcModel from '../workspace_ccs.model';
import WorkspaceAttachmentModel from '../workspace-attachments.model';
import WorkspacePhotoModel from '../workspace-photos.model';
import WorkspaceDocumentModel from '../workspace-document.model';
import WorkspaceCommentModel from '../workspace-comment.model';
import RequestForSignaturesModel from '../request-for-signatures.model';
import WorkspaceGroupModel from '../workspace-group.model';

export const getLocalId = async (remoteId: number): Promise<string | null> => {
  try {
    const queryConditions = [Q.where('remoteId', Q.eq(remoteId))];

    const results = await database.collections
      .get('project_documents')
      .query(...queryConditions)
      .fetch();

    return results[0]?.id ?? null;
  } catch (error) {
    return null;
  }
};

export const getAllDrawings = async (projectId: number): Promise<string[]> => {
  if (projectId === undefined) {
    return [];
  }
  const queryConditions = [
    Q.where('projectId', Q.eq(projectId)),
    Q.where('category', Q.eq('TwoDDrawings')),
    Q.where('localFileUrl', Q.eq(null)),
    Q.where('fileSystemType', Q.notEq('Folder'))
  ];

  const data = database.collections
    .get('project_documents')
    .query(...queryConditions)
    .fetch();

  const project = (await database.collections
    .get('projects')
    .query(Q.where('remoteId', Q.eq(projectId)))
    .fetch()) as ProjectLegacy[];

  if (project[0]?._raw.drawing_sync === null || project[0]?._raw.drawing_sync === false) {
    return [];
  } else {
    return data.then((result: ProjectDocumentLegacy[]) => result.map(item => item.id));
  }
};

export const getAllTemplates = async (projectId: number): Promise<ProjectDocumentLegacy[]> => {
  const queryConditions = [
    Q.where('projectId', Q.eq(projectId)),
    Q.where('category', Q.eq('StandardForm')),
    Q.where('localFileUrl', Q.eq(null)),
    Q.where('fileSystemType', Q.notEq('Folder')),
    Q.where('remoteId', Q.notEq(null)),
    Q.where('download_retry', Q.lte(3))
  ];

  const data = database.collections
    .get('project_documents')
    .query(...queryConditions)
    .fetch() as Promise<ProjectDocumentLegacy[]>;

  return data;
};

const enhanceDocumentsWithWorkspace = async (
  documents: DocumentWithSync[],
  filterItems: any,
  data: any
): Promise<DocumentWithSync[]> => {
  if (!documents.length) return [];

  const workspaceGroupIds = Array.from(new Set(documents.map(doc => doc.workspaceGroupId)));

  const [workspaceGroups] = (await Promise.all([
    database.collections
      .get('workspace_groups')
      .query(Q.where('remoteId', Q.oneOf(workspaceGroupIds.filter(Boolean).map(Number))))
      .fetch()
  ])) as [WorkspaceGroup[]];

  const requestForSignatures = (await database.collections
    .get('request_for_signatures')
    .query(
      Q.or(
        Q.and(
          Q.where('remoteId', Q.notEq(null)),
          Q.where(
            'projectDocumentId',
            Q.oneOf(
              documents
                .map(doc => doc.remoteId)
                .filter(Boolean)
                .map(Number)
            )
          )
        ),
        Q.and(
          Q.where('remoteId', Q.eq(null)),
          Q.where('localProjectDocumentId', Q.oneOf(documents.map(doc => doc.id).filter(Boolean)))
        )
      )
    )
    .fetch()) as RequestForSignature[];

  const workspaceGroupsUsers = (await database.collections
    .get('workspace_group_users')
    .query(Q.where('workspaceGroupId', Q.oneOf(workspaceGroups.map(wg => wg.workspaceGroupId).filter(Boolean))))
    .fetch()) as WorkspaceGroupUser[];

  const userIds = Array.from(
    new Set([
      ...workspaceGroupsUsers.map(wgu => wgu.userId),
      ...requestForSignatures.map(rfs => rfs.signById).filter(Boolean)
    ])
  );

  const projectUsers = (await database.collections
    .get('project_users')
    .query(Q.where('userId', Q.oneOf(userIds.map(Number))))
    .fetch()) as ProjectUserModel[];

  const workspaceGroupsMap = new Map(workspaceGroups.map(wg => [wg.remoteId, wg]));
  const requestForSignaturesMap = new Map();
  const projectUsersMap = new Map(projectUsers.map(pu => [pu.userId, pu]));
  const workspaceGroupsUsersMap = workspaceGroupsUsers.reduce((map, wgu) => {
    if (!map.has(wgu.workspaceGroupId)) map.set(wgu.workspaceGroupId, []);
    const projectUser = projectUsersMap.get(wgu.userId);
    if (map.get(wgu.workspaceGroupId)) {
      map.get(wgu.workspaceGroupId)!.push(projectUser || null);
    }
    return map;
  }, new Map<string, (ProjectUserModel | null)[]>());

  requestForSignatures.forEach(rfs => {
    const key = rfs.projectDocumentId || rfs.localProjectDocumentId;
    if (!requestForSignaturesMap.has(key)) {
      requestForSignaturesMap.set(key, []);
    }
    requestForSignaturesMap.get(key).push(rfs);
  });

  return documents.map(doc => {
    const workspaceParent = workspaceGroupsMap.get(doc.workspaceGroupId || -1) || null;
    const workspaceAssignees = requestForSignaturesMap.get(!doc.remoteId ? doc.id : doc.remoteId) || [];
    const workspaceWithAssignees = workspaceAssignees
      //? Disabled this to ensure can request ammend to owner
      // .filter((assignee: RequestForSignature) => assignee.assigneeNo !== 1)
      .map((assignee: RequestForSignature) => ({
        ...assignee._raw,
        ...(projectUsersMap.get(assignee.signById)?._raw || {})
      }));

    const workspaceGroupUsers = workspaceGroupsUsersMap.get(workspaceParent?.workspaceGroupId || '') || [];

    return {
      ...doc,
      workspaceParent,
      workspaceWithAssignees,
      workspaceGroupUsers
    } as unknown as DocumentWithSync;
  });
};

const getProjectDocumentWithPresignedUrl = async (
  documents: DocumentWithSync[],
  queryConditions: any
): Promise<DocumentWithSync[]> => {
  if (queryConditions.category !== Gql.CategoryType.Photo) return documents;

  let updatedDocuments = [];

  const response = await apolloClient
    .query({
      query: Gql.ProjectDocumentsDocument,
      variables: {
        filter: {
          category: {
            eq: Gql.CategoryType.Photo
          },
          projectId: { eq: queryConditions.projectId ?? '' },
          fileSystemType: { eq: Gql.FileSystemType.Document },
          id: {
            in: documents
              .map(photo => photo.remoteId)
              .filter(Boolean)
              .map(String)
          }
        }
      }
    })
    .catch(error => {});

  const serverPhotos = response?.data?.projectDocuments?.nodes || [];

  for (const localPhoto of documents) {
    const serverPhoto = serverPhotos.find((sp: { id: string }) => parseInt(sp.id) === localPhoto.remoteId);
    updatedDocuments.push(serverPhoto ? { ...localPhoto, fileUrl: serverPhoto.fileUrl } : localPhoto);
  }

  return updatedDocuments as DocumentWithSync[];
};

const isDocumentUnsynced = async (projectDocument: DocumentWithSync): Promise<boolean> => {
  const { id } = projectDocument;

  if (projectDocument._status !== 'synced') {
    return true;
  }

  const [
    isRequestForSignatureUnsynced,
    isWorkspaceCcUnsynced,
    isAttachmentUnsynced,
    isPhotosUnsynced,
    isDocumentUnsynced,
    isCommentUnsynced
  ] = await Promise.all([
    RequestForSignaturesModel.isRequestForSignatureUnsynced(id),
    WorkspaceCcModel.isWorkspaceCcUnsynced(id),
    WorkspaceAttachmentModel.isAttachmentUnsynced(id),
    WorkspacePhotoModel.isPhotosUnsynced(id),
    WorkspaceDocumentModel.isDocumentUnsynced(id),
    WorkspaceCommentModel.isCommentUnsynced(id)
  ]);

  return (
    isRequestForSignatureUnsynced ||
    isWorkspaceCcUnsynced ||
    isAttachmentUnsynced ||
    isPhotosUnsynced ||
    isDocumentUnsynced ||
    isCommentUnsynced
  );
};

export const getProjectDocuments = async (
  filteredValue: string,
  filterItems: any,
  page: number = 1,
  limit: number = 10,
  data: any
): Promise<PaginatedResults<DocumentWithSync>> => {
  const hasValue = Object.values(filterItems).some(value => value !== undefined && value !== null && value !== false);

  try {
    const { sql: queryConditions, params } = buildQueryConditions(
      data,
      filteredValue,
      data.sorting,
      page,
      hasValue ? 9999 : limit,
      filterItems
    );

    let results = (await fetchDocuments(
      database,
      'project_documents',
      queryConditions,
      params
    )) as unknown as DocumentWithSync[];

    if (data.category === Gql.CategoryType.AllForm) {
      results = await enhanceDocumentsWithWorkspace(results, filterItems, data);
      for (let doc of results) {
        doc.isUnsync = await isDocumentUnsynced(doc);
      }
    } else if (data.category === Gql.CategoryType.TwoDDrawings) {
      for (let doc of results) {
        doc.isUnsync = await DrawingRevisionModel.isRevisionUnsyced(doc.id);
      }
    }

    const paginatedResultsWithPresigned = await getProjectDocumentWithPresignedUrl(results, data);

    return {
      results: data.category !== Gql.CategoryType.Photo ? results : paginatedResultsWithPresigned,
      nextPage: results.length === limit ? page + 1 : undefined,
      length: results.length
    };
  } catch (error) {
    return { results: [], error: 'Failed to process the request.', length: 0 };
  }
};

export const getProjectDocumentDetail = async (id: string, projectId: number): Promise<any> => {
  try {
    const isNumeric = /^\d+$/.test(id);
    const queryKey = isNumeric ? 'remoteId' : 'id';
    const { sql: queryConditions, params } = buildQueryConditions({ [queryKey]: id, projectId }, '');
    const results = await fetchDocuments(database, 'project_documents', queryConditions, params);

    if (results.length === 0) {
      return null;
    }

    const document = results[0];
    const foreignKey = document.remoteId ? document.remoteId : document.id;

    const [createdBy, requestForSignature, workspaceCcs, group, attachments, photos, linkedToDocument, comments] =
      await Promise.all([
        ProjectUserModel.getUser(document.addedBy),
        RequestForSignaturesModel.getRequestForSignatures(foreignKey),
        WorkspaceCcModel.getWorkspaceCcs(foreignKey),
        WorkspaceGroupModel.getWorkspaceGroup(document.workspaceGroupId),
        WorkspaceAttachmentModel.getWorkspaceAttachment(foreignKey),
        WorkspacePhotoModel.getWorkspacePhotos(foreignKey),
        WorkspaceDocumentModel.getWorkspaceDocument(foreignKey),
        WorkspaceCommentModel.getWorkspaceDocumentComments(foreignKey)
      ]);

    const datas = {
      document: document,
      createdBy: createdBy,
      requestForSignature,
      workspaceCcs,
      status: document.status,
      group: group,
      attachments,
      photos,
      linkedToDocument,
      comments
    };

    return datas;
  } catch (error) {
    return null;
  }
};

export const getProjectDocumentById = async (id: string): Promise<ProjectDocumentEntity | null> => {
  try {
    return (await database.collections.get('project_documents').find(id)) as unknown as ProjectDocumentEntity;
  } catch (error) {
    return null;
  }
};

export const getProjectDocumentAndChildren = async (id: string): Promise<any> => {
  try {
    const results = [];
    const projectDocument: any = await getProjectDocumentById(id);

    if (!projectDocument) {
      return null;
    }

    results.push(projectDocument);

    if (projectDocument.category === Gql.CategoryType.TwoDDrawings) {
      const drawingRevisions = await database.collections
        .get('drawing_revisions')
        .query(Q.where('projectDocumentId', Q.eq(parseInt(projectDocument.remoteId))))
        .fetch();

      results.push(...drawingRevisions);
    }

    if (projectDocument.category === Gql.CategoryType.AllForm) {
      const workspaceAttachments = await database.collections
        .get('workspace_attachments')
        .query(Q.where('projectDocumentId', Q.eq(parseInt(projectDocument.remoteId))))
        .fetch();

      const workspacePhotos = await database.collections
        .get('workspace_photos')
        .query(Q.where('projectDocumentId', Q.eq(parseInt(projectDocument.remoteId))))
        .fetch();

      results.push(...workspaceAttachments);
      results.push(...workspacePhotos);
    }

    return results;
  } catch (error) {
    return null;
  }
};

export const countDocumentsByStatus = async (
  projectId: number,
  groupId: number | undefined,
  status: string
): Promise<number> => {
  const queryConditions = [Q.where('projectId', Q.eq(projectId))];

  if (groupId) {
    queryConditions.push(Q.where('workspaceGroupId', Q.eq(groupId)));
  }

  queryConditions.push(Q.where('status', Q.eq(status)));

  return await database.collections
    .get('project_documents')
    .query(...queryConditions)
    .fetchCount();
};

export const getProjectDocumentSummary = async (
  projectId: number,
  groupId?: number
): Promise<
  | {
      approvedWorkspaceTasks: number;
      inProgressWorkspaceTasks: number;
      inReviewWorkspaceTasks: number;
      name: string | null;
      pendingWorkspaceTasks: number;
      rejectedWorkspaceTasks: number;
      submittedWorkspaceTasks: number;
    }
  | undefined
> => {
  try {
    const [
      approvedWorkspaceTasks,
      inProgressWorkspaceTasks,
      inReviewWorkspaceTasks,
      pendingWorkspaceTasks,
      rejectedWorkspaceTasks,
      submittedWorkspaceTasks
    ] = await Promise.all([
      countDocumentsByStatus(projectId, groupId, 'Approved'),
      countDocumentsByStatus(projectId, groupId, 'In Progress'),
      countDocumentsByStatus(projectId, groupId, 'In Review'),
      countDocumentsByStatus(projectId, groupId, 'Pending'),
      countDocumentsByStatus(projectId, groupId, 'Rejected'),
      countDocumentsByStatus(projectId, groupId, 'Submitted')
    ]);

    const summary: any = {
      approvedWorkspaceTasks,
      inProgressWorkspaceTasks,
      inReviewWorkspaceTasks,
      name: null,
      pendingWorkspaceTasks,
      rejectedWorkspaceTasks,
      submittedWorkspaceTasks
    };

    return summary;
  } catch (error) {
    return undefined;
  }
};

export const getSiteDiary = async (projectId: string | number): Promise<ProjectDocumentEntity[] | null> => {
  try {
    const projectDocumentsCollection = database.collections.get('project_documents');
    const workspaceGroupCollection = database.collections.get('workspace_groups');

    const defaultGroup = (await workspaceGroupCollection
      .query(Q.and(Q.where('projectId', Q.eq(String(projectId))), Q.where('name', Q.eq('Site Diary'))))
      .fetch()) as WorkspaceGroup[];

    if (defaultGroup.length === 0) {
      return null;
    }

    const siteDiarGroupId = defaultGroup[0].remoteId;

    if (!siteDiarGroupId) {
      return null;
    }

    const query = Q.and(
      Q.where('projectId', Q.eq(String(projectId))),
      Q.where('workspaceGroupId', Q.eq(String(siteDiarGroupId))),
      Q.where('status', Q.notIn(['Draft', 'Rejected']))
    );

    const siteDiaries = (await projectDocumentsCollection.query(query as any).fetch()) as ProjectDocumentEntity[];

    return siteDiaries;
  } catch (error) {
    throw error;
  }
};

export const getTotalTemplatesForProject = async (projectId: string | number): Promise<number> => {
  const queryConditions = [
    Q.where('projectId', Q.eq(String(projectId))),
    Q.where('category', Q.eq(Gql.CategoryType.StandardForm))
  ];

  try {
    const count = await database.collections
      .get('project_documents')
      .query(...queryConditions)
      .fetchCount();
    return count > 0 ? count : 1;
  } catch (error) {
    return 1;
  }
};

export const getAdjacentDocument = async (
  currentId: string,
  direction: 'next' | 'previous',
  projectId: number,
  category: string,
  projectDocumentId: number | string
): Promise<any> => {
  try {
    if (!currentId || !projectId || !category) {
      return undefined;
    }

    const baseQuery = [
      Q.where('projectId', Q.eq(projectId)),
      Q.where('category', Q.eq(category)),
      Q.where('fileSystemType', Q.notEq('Folder'))
    ];

    const docIdQuery =
      typeof projectDocumentId === 'string'
        ? [
            ...baseQuery,
            Q.or(
              Q.where('localProjectDocumentId', Q.eq(projectDocumentId)),
              Q.where('projectDocumentId', Q.eq(projectDocumentId))
            )
          ]
        : [...baseQuery, Q.where('projectDocumentId', Q.eq(projectDocumentId))];

    const documents = await database.collections
      .get('project_documents')
      .query(...docIdQuery)
      .fetch();

    if (!documents.length) return undefined;

    const currentIndex = documents.findIndex(doc => doc.id === currentId);
    if (currentIndex === -1) return undefined;

    const targetIndex = direction === 'next' ? currentIndex + 1 : currentIndex - 1;

    if (targetIndex < 0 || targetIndex >= documents.length) {
      return undefined;
    }

    return documents[targetIndex]._raw;
  } catch (error) {
    return undefined;
  }
};

export const getUnsyncAttachmentMedia = async (id: string): Promise<Boolean> => {
  try {
    // Run all checks concurrently using Promise.all
    const [hasUnsyncAttachments, hasUnsyncMedia, hasUnsyncDocuments] = await Promise.all([
      WorkspaceAttachmentModel.isAttachmentUnsynced(id),
      WorkspacePhotoModel.isPhotosUnsynced(id),
      WorkspaceDocumentModel.isDocumentUnsynced(id)
    ]);

    // Return true if any of the checks found unsync items
    return hasUnsyncAttachments || hasUnsyncMedia || hasUnsyncDocuments;
  } catch (error) {
    return false;
  }
};
