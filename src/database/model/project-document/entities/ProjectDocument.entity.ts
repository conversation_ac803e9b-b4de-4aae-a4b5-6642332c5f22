import { date, field, json, text } from '@nozbe/watermelondb/decorators';
import BaseModel from '../../base-model';
import { sanitizer } from '@src/configs/utils';

export class ProjectDocumentEntity extends BaseModel {
  static table = 'project_documents';

  @field('remoteId') remoteId?: number | null;
  @text('localRemoteId') localRemoteId?: string | null;
  @field('projectId') projectId!: number;
  @field('projectDocumentId') projectDocumentId?: number | null;
  @field('addedBy') addedBy?: number | null;
  @field('formCategoryId') formCategoryId?: number | null;
  @field('workspaceGroupId') workspaceGroupId?: number | null;
  @text('name') name!: string;
  @text('description') description?: string | null;
  @text('fileSystemType') fileSystemType?: string | null;
  @text('driveType') driveType!: string;
  @text('fileUrl') fileUrl?: string | null;
  @text('obsUrl') obsUrl?: string | null;
  @field('obsFileSize') obsFileSize?: number | null;
  @text('obsFileType') obsFileType?: string | null;
  @text('type') type?: string | null;
  @field('fileSize') fileSize?: number | null;
  @text('status') status?: string | null;
  @text('category') category?: string | null;
  @text('fileChannel') fileChannel!: string;
  @text('xfdf') xfdf?: string | null;
  @text('notes') notes?: string | null;
  @text('versionName') versionName?: string | null;
  @field('allFormCode') allFormCode?: number | null;
  @text('watermarkId') watermarkId?: string | null;
  @text('uploadLatitude') uploadLatitude?: string | null;
  @text('uploadLongitude') uploadLongitude?: string | null;
  @text('uploadAddress') uploadAddress?: string | null;
  @text('videoThumbnail') videoThumbnail?: string | null;
  @field('isDocsStored') isDocsStored!: boolean;
  @date('submittedAt') submittedAt?: number | null;
  @field('groupCode') groupCode?: string | null;
  @field('currentUserId') currentUserId?: number | null;
  @field('localCurrentUserId') localCurrentUserId?: string | null;
  @text('workflow') workflow?: string | null;
  @field('localFileUrl') localFileUrl?: string | null;
  @field('fileKey') fileKey?: string | null;
  @field('server_created_at') server_created_at?: string | null;
  @field('server_updated_at') server_updated_at?: string | null;
  @field('isQueued') isQueued?: boolean;
  @field('download_retry') download_retry!: number;
  @date('autosavedAt') autosavedAt?: number | null;
  @json('assigneeIds', sanitizer) assigneeIds?: string | null;

  // Helper method to mark document as synced
  markAsSynced() {
    return this.update(() => {
      this._raw._status = 'synced';
      this._raw._changed = '';
    });
  }
}
