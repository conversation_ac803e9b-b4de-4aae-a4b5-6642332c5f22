import { Model } from '@nozbe/watermelondb';
import { _RawRecord } from '@nozbe/watermelondb/RawRecord';
import { SyncStatus } from '@nozbe/watermelondb/Model';
import ProjectUserModel from '../project-user.model';

export interface WorkspaceGroup extends Model {
  remoteId: number;
  workspaceGroupId: string;
}

export interface WorkspaceGroupUser extends Model {
  workspaceGroupId: string;
  userId: number;
}

export interface RequestForSignature extends Model {
  projectDocumentId: number | null;
  localProjectDocumentId: string | null;
  signById: number;
  assigneeNo: number;
}

export interface PaginatedResults<T> {
  length: number;
  results: T[];
  nextPage?: number;
  error?: string;
}

// Base properties for a document
export interface DocumentBase {
  id: string;
  remoteId?: number;
  workspaceGroupId?: number;
  isUnsync?: boolean;
  _raw: any;
  _status: SyncStatus;
  collection?: any;
  database?: any;
}

// Enhanced document with workspace-related properties
export interface EnhancedDocument extends DocumentBase {
  workspaceParent?: WorkspaceGroup | null;
  workspaceWithAssignees?: any[];
  workspaceGroupUsers?: (ProjectUserModel | null)[];
}

// Type used in database operations
export type DocumentWithSync = Model & DocumentBase;

// Raw type for project document
export interface RawProjectDocument extends _RawRecord {
  localFileUrl?: string;
  download_retry?: number;
  remoteId?: number;
  workspaceGroupId?: number;
  projectId: number;
  name: string;
  fileName?: string;
  fileUrl?: string;
  category?: string;
  tableName?: string;
  _changed: string;
}

// Project document model type
export interface ProjectDocument extends Model {
  _raw: RawProjectDocument;
  id: string;
  remoteId?: number;
  workspaceGroupId?: number;
  projectId: number;
  name: string;
  fileName?: string;
  fileUrl?: string;
  category?: string;
  tableName?: string;
  download_retry: number;
  syncStatus: SyncStatus;
}
