import { ProjectDocumentEntity } from './entities';
import * as queries from './queries';
import * as mutations from './mutations';

export class ProjectDocumentModel extends ProjectDocumentEntity {
  // Existing query methods
  static getProjectDocuments = queries.getProjectDocuments;
  static getLocalId = queries.getLocalId;
  static getAllDrawings = queries.getAllDrawings;
  static getAllTemplates = queries.getAllTemplates;

  // Additional query methods
  static getProjectDocumentDetail = queries.getProjectDocumentDetail;
  static getProjectDocumentById = queries.getProjectDocumentById;
  static getProjectDocumentAndChildren = queries.getProjectDocumentAndChildren;
  static countDocumentsByStatus = queries.countDocumentsByStatus;
  static getProjectDocumentSummary = queries.getProjectDocumentSummary;
  static getSiteDiary = queries.getSiteDiary;
  static getTotalTemplatesForProject = queries.getTotalTemplatesForProject;
  static getAdjacentDocument = queries.getAdjacentDocument;
  static getUnsyncAttachmentMedia = queries.getUnsyncAttachmentMedia;

  // Mutation methods
  static createProjectDocs = mutations.createProjectDocs;
  static updateProjectDocs = mutations.updateProjectDocs;
  static updateDownloadedDocs = mutations.updateDownloadedDocs;
  static updateWorkspaceDocument = mutations.updateWorkspaceDocument;
  static saveProjectDocument = mutations.saveProjectDocument;
  static incrementDownloadRetry = mutations.incrementDownloadRetry;
  static deleteDocument = mutations.deleteDocument;
}

export default ProjectDocumentModel;
