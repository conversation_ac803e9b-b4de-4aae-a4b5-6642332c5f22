import { field, writer } from '@nozbe/watermelondb/decorators';
import BaseModel from './base-model';
import { CreateTaskMedia, TaskMedia } from 'task-media';
import database from '../index.native';
import { storeBinaryFile } from '@src/configs/async-storage';

class TasksMediaModel extends BaseModel {
  static table = 'tasks_medias';

  @field('remoteId') remoteId?: number | null;
  @field('taskId') taskId!: number;
  @field('userId') userId!: number;
  @field('name') name!: string;
  @field('fileUrl') fileUrl?: string | null;
  @field('type') type?: string | null;
  @field('localTaskId') localTaskId?: string | null;

  @writer async createTaskMedia(newMedia: CreateTaskMedia[]) {
    try {
      if (!newMedia || newMedia.length === 0) return;

      newMedia.forEach(async (media: CreateTaskMedia) => {
        await storeBinaryFile(media.name, media);
      });

      const createdMedia = newMedia.map((media: CreateTaskMedia) => {
        return this.collections.get('tasks_medias').prepareCreate((taskMedia: any) => {
          taskMedia.remoteId = null;
          taskMedia.taskId = media.taskId;
          taskMedia.userId = media.userId;
          taskMedia.name = media.name;
          taskMedia.fileUrl = media.uri;
          taskMedia.type = media.type ?? null;
          taskMedia._raw._status = 'created';
          taskMedia.localTaskId = media.localTaskId;
          taskMedia.recordSource = 'OfflineApp';
        });
      });

      await this.database.batch(...createdMedia);

      return createdMedia;
    } catch (error) {
      throw error;
    }
  }

  static async deleteMedia(mediaId: string) {
    try {
      return await database.write(async () => {
        const media = await database.collections.get(TasksMediaModel.table).find(mediaId);
        await media.markAsDeleted();
      });
    } catch (error) {
      throw error;
    }
  }
}

export default TasksMediaModel;
