import { field, relation } from '@nozbe/watermelondb/decorators';
import BaseModel from './base-model';
import { Q } from '@nozbe/watermelondb';
import database from '../index.native';

interface WorkspaceGroupUserRaw {
  userId: number | null;
  workspaceGroupId: number | null;
  remoteId: number | null;
  [key: string]: any; // Optional, for other fields in the table
}

export default class WorkspaceGroupUserModel extends BaseModel {
  static table = 'workspace_group_users';

  @field('userId') userId?: number | null;
  @field('workspaceGroupId') workspaceGroupId?: number | null;
  @field('remoteId') remoteId!: number | null;

  static async getWorkspaceGroupUser(workspaceGroupId: number) {
    try {
      if (!workspaceGroupId) {
        throw new Error('Workspace group id is required');
      }

      // Fetch workspace group users
      const workspaceGroupUsers = await database.collections
        .get('workspace_group_users')
        .query(Q.where('workspaceGroupId', workspaceGroupId))
        .unsafeFetchRaw();

      if (workspaceGroupUsers.length === 0) {
        console.warn(`No workspace group users found for group ID: ${workspaceGroupId}`);
        return [];
      }

      // Collect userIds for batch fetching project users
      const userIds = workspaceGroupUsers.map(user => user.userId).filter(Boolean); // Remove null/undefined

      // Fetch all related project users in one query
      const projectUsers = await database.collections
        .get('project_users')
        .query(Q.where('userId', Q.oneOf(userIds)))
        .unsafeFetchRaw();

      // Create a map for quick lookup of project users by userId
      const projectUserMap = projectUsers.reduce(
        (map, user) => {
          map[user.userId] = user;
          return map;
        },
        {} as Record<number, any>
      );

      // Combine workspace group users with their related project users
      const sanitizedWorkspaceGroupUsers = workspaceGroupUsers.map(workspaceGroupUser => {
        const user = projectUserMap[workspaceGroupUser.userId] || null; // Get user or null if not found
        return {
          ...workspaceGroupUser, // Keep raw workspace group user fields
          user // Add related project user
        };
      });

      return sanitizedWorkspaceGroupUsers;
    } catch (error) {
      throw new Error(`Failed to fetch workspace group users: ${error}`);
    }
  }

  static async getAccessibleWorkspaceGroupIds(userId: any, projectId: any) {
    const parsedUserId = parseInt(userId);
    const parsedProjectId = parseInt(projectId);

    // Step 1: Fetch parent groups
    const parentGroups = await database.collections
      .get('workspace_groups')
      .query(
        Q.where('projectId', parsedProjectId),
        Q.where('workspaceGroupId', null) // Only parent groups
      )
      .fetch();

    const workspaceGroupIds = parentGroups.map(group => group?._raw?.remoteId);

    if (workspaceGroupIds.length === 0) {
      return []; // Early exit if no parent groups exist
    }

    // Step 2: Fetch all relevant workspace group users
    const allGroupUsers = await database.collections
      .get('workspace_group_users')
      .query(
        Q.where('workspaceGroupId', Q.oneOf(workspaceGroupIds)),
        Q.or(
          Q.where('userId', parsedUserId), // User's group IDs
          Q.where('workspaceGroupId', Q.oneOf(workspaceGroupIds)) // Groups accessible via workspaceGroupIds
        )
      )
      .fetch();

    // Step 3: Partition data into user-specific and accessible groups
    const userWorkspaceGroupIds = new Set();
    const accessibleGroupIds = new Set();

    allGroupUsers.forEach(groupUser => {
      const groupId = groupUser?._raw?.workspaceGroupId;
      if (groupUser?._raw?.userId === parsedUserId) {
        userWorkspaceGroupIds.add(groupId);
      } else {
        accessibleGroupIds.add(groupId);
      }
    });

    // Filter out user-specific groups from accessible groups
    const filteredAccessibleGroupIds = Array.from(accessibleGroupIds).filter(id => !userWorkspaceGroupIds.has(id));

    if (filteredAccessibleGroupIds.length === 0) {
      return []; // Early exit if no accessible groups
    }

    // Step 4: Fetch child groups of accessible parent groups
    const childGroups = await database.collections
      .get('workspace_groups')
      .query(Q.where('workspaceGroupId', Q.oneOf(filteredAccessibleGroupIds)))
      .fetch();

    const childGroupIds = childGroups.map(group => group?._raw?.remoteId);

    // Combine parent and child accessible group IDs
    const finalAccessibleWorkspaceGroupIds = [
      ...new Set([...filteredAccessibleGroupIds, ...childGroupIds]) // Ensure uniqueness
    ];

    return finalAccessibleWorkspaceGroupIds;
  }
}
