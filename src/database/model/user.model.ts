import { field } from '@nozbe/watermelondb/decorators';
import BaseModel from './base-model';
import database from '../index.native';
import apolloClient from '@src/lib/apollo';
import { Gql } from '@src/api';
import { Q } from '@nozbe/watermelondb';
import storeFileLocally from '@src/configs/saveOffline';
import AsyncStorage from '@react-native-async-storage/async-storage';

class UserModel extends BaseModel {
  static table = 'users';

  @field('remoteId') remoteId?: number | null;
  @field('name') name!: string;
  @field('phoneNo') phoneNo?: string | null;
  @field('stampUrl') stampUrl?: string | null;
  @field('isReadChangeLogMobile') isReadChangeLogMobile?: boolean | null;
  @field('signUrl') signUrl?: string | null;
  @field('fontSize') fontSize?: number | null;
  @field('stampAndSignUrl') stampAndSignUrl?: string | null;
  @field('stampKey') stampKey?: string | null;
  @field('signKey') signKey?: string | null;
  @field('stampAndSignKey') stampAndSignKey?: string | null;
  @field('drawing_sync') drawing_sync!: boolean;

  /**
   * Toggles the drawing sync state for the first user.
   */
  static async toggleDrawingSync(state: boolean): Promise<UserModel> {
    return database.write(async () => {
      const users = await database.collections.get<UserModel>('users').query().fetch();
      if (!users.length) {
        throw new Error('No users found in the database.');
      }

      const user = users[0];
      return user.update(record => {
        record.drawing_sync = state;
      });
    });
  }

  /**
   * Downloads the stamp and signature URLs for a user, stores them locally if they exist, and updates the database.
   * If any URL is missing or storing fails, it assigns null and continues.
   */
  static async downloadStampAndSignUrl(userId: string): Promise<any> {
    try {
      // Fetch user from the database
      const users = await this.fetchUsersByRemoteId(userId);
      if (!users.length) {
        return null;
      }
      const user = users[0];

      // Check if stampUrl and signUrl already exist
      if (user.stampUrl && user.signUrl) {
        return null;
      }

      const response = await apolloClient.query({
        query: Gql.GetUserMeDocument,
        variables: { id: userId }
      });

      const { signUrl: remoteSignUrl, stampUrl: remoteStampUrl, company } = response.data.getUserMe;
      const companyStampUrl = company?.stampUrl;
      const subPath = `${response.data.getUserMe.id}`;

      // Use ternary operators with .catch() to assign null if storing fails
      const localSignUrl = remoteSignUrl
        ? await storeFileLocally(remoteSignUrl, subPath, 'UserSignature.png').catch(error => {
            return null;
          })
        : null;

      const localStampUrl = remoteStampUrl
        ? await storeFileLocally(remoteStampUrl, subPath, 'UserStamp.png').catch(error => {
            return null;
          })
        : null;

      const localCompanyStampUrl = companyStampUrl
        ? await storeFileLocally(companyStampUrl, subPath, 'CompanyStamp.png').catch(error => {
            return null;
          })
        : null;

      // Update the user record with the (possibly null) local URLs
      await database.write(async () => {
        await user.update(record => {
          record.signUrl = localSignUrl;
          record.stampUrl = localStampUrl;
          record._raw._status = 'synced';
          record._raw._changed = '';
        });
      });

      const updatedUser = await this.updateUserInAsyncStorage({
        signUrl: localSignUrl,
        stampUrl: localStampUrl
      });

      return updatedUser;
    } catch (error) {
      return null;
    }
  }

  /**
   * Fetches all users from the database.
   */
  static async fetchAllUsers(): Promise<UserModel[]> {
    return database.collections.get<UserModel>('users').query().fetch();
  }

  /**
   * Fetches users by their remote ID.
   */
  static async fetchUsersByRemoteId(remoteId: string): Promise<UserModel[]> {
    return database.collections
      .get<UserModel>('users')
      .query(Q.where('remoteId', parseInt(remoteId)))
      .fetch();
  }

  /**
   * Updates user data in AsyncStorage.
   */
  private static async updateUserInAsyncStorage(updates: Partial<UserModel>): Promise<any> {
    const storedUser = await AsyncStorage.getItem('@user');
    if (storedUser) {
      const parsedUser = JSON.parse(storedUser);
      const updatedUser = { ...parsedUser, ...updates };
      await AsyncStorage.setItem('@user', JSON.stringify(updatedUser));
      return updatedUser;
    } else {
      return null;
    }
  }
}

export default UserModel;
