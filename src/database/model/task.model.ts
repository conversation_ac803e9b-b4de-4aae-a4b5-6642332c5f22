import field from '@nozbe/watermelondb/decorators/field';
import BaseModel from './base-model';
import { json, relation, writer } from '@nozbe/watermelondb/decorators';
import Query from '@nozbe/watermelondb/Query';
import ProjectGroupModel from './project-group.model';
import { Q } from '@nozbe/watermelondb';
import database from '../index.native';
import { CreateTaskInput, MergedTask, TaskDetail } from 'task';
import TasksAttachmentModel from './task-attachment.model';
import TasksMediaModel from './task-media.model';
import ProjectUserModel from './project-user.model';
import TasksCommentModel from './task-comment.model';
import { Filter } from '@src/slice/tasks.slice';
import { sanitizer } from '@src/configs/utils';

interface AssigneeDetail {
  id: number;
  url?: string;
  userId: number;
}

interface PaginatedResult {
  tasks: Promise<MergedTask[]>;
  nextPage: number | null; // null if there's no next page
}

class TaskModel extends BaseModel {
  static table = 'tasks';

  @field('remoteId') remoteId?: number;
  @field('projectId') projectId!: string;
  @field('ownerId') ownerId!: number;
  @field('groupId') groupId?: number;
  @field('taskCode') taskCode!: number;
  @field('title') title!: string;
  @field('annotId') annotId?: string;
  @field('spriteId') spriteId?: string;
  @field('elementId') elementId?: string;
  @field('pos3D') pos3D?: string;
  @field('bimId') bimId?: string;
  @field('imageURL') imageURL?: string;
  @field('description') description?: string;
  @field('dueDate') dueDate?: string;
  @field('status') status!: string;
  @field('isSentBeforeOneDay') isSentBeforeOneDay!: boolean;
  @field('isSentBeforeOneHour') isSentBeforeOneHour!: boolean;
  @field('permanentlyDeleted') permanentlyDeleted!: boolean;
  @field('proposedStatus') proposedStatus?: string | null;
  @field('isUrgent') isUrgent?: boolean;
  @field('memoUrl') memoUrl?: string;
  @field('previewMemoUrl') previewMemoUrl?: string | null;
  @field('isMemoReceive') isMemoReceive?: boolean;
  @field('issuedById') issuedById!: number;

  @field('localMemoUrl') localMemoUrl?: string | null;

  @json('assignees', sanitizer) assignees?: string;
  @json('copies', sanitizer) copies?: string;
  @json('documents', sanitizer) documents?: string;

  // ======================== RELATION ========================

  @relation('project_groups', 'groupId') projectGroup?: Query<ProjectGroupModel>;

  // ======================== FUNCTION ========================

  static async getTask(taskId: string): Promise<TaskModel | null> {
    try {
      return await database.collections.get<TaskModel>('tasks').find(taskId);
    } catch (error) {
      throw error;
    }
  }

  static async fetchTasksPaginated(
    page: number = 1,
    limit: number = 10,
    projectId: string,
    filterItems: Filter,
    filteredValue: string,
    userId: string
  ): Promise<PaginatedResult> {
    try {
      const offset = (page - 1) * limit;

      let queryConditions: any = [
        Q.where('projectId', Q.eq(parseInt(projectId, 10))),
        Q.where('title', Q.like(`%${Q.sanitizeLikeString(filteredValue ?? '')}%`))
      ];

      queryConditions.push(Q.sortBy('isUrgent', Q.desc));

      if (filterItems.id) {
        queryConditions.push(Q.where('taskCode', parseInt(filterItems.id)));
      }

      if (filterItems.hideCompleted) {
        queryConditions.push(Q.where('status', Q.notEq('Completed')));
      }

      if (filterItems.status && filterItems.status.length > 0) {
        queryConditions.push(Q.where('status', Q.oneOf(filterItems.status)));
      }

      if (filterItems.group?.id) {
        queryConditions.push(Q.where('groupId', parseInt(filterItems.group.id)));
      }

      if (filterItems?.date !== undefined) {
        if (filterItems.date === 'Ascending') {
          queryConditions.push(Q.sortBy('created_at', Q.asc));
        }
      } else {
        queryConditions.push(Q.sortBy('created_at', Q.desc));
      }

      let unsyncedTasks = await database.collections
        .get('tasks')
        .query(...queryConditions, Q.where('remoteId', Q.eq(null)))
        .fetch();

      let neededSyncedTasks = limit - unsyncedTasks.length;

      let syncedTasks: any = [];
      if (neededSyncedTasks > 0) {
        syncedTasks = await database.collections
          .get<TaskModel>('tasks')
          .query(
            ...queryConditions,
            Q.where('remoteId', Q.notEq(null)),
            Q.sortBy('taskCode', Q.desc),
            Q.skip(offset - unsyncedTasks.length),
            Q.take(neededSyncedTasks)
          )
          .fetch();
      }

      const tasks = [...unsyncedTasks, ...syncedTasks];

      const hasNextPage = tasks.length === limit;
      let filteredTasks = tasks;

      if (filterItems.assignee && filterItems.assignee.length > 0) {
        filteredTasks = tasks.filter(task =>
          task?.assignees?.some?.((aid: any) => filterItems?.assignee?.includes(aid))
        );
      }

      if (filterItems.cc && filterItems.cc.length > 0) {
        filteredTasks = tasks.filter(task => task?.copies?.some((aid: any) => filterItems?.cc?.includes(aid)));
      }

      if (filterItems.assignToMe) {
        const projectUser = await database.collections
          .get<ProjectUserModel>('project_users')
          .query(Q.where('userId', parseInt(userId)), Q.where('projectId', parseInt(projectId)))
          .fetch();

        if (projectUser.length === 0) {
          return {
            tasks: [],
            nextPage: null
          };
        }

        const currentProjectUser = projectUser[0];
        filteredTasks = tasks.filter(task => task?.assignees?.includes(currentProjectUser?.remoteId?.toString() || ''));
      }

      const assigneeDetails = await Promise.all(
        filteredTasks?.map(async task => {
          const assigneeIds = task?.assignees?.map?.(Number) || [];
          const users = await database.collections
            .get<ProjectUserModel>('project_users')
            .query(Q.where('remoteId', Q.oneOf(assigneeIds)), Q.where('projectId', task.projectId))
            .fetch();

          return {
            ...task._raw,
            assignees: users
          };
        })
      );

      return {
        tasks: assigneeDetails,
        nextPage: hasNextPage ? page + 1 : null
      };
    } catch (error) {
      throw error; // Re-throw the error to handle it in the caller function or log it persistently
    }
  }

  async getTaskDetails(): Promise<TaskDetail> {
    try {
      if (!this.groupId) {
        throw new Error('Failed to get task due to missing identifiers');
      }

      const parseIds = (ids: string | null): number[] => {
        try {
          return JSON.parse(ids || '[]')
            .map(Number)
            .filter((id: number) => id > 0);
        } catch (error) {
          return [];
        }
      };

      //@ts-ignore
      const assigneeIds = parseIds(this._raw.assignees);
      //@ts-ignore
      const copiesIds = parseIds(this._raw.copies);
      //@ts-ignore
      const documentsIds = parseIds(this._raw.documents);

      const fetchDetails = async (
        collectionName: string,
        field: string,
        ids: number[],
        additionalQuery?: any
      ): Promise<any[]> => {
        if (ids.length === 0) {
          return [];
        }
        const collection = database.collections.get(collectionName);
        let query = collection.query(Q.where(field, Q.oneOf(ids)));
        if (additionalQuery) {
          query = query.extend(additionalQuery);
        }
        return await query.fetch();
      };

      const taskIdValue = this.remoteId ? this.remoteId : this.id;
      const taskJoinField = this.remoteId ? 'taskId' : 'localTaskId';

      const [group, assigneesDetail, copiesDetail, attachmentsDetail, mediaDetail, owner, comments, documents] =
        await Promise.all([
          database.collections
            .get<ProjectGroupModel>('project_groups')
            .query(Q.where('remoteId', this.groupId))
            .fetch(),
          fetchDetails('project_users', 'remoteId', assigneeIds, Q.and(Q.where('projectId', this.projectId))),
          fetchDetails('project_users', 'remoteId', copiesIds, Q.and(Q.where('projectId', this.projectId))),
          database.collections
            .get<TasksAttachmentModel>('tasks_attachments')
            .query(Q.where(taskJoinField, taskIdValue))
            .fetch(),
          database.collections.get<TasksMediaModel>('tasks_medias').query(Q.where(taskJoinField, taskIdValue)).fetch(),
          database.collections.get<ProjectUserModel>('project_users').query(Q.where('userId', this.ownerId)).fetch(),
          database.collections.get<TasksCommentModel>('task_comments').query(Q.where('taskId', taskIdValue)).fetch(),
          fetchDetails('project_documents', 'remoteId', documentsIds)
        ]);

      return {
        group: group[0] ?? null,
        assignees: assigneesDetail,
        copies: copiesDetail,
        attachments: attachmentsDetail,
        media: mediaDetail,
        comments: comments,
        owner: owner[0] ?? null,
        documents: documents
      };
    } catch (error) {
      throw error;
    }
  }

  static async getTaskSummary(
    projectId: number,
    groupId?: number
  ): Promise<{
    countOfCompletedTasks: number;
    countOfHoldTasks: number;
    countOfInProgressTasks: number;
    countOfOpenTasks: number;
  }> {
    const baseQuery = [Q.where('projectId', projectId)];
    if (groupId !== undefined) {
      baseQuery.push(Q.where('groupId', groupId));
    }

    const completedTasks = await database.collections
      .get('tasks')
      .query(...baseQuery, Q.where('status', 'Completed'))
      .fetchCount();

    const holdTasks = await database.collections
      .get('tasks')
      .query(...baseQuery, Q.where('status', 'Hold'))
      .fetchCount();

    const inProgressTasks = await database.collections
      .get('tasks')
      .query(...baseQuery, Q.where('status', 'InProgress'))
      .fetchCount();

    const openTasks = await database.collections
      .get('tasks')
      .query(...baseQuery, Q.where('status', 'Open'))
      .fetchCount();

    return {
      countOfCompletedTasks: completedTasks,
      countOfHoldTasks: holdTasks,
      countOfInProgressTasks: inProgressTasks,
      countOfOpenTasks: openTasks
    };
  }

  static async getUnreceivedMemoTasks(projectId: string, userId: string) {
    const projectUser = await database.collections
      .get<ProjectUserModel>('project_users')
      .query(Q.where('userId', parseInt(userId)), Q.where('projectId', parseInt(projectId)))
      .fetch();

    if (projectUser.length === 0) {
      return [];
    }

    const projectUserId = projectUser[0]?._raw?.remoteId;

    const tasks = await database.collections
      .get<TaskModel>('tasks')
      .query(
        Q.where('projectId', Q.eq(parseInt(projectId))),
        // Q.where('isMemoReceive', Q.eq(false)),
        Q.where('assignees', Q.includes(projectUserId?.toString())),
        Q.where('memoUrl', Q.notEq(null))
      )
      .fetch();

    return tasks;
  }

  static async getLocalId(remoteId: number): Promise<string | null> {
    try {
      const task = await database.collections.get<TaskModel>('tasks').query(Q.where('remoteId', remoteId)).fetch();
      return task[0]?.id || null;
    } catch (error) {
      return null;
    }
  }

  // ======================== MUTATION ========================

  @writer async createTask(newTask: CreateTaskInput) {
    try {
      const task = await this.collections.get('tasks').create(task => {
        task.recordSource = 'OfflineApp';
        Object.keys(newTask).forEach((key: any) => {
          task[key] = newTask[key];
        });
      });

      return task;
    } catch (error) {
      throw error;
    }
  }

  @writer async updateTask(id: string, updatedTask: Partial<this>, updatedBy: number) {
    try {
      // @ts-ignore
      const { _status, createdAt, updatedAt, deletedAt, ...updatableFields } = updatedTask;

      const task = await this.collections.get('tasks').find(id);

      await task.update(task => {
        Object.entries(updatableFields).forEach(([key, value]) => {
          updatedBy && (task.updatedBy = updatedBy);
          if (key in task) {
            (task as any)[key] = value;
          }
        });
      });

      return this;
    } catch (error) {
      throw error;
    }
  }

  async deleteTask(taskId: string) {
    try {
      return await this.database.write(async () => {
        const task = await this.collections.get(TaskModel.table).find(taskId);
        await task.markAsDeleted();
      });
    } catch (error) {
      throw error;
    }
  }
}

export default TaskModel;
