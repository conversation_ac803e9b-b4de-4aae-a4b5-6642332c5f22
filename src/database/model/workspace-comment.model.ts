import { field, writer } from '@nozbe/watermelondb/decorators';
import BaseModel from './base-model';
import database from '../index.native';
import { Model, Q } from '@nozbe/watermelondb';
import { getForeignKey } from '../utils/numeric';

class WorkspaceCommentModel extends BaseModel {
  static table = 'project_document_comments';

  @field('remoteId') remoteId?: number | null;
  @field('projectDocumentId') projectDocumentId?: number;
  @field('userId') userId?: number;
  @field('message') message?: string;
  @field('localProjectDocumentId') localProjectDocumentId?: string | null;
  @field('commentType') commentType?: string | null;

  /**
   * Fetches comments for a specific project document and enriches them with user details.
   * @param {number} projectDocumentId - The project document ID to fetch comments for.
   * @returns {Promise<any[]>} - An array of comments with user details.
   */
  // static async getWorkspaceDocumentComments(projectDocumentId: number): Promise<any[]> {
  //   try {

  //     const foreignKeyInfo = getForeignKey(projectDocumentId, 'localProjectDocumentId', 'projectDocumentId');

  //     const comments = await database.collections
  //       .get<WorkspaceCommentModel>('project_document_comments')
  //       .query(
  //         // Q.where('projectDocumentId', Q.eq(projectDocumentId))
  //         Q.where(foreignKeyInfo.fieldName, foreignKeyInfo.keyValue[foreignKeyInfo.fieldName])
  //       )
  //       .fetch();

  //     if (comments.length === 0) return [];

  //     const userIds: any = comments.map(comment => comment.userId).filter(Boolean);
  //     const users = await database.collections
  //       .get('project_users')
  //       .query(Q.where('userId', Q.oneOf(userIds)))
  //       .fetch();

  //     const userMap = new Map(users.map((user: any) => [user.userId, user]));

  //     return comments.map((comment: any) => ({
  //       ...comment._raw,
  //       user: userMap.get(comment.userId)?._raw,
  //       createdAt: comment._raw.created_at
  //     }));
  //   } catch (error) {
  //     //     return [];
  //   }
  // }

  static async getWorkspaceDocumentComments(projectDocumentId: number | string): Promise<any[]> {
    try {
      const foreignKeyInfo = getForeignKey(projectDocumentId, 'localProjectDocumentId', 'projectDocumentId');

      const comments = await database.collections
        .get('project_document_comments')
        .query(Q.where(foreignKeyInfo.fieldName, Q.eq(foreignKeyInfo.keyValue[foreignKeyInfo.fieldName])))
        .fetch();

      if (comments.length === 0) {
        return [];
      }

      const userIds = [
        ...new Set(comments.map(comment => comment.userId).filter(id => id !== undefined && id !== null))
      ];

      const users =
        userIds.length > 0
          ? await database.collections
              .get('project_users')
              .query(Q.where('userId', Q.oneOf(userIds)))
              .fetch()
          : [];

      const userMap = new Map(users.map((user: any) => [user.userId, user]));

      return comments.map(comment => ({
        ...comment._raw,
        user: userMap.get(comment.userId)?._raw,
        createdAt: comment.createdAt
      }));
    } catch (error) {
      return [];
    }
  }

  static async isCommentUnsynced(localProjectDocumentId: string): Promise<boolean> {
    try {
      const commentCollection = database.collections.get('project_document_comments');
      const unsyncedQuery = commentCollection.query(
        Q.where('localProjectDocumentId', Q.eq(localProjectDocumentId)),
        Q.where('_status', Q.notEq('synced'))
      );
      const unsyncedComment = await unsyncedQuery.fetch();

      return unsyncedComment.length > 0;
    } catch (error) {
      return true; // Assume unsynced on error
    }
  }

  /**
   * Creates a new comment for a project document.
   * @param {number} projectDocumentId - The project document ID to create the comment for.
   * @param {string} message - The message to create the comment with.
   * @returns {Promise<void>}
   */
  @writer
  async createWorkspaceDocumentComment(input: CreateCommentInput): Promise<void> {
    try {
      await database.collections.get('project_document_comments').create((record: Model) => {
        const comment = record as unknown as WorkspaceCommentModel;
        record.recordSource = 'OfflineApp';
        comment.projectDocumentId = input.projectDocumentId;
        comment.userId = input.userId;
        comment.message = input.comment;
        comment.localProjectDocumentId = input.localProjectDocumentId;
        comment.commentType = 'Workspace';
      });
    } catch (error) {}
  }

  /**
   * Deletes a comment for a project document.
   * @param {string} id - The comment ID to delete.
   * @returns {Promise<void>}
   */

  @writer
  async deleteWorkspaceDocumentComment(id: string): Promise<void> {
    try {
      const comment = await database.collections.get('project_document_comments').find(id);
      return await comment?.markAsDeleted();
    } catch (error) {}
  }
}

export default WorkspaceCommentModel;
