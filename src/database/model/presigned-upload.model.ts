import { Q } from '@nozbe/watermelondb';
import BaseModel from './base-model';
import database from '../index.native';
import RNFS from 'react-native-fs';
import { SystemService } from '@src/api/rest';
import _ from 'lodash';
import axios from 'axios';
import { Buffer } from 'buffer';
import { store } from '@src/store';
import { AppActions } from '@src/slice/app.slice';
import ProjectDocumentModel from './project-document';

export default class PresignedUploadModel extends BaseModel {
  static async createPresignedUpload(dispatchStatus: any) {
    let uploadCount = 0; // Initialize the upload counter
    let uploadLimit = 20; // Set the upload limit

    const modelMap = {
      project_documents: ProjectDocumentModel
      // tasks_attachments: TasksAttachmentModel,
      // tasks_medias: TasksMediaModel,
      // drawing_revisions: DrawingRevisionModel,
      // project_carousels: ProjectCarouselModel
      // workspace_attachments: WorkspaceAttachmentModel,
      // workspace_photos: WorkspacePhotoModel
    };

    // Label for the outer loop for breaking from inner loop
    outerLoop: for (const tableName of Object.keys(modelMap)) {
      const queryConditions = [
        Q.where('_status', Q.notEq('synced')),
        Q.or(Q.where('fileKey', Q.eq('')), Q.where('fileKey', Q.eq(null)))
      ];

      if (tableName === 'project_documents') {
        queryConditions.push(Q.where('fileSystemType', Q.notEq('Folder')), Q.where('localFileUrl', Q.eq(null)));
      }

      //@ts-ignore
      const Model = modelMap[tableName];
      const docs = await database.collections
        .get(tableName)
        .query(...queryConditions)
        .fetch();

      if (dispatchStatus) store.dispatch(AppActions.setSyncStatus('upload'));
      for (const doc of docs) {
        // if (doc.fileKey === '' || doc.fileKey === undefined) {
        await PresignedUploadModel.uploadFile(doc);
        store.dispatch(AppActions.setSyncProgress(Math.floor((uploadCount / docs.length) * 100)));
        uploadCount++; // Increment the upload counter after each upload
        // }
        if (uploadCount >= uploadLimit) {
          break outerLoop; // Break the outer loop as soon as the limit is reached
        }
      }
    }
  }

  static uploadFile = async (doc: any) => {
    // const fileExists = await RNFS.exists(doc.fileUrl);
    // const base64Data = await RNFS.readFile(doc.fileUrl.replace('file://', ''), 'base64');
    const binary = Buffer.from(base64Data, 'base64');
    const tmFileName = doc.category + '/' + `${doc.id}-${_.replace(doc.name || doc.fileName, / /g, '-')}`;
    const res = await SystemService.presignedUpload({
      key: tmFileName,
      size: doc?.fileSize ?? 0,
      mimeType: doc.type
    });
    const req = {
      method: 'PUT',
      url: res.uploadUrl.SignedUrl,
      withCredentials: false,
      headers: res.uploadUrl.ActualSignedRequestHeaders,
      validateStatus: function (status: any) {
        return status >= 200;
      },
      data: binary
    };

    await axios
      .request({ ...req })
      .then(async () => {
        await database.write(async () => {
          await doc.update((mdl: any) => {
            mdl.fileKey = tmFileName;
          });
        });
      })
      .catch(function (error: any) {});
  };
}
