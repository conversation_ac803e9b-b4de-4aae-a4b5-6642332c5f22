import { field, writer } from '@nozbe/watermelondb/decorators';
import BaseModel from './base-model';
import database from '../index.native';
import { Q } from '@nozbe/watermelondb';
import { getForeignKey } from '../utils/numeric';
import { storeBinaryFile } from '@src/configs';

class WorkspaceAttachmentModel extends BaseModel {
  static table = 'workspace_attachments';

  @field('remoteId') remoteId?: number | null;
  @field('projectDocumentId') projectDocumentId!: number;
  @field('userId') userId!: number;
  @field('name') name!: string;
  @field('fileUrl') fileUrl?: string | null;
  @field('type') type?: string | null;
  @field('localProjectDocumentId') localProjectDocumentId?: string | null;
  @field('fileKey') fileKey?: string | null;
  @field('localFileUrl') localFileUrl?: string | null;

  static async getWorkspaceAttachment(remoteId: number | string): Promise<any[]> {
    try {
      const foreignKeyInfo = getForeignKey(remoteId, 'localProjectDocumentId', 'projectDocumentId');

      const attachments = await database.collections
        .get('workspace_attachments')
        .query(Q.where(foreignKeyInfo.fieldName, Q.eq(foreignKeyInfo.keyValue[foreignKeyInfo.fieldName])))
        .fetch();

      if (attachments.length === 0) {
        return [];
      }

      return attachments.map(attachment => attachment._raw);
    } catch (error) {
      return [];
    }
  }

  static async isAttachmentUnsynced(localProjectDocumentId: string): Promise<boolean> {
    try {
      const attachmentCollection = database.collections.get('workspace_attachments');
      const unsyncedQuery = attachmentCollection.query(
        Q.where('localProjectDocumentId', Q.eq(localProjectDocumentId)),
        Q.where('_status', Q.notEq('synced'))
      );
      const unsyncedAttachments = await unsyncedQuery.fetch();

      return unsyncedAttachments.length > 0;
    } catch (error) {
      return true; // Assume unsynced on error
    }
  }

  @writer
  async createWorkspaceAttachment(updatedAttachment: WorkspaceAttachmentModel[]): Promise<void> {
    try {
      // return await database.action(async () => {
      //   const newWorkspaceAttachments = updatedAttachment.map((attachment: WorkspaceAttachmentModel) => {
      //     return database.collections.get('workspace_attachments').prepareCreate((att: any) => {
      //       att.remoteId = attachment.remoteId;
      //       att.projectDocumentId = attachment.projectDocumentId;
      //       att.userId = attachment.userId;
      //       att.name = attachment.name;
      //       att.fileUrl = attachment.fileUrl;
      //       att.type = attachment.type;
      //       att.localProjectDocumentId = attachment.localProjectDocumentId;
      //     });
      //   });

      //   //   return await database.batch(...newWorkspaceAttachments);
      // });

      // updatedAttachment.forEach(async (attachment: WorkspaceAttachmentModel) => {
      //   await storeBinaryFile(attachment.name, attachment);
      // });

      const newWorkspaceAttachments = updatedAttachment.map((attachment: WorkspaceAttachmentModel) => {
        return database.collections.get('workspace_attachments').prepareCreate((att: any) => {
          att.remoteId = attachment.remoteId;
          att.projectDocumentId = attachment.projectDocumentId;
          att.userId = attachment.userId;
          att.name = attachment.name;
          att.fileUrl = attachment.fileUrl;
          att.type = attachment.type;
          att.localProjectDocumentId = attachment.localProjectDocumentId;
          att.recordSource = 'OfflineApp';
        });
      });

      return await database.batch(...newWorkspaceAttachments);
    } catch (error) {
      throw new Error(`Error creating workspace attachments: ${error}`);
    }
  }

  static async deleteAttachment(attachmentId: string) {
    try {
      return await database.write(async () => {
        await database.adapter.unsafeExecute({
          sqls: [['UPDATE workspace_attachments SET _status = ? WHERE id = ?', ['deleted', attachmentId]]]
        });
      });
    } catch (error) {
      throw error;
    }
  }
}

export default WorkspaceAttachmentModel;
