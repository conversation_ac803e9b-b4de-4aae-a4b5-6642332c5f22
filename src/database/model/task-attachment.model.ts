import { field, json, writer } from '@nozbe/watermelondb/decorators';
import BaseModel from './base-model';
import { CreateTaskAttachmentInput, TaskAttachment } from 'task-attachment';
import { storeBinaryFile } from '@src/configs/async-storage';
import database from '../index.native';

const sanitizer = (rawData: string) => {
  return rawData ? JSON.stringify(rawData) : null;
};

class TasksAttachmentModel extends BaseModel {
  static table = 'tasks_attachments';

  @field('remoteId') remoteId?: number | null;
  @field('taskId') taskId!: number;
  @field('userId') userId!: number;
  @field('name') name!: string;
  @field('fileUrl') fileUrl?: string | null;
  @field('type') type?: string | null;
  @field('localTaskId') localTaskId?: string | null;

  @json('file', sanitizer) file?: string | null;

  @writer async createTaskAttachment(newAttachment: CreateTaskAttachmentInput[]) {
    try {
      if (!newAttachment || newAttachment.length === 0) return;

      newAttachment.forEach(async (attachment: CreateTaskAttachmentInput) => {
        await storeBinaryFile(attachment.name, attachment);
      });

      const createdAttachment = newAttachment.map((attachment: CreateTaskAttachmentInput) => {
        return this.collections.get('tasks_attachments').prepareCreate((taskAttachment: any) => {
          taskAttachment.remoteId = null;
          taskAttachment.taskId = attachment.taskId;
          taskAttachment.userId = attachment.userId;
          taskAttachment.name = attachment.name;
          taskAttachment.fileUrl = attachment.uri;
          taskAttachment.type = attachment.type ?? null;
          taskAttachment.localTaskId = attachment.localTaskId;
          taskAttachment.recordSource = 'OfflineApp';
        });
      });

      await this.database.batch(...createdAttachment);

      return createdAttachment;
    } catch (error) {
      throw error;
    }
  }

  static async deleteAttachment(attachmentId: string) {
    try {
      return await database.write(async () => {
        const attachment = await database.collections.get(TasksAttachmentModel.table).find(attachmentId);
        await attachment.markAsDeleted();
      });
    } catch (error) {
      throw error;
    }
  }
}

export default TasksAttachmentModel;
