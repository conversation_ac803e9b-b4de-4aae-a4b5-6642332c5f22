import { field, writer } from '@nozbe/watermelondb/decorators';
import BaseModel from './base-model';
import database from '../index.native';
import { Q } from '@nozbe/watermelondb';
import { CreateTaskCommentInput } from 'task-comment';

class TasksCommentModel extends BaseModel {
  static table = 'task_comments';

  @field('remoteId') remoteId?: number | null;
  @field('taskId') taskId!: number;
  @field('userId') userId!: number;
  @field('message') message!: string;
  @field('localTaskId') localTaskId?: string | null;
  @field('server_created_at') server_created_at?: string;
  @field('server_updated_at') server_updated_at?: string;

  // ======================== FUNCTION ========================

  static async getTaskComment(taskId: number) {
    const comments = await database.collections
      .get(TasksCommentModel.table)
      .query(Q.where('taskId', taskId), Q.sortBy('created_at', Q.desc))
      .fetch();

    const projectUsersIds = comments.map((comment: any) => comment.userId);
    const projectUsers = await database.collections
      .get('project_users')
      .query(Q.where('userId', Q.oneOf(projectUsersIds)))
      .fetch();

    const commentsWithUsers = comments.map((comment: any) => {
      const user = projectUsers.find((u: any) => u.userId === comment.userId);
      return {
        ...comment?._raw,
        user: user?._raw,
        createdAt: comment.server_created_at
      };
    });

    commentsWithUsers.sort((a: any, b: any) => {
      return new Date(a.server_created_at).getTime() - new Date(b.server_created_at).getTime();
    });

    return commentsWithUsers;
  }

  @writer async createComment(input: CreateTaskCommentInput) {
    try {
      const newComment = this.collections.get(TasksCommentModel.table).prepareCreate((comment: any) => {
        comment.taskId = input.taskId;
        comment.userId = input.userId;
        comment.message = input.message;
        comment.recordSource = 'OfflineApp';
      });

      await this.database.batch(newComment);

      return newComment;
    } catch (error) {
      throw error;
    }
  }

  static async deleteComment(commentId: string) {
    try {
      return await database.write(async () => {
        const comment = await database.collections.get(TasksCommentModel.table).find(commentId);
        await comment.markAsDeleted();
      });
    } catch (error) {
      throw error;
    }
  }
}

export default TasksCommentModel;
