import { field, text } from '@nozbe/watermelondb/decorators';
import BaseModel from './base-model';
import database from '../index.native';
import { Collection, Q } from '@nozbe/watermelondb';

class ProjectUserModel extends BaseModel {
  static table = 'project_users';

  @field('userId') userId!: number;
  @field('projectId') projectId!: string;
  @field('role') role!: string;
  @field('name') name!: string;
  @field('email') email!: string;
  @field('avatarUrl') avatarUrl?: string | null;

  @field('remoteId') remoteId?: number | null;

  static async getProjectUser(
    page: number = 1,
    limit: number = 10,
    projectId: string,
    filteredValue: string,
    data: any
  ): Promise<{ items: any; nextPage?: number }> {
    let queryConditions = [
      Q.where('projectId', Q.eq(parseInt(projectId))),
      Q.where('name', Q.like(`%${filteredValue}%`)),
      Q.take(limit),
      Q.skip((page - 1) * limit)
    ];

    if (data.assigneesId) {
      queryConditions.push(Q.where('userId', Q.oneOf(data.assigneesId)));
    }

    const projectUser = await database.collections
      .get('project_users')
      .query(...queryConditions)
      .fetch();

    return {
      items: projectUser,
      nextPage: projectUser.length === limit ? page + 1 : undefined
    };
  }

  static async getUser(remoteId: number) {
    try {
      const userCollection: Collection<ProjectUserModel> = database.collections.get('project_users');
      const users = await userCollection.query(Q.where('userId', Q.eq(remoteId)), Q.take(1)).unsafeFetchRaw();

      if (users.length === 0) {
        return null;
      }

      return users[0];
    } catch (error) {
      return null;
    }
  }

  static async getUsers(remoteId: number[]) {
    try {
      const users = await database.collections.get('project_users').query(Q.where('userId', Q.oneOf(remoteId)));
      return users;
    } catch (error) {}
  }

  static async getProjectUserRole(projectId: string, userId: number) {
    try {
      const projectUser: ProjectUserModel[] = await (database.collections
        .get('project_users')
        .query(Q.where('projectId', Q.eq(parseInt(projectId))), Q.where('userId', Q.eq(userId)))
        .fetch() as Promise<ProjectUserModel[]>);

      if (projectUser.length === 0) {
        return null;
      }

      const role = projectUser[0].role ?? 'CanView';

      return role;
    } catch (error) {
      return null;
    }
  }
}

export default ProjectUserModel;
