import { field, date, text, writer, json } from '@nozbe/watermelondb/decorators';
import BaseModel from './base-model';
import database from '../index.native';
import { Q } from '@nozbe/watermelondb';
import ProjectUserModel from './project-user.model';
import ProjectCarouselModel from './project-carousel.model';
import apolloClient from '@src/lib/apollo';
import { Gql } from '@src/api';

const sanitizer = (rawData: string) => {
  // return rawData ? JSON.stringify(rawData) : null;
  if (!rawData) {
    return null;
  }

  if (typeof rawData === 'string') {
    return rawData;
  }

  return JSON.stringify(rawData);
};

class ProjectModel extends BaseModel {
  static table = 'projects';

  @field('companyId') companyId?: number;
  @field('userId') userId?: number;
  @field('refNo') refNo?: string | null;
  @field('title') title?: string;
  @field('description') description?: string | null;
  @field('status') status?: string;
  @field('managedBy') managedBy?: string | null;
  @field('deputySuperintendent') deputySuperintendent?: string | null;
  @field('client') client?: string | null;
  @field('contractor') contractor?: string | null;
  @date('startDate') startDate?: number | null;
  @date('completionDate') completionDate?: number | null;
  @field('cnsConsultant') cnsConsultant?: string | null;
  @field('mneConsultant') mneConsultant?: string | null;
  @field('qsConsultant') qsConsultant?: string | null;
  @field('environmentConsultant') environmentConsultant?: string | null;
  @field('fileUrlCover') fileUrlCover?: string | null;
  @field('metTownId') metTownId?: string | null;
  @text('fileUrlProgress') fileUrlProgress?: string | null;
  @text('fileUrlProgressFinance') fileUrlProgressFinance?: string | null;
  @text('contractValue') contractValue?: string | null;
  @text('ganttChartUrl') ganttChartUrl?: string | null;
  @field('isRemindSiteDiary') isRemindSiteDiary?: boolean;
  @field('isNotify2DUploaded') isNotify2DUploaded?: boolean;
  @text('issuesAndProblem') issuesAndProblem?: string | null;
  @text('solution') solution?: string | null;
  @text('fileProgressKey') fileProgressKey?: string | null;
  @text('ganttChartKey') ganttChartKey?: string | null;
  @text('fileProgressFinanceKey') fileProgressFinanceKey?: string | null;
  @field('createdBy') createdBy?: number | null;
  @field('drawing_sync') drawing_sync!: boolean;
  @json('fileUrlProgressJson', sanitizer) fileUrlProgressJson?: string | null;
  @json('fileUrlProgressFinanceJson', sanitizer) fileUrlProgressFinanceJson?: string | null;
  @field('subscriptionPackageId') subscriptionPackageId?: number | null;

  @field('remoteId') remoteId?: number | null;

  static async getProjects(
    page: number = 1,
    limit: number = 10,
    filteredValue: string = ''
  ): Promise<{
    items: ProjectModel[];
    nextPage?: number;
  }> {
    const offset = (page - 1) * limit;

    const queryConditions = [];

    if (filteredValue) {
      queryConditions.push(Q.where('title', Q.like(`%${Q.sanitizeLikeString(filteredValue)}%`)));
    }

    const projects = await database.collections
      .get<ProjectModel>('projects')
      .query(Q.sortBy('title', Q.asc), Q.skip(offset), Q.take(limit), ...queryConditions)
      .fetch();

    const sanitizedProjects = await Promise.all(
      projects.map(async project => {
        const projectUsers = await database.collections
          .get<ProjectUserModel>('project_users')
          .query(Q.where('projectId', Q.eq(project.remoteId ?? 0)))
          .fetchCount();

        const taskIdValue = project.remoteId ? project.remoteId : project.id;
        const taskJoinField = project.remoteId ? 'projectId' : 'localProjectId';

        let uri = '';

        try {
          const carouselEntries = await database.collections
            .get<ProjectCarouselModel>('project_carousels')
            .query(Q.where(taskJoinField, Q.eq(taskIdValue)))
            .fetch();

          if (carouselEntries.length > 0) {
            uri = carouselEntries[0].fileUrl;
          }
        } catch (error) {
          uri = ''; // Set URI to an empty string if there is an error
        }

        return {
          ...project._raw,
          users: projectUsers,
          uri: uri
        };
      })
    );

    return {
      items: sanitizedProjects as any,
      nextPage: projects.length === limit ? page + 1 : undefined
    };
  }

  static async getProject(projectId: string) {
    try {
      const project = await database.collections
        .get('projects')
        .query(Q.where('remoteId', Q.eq(parseInt(projectId))))
        .fetch();

      const sanitizeProject = project?.[0]?._raw;

      return sanitizeProject;
    } catch (error) {}
  }

  static async getAllProjectIds() {
    try {
      const projects = await database.collections.get('projects').query().fetch();
      return projects.map((project: any) => project.remoteId);
    } catch (error) {
      throw error;
    }
  }

  static async createProject(title: string, fileUrl: string, companyId: number, userId: number) {
    try {
      const project = await database.write(async () => {
        return await database.collections.get<ProjectModel>('projects').create(project => {
          project.recordSource = 'OfflineApp';
          project.title = title;
          project.companyId = companyId;
          project.userId = userId;
          return project; // Ensure to return the created project
        });
      });

      const createdCarousel = await database.write(async () => {
        return await database.collections.get<ProjectCarouselModel>('project_carousels').create(carousel => {
          carousel.fileUrl = fileUrl;
          carousel.localProjectId = project.id;
          return carousel;
        });
      });

      return { project, createdCarousel }; // Optionally, return both project and carousel
    } catch (error) {
      throw error;
    }
  }

  //toggle drawing sync to true and false
  static async updateDrawingSyncProjects(projectId: number, drawingSync: boolean) {
    try {
      await database.write(async () => {
        const project = await database.collections.get('projects').query(Q.where('remoteId', projectId)).fetch();

        await project[0].update((upd: any) => {
          upd.drawing_sync = drawingSync;
        });

        return;
      });
    } catch (error) {
      throw error;
    }
  }

  static async updateProject(projectId: string, title: string, carousel: string) {
    try {
      await database.write(async () => {
        const project: any = await database.collections.get('projects').find(projectId);

        const projectUpdate = project.prepareUpdate((upd: any) => {
          upd.title = title;
        });

        let carouselUpdates: ProjectCarouselModel[] = [];

        if (carousel) {
          const taskIdValue = project.remoteId ? project.remoteId : project.id;
          const taskJoinField = project.remoteId ? 'projectId' : 'localProjectId';

          const carouselDetails = await database.collections
            .get<ProjectCarouselModel>('project_carousels')
            .query(Q.where(taskJoinField, Q.eq(taskIdValue)))
            .fetch();

          if (carouselDetails.length === 0) {
          } else {
            // Prepare all carousel updates
            carouselUpdates = carouselDetails.map(carouselItem =>
              carouselItem.prepareUpdate(upd => {
                upd.fileUrl = carousel;
              })
            );
          }
        }

        await database.batch(projectUpdate, ...carouselUpdates);
      });
    } catch (error) {
      throw error;
    }
  }

  //Get drawing sync status based on projectId
  static async getDrawingSyncStatus(projectId: number) {
    try {
      if (projectId === undefined) {
        return false;
      }
      const project: any = await database.collections.get('projects').query(Q.where('remoteId', projectId)).fetch();

      // Check if the drawing_sync is true explicitly
      return project[0]._raw.drawing_sync === true;
    } catch (error) {
      throw error;
    }
  }
}

export default ProjectModel;
