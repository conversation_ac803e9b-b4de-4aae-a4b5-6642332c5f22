import { field, text } from '@nozbe/watermelondb/decorators';
import BaseModel from './base-model';
import database from '../index.native';
import apolloClient from '@src/lib/apollo';
import { Gql } from '@src/api';
import storeFileLocally from '@src/configs/saveOffline';
import { Platform } from 'react-native';

class ProjectCarouselModel extends BaseModel {
  static table = 'project_carousels';

  @field('projectId') projectId?: number;
  @text('name') name?: string;
  @text('fileUrl') fileUrl!: string;
  @text('type') type?: string;
  @text('fileKey') fileKey?: string | null;
  @field('remoteId') remoteId?: number | null;
  @field('localProjectId') localProjectId?: string | null;
  @field('isDownloaded') isDownloaded?: boolean;

  //download fileURL to local device
  static async syncProjectCarouselImages() {
    const projectCarousels = await database.collections.get<ProjectCarouselModel>('project_carousels').query().fetch();

    await Promise.all(
      projectCarousels.map(async (record: any) => {
        if (!record.remoteId || record.isDownloaded) return; // Skip if no remoteId or already downloaded

        const { data } = await apolloClient.query({
          query: Gql.GetProjectCarouselDocument,
          variables: { id: record.remoteId },
          errorPolicy: 'ignore'
        });

        const carousel = data.projectCarousel;
        if (!carousel) return;

        const subPath = `${carousel.projectId}/project-carousel`;
        let localFileUrl = await storeFileLocally(carousel.fileUrl, subPath, carousel.name);

        // Add file:// prefix if the platform is Android
        if (Platform.OS === 'android') {
          localFileUrl = `file://${localFileUrl}`;
        }

        // Update the local record's fileUrl using the write method
        await database.write(async () => {
          await record.update((rec: any) => {
            rec.fileUrl = localFileUrl;
            rec.isDownloaded = true;
            rec._raw._status = 'synced';
            rec._raw._changed = '';
          });
        });
      })
    );

    return;
  }
}

export default ProjectCarouselModel;
