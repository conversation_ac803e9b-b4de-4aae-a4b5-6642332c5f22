import { useInfiniteQuery } from '@tanstack/react-query';
import { useSelector } from '@src/store';
import ProjectGroupModel from '@src/database/model/project-group.model';

export const useGetTaskGroups = (filteredValue: string, shouldPauseQuery = false) => {
  const projectId = useSelector(state => state.project.projectId);

  if (!projectId) {
    return { data: null, fetchNextPage: () => {}, hasNextPage: false, isFetchingNextPage: false, isLoading: false };
  }

  const enabled = projectId && !(shouldPauseQuery && filteredValue === '');

  return useInfiniteQuery({
    queryKey: ['taskGroup', projectId, filteredValue],
    queryFn: ({ pageParam = 1 }) => ProjectGroupModel.getProjectGroups(pageParam, 10, projectId, filteredValue),
    getNextPageParam: lastPage => lastPage.nextPage ?? undefined,
    initialPageParam: 1,
    enabled: Boolean(enabled)
  });
};
