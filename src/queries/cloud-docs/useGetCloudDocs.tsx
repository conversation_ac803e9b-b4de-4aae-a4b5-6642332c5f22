import { useInfiniteQuery } from '@tanstack/react-query';
import ProjectDocumentModel from '@src/database/model/project-document';

interface ProjectDocumentQueryParams {
  projectId?: string | undefined;
  driveType?: string;
  category?: string;
  name?: string;
  projectDocumentId?: number;
  localRemoteId?: string;
  limit?: number;
  pageIndex?: number;
}

export const useGetProjectDocuments = (filteredValue: string, data: ProjectDocumentQueryParams) => {
  if (!data.projectId) {
    return { data: null, fetchNextPage: () => {}, hasNextPage: false, isFetchingNextPage: false, isLoading: false };
  }

  return useInfiniteQuery({
    queryKey: ['cloudDocs', data, filteredValue],
    queryFn: ({ pageParam = 1 }) => ProjectDocumentModel.getProjectDocuments(filteredValue, '', pageParam, 10, data),
    getNextPageParam: lastPage => lastPage.nextPage ?? undefined,
    initialPageParam: 1,
    staleTime: 5000,
    refetchOnMount: 'always'
  });
};
