import { useInfiniteQuery } from '@tanstack/react-query';
import { useSelector } from '@src/store';
import ProjectUserModel from '@src/database/model/project-user.model';

interface ProjectUserQueryParams {
  assigneesId?: string[];
}

export const useGetProjectUser = (filteredValue: string) => {
  const projectId = useSelector(state => state.project.projectId);
  const queryInfo = useInfiniteQuery({
    queryKey: ['projectUser', filteredValue],
    queryFn: ({ pageParam = 1 }) => ProjectUserModel.getProjectUser(pageParam, 10, projectId ?? '', filteredValue, {}),
    getNextPageParam: lastPage => lastPage.nextPage ?? undefined,
    initialPageParam: 1
  });

  return queryInfo;
};
