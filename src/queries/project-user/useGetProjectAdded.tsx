import { useInfiniteQuery } from '@tanstack/react-query';
import { useSelector } from '@src/store';
import ProjectUserModel from '@src/database/model/project-user.model';

interface ProjectUserQueryParams {
  assigneesId?: string[];
}

export const useGetProjectAdded = (data: ProjectUserQueryParams) => {
  const projectId = useSelector(state => state.project.projectId);
  const queryInfo = useInfiniteQuery({
    queryKey: ['projectAdded', data],
    queryFn: ({ pageParam = 1 }) => ProjectUserModel.getProjectUser(pageParam, 10, projectId ?? '', '', data),
    getNextPageParam: lastPage => lastPage.nextPage ?? undefined,
    initialPageParam: 1
  });

  return queryInfo;
};
