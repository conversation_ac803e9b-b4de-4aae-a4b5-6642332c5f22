import ProjectDocumentModel from '@src/database/model/project-document';
import { useSelector } from '@src/store';
import { useQuery, UseQueryOptions, QueryKey } from '@tanstack/react-query';

export const useGetDocumentDetail = (id: string, options?: Partial<UseQueryOptions<any, Error, any, QueryKey>>) => {
  const documentWorkspace = useSelector(state => state.documentWorkspace);
  const project = useSelector(state => state.project);

  // Handle the case where projectId might not be available
  if (!project.projectId) {
    return {
      data: undefined,
      error: new Error('Project ID is not available'),
      isFetching: false,
      isPending: false
    };
  }

  const { data, error, isFetching, isPending, refetch } = useQuery({
    queryKey: ['documentDetail', id, documentWorkspace.driveType],
    queryFn: () => ProjectDocumentModel.getProjectDocumentDetail(id, parseInt(project.projectId as string)),
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    enabled: !!id,
    staleTime: 0, // Always refetch when requested
    ...options
  });

  return { data, error, isFetching, isPending, refetch };
};
