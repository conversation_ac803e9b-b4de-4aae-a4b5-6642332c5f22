import ProjectDocumentModel from '@src/database/model/project-document';
import { useQuery } from '@tanstack/react-query';

/**
 * Hook to check if a document has any unsynchronized attachments, media, or documents
 * @param id The document ID to check
 * @returns Query result with data indicating if there are unsynchronized items
 */
export const useGetUnsyncAttachmentMedia = (id: string) => {
  const result = useQuery({
    queryKey: ['unsyncAttachmentMedia', id],
    queryFn: async () => {
      // Call the model function and return the result directly
      const hasUnsyncItems = await ProjectDocumentModel.getUnsyncAttachmentMedia(id);
      return hasUnsyncItems;
    },
    refetchOnWindowFocus: true,
    enabled: !!id
  });

  return result;
};
