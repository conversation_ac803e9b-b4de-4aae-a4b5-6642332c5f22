import { useInfiniteQuery } from '@tanstack/react-query';
import ProjectDocumentModel from '@src/database/model/project-document';
import { useSelector } from '@src/store';

interface ProjectDocumentQueryParams {
  projectId?: string | undefined;
  category?: string;
  name?: string;
  projectDocumentId?: number;
  localRemoteId?: string;
  limit?: number;
  pageIndex?: number;
  status?: string;
  id?: string;
  sorting?: string;
  userId: number;
}

export const useGetDocuments = (filteredValue: string, data: ProjectDocumentQueryParams) => {
  const { projectId, category, name, projectDocumentId, localRemoteId, limit, pageIndex, userId } = data;
  const { driveType, filterItems } = useSelector(state => state.documentWorkspace);

  if (!projectId) {
    return { data: null, fetchNextPage: () => {}, hasNextPage: false, isFetchingNextPage: false, isLoading: false };
  }
  return useInfiniteQuery({
    queryKey: [
      'workspaceDocuments',
      projectId,
      category,
      name,
      projectDocumentId,
      localRemoteId,
      filteredValue,
      limit,
      pageIndex,
      driveType,
      filterItems,
      userId
    ],
    queryFn: ({ pageParam = 1 }) =>
      ProjectDocumentModel.getProjectDocuments(filteredValue, filterItems, pageParam, limit, data),
    getNextPageParam: lastPage => lastPage.nextPage ?? undefined,
    initialPageParam: 1,
    refetchOnWindowFocus: true
  });
};
