import ProjectDocumentModel from '@src/database/model/project-document';
import { useQuery } from '@tanstack/react-query';

export const useGetWorkspaceGroupSummary = (projectId: number | undefined, groupId?: number) => {
  return useQuery({
    queryKey: ['workspaceSummary', projectId, groupId],
    queryFn: () => ProjectDocumentModel.getProjectDocumentSummary(projectId ?? 0, groupId),
    enabled: !!projectId
  });
};
