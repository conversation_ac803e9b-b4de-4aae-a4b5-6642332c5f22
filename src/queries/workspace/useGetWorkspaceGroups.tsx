import { useInfiniteQuery } from '@tanstack/react-query';
import { useSelector } from '@src/store';
import WorkspaceGroupModel from '@src/database/model/workspace-group.model';

export const useGetWorkspaceGroup = (filteredValue: string, shouldPauseQuery = false) => {
  const projectId = useSelector(state => state.project.projectId);
  const userId = useSelector(state => state?.auth?.user?.id);

  if (!projectId) {
    return { data: null, fetchNextPage: () => {}, hasNextPage: false, isFetchingNextPage: false, isLoading: false };
  }

  const enabled = projectId && !(shouldPauseQuery && filteredValue === '');

  return useInfiniteQuery({
    queryKey: ['workspaceGroup', projectId, filteredValue],
    queryFn: ({ pageParam = 1 }) =>
      WorkspaceGroupModel.getWorkspaceGroups(pageParam, 10, projectId, filteredValue, userId),
    getNextPageParam: lastPage => lastPage.nextPage ?? undefined,
    initialPageParam: 1,
    enabled: Boolean(enabled)
  });
};
