import ProjectDocumentModel from '@src/database/model/project-document';
import { useSelector } from '@src/store';
import { useQuery } from '@tanstack/react-query';

export const useGetSiteDiary = () => {
  const project = useSelector(state => state.project);
  if (!project.projectId) {
    return {
      data: undefined,
      error: new Error('Project ID is not available'),
      isFetching: false,
      isPending: false
    };
  }

  const { data, error, isFetching, isPending } = useQuery({
    queryKey: ['siteDiary', project.projectId],
    queryFn: () => ProjectDocumentModel.getSiteDiary(project.projectId || ''),
    refetchOnWindowFocus: true,
    enabled: !!project.projectId
  });

  return { data, error, isFetching, isPending };
};
