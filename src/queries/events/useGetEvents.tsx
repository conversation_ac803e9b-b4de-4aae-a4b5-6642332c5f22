import EventModel from '@src/database/model/event.model';
import { useSelector } from '@src/store';
import { useInfiniteQuery } from '@tanstack/react-query';

export const useGetEvents = () => {
  const projectId = useSelector(state => state.project.projectId);
  const userId = useSelector(state => state.auth.user?.id);

  if (!projectId || !userId) {
    return { data: null, fetchNextPage: () => {}, hasNextPage: false, isFetchingNextPage: false, isLoading: false };
  }
  return useInfiniteQuery({
    queryKey: ['events', projectId, userId],
    queryFn: ({ pageParam = 1 }) => EventModel.fetchEvents(userId, projectId, pageParam),
    getNextPageParam: lastPage => lastPage.nextPage ?? undefined,
    initialPageParam: 1,
    enabled: !!projectId && !!userId
  });
};
