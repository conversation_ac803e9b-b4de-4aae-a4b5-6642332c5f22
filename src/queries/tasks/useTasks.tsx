import { useInfiniteQuery } from '@tanstack/react-query';
import TaskModel from '@src/database/model/task.model';
import { Filter } from '@src/slice/tasks.slice';
import { useSelector } from '@src/store';

interface UseTasksParams {
  filteredValue: string;
  filterItems: Filter;
}

export const useTasks = ({ filteredValue, filterItems }: UseTasksParams) => {
  const projectId = useSelector(state => state.project.projectId);
  const userId = useSelector(state => state.auth.user?.id);

  return useInfiniteQuery({
    initialPageParam: 1,
    queryKey: ['tasks', projectId, filteredValue, filterItems],
    queryFn: ({ pageParam = 1 }) =>
      TaskModel.fetchTasksPaginated(pageParam, 10, projectId as string, filterItems, filteredValue, userId as string),

    getNextPageParam: lastPage => {
      return lastPage.nextPage ?? undefined;
    },
    enabled: !!projectId || !!userId
  });
};
