import TaskModel from '@src/database/model/task.model';
import { useSelector } from '@src/store';
import { useQuery } from '@tanstack/react-query';

export const useGetUnreceivedMemoTask = () => {
  const projectId = useSelector(state => state.project.projectId);
  const userId = useSelector(state => state.auth.user?.id);

  return useQuery({
    queryKey: ['unreceivedMemoTask', projectId, userId],
    queryFn: () => TaskModel.getUnreceivedMemoTasks(projectId?.toString() ?? '', userId ?? ''),
    enabled: !!projectId
  });
};
