import { useInfiniteQuery } from '@tanstack/react-query';
import { useSelector } from '@src/store';
import ProjectModel from '@src/database/model/project.model';

export const useGetProject = (filteredValue: string) => {
  const queryInfo = useInfiniteQuery({
    queryKey: ['project', filteredValue],
    queryFn: ({ pageParam = 1 }) => ProjectModel.getProjects(pageParam, 10, filteredValue),
    getNextPageParam: lastPage => lastPage.nextPage ?? undefined,
    initialPageParam: 1
  });
  return queryInfo;
};
