import ProjectDocumentModel from '@src/database/model/project-document';
import { useInfiniteQuery } from '@tanstack/react-query';

interface ProjectDocumentQueryParams {
  projectId?: string | undefined;
  category?: string;
  name?: string;
  projectDocumentId?: number;
  localRemoteId?: string;
  limit?: number;
  pageIndex?: number;
}

export const useGetBimDrawing = (filteredValue: string, data: ProjectDocumentQueryParams) => {
  const { projectId, category, name, projectDocumentId, localRemoteId, limit, pageIndex } = data;
  if (!projectId) {
    return { data: null, fetchNextPage: () => {}, hasNextPage: false, isFetchingNextPage: false, isLoading: false };
  }
  return useInfiniteQuery({
    queryKey: [
      'bimDrawing',
      projectId,
      category,
      name,
      projectDocumentId,
      localRemoteId,
      filteredValue,
      limit,
      pageIndex
    ],
    queryFn: ({ pageParam = 1 }) => ProjectDocumentModel.getProjectDocuments(filteredValue, '', pageParam, limit, data),
    getNextPageParam: lastPage => lastPage.nextPage ?? undefined,
    initialPageParam: 1,
    refetchOnWindowFocus: true,
    staleTime: 5000,
    refetchOnMount: 'always'
  });
};
