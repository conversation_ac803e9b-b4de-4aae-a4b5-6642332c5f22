import ProjectDocumentModel from '@src/database/model/project-document';
import { useInfiniteQuery } from '@tanstack/react-query';

interface ProjectDocumentQueryParams {
  projectId?: string | undefined;
  driveType?: string;
  category?: string;
  name?: string;
  projectDocumentId?: number;
  localRemoteId?: string;
  limit?: number;
  pageIndex?: number;
  sorting?: string;
  exclude?: string[];
}

export const useGetPhotos = (filteredValue: string, data: ProjectDocumentQueryParams) => {
  if (!data.projectId) {
    return { data: null, fetchNextPage: () => {}, hasNextPage: false, isFetchingNextPage: false, isLoading: false };
  }

  return useInfiniteQuery({
    queryKey: ['photos', data, filteredValue],
    queryFn: ({ pageParam = 1 }) => ProjectDocumentModel.getProjectDocuments(filteredValue, '', pageParam, 10, data),
    getNextPageParam: lastPage => lastPage.nextPage ?? undefined,
    initialPageParam: 1
  });
};
