import { useInfiniteQuery } from '@tanstack/react-query';
import DrawingRevisionModel from '@src/database/model/drawing-revision.model';

export const useGetDrawingRevision = (projectDocumentId?: string | number) => {
  return useInfiniteQuery({
    queryKey: ['drawingRevision', projectDocumentId],
    queryFn: ({ pageParam = 1 }) => {
      if (projectDocumentId === undefined) {
        throw new Error('Project Document ID is required');
      }
      return DrawingRevisionModel.getDrawingRevisions(projectDocumentId, pageParam, 99999);
    },
    getNextPageParam: lastPage => lastPage.nextPage ?? undefined,
    initialPageParam: 1,
    staleTime: 5000,
    refetchOnMount: 'always',
    enabled: !!projectDocumentId
  });
};
