import { useEffect } from 'react';
import { useDispatch } from '@src/store';
import { AppActions } from '@src/slice/app.slice';

/**
 * Hook to disable sync operations when a component is mounted
 * Useful for preventing sync operations during document editing
 *
 * @returns void
 */
const useSyncDisabler = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    // Disable sync when component mounts
    dispatch(AppActions.setSyncDisabled(true));

    // Re-enable sync when component unmounts
    return () => {
      dispatch(AppActions.setSyncDisabled(false));
    };
  }, [dispatch]);
};

export default useSyncDisabler;
