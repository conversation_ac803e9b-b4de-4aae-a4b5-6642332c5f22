import { Alert } from 'react-native';

type UseDeleteConfirmationProps = {
  fileName: string;
  action: () => Promise<void>;
};

const useDeleteConfirmation = () => {
  const showDeleteConfirmation = (props: UseDeleteConfirmationProps) => {
    Alert.alert(
      'Delete Confirmation',
      `Are you sure want to delete ${props.fileName} ?`,
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Delete',
          onPress: async () => {
            try {
              return await props.action();
            } catch (error) {}
          }
        }
      ],
      {
        cancelable: true
      }
    );
  };

  return {
    showDeleteConfirmation
  };
};

export default useDeleteConfirmation;
