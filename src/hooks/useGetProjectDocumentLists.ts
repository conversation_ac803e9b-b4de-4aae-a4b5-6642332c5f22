import { useState, useEffect, useCallback } from 'react';
import { useSelector } from '@src/store';
import { Gql } from '@src/api';
import { useGetProjectDocuments } from '@src/database/services/useProjectDocument';

type IProjectDocument = {
  projectId: string | undefined;
  limit: number;
  filter: Gql.ProjectDocumentFilter;
};

const useProjectDocuments = ({ projectId, limit, filter }: IProjectDocument) => {
  const { OFFLINE_MODE } = useSelector(state => state.app);
  const [projectDocuments, setProjectDocuments] = useState<any>([]);
  const getOfflineProjectDocs = useGetProjectDocuments();
  const [hasNextPage, setHasNextPage] = useState<boolean>(false);
  const [offlineDocuments, setOfflineDocuments] = useState(new Map()); // New state for offline documents

  const [getProjectDocs, { data, loading, fetchMore, refetch }] = Gql.useGetProjectDocumentsListLazyQuery({
    variables: {
      filter,
      paging: {
        offset: 0,
        limit
      }
    },
    fetchPolicy: 'network-only'
  });

  const loadMoreDocuments = useCallback(() => {
    if (OFFLINE_MODE || !data?.getProjectDocuments.pageInfo.hasNextPage) {
      return;
    }
    fetchMore({
      variables: {
        paging: {
          offset: data.getProjectDocuments.nodes.length,
          limit
        }
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;
        return {
          getProjectDocuments: {
            ...fetchMoreResult.getProjectDocuments,
            nodes: [...prev.getProjectDocuments.nodes, ...fetchMoreResult.getProjectDocuments.nodes]
          }
        };
      }
    });
  }, [data, fetchMore, OFFLINE_MODE, limit]);

  const refetchDocuments = useCallback(
    (searchTerm: any) => {
      const refetchFilter = {
        ...filter,
        ...(searchTerm && { name: { like: searchTerm } })
      };
      const offlineData = getOfflineProjectDocs({
        projectId: projectId ?? '',
        name: searchTerm,
        category: refetchFilter.category
      });
      setProjectDocuments(offlineData);
    },
    [OFFLINE_MODE, projectId, refetch, limit, filter]
  );

  const getOfflineDocuments = useCallback(
    (ProjectId: string) => {
      const offlineDocuments = getOfflineProjectDocs({
        projectId: ProjectId,
        name: '',
        category: filter?.category as any
      });

      offlineDocuments.forEach((doc: any) => {
        setOfflineDocuments(offlineDocuments => new Map(offlineDocuments.set(doc.id, true)));
      });
    },
    [getOfflineProjectDocs, filter?.category?.eq]
  );

  useEffect(() => {
    if (OFFLINE_MODE) {
      if (!projectId) return;
      const offlineData = getOfflineProjectDocs({
        projectId,
        name: filter?.name as string,
        category: filter?.category as any
      });

      setProjectDocuments(offlineData);
    } else {
      getProjectDocs();
      getOfflineDocuments(projectId ?? '');
    }
  }, [OFFLINE_MODE, projectId, filter.category?.eq]);

  useEffect(() => {
    if (data) {
      setProjectDocuments(data.getProjectDocuments.nodes);
      setHasNextPage(data.getProjectDocuments.pageInfo.hasNextPage as boolean);
    }
  }, [data]);

  return {
    projectDocuments: projectDocuments ?? [],
    isLoading: OFFLINE_MODE ? false : loading,
    loadMoreDocuments,
    refetch,
    refetchDocuments,
    getOfflineDocuments,
    offlineDocuments,
    hasNextPage: OFFLINE_MODE ? false : hasNextPage
  };
};

export default useProjectDocuments;
