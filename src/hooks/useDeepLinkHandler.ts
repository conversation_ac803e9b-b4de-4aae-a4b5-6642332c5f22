import { useCallback, useRef } from 'react';
import { Linking } from 'react-native';
import { useDispatch } from '@src/store';
import { ProjectActions } from '@src/slice/project.slice';
import { DEEP_LINK_CONFIGS, DEEP_LINK_PREFIX } from '@src/constants/notification.constants';
import { pushError } from '@src/configs';
import { NotificationData } from '@src/types/notification.types';

export const useDeepLinkHandler = () => {
  const deeplinkPath = useRef<string | null>(null);
  const dispatch = useDispatch();

  const handleDeepLink = useCallback(
    async (notificationData: NotificationData) => {
      try {
        const { link, projectId } = notificationData;

        if (!link) {
          return;
        }

        // Store the deep link path
        deeplinkPath.current = link;

        // Clean the deep link
        const sanitizedDeeplink = link.replace(/:/g, '');
        let processedDeepLink = sanitizedDeeplink.replace(/\/0\//g, '/1/');

        // Process digital form deep links
        if (processedDeepLink.includes('digital-form/1/')) {
          const matches = processedDeepLink.match(DEEP_LINK_CONFIGS.digitalForm.pattern);
          if (!matches) {
            throw new Error('Invalid digital form deep link');
          }
          const result = DEEP_LINK_CONFIGS.digitalForm.transform(matches);
          if (!result) {
            throw new Error('Could not transform digital form deep link');
          }
          processedDeepLink = result;
        }

        // Process task deep links
        if (processedDeepLink.startsWith('task/1/')) {
          const matches = processedDeepLink.match(DEEP_LINK_CONFIGS.task.pattern);
          if (!matches) {
            throw new Error('Invalid task deep link');
          }
          const result = DEEP_LINK_CONFIGS.task.transform(matches);
          if (!result) {
            throw new Error('Could not transform task deep link');
          }
          processedDeepLink = result;
        }

        // Update project ID if provided
        if (projectId) {
          dispatch(ProjectActions.updateProjectId(projectId));
        }

        // Open the deep link
        await Linking.openURL(`${DEEP_LINK_PREFIX}${processedDeepLink}`);

        // Reset the deep link path
        deeplinkPath.current = null;
      } catch (error) {
        pushError(error instanceof Error ? error.message : 'Invalid deep link');
      }
    },
    [dispatch]
  );

  return {
    handleDeepLink,
    currentDeepLink: deeplinkPath.current
  };
};
