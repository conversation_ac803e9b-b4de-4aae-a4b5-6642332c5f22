import { useEffect, useState } from 'react';
import { useSelector } from '@src/store';
import { ProjectAccess } from '@src/constants/subscription';
import database from '@src/database/index.native';
import SubscriptionPackageModel from '@src/database/model/subscription-package.model';
import ProjectModel from '@src/database/model/project.model';
import { Q } from '@nozbe/watermelondb';
import { Subscription } from 'rxjs';

export const useProjectSubscriptionPackage = () => {
  const [projectSubscriptionPackageId, setProjectSubscriptionPackageId] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentSubscriptionPackage, setCurrentSubscriptionPackage] = useState<SubscriptionPackageModel | null>(null);
  const [currentProject, setCurrentProject] = useState<ProjectModel | null>(null);
  const [forceUpdate, setForceUpdate] = useState(0);

  const projectId = useSelector(state => state.project.projectId);
  const syncStatus = useSelector(state => state.app.syncStatus);

  // Note: Removed GraphQL call for true offline-first implementation
  // Company subscriptions should be synced via standard sync system if needed

  // Load local data only - sync is handled by standard sync system
  useEffect(() => {
    const loadData = async () => {
      try {
        const projectIdNumber = typeof projectId === 'string' ? parseInt(projectId, 10) : projectId;
        setLoading(true);
        setError(null);

        if (!projectId) {
          setProjectSubscriptionPackageId(null);
          setCurrentProject(null);
          setCurrentSubscriptionPackage(null);
          setLoading(false);
          return;
        }

        // Step 1: Query specific project by projectId
        const projectsCollection = database.collections.get<ProjectModel>('projects');
        const projects = await projectsCollection.query(Q.where('remoteId', projectIdNumber)).fetch();
        const project = projects.length > 0 ? projects[0] : null;
        setCurrentProject(project);

        if (project?.subscriptionPackageId) {
          setProjectSubscriptionPackageId(project.subscriptionPackageId);

          // Step 2: Query specific subscription package by subscriptionPackageId
          const subscriptionPackagesCollection =
            database.collections.get<SubscriptionPackageModel>('subscription_packages');
          const subscriptionPackages = await subscriptionPackagesCollection
            .query(Q.where('remoteId', project.subscriptionPackageId))
            .fetch();

          const subscriptionPackage = subscriptionPackages.length > 0 ? subscriptionPackages[0] : null;
          setCurrentSubscriptionPackage(subscriptionPackage);
        } else {
          setProjectSubscriptionPackageId(null);
          setCurrentSubscriptionPackage(null);
        }

        setLoading(false);
      } catch (loadError) {
        setError(loadError instanceof Error ? loadError.message : 'Failed to load data');
        setLoading(false);
      }
    };

    loadData();
  }, [projectId, forceUpdate]);

  // Listen for sync completion to trigger data reload
  useEffect(() => {
    // Only trigger refresh when sync actually completes (not on initial load)
    const isInitialLoad = syncStatus === 'idle' && !projectSubscriptionPackageId;
    if (syncStatus === 'idle' && !isInitialLoad) {
      // Small delay to ensure database changes are applied
      const timeoutId = setTimeout(() => {
        setForceUpdate(prev => prev + 1);
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [syncStatus, projectSubscriptionPackageId]);

  // Listen for database changes to subscription packages and projects (scoped to current project)
  useEffect(() => {
    if (!projectId) return;

    const subscriptions: Subscription[] = [];
    const projectIdNumber = typeof projectId === 'string' ? parseInt(projectId, 10) : projectId;

    // Subscribe to specific project changes only
    const projectsSubscription = database.collections
      .get<ProjectModel>('projects')
      .query(Q.where('remoteId', projectIdNumber))
      .observe()
      .subscribe(projects => {
        if (projects.length > 0 && projects[0].subscriptionPackageId !== projectSubscriptionPackageId) {
          setForceUpdate(prev => prev + 1);
        }
      });

    // Subscribe to specific subscription package changes only (if we have a package ID)
    if (projectSubscriptionPackageId) {
      const subscriptionPackagesSubscription = database.collections
        .get<SubscriptionPackageModel>('subscription_packages')
        .query(Q.where('remoteId', projectSubscriptionPackageId))
        .observe()
        .subscribe(() => {
          setForceUpdate(prev => prev + 1);
        });

      subscriptions.push(subscriptionPackagesSubscription);
    }

    subscriptions.push(projectsSubscription);

    return () => {
      subscriptions.forEach(sub => sub.unsubscribe());
    };
  }, [projectId, projectSubscriptionPackageId]);

  const isFeatureAllowed = (feature: ProjectAccess): boolean => {
    // Check permission directly from the current subscription package
    if (currentSubscriptionPackage) {
      return currentSubscriptionPackage[feature] ?? false;
    }

    // Default to false if no subscription package found
    // This ensures secure behavior - features are disabled unless explicitly allowed
    return false;
  };

  const getProjectSubscriptionPackage = () => {
    // Return the current subscription package data
    if (currentSubscriptionPackage) {
      return {
        id: currentSubscriptionPackage.remoteId.toString(),
        title: currentSubscriptionPackage.title,
        allowTask: currentSubscriptionPackage.allowTask,
        allowProjectDocument: currentSubscriptionPackage.allowProjectDocument,
        allowWorkProgramme: currentSubscriptionPackage.allowWorkProgramme,
        allowCorrespondence: currentSubscriptionPackage.allowCorrespondence,
        allowWorkspaceDocument: currentSubscriptionPackage.allowWorkspaceDocument,
        allowWorkspaceTemplate: currentSubscriptionPackage.allowWorkspaceTemplate,
        allowDrawing: currentSubscriptionPackage.allowDrawing,
        allowBimModel: currentSubscriptionPackage.allowBimModel,
        allowPhoto: currentSubscriptionPackage.allowPhoto,
        allowScheduleChart: currentSubscriptionPackage.allowScheduleChart,
        allowScheduleActivity: currentSubscriptionPackage.allowScheduleActivity,
        allowDashboard: currentSubscriptionPackage.allowDashboard,
        allowEmailCorrespondence: currentSubscriptionPackage.allowEmailCorrespondence
      };
    }

    return null;
  };

  const result = {
    projectSubscriptionPackageId,
    projectSubscriptionPackage: getProjectSubscriptionPackage(),
    loading,
    error,
    isFeatureAllowed,
    hasProjectSubscriptionPackage: !!projectSubscriptionPackageId
  };

  return result;
};
