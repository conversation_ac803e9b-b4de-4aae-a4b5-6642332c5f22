import { useCallback, useRef } from 'react';
import { SystemService } from '@api/rest';
import useSyncAllDrawing from '@src/mutation/offline-download/useSyncAllDrawing';
import useSaveProjectDocument from '@src/mutation/project-document/useSaveProjectDocument';
import useSyncMutation from '@src/mutation/useSync';
import { startBackgroundSync, isSyncRunning } from '@src/mutation/offline-download/useSyncTemplates';
import { startSync } from '@src/background/project-document/presigned-upload';
import { useDispatch } from '@src/store';
import { AppActions } from '@src/slice/app.slice';
import { SyncErrorHandler } from '@src/lib/errorHandler';
import { exponentialBackoff } from '@src/lib/network';
import { AbortError } from '@src/lib/error';
import { useSelector } from '@store/index';
import { useAbortController } from './useAbortController';
import { syncMutex } from '@lib/syncMutex';
import useSyncAllProjectCarousel from '@src/mutation/offline-download/useSyncAllProjectCarousel';
import { updateLastSyncTimestamp } from '@src/utils/syncTimestamp';

const SYNC_TIMEOUT = 300000; // 5 minutes
const MAX_RETRIES = 0;

const useSyncWithDownload = (options = { showSyncModal: false, firstTimeSync: false }) => {
  const { mutateAsync: syncMutate } = useSyncMutation();
  const { mutateAsync: syncAllDrawing } = useSyncAllDrawing();
  const { mutateAsync: handleOfflineDownload } = useSaveProjectDocument();
  const { mutateAsync: syncAllProjectCarousel } = useSyncAllProjectCarousel();
  const dispatch = useDispatch();
  const { syncStatus, syncDisabled } = useSelector(state => state.app);
  const { signal, isMounted, abort, renewController } = useAbortController();

  if (options.showSyncModal) {
    dispatch(AppActions.setShowSyncModal(true));
  }

  const executePhase = async <T>(operation: (phaseSignal: AbortSignal) => Promise<T>, phase: string): Promise<T> => {
    let attempt = 0;

    while (attempt <= MAX_RETRIES) {
      const phaseController = new AbortController();
      const timeoutId = setTimeout(() => {
        phaseController.abort();
      }, SYNC_TIMEOUT);

      // Link to both global and phase-specific abortion
      const onAbort = () => phaseController.abort();
      signal.addEventListener('abort', onAbort);

      try {
        return await operation(phaseController.signal);
      } catch (error) {
        await SyncErrorHandler.capture(error as any, { phase });

        if (error instanceof AbortError || attempt >= MAX_RETRIES) {
          updateState(() => {
            dispatch(AppActions.setSyncStatus('failed'));
            dispatch(AppActions.setSyncProgress(0));
            dispatch(AppActions.setShowSyncModal(false));
          });
          throw error;
        }

        attempt++;
        await exponentialBackoff(attempt);
      } finally {
        clearTimeout(timeoutId);
        signal.removeEventListener('abort', onAbort);
        phaseController.abort();
      }
    }

    throw new AbortError('Max retries reached');
  };

  const isSyncInProgressRef = useRef(false);

  const updateState = useCallback(
    (fn: () => void) => {
      if (isMounted) fn();
    },
    [isMounted]
  );

  const syncAndDownload = useCallback(
    async (options: {
      syncMutateOptions: { dispatchStatus: boolean };
      offlineDownloadOptions: { id: string; dispatchStatus: boolean };
      showSyncModal?: boolean;
      firstTimeSync?: boolean;
    }) => {
      if (!isMounted) return; // Early return if not mounted

      if (isSyncInProgressRef.current) {
        console.warn('Sync already in progress');
        return;
      }

      // Skip sync if it's disabled (e.g., when in document detail pages)
      if (syncDisabled) {
        return;
      }

      try {
        if (options.showSyncModal) {
          updateState(() => dispatch(AppActions.setShowSyncModal(true)));
        }
        // Check system health before sync
        await SystemService.alive();
      } catch (error) {
        dispatch(AppActions.setSyncStatus('failed'));
        setTimeout(() => {
          dispatch(AppActions.setShowSyncModal(false));
        }, 3000);
        return;
      }

      // Wrap the whole sync operation in the mutex
      await syncMutex.runExclusive(async () => {
        try {
          isSyncInProgressRef.current = true;
          renewController();
          dispatch(AppActions.setSyncStatus('pending'));
          dispatch(AppActions.setSyncProgress(0));

          // Skip presigned-upload for first time sync
          if (!options.firstTimeSync) {
            await executePhase(async phaseSignal => {
              await startSync(phaseSignal);
            }, 'presigned-upload');
          }

          await executePhase(async () => {
            await syncMutate(options.syncMutateOptions.dispatchStatus);
          }, 'data-sync');

          await executePhase(async () => {
            await syncAllDrawing();
          }, 'drawing-sync');

          // Skip offline-download for first time sync
          if (!options.firstTimeSync) {
            await executePhase(async () => {
              await handleOfflineDownload(options.offlineDownloadOptions);
            }, 'offline-download');
          }

          await executePhase(async () => {
            await syncAllProjectCarousel();
          }, 'project-carousel-sync');

          await executePhase(async () => {
            if (!(await isSyncRunning())) {
              startBackgroundSync();
            }
          }, 'background-sync');

          // Update the last sync timestamp after successful sync
          await updateLastSyncTimestamp();

          updateState(() => dispatch(AppActions.setSyncProgress(100)));
        } catch (error) {
          updateState(() => {
            dispatch(AppActions.setSyncStatus('failed'));
            setTimeout(() => {
              if (isMounted) {
                dispatch(AppActions.setSyncStatus('idle'));
                dispatch(AppActions.setShowSyncModal(false));
              }
            }, 2000);
          });
          throw error;
        } finally {
          isSyncInProgressRef.current = false;
          updateState(() => {
            dispatch(AppActions.setShowSyncModal(false));
            dispatch(AppActions.setSyncStatus('idle'));
            dispatch(AppActions.setSyncProgress(0));
          });
        }
      });
    },
    [dispatch, syncMutate, syncAllDrawing, handleOfflineDownload, isMounted, renewController, syncStatus, updateState]
  );

  return {
    syncAndDownload,
    abortSync: abort
  };
};

export default useSyncWithDownload;
