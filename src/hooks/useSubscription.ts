import { Gql } from '@src/api';
import { AuthActions } from '@src/slice/auth.slice';
import { useDispatch } from '@src/store';
import { useCallback, useState } from 'react';

const useSubscription = (user: any) => {
  const [isGracePeriod, setIsGracePeriod] = useState(false);
  const [isSubscriptionActive, setIsSubscriptionActive] = useState(true);
  const [hasCheckedSubscriptions, setHasCheckedSubscriptions] = useState(false);
  const [isCompanyOwner, setIsCompanyOwner] = useState(false); // New state for company owner status
  const dispatch = useDispatch();

  const [getCompanySubscription, { data: companySubscription }] = Gql.useCompanySubscriptionsLazyQuery({
    variables: {
      sorting: {
        direction: Gql.SortDirection.Desc,
        field: Gql.CompanySubscriptionSortFields.SubscriptionEndDate
      },
      paging: { limit: 1 }
    },
    fetchPolicy: 'cache-and-network'
  });

  const [getCompany, { data: company }] = Gql.useCompanyLazyQuery({
    variables: { id: user?.getUserMe?.company?.id ?? '' },
    fetchPolicy: 'cache-and-network'
  });

  const checkSubscriptions = useCallback(async () => {
    if (hasCheckedSubscriptions || !user?.getUserMe?.company?.id) return;

    const companySubscriptionRes = await getCompanySubscription();
    const companySubscriptionData = companySubscriptionRes?.data?.companySubscriptions?.nodes[0];

    setIsGracePeriod(companySubscriptionData?.isSubscriptionInGracePeriod ?? false);
    setIsSubscriptionActive(companySubscriptionData?.isSubscriptionActive ?? false);

    if (companySubscriptionData) {
      const [companyRes] = await Promise.all([getCompany()]);
      const companyData = companyRes?.data?.company;

      // Set company data and check if the user is the owner
      dispatch(AuthActions.setCompany((companyData as any) ?? {}));
      setIsCompanyOwner(companyData?.ownerId === user?.getUserMe?.id);
    }

    setHasCheckedSubscriptions(true);
  }, [user, hasCheckedSubscriptions, getCompanySubscription, getCompany, dispatch]);

  return { isGracePeriod, isSubscriptionActive, isCompanyOwner, checkSubscriptions, company, companySubscription };
};

export default useSubscription;
