import { useEffect, useRef } from 'react';
import NetInfo from '@react-native-community/netinfo';
import useSyncWithDownload from '@src/hooks/useSyncWithDownload';

const useRestoreConnection = () => {
  const isFirstCheck = useRef(true);
  const wasOffline = useRef(false);
  const { syncAndDownload } = useSyncWithDownload();

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      const { isConnected } = state;
      if (isFirstCheck.current) {
        isFirstCheck.current = false;
        wasOffline.current = !isConnected;
      } else if (wasOffline.current && isConnected) {
        (async () => {
          try {
            syncAndDownload({
              syncMutateOptions: { dispatchStatus: true },
              offlineDownloadOptions: { id: '', dispatchStatus: true },
              showSyncModal: true
            });
          } catch (error) {}
        })();
      }

      wasOffline.current = !isConnected;
    });

    return () => unsubscribe();
  }, []);
};

export default useRestoreConnection;
