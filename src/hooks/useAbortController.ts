// Updated useAbortController.ts
import { useRef, useEffect } from 'react';

export const useAbortController = () => {
  const abortControllerRef = useRef(new AbortController());
  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      abortControllerRef.current.abort();
    };
  }, []);

  const renewController = () => {
    if (!isMountedRef.current) return;
    abortControllerRef.current.abort();
    abortControllerRef.current = new AbortController();
  };

  return {
    signal: abortControllerRef.current.signal,
    abort: () => abortControllerRef.current.abort(),
    renewController,
    get isMounted() {
      // 👈 Use a getter for fresh value
      return isMountedRef.current;
    }
  };
};
