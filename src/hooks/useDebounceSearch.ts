import { useEffect, useState } from 'react';
import debounce from 'lodash/debounce';

const useDebouncedSearch = (debounceTime = 700) => {
  const [inputValue, setInputValue] = useState('');
  const [debouncedValue, setDebouncedValue] = useState('');

  useEffect(() => {
    const handler = debounce((nextValue: string) => {
      setDebouncedValue(nextValue);
    }, debounceTime);

    handler(inputValue);

    return () => {
      handler.cancel();
    };
  }, [inputValue, debounceTime]);

  const handleInputChange = (value: string) => {
    setInputValue(value);
  };

  return {
    inputValue,
    debouncedValue,
    setInputValue: handleInputChange
  };
};

export default useDebouncedSearch;
