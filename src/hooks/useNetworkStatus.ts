// utils/useNetworkStatus.ts

import { useState, useEffect } from 'react';
import NetInfo, { NetInfoState } from '@react-native-community/netinfo';

const useNetworkStatus = (): boolean => {
  const [isConnected, setIsConnected] = useState<boolean | null>(false);

  useEffect(() => {
    const handleConnectivityChange = (state: NetInfoState) => {
      setIsConnected(state.isConnected && state.isInternetReachable !== null && state.isInternetReachable);
    };

    const unsubscribe = NetInfo.addEventListener(handleConnectivityChange);

    NetInfo.fetch().then(handleConnectivityChange);

    return () => unsubscribe();
  }, []);

  return isConnected!;
};

export default useNetworkStatus;
