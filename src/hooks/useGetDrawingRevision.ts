import { useState, useEffect, useCallback } from 'react';
import { useSelector } from '@src/store';
import { Gql } from '@src/api';
import { useRealm } from '@src/database/config';

type IProjectDocument = {
  projectDocumentId: string;
};

const useGetDrawingRevision = ({ projectDocumentId }: IProjectDocument) => {
  const realm = useRealm();
  const { OFFLINE_MODE } = useSelector(state => state.app);
  const [drawingRevision, setDrawingRevision] = useState<any>([]);

  const [getDrawingRevision, { data: drawingRevisionData, refetch, loading }] = Gql.useGetDrawingRevisionsLazyQuery({
    variables: {
      sorting: {
        direction: Gql.SortDirection.Desc,
        field: Gql.DrawingRevisionSortFields.CreatedAt
      },
      filter: {
        projectDocumentId: { eq: parseInt(projectDocumentId) }
      }
    }
  });

  const getOfflineRevision = useCallback((projectDocumentId: string) => {
    return realm.objects('DrawingRevision').filtered('projectDocumentId == $0', projectDocumentId).sorted('id', true);
  }, []);

  useEffect(() => {
    if (OFFLINE_MODE) {
      const offlineData = getOfflineRevision(projectDocumentId);
      setDrawingRevision(offlineData);
    } else {
      getDrawingRevision();
    }
  }, [OFFLINE_MODE, projectDocumentId]);

  useEffect(() => {
    if (drawingRevisionData) {
      setDrawingRevision(drawingRevisionData.drawingRevisions.nodes);
    }
  }, [drawingRevisionData]);

  return {
    drawingRevision,
    isLoading: OFFLINE_MODE ? false : loading,
    refetch
  };
};

export default useGetDrawingRevision;
