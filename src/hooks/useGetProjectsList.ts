import { useState, useEffect, useCallback } from 'react';
import { useSelector } from '@src/store';
import { Gql } from '@src/api';
import { useGetProjects } from '@src/database/services/useProjects';

type IProject = {
  title?: string;
  imageUrl?: string;
  companyName?: string;
  lastUpdated?: string;
  limit: number;
  filter: Gql.ProjectFilter;
};

const useProjects = ({ limit, filter }: IProject) => {
  const user = useSelector(state => state.auth);
  const { OFFLINE_MODE } = useSelector(state => state.app);
  const [projects, setProjects] = useState<any>([]);
  const getOfflineProjects = useGetProjects();
  const [hasNextPage, setHasNextPage] = useState<boolean>(false);
  const [offlineProjects, setOfflineProjects] = useState(new Map()); // New state for offline projects

  const [getProjects, { data, loading, fetchMore, refetch }] = Gql.useAllProjectsLazyQuery({
    variables: {
      filter,
      paging: {
        offset: 0,
        limit
      }
    },
    fetchPolicy: 'cache-and-network'
  });

  const loadMoreProjects = useCallback(() => {
    if (OFFLINE_MODE || !data?.projects.pageInfo.hasNextPage) {
      return;
    }
    fetchMore({
      variables: {
        paging: {
          offset: data.projects.nodes.length,
          limit
        }
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;
        return {
          projects: {
            ...fetchMoreResult.projects,
            nodes: [...prev.projects.nodes, ...fetchMoreResult.projects.nodes]
          }
        };
      }
    });
  }, [data, fetchMore, OFFLINE_MODE, limit]);

  const refetchProjects = useCallback(
    (searchTerm: any) => {
      const refetchFilter = {
        ...filter,
        ...(searchTerm && { title: { like: `%${searchTerm}%` } })
      };

      if (OFFLINE_MODE) {
        const offlineData = getOfflineProjects({
          title: searchTerm
        });
        setProjects(offlineData);
      } else {
        return refetch({
          filter: refetchFilter,
          paging: {
            offset: 0,
            limit
          }
        });
      }
    },
    [getOfflineProjects, OFFLINE_MODE, refetch, limit]
  );

  const getOfflineProjectsAll = useCallback(() => {
    const offlineProjects = getOfflineProjects({ title: '' });

    offlineProjects.forEach(doc => {
      setOfflineProjects(offlineProjects => new Map(offlineProjects.set(doc.id, true)));
    });
  }, []);

  useEffect(() => {
    if (OFFLINE_MODE) {
      const offlineData = getOfflineProjects({ title: filter?.title as string });
      setProjects(offlineData);
    } else {
      getProjects();
    }
  }, [OFFLINE_MODE, user?.company]);

  useEffect(() => {
    if (data) {
      setProjects(data.projects.nodes);
      setHasNextPage(data.projects.pageInfo.hasNextPage as boolean);
    }
  }, [data]);

  return {
    projects,
    loading: OFFLINE_MODE ? false : loading,
    loadMoreProjects,
    refetch,
    refetchProjects,
    getOfflineProjects,
    offlineProjects,
    hasNextPage: OFFLINE_MODE ? false : hasNextPage
  };
};

export default useProjects;
