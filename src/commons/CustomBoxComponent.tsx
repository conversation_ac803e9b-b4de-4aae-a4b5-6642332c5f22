import { COLORS } from '@src/constants';
import { Box, Divider, Text, VStack } from 'native-base';
import React from 'react';
import { StyleSheet } from 'react-native';

const CustomBoxComponent = ({
  title,
  description
}: {
  source?: any;
  iconBg?: string;
  title: string;
  description?: React.ReactNode;
  mt?: number | string;
  style?: React.CSSProperties;
}) => {
  return (
    <>
      <Box style={styles.dashboardBox}>
        <VStack space={1}>
          <Text fontWeight={700} color={COLORS.neutrals.gray90} fontSize={16}>
            {title}
          </Text>
          <Text fontWeight={400} color={COLORS.neutrals.gray100} fontSize={14}>
            {description}
          </Text>
        </VStack>
      </Box>
    </>
  );
};

const styles = StyleSheet.create({
  dashboardBox: {
    backgroundColor: 'white',
    paddingLeft: 12,
    paddingRight: 12,
    paddingTop: 16,
    paddingBottom: 16
  }
});

export { CustomBoxComponent };
