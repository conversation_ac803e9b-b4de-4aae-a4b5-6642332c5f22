import React, { useState, useEffect } from 'react';
import FastImage from 'react-native-fast-image';
import { StyleSheet, Image } from 'react-native';

// Define a custom type for FastImage source props
interface FastImageSourceProps {
  uri: string;
  cache: 'immutable' | 'web' | 'cacheOnly';
  priority: 'low' | 'normal' | 'high';
}

// Define the types for the component props
interface SafeImageProps {
  initialUri: string;
  style?: any;
}

const SafeImage: React.FC<SafeImageProps> = ({ initialUri, style }) => {
  // Retrieve the URI for the default image
  const defaultImage = require('@assets/default-project.png');
  const defaultImageUri = Image.resolveAssetSource(defaultImage).uri; // Get the correct URI from the required asset

  // Initialize the image source state
  const [source, setSource] = useState<FastImageSourceProps>({
    uri: initialUri || defaultImageUri,
    cache: 'immutable',
    priority: 'high'
  });

  const isDefaultImage = source.uri === Image.resolveAssetSource(defaultImage).uri;

  useEffect(() => {
    if (!initialUri) {
      setSource({
        uri: defaultImageUri,
        cache: 'immutable',
        priority: 'high'
      });
    }
  }, [initialUri, defaultImageUri]);

  const handleImageError = () => {
    if (source.uri !== defaultImageUri) {
      setSource({
        uri: defaultImageUri,
        cache: 'immutable',
        priority: 'high'
      });
    }
  };

  return (
    <FastImage
      style={StyleSheet.flatten(style)}
      source={source}
      onError={handleImageError}
      fallback={true}
      resizeMode={'cover'}
    />
  );
};

export default SafeImage;
