import React from 'react';
import _ from 'lodash';
import CheckBox from 'react-native-checkbox-animated';
import { COLORS } from '@constants';
import { Icon } from '@commons';
import { Circle } from 'native-base';

interface Props {
  checked: boolean;
  disabled?: boolean;
  onChange: (val: boolean) => boolean;
}

const Checkbox: React.FC<Props> = ({ checked, disabled, ...props }) => {
  // const [toggleCheckBox, setToggleCheckBox] = useState(false);
  const onChange = (c: boolean) => {
    // setToggleCheckBox(c);
    props.onChange(c);
    return c;
  };

  // useEffect(() => {
  //   setToggleCheckBox(checked);
  // }, [checked]);

  return (
    <CheckBox
      label={false}
      rounded
      onValueChange={onChange}
      checked={checked}
      checkedBackgroundColor={COLORS.primary[1]}
      customMarker={<Circle />}
      checkboxContainerStyle={{ padding: 0 }}
      unCheckedBorderColor={COLORS.neutrals.gray50}
    />
  );
};

// const styles = StyleSheet.create({
//   checkbox: {
//     height: 22,
//     width: 22,
//     borderRadius: 11,
//     borderWidth: 1,
//     borderColor: COLORS.grayLight,

//     justifyContent: 'center',
//     alignItems: 'center'
//   },
//   checkboxOuter: {
//     height: 22,
//     width: 22,
//     borderRadius: 11
//   }
// });

Checkbox.defaultProps = {
  onChange: (v: any) => v,
  disabled: false
};

export { Checkbox };
