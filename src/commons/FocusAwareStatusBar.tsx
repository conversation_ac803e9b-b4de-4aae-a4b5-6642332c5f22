import React from 'react';
import { StatusBar } from 'react-native';
import { useIsFocused } from '@react-navigation/native';

interface Props {
  translucent?: boolean;
  barStyle?: 'light-content' | 'dark-content';
  backgroundColor?: string;
}

const FocusAwareStatusBar: React.FC<Props> = props => {
  const isFocused = useIsFocused();

  return isFocused ? <StatusBar {...props} /> : null;
};

export { FocusAwareStatusBar };
