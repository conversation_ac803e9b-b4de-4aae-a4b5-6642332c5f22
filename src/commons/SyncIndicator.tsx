import React from 'react';
import { Text, VStack } from 'native-base';
import Modal from './Modal';
import { useSelector } from '@store';
import * as Progress from 'react-native-progress';

const SyncIndicator = () => {
  const { showSyncModal, syncProgress, syncStatus } = useSelector(state => state.app);
  const isShowSyncModal = showSyncModal;

  let message = '';
  let iconName: any = '';
  let additionalMessage = '';

  switch (syncStatus) {
    case 'upload':
      message = 'Uploading documents...';
      iconName = 'sync';
      additionalMessage = 'Please keep the app open.';
      break;
    case 'pending':
      message = 'Syncing your offline activity...';
      iconName = 'sync';
      additionalMessage = 'Please keep the app open.';
      break;
    case 'success':
      message = 'Sync Complete!';
      iconName = 'sync';
      additionalMessage = 'All data is up to date.';
      break;
    case 'failed':
      message = 'Unable to sync.';
      iconName = 'sync';
      additionalMessage = 'Check your connection and try again later';
      break;
  }

  return (
    <Modal
      isVisible={isShowSyncModal}
      type="middle"
      containerStyle={{ backgroundColor: 'transparent' }}
      backdropColor="#F5F7FB"
      backdropOpacity={0.8}
    >
      <VStack display={'flex'} justifyItems={'center'} justifyContent={'center'} alignItems={'center'} padding={7}>
        <Progress.Circle
          size={70}
          progress={syncProgress / 100}
          thickness={3}
          color="#0695D7"
          unfilledColor="#E6E6ED"
          borderWidth={0}
        />
        <VStack justifyContent={'center'} justifyItems={'center'} alignItems={'center'} mt={1}>
          <Text variant="h4">{`${syncProgress}%`}</Text>
          <Text variant="body2">{message}</Text>
          {additionalMessage && <Text variant="body2">{additionalMessage}</Text>}
        </VStack>
      </VStack>
    </Modal>
  );
};

export default SyncIndicator;
