import { Icon } from '@src/commons';
import _ from 'lodash';
import React from 'react';

const FileIcons = {
  folder: <Icon name="folder" />,
  Folder: <Icon name="folder" />,
  pdf: <Icon name="filetype-pdf" />,
  PDF: <Icon name="filetype-pdf" />,
  doc: <Icon name="filetype-docs" />,
  docs: <Icon name="filetype-docs" />,
  docx: <Icon name="filetype-docs" />,
  xlsx: <Icon name="xlsx" />,
  pptx: <Icon name="pptx" />,
  dwg: <Icon name="filetype-dwg" />,
  DWG: <Icon name="filetype-dwg" />,
  obj: <Icon name="filetype-dwg" />,
  rvt: <Icon name="filetype-rvt" />,
  fbx: <Icon name="filetype-fbx" />,
  nwd: <Icon name="filetype-nwd" />,
  drw: <Icon name="filetype-drw" />,
  urn: <Icon name="filetype-dwg" />,
  excel: <Icon name="filetype-excel" />,
  svg: <Icon name="filetype-image" />,
  png: <Icon name="filetype-image" />,
  jpg: <Icon name="filetype-image" />,
  PNG: <Icon name="filetype-image" />,
  jpeg: <Icon name="filetype-image" />,
  msProject: <Icon name="filetype-project" />,
  storeDocument: <Icon name="store-document" />
};

export const getFileIcon = (type: string) => _.get(FileIcons, type) ?? '';

export { FileIcons };
