import React from 'react';
import { Box, Image, Text } from 'native-base';
import notFound from '@assets/not_found.png';
import { Dimensions } from 'react-native';

const NotFound = () => {
  const { height } = Dimensions.get('screen');

  return (
    <Box w={'full'} h={height / 2} style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
      <Image source={notFound} w={100} h={100} alt="Not Found" />
      <Text>No data</Text>
    </Box>
  );
};

export default NotFound;
