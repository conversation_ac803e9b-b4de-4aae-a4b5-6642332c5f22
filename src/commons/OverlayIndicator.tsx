import { useSelector } from '@src/store';
import { Spinner } from 'native-base';
import React from 'react';
import { StyleSheet, View } from 'react-native';

type Props = {};

const OverlayIndicator = (props: Props) => {
  const auth = useSelector(state => state.auth);

  return auth.requestForLoggedOut ? (
    <View style={styles.loading}>
      <Spinner size={'lg'} />
    </View>
  ) : null;
};

const styles = StyleSheet.create({
  loading: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 9999,
    backgroundColor: 'rgba(0, 0, 0, 0.4)'
  }
});

export default OverlayIndicator;
