import { Icon } from '@commons/Icon';
import { COLORS } from '@constants';
import { useNavigation } from '@react-navigation/native';
import { Gql } from '@src/api';
import _ from 'lodash';
import { Box, Button, HStack, Pressable, Text, VStack } from 'native-base';
import React from 'react';
import { Platform, StatusBar } from 'react-native';

type MoreHeaderBarProps = {
  goBack?: boolean;
  onGoBack?: () => void;
  background?: string;
  hasShadow?: boolean;
  title?: string;
  transparent?: boolean;
  rightComponent?: any;
  leftIconProps?: object;
  dark?: boolean;
  barStyle?: 'light-content' | 'dark-content';
  modules?: any;
};
const MoreHeaderBar: React.FC<MoreHeaderBarProps> = props => {
  const navigation = useNavigation();
  return (
    <Box py={4}>
      <StatusBar
        translucent={false}
        backgroundColor={Platform.OS === 'android' ? '#FFF' : props.transparent ? 'transparent' : props.background}
        barStyle={Platform.OS === 'android' ? 'dark-content' : props.barStyle || 'dark-content'}
      />
      <HStack
        justifyContent="space-between"
        alignItems="center"
        space={2}
        marginTop={props.modules === Gql.CategoryType.Photo ? 4 : 10}
        marginRight={5}
      >
        <HStack space={2} position={'absolute'} left={0} zIndex={10}>
          <Pressable
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            bg="transparent"
            onPress={() => (props.onGoBack ? props.onGoBack() : navigation.goBack())}
            justifyContent="flex-start"
            ml={3}
            hitSlop={10}
          >
            <Icon name={'chevron-left'} fill={props.dark ? '#FFF' : COLORS.neutrals.gray90} {...props.leftIconProps} />
          </Pressable>

          <Pressable
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            m={'auto'}
            bg="transparent"
            onPress={() => (props.onGoBack ? props.onGoBack() : navigation.goBack())}
            hitSlop={10}
          >
            <Icon name={'bina-logo'} fill={props.dark ? '#FFF' : COLORS.neutrals.gray90} {...props.leftIconProps} />
          </Pressable>
        </HStack>

        <VStack>
          <Text
            textAlign="center"
            fontWeight={600}
            fontSize="18px"
            color={props.dark ? '#FFF' : 'neutrals.black'}
            // m='auto'
          >
            {props.title || ''}
          </Text>
        </VStack>

        <HStack position={'absolute'} right={0}>
          {props.rightComponent && props.rightComponent}
        </HStack>
      </HStack>
    </Box>
  );
};

MoreHeaderBar.defaultProps = {
  background: '#FFFFFF',
  goBack: false,
  hasShadow: false,
  transparent: false,
  dark: false
};

export { MoreHeaderBar };
export default MoreHeaderBar;
