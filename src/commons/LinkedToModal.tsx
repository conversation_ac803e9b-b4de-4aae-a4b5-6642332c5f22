import { Icon, Modal } from '@commons';
import { Box, Button, Divider, FlatList, Flex, HStack, Input, Pressable, Spinner, Text, View } from 'native-base';
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Dimensions, ModalProps, RefreshControl, TouchableOpacity } from 'react-native';
import { Gql } from '@src/api';
import { useSelector } from '@src/store';
import { COLORS } from '@src/constants/colors';
import { getFileIcon } from './FileIcon';
import _ from 'lodash';
import NotFound from './NotFound';
import PhotoFiles from '@src/modules/Photos/components/PhotoFiles';
import { Q } from '@nozbe/watermelondb';
import database from '@src/database/index.native';

interface Props {
  documents?: any;
  setLinkedDocument: (file: any) => void;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const LinkedToModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const [selectedModule, setSelectedModule] = useState<any | null>(null);
  const [selectedTab, setSelectedTab] = useState<Gql.CategoryType | null>(null);
  const [projectDocumentId, setProjectDocumentId] = useState<string | null>(null);
  const states = useSelector(state => state.project);
  const projectId = states.projectId;
  const [documents, setDocuments] = useState<any[]>([]);
  const [filteredValue, setFilteredValue] = useState<any>('');
  const [loading, setLoading] = useState<boolean>(false);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const tabNames: any = {
    [Gql.CategoryType.ProjectDocument]: 'Project Documents',
    [Gql.CategoryType.Correspondence]: 'Correspondence',
    [Gql.CategoryType.AllForm]: 'Documents',
    [Gql.CategoryType.WorkProgramme]: 'Work Programme',
    [Gql.CategoryType.BimDrawings]: 'BIM Models',
    [Gql.CategoryType.TwoDDrawings]: 'Drawings',
    [Gql.CategoryType.StandardForm]: 'Templates',
    ['Recent']: 'Recent',
    ['Folder']: 'Folder'
  };
  const modules = [
    {
      name: 'Cloud Docs',
      icon: 'cloud-docs',
      type: [Gql.CategoryType.ProjectDocument, Gql.CategoryType.WorkProgramme, Gql.CategoryType.Correspondence]
    },
    {
      name: 'Workspace',
      icon: 'digital-form',
      type: [Gql.CategoryType.AllForm, Gql.CategoryType.StandardForm]
    },
    {
      name: 'Drawings',
      icon: 'drw',
      type: [Gql.CategoryType.TwoDDrawings, Gql.CategoryType.BimDrawings]
    },
    {
      name: 'Photos',
      icon: 'photos',
      type: ['Recent', 'Folder']
    }
  ];

  const debounceModuleTab = useCallback(
    _.debounce((module, tab, setDocuments, setLoading, projectId, projectDocumentId) => {
      (async () => {
        if (!projectId) {
          setLoading(false);
          return;
        }

        setLoading(true);
        const conditions = [];
        if (tab) {
          conditions.push(Q.where('projectId', parseInt(projectId)));
          if (projectDocumentId) {
            conditions.push(Q.where('projectDocumentId', parseInt(projectDocumentId)));
          } else {
            conditions.push(Q.where('projectDocumentId', null));
          }

          // ============= PHOTOS =============
          if (tab === 'Recent' || tab === 'Folder') {
            if (tab === 'Recent') {
              conditions.push(Q.where('fileSystemType', 'Document'));
            }
            conditions.push(Q.where('category', 'Photo'));
          } else {
            conditions.push(Q.where('category', tab));
          }

          if (tab === Gql.CategoryType.ProjectDocument || tab === Gql.CategoryType.Correspondence) {
            conditions.push(Q.where('driveType', 'Shared'));
          }
        }

        if (conditions.length === 0) {
          setLoading(false);
          return;
        }

        try {
          const documents = await database.collections
            .get('project_documents')
            .query(...conditions, Q.sortBy('fileSystemType', Q.desc), Q.sortBy('name', Q.asc))
            .fetch();

          setDocuments(documents);
        } catch (error) {
        } finally {
          setLoading(false);
        }
      })();
    }, 300),
    [projectId] // Dependencies for useCallback
  );

  useEffect(() => {
    debounceModuleTab(selectedModule, selectedTab, setDocuments, setLoading, projectId, projectDocumentId);
  }, [selectedModule, selectedTab, debounceModuleTab, projectDocumentId]);

  const deviceHeight = Dimensions.get('window').height;

  const renderItem = useCallback(
    ({ item, index }: { item: any; index: number }) => (
      <Flex direction="column" key={index}>
        <TouchableOpacity
          key={item.id}
          onPress={() => {
            if (item.type === 'folder') {
              setFilteredValue('');
              return setProjectDocumentId(item.remoteId);
            } else {
              props.setLinkedDocument(item as Gql.ProjectDocument);
              setSelectedTab(null);
              setSelectedModule(null);
              setProjectDocumentId(null);
              setFilteredValue('');
              modalRef?.current?.closeModal();
            }
          }}
        >
          <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
            <HStack space={2}>
              {getFileIcon(item?.type ?? '')}
              <Text fontSize="16px" color="#8F8989">
                {item.name}
              </Text>
            </HStack>
          </Box>
        </TouchableOpacity>

        <Divider mt={4} />
      </Flex>
    ),
    [documents, selectedTab, selectedModule, projectDocumentId]
  );

  return (
    <Modal ref={modalRef} type="bottom" onShow={() => {}} avoidKeyboard={true}>
      <View style={{ height: deviceHeight - 310 }}>
        <Flex direction="row" justifyContent="space-between" alignItems="center">
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            bg="transparent"
            onPress={() => {
              documents?.length > 0 && setDocuments([]);

              if (selectedTab && selectedModule && projectDocumentId) {
                return setProjectDocumentId(null);
              } else if (selectedTab === Gql.CategoryType.TwoDDrawings && selectedModule) {
                setSelectedTab(null);
                return setSelectedModule(null);
              } else if (selectedTab && selectedModule && !projectDocumentId) {
                return setSelectedTab(null);
              } else if (selectedModule && !selectedTab) {
                return setSelectedModule(null);
              } else {
                return modalRef?.current?.closeModal();
              }
            }}
          >
            <HStack alignItems={'center'} space={1}>
              <Icon name="chevron-left" fill={COLORS.primary[1]} />
              <Text color={COLORS.primary[1]}>Back</Text>
            </HStack>
          </Button>
        </Flex>
        <Divider color="#E8E8E8" />
        <Flex direction="column" p={5} mb={5}>
          {!selectedModule &&
            modules.map((module, index) => (
              <Box key={index}>
                <TouchableOpacity
                  key={index}
                  onPress={() => {
                    if (module?.name === 'Drawings') {
                      setSelectedModule(module);
                      return setSelectedTab(Gql.CategoryType.TwoDDrawings);
                    }
                    setSelectedModule(module);
                  }}
                >
                  <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
                    <HStack space={2}>
                      <Icon name={module.icon as any} fill={COLORS.primary[1]} />
                      <Text fontSize="16px" color="#8F8989">
                        {module?.name}
                      </Text>
                    </HStack>
                  </Box>
                </TouchableOpacity>
                <Divider mt={4} />
              </Box>
            ))}

          {selectedModule &&
            !selectedTab &&
            selectedModule.type.map((tab: string, index: number) => (
              <Box key={index}>
                <TouchableOpacity
                  key={index}
                  onPress={() => {
                    setSelectedTab(tab as Gql.CategoryType);
                  }}
                >
                  <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
                    <HStack space={2}>
                      <Icon name="folder" />
                      <Text fontSize="16px" color="#8F8989">
                        {tabNames[tab]}
                      </Text>
                    </HStack>
                  </Box>
                </TouchableOpacity>

                <Divider mt={4} />
              </Box>
            ))}

          {selectedModule && selectedTab && (
            <>
              <Input
                backgroundColor={COLORS.neutrals.gray40}
                placeholder="Search for documents"
                my="2"
                borderRadius="8"
                fontSize="14"
                InputLeftElement={
                  <Box pl="2" py="6">
                    <Icon name="search" />
                  </Box>
                }
                value={filteredValue}
                InputRightElement={
                  <Pressable px="4" py="6" onPress={() => setFilteredValue('')}>
                    {filteredValue !== '' && <Icon name="cancel" />}
                  </Pressable>
                }
                onChangeText={text => setFilteredValue(text)}
              />
              {loading ? (
                <Spinner size={'lg'} />
              ) : selectedModule?.name === 'Photos' ? (
                <PhotoFiles
                  data={documents?.filter((item: any) => item?.type?.toLowerCase?.() !== 'mp4') ?? []}
                  loading={loading}
                  filterValue={''}
                  loadMore={() => {}}
                  refetch={() => {}}
                  fetchMoreLoading={false}
                  photosData={documents?.filter((item: any) => item?.type?.toLowerCase?.() !== 'mp4') ?? []}
                  searchLoading={false}
                  key={'photo'}
                  spacingBottom={false}
                  disableThreeDots={true}
                  onClick={(item: any) => {
                    if (item.type === 'folder') {
                      setFilteredValue('');
                      return setProjectDocumentId(item.id);
                    } else {
                      props.setLinkedDocument(item as Gql.ProjectDocument);
                      setSelectedTab(null);
                      setSelectedModule(null);
                      setProjectDocumentId(null);
                      setFilteredValue('');
                      modalRef?.current?.closeModal();
                    }
                  }}
                />
              ) : (
                <FlatList
                  keyboardShouldPersistTaps="handled"
                  data={documents}
                  keyExtractor={item => item.id}
                  onEndReachedThreshold={0.1}
                  refreshControl={<RefreshControl refreshing={loading} onRefresh={() => {}} />}
                  onEndReached={() => {}}
                  ListEmptyComponent={filteredValue.length > 0 && !loading ? <NotFound /> : <></>}
                  // ListFooterComponent={
                  //   documentsSource?.getProjectDocuments.pageInfo.hasNextPage ? <Spinner pb={10} size={'lg'} /> : null
                  // }
                  renderItem={renderItem}
                  contentContainerStyle={{ paddingBottom: 65 }}
                />
              )}
            </>
          )}
        </Flex>
      </View>
    </Modal>
  );
});

LinkedToModal.displayName = 'LinkedToModal';
export default LinkedToModal;
