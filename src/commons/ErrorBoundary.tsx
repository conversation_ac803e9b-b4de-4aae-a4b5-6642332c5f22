import React from 'react';
import { Box, Button, Text, VStack } from 'native-base';
import { COLORS } from '@src/constants';

interface Props {
  error?: Error | null;
  onRetry: () => void;
  children: React.ReactNode;
}

export const ErrorDisplay: React.FC<Omit<Props, 'children'>> = ({ error, onRetry }) => {
  if (!error) return null;

  return (
    <Box flex={1} bg="white" justifyContent="center" alignItems="center" p={4}>
      <VStack space={4} alignItems="center">
        <Text color={COLORS.neutrals.gray90} fontSize={16} fontWeight="500" textAlign="center">
          Something went wrong
        </Text>
        <Text color={COLORS.neutrals.gray70} fontSize={14} textAlign="center">
          {error.message || 'Please try again later'}
        </Text>
        <Button variant="outline" onPress={onRetry}>
          Retry
        </Button>
      </VStack>
    </Box>
  );
};

export const ErrorBoundary: React.FC<Props> = ({ error, onRetry, children }) => {
  if (error) {
    return <ErrorDisplay error={error} onRetry={onRetry} />;
  }

  return <>{children}</>;
};

ErrorBoundary.displayName = 'ErrorBoundary';
ErrorDisplay.displayName = 'ErrorDisplay';
