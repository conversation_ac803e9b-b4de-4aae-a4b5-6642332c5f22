import { Icon } from '@src/commons';
import { Button, Flex, HStack } from 'native-base';
import React from 'react';

type HeaderBarProps = {
  onAddFolder: () => void;
  onUploadFile: () => void;
  onSearch: () => void;
  isDisabledAddFolder?: boolean;
  isDisabledUploadFile?: boolean;
};

const HeaderBar: React.FC<HeaderBarProps> = props => {
  return (
    <>
      <Flex
        direction="row"
        width="full"
        height="48px"
        bg="white"
        alignItems="center"
        justifyContent="space-between"
        px={4}
      >
        <Button
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          bg="transparent"
          onPress={() => {
            props.onSearch();
          }}
          p={0}
          justifyContent="flex-start"
        >
          <Icon name="search" />
        </Button>

        <HStack>
          <Button variant="light" height={36}>
            <Icon name="tree-view" />
          </Button>
          <Button variant="primary" height={36}>
            <Icon name="table-view" />
          </Button>
        </HStack>

        <Button
          variant="light"
          height={36}
          onPress={() => {
            props.onAddFolder();
          }}
          disabled={props.isDisabledAddFolder}
        >
          Add folder
        </Button>
        <Button
          variant="primary"
          height={36}
          onPress={() => {
            props.onUploadFile();
          }}
          disabled={props.isDisabledUploadFile}
        >
          Upload file
        </Button>
      </Flex>
    </>
  );
};

export { HeaderBar };
export default HeaderBar;
