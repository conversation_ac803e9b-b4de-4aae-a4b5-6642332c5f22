import { useNetInfo } from '@react-native-community/netinfo';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { AppActions } from '@src/slice/app.slice';
import { useDispatch, useSelector } from '@src/store';
import { RootNavigatorParams } from '@src/types';
import { useEffect, useState } from 'react';
import { Alert } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import { AuthActions } from '@src/slice/auth.slice';

const OfflineNotice = () => {
  const netInfo = useNetInfo();
  const dispatch = useDispatch();

  useEffect(() => {
    if (netInfo.type?.toString?.() === 'unknown') return;
    if (!netInfo.isConnected) {
      dispatch(AppActions.setOfflineMode(true));
    } else {
      dispatch(AuthActions.initialize());
    }
  }, [netInfo.type, netInfo.isConnected]);

  return null;
};

export default OfflineNotice;
