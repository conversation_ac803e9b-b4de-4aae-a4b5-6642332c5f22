import RNFetchBlob from 'rn-fetch-blob';
import <PERSON><PERSON>iewer from 'react-native-file-viewer';
import { Platform } from 'react-native';
import { pushError } from '@src/configs';
import apolloClient from '@src/lib/apollo';
import { Gql } from '@src/api';

export const openDocsViewer = async (item: any) => {
  try {
    // Check if localFileUrl exists
    if (item.localFileUrl) {
      const localFilePath = Platform.OS === 'android' ? 'file:/' + item.localFileUrl : item.localFileUrl;

      // Open the file directly from localFileUrl
      await FileViewer.open(localFilePath, {
        showOpenWithDialog: true,
        showAppsSuggestions: true,
        displayName: item.name // Display the provided file name
      });
      return true;
    } else {
      // Query file using graphQL
      const response = await apolloClient.query({
        query: Gql.ProjectDocumentDocument,
        variables: {
          id: item.remoteId
        }
      });

      return new Promise((resolve, reject) => {
        RNFetchBlob.config({
          fileCache: true,
          appendExt: item.type
        })
          .fetch('GET', response.data.projectDocument.fileUrl)
          .then(res => {
            const downloadFile = Platform.OS === 'android' ? 'file:/' + res.path() : res.path();

            FileViewer.open(downloadFile, {
              showOpenWithDialog: true,
              showAppsSuggestions: true,
              onDismiss: () => RNFetchBlob.fs.unlink(downloadFile),
              displayName: item.name
            });

            resolve(true);
          })
          .catch(err => {
            pushError(err);
            reject(err);
          });
      });
    }
  } catch (error) {
    pushError(error);
  } finally {
    // Optional cleanup logic can go here, e.g., setLoading(false);
  }
};
