// DriveTypeSelect.tsx
import React from 'react';
import { Select } from 'native-base';

interface DriveTypeSelectProps {
  driveType?: string;
  onDriveTypeChange?: (value: string) => void;
  projectUserRole?: string;
  gotPersonal: boolean;
  Gql: {
    ProjectDocumentDriveType: {
      Shared: string;
      Personal: string;
    };
  };
  OFFLINE_MODE?: boolean;
}

const DriveTypeSelect: React.FC<DriveTypeSelectProps> = ({
  driveType,
  onDriveTypeChange,
  projectUserRole,
  Gql,
  OFFLINE_MODE,
  gotPersonal
}) => {
  return (
    <Select
      minW="130px"
      borderRadius={8}
      borderColor="#E6E6E6"
      backgroundColor="#FFFFFF"
      fontWeight="600"
      fontSize={13}
      mt={3}
      color="blue.600"
      selectedValue={driveType}
      onValueChange={onDriveTypeChange}
      accessibilityLabel={driveType}
      _selectedItem={{ bg: 'gray.300' }}
      isDisabled={projectUserRole === 'CanView' || OFFLINE_MODE}
    >
      <Select.Item key="shared" label="Shared Drives" value={Gql.ProjectDocumentDriveType.Shared} />
      {gotPersonal && (
        <Select.Item key="personal" label="Personal Drives" value={Gql.ProjectDocumentDriveType.Personal} />
      )}
    </Select>
  );
};

export default DriveTypeSelect;
