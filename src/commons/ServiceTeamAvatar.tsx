import React from 'react';
import { Avatar, IAvatarProps } from 'native-base';

interface Props extends IAvatarProps {
  borderWidth?: number;
  size: number | string;
  status: 'available' | 'idle';
  uri: string;
  text?: string;
}

const ServiceTeamAvatar = ({ borderWidth, size, status, uri, text, ...props }: Props) => {
  return (
    <Avatar
      borderWidth={borderWidth || 3}
      borderColor={status === 'available' ? 'semantics.success' : 'semantics.warning'}
      size={size}
      source={{
        uri:
          uri ||
          'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=687&q=80'
      }}
      {...props}
    >
      {text || ''}
    </Avatar>
  );
};

export { ServiceTeamAvatar };
