import React, { useState, useEffect, useRef, forwardRef } from 'react';
import { View, Animated, StyleSheet, ScrollView, Dimensions } from 'react-native';
import { Text } from 'native-base';
import _, { set } from 'lodash';
import { COLORS, FONTS } from '@constants';
import { NativeTouchable } from './NativeTouchable';
import DeviceInfo from 'react-native-device-info';

const WINDOW_WIDTH = Dimensions.get('window').width;
interface Props {
  activeTextColor?: string;
  inactiveTextColor?: string;
  items?: any;
  currentTab?: any;
  onChange?: any;
  onScroll?: any;
  hasUnderlineIndicator?: boolean;
  containerStyle?: any;
}

const ScrollableTabBar = forwardRef(({ items, ...props }: Props, ref) => {
  const { tabs, goToPage, scrollValue } = props as any;
  const scrollView: any = useRef();
  const leftTabUnderline = useRef(new Animated.Value(0)).current;
  const widthTabUnderline = useRef(new Animated.Value(0)).current;
  const scrollPosition = useRef(new Animated.Value(0)).current;

  const [activeTab, setActiveTab] = useState(0);
  const [containerMeasurements, setContainerMesurements] = useState(null);
  const [tabsMeasurements, setTabsMeasurements] = useState([]);
  const [tabContainerMeasurements, setTabContainerMeasurements] = useState({ width: 0 });

  useEffect(() => {
    scrollValue.addListener(updateView);

    return () => {
      scrollValue.removeListener(updateView);
    };
  }, []);

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setTabContainerMeasurements({ width: window.width });
    });
    return () => subscription.remove();
  });

  useEffect(() => {
    // If the tabs change, force the width of the tabs container to be recalculated
    if (activeTab === 0) {
      updateTabUnderline(activeTab, activeTab % 1, items.length);
      updateTabPanel(activeTab, 0);
    }
  }, [activeTab, tabsMeasurements]);

  const updateTabPanel = (position: any, pageOffset: any) => {
    const cWidth = _.get(containerMeasurements, 'width', 0);
    const tabWidth = _.get(tabsMeasurements, `[${position}].width`, 0);
    const nextTabMeasurements = _.get(tabsMeasurements, `[${position}]`, {});
    const nextTabWidth = (nextTabMeasurements && _.get(nextTabMeasurements, 'width', 0)) || 0;
    const tabOffset = _.get(tabsMeasurements, `[${position}].left`, 0);
    const absolutePageOffset = pageOffset * tabWidth;
    let newScrollX = tabOffset + absolutePageOffset;
    // center tab and smooth tab change (for when tabWidth changes a lot between two tabs)
    newScrollX -= (cWidth - (1 - pageOffset) * tabWidth - pageOffset * nextTabWidth) / 2;
    newScrollX = newScrollX >= 0 ? newScrollX : 0;
  };

  const updateTabUnderline = (position: any, pageOffset: any, tabCount: any) => {
    const lineLeft = _.get(tabsMeasurements, `[${position}].left`, 0);
    const lineRight = _.get(tabsMeasurements, `[${position}].right`, 0);

    const duration = 70;

    if (position < tabCount - 1) {
      const nextTabLeft = _.get(tabsMeasurements, `[${position + 1}].left`, 0);
      const nextTabRight = _.get(tabsMeasurements, `[${position + 1}].right`, 0);

      const newLineLeft = pageOffset * nextTabLeft + (1 - pageOffset) * lineLeft;
      const newLineRight = pageOffset * nextTabRight + (1 - pageOffset) * lineRight;

      Animated.parallel([
        Animated.timing(leftTabUnderline, {
          toValue: newLineLeft + 20,
          duration,
          useNativeDriver: false
        }),
        Animated.timing(widthTabUnderline, {
          toValue: newLineRight - newLineLeft - 40,
          duration,
          useNativeDriver: false
        })
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(leftTabUnderline, {
          toValue: lineLeft + 20,
          duration,
          useNativeDriver: false
        }),
        Animated.timing(widthTabUnderline, {
          toValue: lineRight - lineLeft - 40,
          duration,
          useNativeDriver: false
        })
      ]).start();
    }
  };

  const renderTab = (
    name: any,
    page: any,
    isTabActive: any,
    onPressHandler: any,
    onLayoutHandler: any,
    tabLength: number
  ) => {
    const { activeTextColor, inactiveTextColor, activeTabStyle, tabStyle, textStyle, activeTextStyle } = props as any;
    const textColor = isTabActive && name != 'Notifications' ? activeTextColor : inactiveTextColor;
    const fontFamily = isTabActive && name != 'Notifications' ? FONTS.interSemiBold : FONTS.interMedium;
    const divideBy = DeviceInfo.isTablet() ? tabLength : 2;
    const tabWidth =
      tabContainerMeasurements.width > 0 ? tabContainerMeasurements.width / divideBy : WINDOW_WIDTH / divideBy;

    return (
      <NativeTouchable
        key={`${name}_${page}`}
        accessible
        accessibilityLabel={name}
        accessibilityTraits="button"
        onPress={() => onPressHandler(page)}
        onLayout={(e: any) => onLayoutHandler(page, e)}
        disabled={name == 'Notifications' ? true : false}
      >
        <View
          style={[
            styles.tab,
            tabStyle,
            isTabActive && activeTabStyle,
            { width: DeviceInfo.isTablet() ? tabWidth : tabLength === 1 ? WINDOW_WIDTH : WINDOW_WIDTH / 2 }
          ]}
        >
          <Text style={[{ color: textColor, fontFamily }, textStyle, isTabActive && activeTextStyle]}>{name}</Text>
          {isTabActive && <View style={[styles.tabUnderline]} />}
        </View>
      </NativeTouchable>
    );
  };

  // const setTab = (item, i) => {
  //   props.onChange(item);
  //   updateView(i);
  // };

  const updateView = (offset: any) => {
    const position = Math.floor(offset.value);
    const pageOffset = offset.value % 1;
    const tabCount = tabs.length;
    const lastTabPosition = tabCount - 1;

    if (tabCount === 0 || offset.value < 0 || offset.value > lastTabPosition) {
      return;
    }
    setActiveTab(position);
    props.onChange(position);
    updateTabUnderline(position, position % 1, items.length);
    if (necessarilyMeasurementsCompleted(position, position === lastTabPosition)) updateTabPanel(position, pageOffset);
  };

  const necessarilyMeasurementsCompleted = (position: any, isLastTab: any) => {
    return (
      tabsMeasurements[position] &&
      (isLastTab || tabsMeasurements[position + 1]) &&
      tabContainerMeasurements &&
      containerMeasurements
    );
  };

  const measureTab = (page: any, e: any) => {
    const { x, width, height } = e.nativeEvent.layout;
    const measurementsArr = [...tabsMeasurements] as any;
    measurementsArr[page] = { left: x, right: x + width, width, height };
    setTabsMeasurements(measurementsArr);
    updateView({ value: scrollValue.__getValue() });
  };

  const onTabContainerLayout = (e: any) => {
    setTabContainerMeasurements(e.nativeEvent.layout);
    let { width } = tabContainerMeasurements;
    if (width < WINDOW_WIDTH) {
      width = WINDOW_WIDTH;
    }
    // setContainerWidth(width);
    updateView({ value: scrollValue.__getValue() });
  };

  const onContainerLayout = (e: any) => {
    setContainerMesurements(e.nativeEvent.layout);

    updateView({ value: scrollValue.__getValue() });
  };

  const dynamicTabUnderline = {
    left: leftTabUnderline,
    width: widthTabUnderline
  };

  return (
    <View style={[styles.container, props.containerStyle]} onLayout={onContainerLayout}>
      <ScrollView
        keyboardShouldPersistTaps="handled"
        ref={scrollView}
        horizontal
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        directionalLockEnabled
        bounces={false}
        scrollsToTop={false}
        onScroll={Animated.event([{ nativeEvent: { contentOffset: { x: scrollPosition } } }], {
          useNativeDriver: false
        })}
        scrollEventThrottle={16}
      >
        <View style={[styles.tabs]} onLayout={onTabContainerLayout}>
          {tabs.map((name: any, page: any) => {
            const tabLength = tabs.length;
            const isTabActive = activeTab === page;
            return renderTab(name, page, isTabActive, goToPage, measureTab, tabLength);
          })}
        </View>
      </ScrollView>
    </View>
  );
});

const styles = StyleSheet.create({
  tab: {
    height: 49,
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 20,
    paddingRight: 20
  },
  container: {
    height: 50
  },
  tabs: {
    flexDirection: 'row',
    justifyContent: 'space-around'
  },
  tabUnderline: {
    height: 2,
    width: 100,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: COLORS.primary[1],
    bottom: 0,
    position: 'absolute'
  }
});

ScrollableTabBar.defaultProps = {
  items: [],
  currentTab: 0,
  onChange: (v: any) => v,
  onScroll: (e: any) => e,
  hasUnderlineIndicator: false
};

export { ScrollableTabBar };
