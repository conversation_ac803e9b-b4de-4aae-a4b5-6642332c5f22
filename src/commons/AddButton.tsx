import React from 'react';
import { TouchableOpacity, StyleSheet } from 'react-native';
import { Circle } from 'native-base';
import Icon from './Icon';

interface AddButtonProps {
  isAllowed: boolean;
  onPress: () => void;
}

const AddButton: React.FC<AddButtonProps> = ({ isAllowed, onPress }) => {
  if (!isAllowed) return null;

  return (
    <TouchableOpacity style={styles.addButton} onPress={onPress}>
      <Circle size="65px" bg="#0695D7">
        <Icon name="plus" />
      </Circle>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  addButton: {
    zIndex: 1000,
    position: 'absolute',
    bottom: 10,
    alignSelf: 'flex-end',
    right: '7%',
    shadowColor: 'rgba(0, 0, 0, 0.5)',
    shadowOpacity: 1,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 6 },
    backgroundColor: '#FFFFFF',
    borderRadius: 9999
  }
});

export default AddButton;
