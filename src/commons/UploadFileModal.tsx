import { Modal } from '@commons';
import { Gql } from '@src/api';
import { pushError, pushSuccess } from '@src/configs';
import { useSelector } from '@src/store';
import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { ModalProps } from 'react-native';
import RNFS from 'react-native-fs';
import { Platform } from 'react-native';
import DocumentPicker from 'react-native-document-picker';
import { getLinkId } from '@src/database/utils/numeric';
import useCreateProjectDocument from '@src/mutation/cloud-docs/useCreateCloudDocs';

interface Props {
  category: Gql.CategoryType;
  projectDocumentId?: string;
  onSaved: () => void;
  driveType?: Gql.ProjectDocumentDriveType;
  status?: Gql.ProjectDocumentStatus;
  onLoading?: (loading: boolean) => void;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const UploadFileModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const { mutate: createFile } = useCreateProjectDocument();
  const { user } = useSelector(state => state.auth);
  // const [document, setDocument] = useState<any[]>([]);

  // const category = props.category;
  const projectDocumentId = props.projectDocumentId as string;
  const project = useSelector(state => state.project);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
      chooseDocument();
    }
  }));

  // // Photos picker
  // const openLibrary = () => {
  //   ImagePicker.openPicker({
  //     width: 500,
  //     height: 500,
  //     cropping: true,
  //     forceJpg: true,
  //     includeBase64: true
  //   }).then(async image => {
  //     const o = generateRNFile({ uri: image.path, name: 'file' });
  //     await apolloClient.mutate<Gql.CreateOneProjectDocumentMutation>({
  //       mutation: Gql.CreateOneProjectDocumentDocument,
  //       variables: {
  //         input: {
  //           projectDocument: {
  //             name: o?.name,
  //             category: props?.category,
  //             fileSystemType: Gql.FileSystemType.Document,
  //             projectDocumentId: projectDocumentId,
  //             fileUrl: o as any,
  //             status: Gql.ProjectDocumentStatus.Draft,
  //             driveType: props?.driveType
  //           }
  //         }
  //       }
  //     });
  //     props.onSaved();
  //     pushSuccess('Image upload successfully');
  //   });
  // };

  // const [uploadFile, { loading: uploadFileLoading }] = Gql.useCreateOneProjectDocumentMutation({
  //   onCompleted: (data: any) => {
  //     pushSuccess('File upload successfully');
  //     // //     props?.onSaved?.();
  //     props?.onLoading?.(false);
  //   },
  //   onError: (err: any) => {
  //     //     props?.onLoading?.(false);
  //     throw err;
  //   }
  // });

  // Document picker
  const chooseDocument = async () => {
    try {
      props?.onLoading?.(true);
      const allowedTypes =
        props.category === Gql.CategoryType.TwoDDrawings
          ? [DocumentPicker.types.pdf]
          : [DocumentPicker.types.pdf, DocumentPicker.types.pptx, DocumentPicker.types.docx, DocumentPicker.types.xlsx];

      const res = await DocumentPicker.pick({
        type: allowedTypes,
        allowMultiSelection: true
      });

      res.forEach(async (element: any) => {
        try {
          const realURI = Platform.select({
            android: element.uri,
            ios: decodeURI(element.uri)
          });

          const linkId = getLinkId(projectDocumentId);
          const fileSizeInMB = ((element?.size as any) / 1048576).toFixed(2);

          // Split the filename to handle extension
          let [baseName, extension] = element.name.split(/(?=\.\w+$)/).map((part: any) => part.replace(/\s+/g, '_'));
          let fileName = baseName;
          let fileCounter = 0;

          // Prepare the initial path for the file
          let destPath = `${RNFS.DocumentDirectoryPath}/${fileName}${extension}`;
          // Check if the file exists and append a counter before the extension
          while (await RNFS.exists(destPath)) {
            fileCounter++;
            destPath = `${RNFS.DocumentDirectoryPath}/${fileName}_${fileCounter}${extension}`;
          }

          // Copy the file to the new destination
          await RNFS.copyFile(realURI, destPath);
          const newDocs = {
            name: fileCounter > 0 ? `${fileName}_${fileCounter}${extension}` : `${fileName}${extension}`,
            projectId: parseInt(project?.projectId ?? '0'),
            category: props.category,
            fileSystemType: Gql.FileSystemType.Document,
            status: props.status ? props.status : null,
            type: extension.slice(1), // Remove the dot from the extension
            fileUrl: `file://${destPath}`,
            driveType: props?.driveType as any,
            fileSize: parseFloat(fileSizeInMB),
            addedBy: parseInt(user?.id as any),
            ...linkId
          };

          await createFile([newDocs], {
            onSuccess: () => {
              pushSuccess('File upload successfully');
              props?.onSaved?.();
              props?.onLoading?.(false);
            }
          });
        } catch (e) {
          pushError(e);
        }
      });
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
        // User canceled the document picker
        props?.onLoading?.(false);
      } else {
        props?.onLoading?.(false);
        throw err;
      }
    }
  };

  return <Modal ref={modalRef} children={undefined}></Modal>;
});

export default UploadFileModal;
