import { Icon } from '@commons/Icon';
import { COLORS, ICON_NAMES } from '@constants';
import { useNavigation } from '@react-navigation/native';
import _ from 'lodash';
import { Box, Button, Flex, HStack, ITextProps, Text } from 'native-base';
import React from 'react';
import { Platform, StatusBar, StyleSheet } from 'react-native';

type AppBarProps = {
  search?: boolean;
  cancel?: boolean;
  header?: boolean;
  goBack?: boolean;
  onPressTitle?: () => void;
  onGoBack?: () => void;
  noLeft?: boolean;
  noRight?: boolean;
  background?: string;
  hasShadow?: boolean;
  textAlign?: 'left' | 'center';
  title?: string;
  onlyStatusBar?: boolean;
  transparent?: boolean;
  rightComponent?: any;
  leftIcon?: ICON_NAMES;
  // rightTextColor?: string;
  // rightButtonText?: string;
  titleOpacity?: number;
  onRightBtn?: () => void;
  isSubmittingRight?: boolean;
  isAbsolute?: boolean;
  leftIconProps?: object;
  hasBorder?: boolean;
  safeAreaTop?: boolean;
  dark?: boolean;
  barStyle?: 'light-content' | 'dark-content';
  titleProps?: ITextProps;
};
const AppBar: React.FC<AppBarProps> = props => {
  const navigation = useNavigation();
  return (
    <Box zIndex={999} style={props.isAbsolute && styles.absolution}>
      <StatusBar
        translucent={false}
        backgroundColor={Platform.OS === 'android' ? '#FFF' : props.transparent ? 'transparent' : props.background}
        barStyle={Platform.OS === 'android' ? 'dark-content' : props.barStyle || 'dark-content'}
      />

      {!props.onlyStatusBar && props.safeAreaTop && (
        <Box safeAreaTop backgroundColor={props.transparent ? 'transparent' : props.background} />
      )}

      {!props.onlyStatusBar && (
        <Box overflow="hidden">
          <HStack
            py={Platform.OS === 'android' ? 4 : 4}
            backgroundColor={props.background}
            justifyContent="space-between"
            alignItems="center"
            borderBottomWidth={props.hasBorder ? 1 : 0}
            borderColor="neutrals.gray10"
            style={[styles.appBarContainer, props.hasShadow && styles.appBarShadow]}
          >
            {props.goBack && (
              <Button
                _pressed={{
                  bg: 'transparent',
                  opacity: 0.8
                }}
                hitSlop={20}
                bg="transparent"
                onPress={() => (props.onGoBack ? props.onGoBack() : navigation.goBack())}
                p={0}
                flex={!props.title ? undefined : 1}
                justifyContent="flex-start"
              >
                <Icon
                  name={props.leftIcon ? props.leftIcon : 'chevron-left'}
                  fill={props.dark ? '#FFF' : COLORS.neutrals.gray90}
                  {...props.leftIconProps}
                />
              </Button>
            )}

            {props.cancel && (
              <Button
                _pressed={{
                  bg: 'transparent',
                  opacity: 0.8
                }}
                bg="transparent"
                onPress={() => (props.onGoBack ? props.onGoBack() : navigation.goBack())}
                p={0}
                flex={!props.title ? undefined : 1}
                justifyContent="flex-start"
              >
                <Icon
                  name={props.leftIcon ? props.leftIcon : 'cancel'}
                  fill={props.dark ? '#FFF' : COLORS.neutrals.gray90}
                  {...props.leftIconProps}
                />
              </Button>
            )}

            {props.search && (
              <>
                <Button
                  _pressed={{
                    bg: 'transparent',
                    opacity: 0.8
                  }}
                  bg="transparent"
                  onPress={() => (props.onGoBack ? props.onGoBack() : navigation.goBack())}
                  p={0}
                  flex={!props.title ? undefined : 1}
                  justifyContent="flex-start"
                >
                  <Icon
                    name={props.leftIcon ? props.leftIcon : 'chevron-left'}
                    fill={props.dark ? '#FFF' : COLORS.neutrals.gray90}
                    {...props.leftIconProps}
                  />
                </Button>
                <Button
                  _pressed={{
                    bg: 'transparent',
                    opacity: 0.8
                  }}
                  bg="transparent"
                  onPress={() => (props.onGoBack ? props.onGoBack() : navigation.goBack())}
                  p={0}
                  flex={!props.title ? undefined : 1}
                  justifyContent="flex-start"
                >
                  <Icon
                    name={props.leftIcon ? props.leftIcon : 'chevron-left'}
                    fill={props.dark ? '#FFF' : COLORS.neutrals.gray90}
                    {...props.leftIconProps}
                  />
                </Button>
              </>
            )}

            {props.noLeft && !props.goBack && <Box flex={1} />}
            {!_.isEmpty(props.title) ? (
              <Text
                px={1}
                flex={props.goBack ? 4 : 5}
                textAlign="center"
                // width={props.goBack ? '72%' : '82%'}
                fontWeight={500}
                fontSize="15px"
                opacity={props.titleOpacity}
                color={props.dark ? '#FFF' : 'neutrals.black'}
                numberOfLines={1}
                onPress={props?.onPressTitle}
                {...props.titleProps}
                // variant={props.goBack ? 'body1' : 'title3'}
              >
                {props.title || ''}
              </Text>
            ) : (
              <Box flex={props.goBack ? 4 : 5} />
            )}
            {props.rightComponent && props.rightComponent}
            {props.noRight && !props.rightComponent && <Box flex={1} />}
          </HStack>
        </Box>
      )}
    </Box>
  );
};

const styles = StyleSheet.create({
  appBarContainer: {
    width: '100%',
    paddingHorizontal: 15
  },
  appBarShadow: {
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 16,
    elevation: 2
  },
  absolution: {
    position: 'absolute',
    width: '100%',
    zIndex: 999,
    top: 0
  }
});

AppBar.defaultProps = {
  background: '#FFFFFF',
  goBack: false,
  hasShadow: false,
  textAlign: 'center',
  onlyStatusBar: false,
  transparent: false,
  titleOpacity: 1,
  hasBorder: false,
  safeAreaTop: true,
  dark: false
};

export { AppBar };
export default React.memo(AppBar);
