import React, { useEffect } from 'react';
import { Avatar, Box, HStack, Text } from 'native-base';
import { NativeTouchable } from '@commons/NativeTouchable';
import { RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { CustomersNavigatorParams } from '@types';

type Props = {
  navigation: StackNavigationProp<CustomersNavigatorParams, 'Customers'>;
  item: any;
  setReload: () => void;
  isReload: boolean;
};

const CustomerItem: React.FC<Props> = ({ item, navigation, setReload, isReload }) => {
  return (
    <Box mb={4}>
      <NativeTouchable onPress={() => navigation.navigate('EditCustomerPage', { id: item.id, setReload, isReload })}>
        {/* <NativeTouchable onPress={() => navigation.navigate('EditCustomerPage', { item: item })}> */}
        <Box px={4} bg="#FFF" rounded={12}>
          <HStack py={4} justifyContent="space-between">
            <Box mr={4}>
              <Avatar source={{ uri: item.logo }} size={12} borderWidth={1} borderColor="neutrals.gray10" bg="#FFF" />
            </Box>
            <Box flex={1}>
              <Text variant="headline" numberOfLines={1} mb={2}>
                {item.name}
              </Text>
              <HStack justifyContent="space-between">
                <Text w="60px">PIC: </Text>
                <Text variant="body2" mb={1} flex={1}>
                  {item.picName}
                </Text>
              </HStack>
              <HStack justifyContent="space-between" width={75}>
                <Text w="60px">Status:</Text>
                {/* {item.isActive?"Active":"Inactive"}  */}
                {item.isActive ? (
                  <Text variant="body2" mb={1} color={'green.400'}>
                    Active
                  </Text>
                ) : (
                  <Text variant="body2" mb={1} color={'red.400'}>
                    Inactive
                  </Text>
                )}
              </HStack>
            </Box>
          </HStack>
        </Box>
      </NativeTouchable>
    </Box>
  );
};

export { CustomerItem };
