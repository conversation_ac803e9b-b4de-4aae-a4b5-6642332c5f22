import { COLORS } from '@src/constants';
import { Box, HStack, Input, Pressable } from 'native-base';
import React from 'react';
import { Icon } from '@src/commons';
import { StyleProp, ViewStyle } from 'react-native';

type Props = {
  filteredValue: string | null;
  setFilteredValue: (value: string | null) => void;
  style?: StyleProp<ViewStyle>;
  onFocused?: (value: boolean) => void;
  onBlurred?: (value: boolean) => void;
  onClose?: () => void;
  placeholder?: string;
  width?: string;
};

const SearchInput = (props: Props) => {
  const { filteredValue, setFilteredValue } = props;

  const handleClear = () => {
    setFilteredValue('');
    if (props?.onClose) props?.onClose();
  };

  return (
    <HStack
      width={props.width ? props.width : 'full'}
      height="48px"
      bg="white"
      alignItems="center"
      px={2}
      mt={1}
      space={2}
      style={props?.style}
    >
      <Input
        flexGrow={1}
        backgroundColor={COLORS.neutrals.white}
        placeholder={props?.placeholder}
        mt="2"
        borderRadius="8"
        fontSize="14"
        InputLeftElement={
          <Box pl="2" py="6">
            <Icon name="search" />
          </Box>
        }
        InputRightElement={
          <Pressable px="4" py="6" onPress={handleClear}>
            {filteredValue !== '' ? <Icon name="cancel" /> : null}
          </Pressable>
        }
        value={filteredValue ?? ''}
        onChangeText={value => setFilteredValue(value)}
        onFocus={() => props?.onFocused?.(true)}
        onBlur={() => props?.onBlurred?.(false)}
      />
    </HStack>
  );
};

export default React.memo(SearchInput);
