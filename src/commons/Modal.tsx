import React, { useState, forwardRef, useImperativeHandle } from 'react';
import { Box } from 'native-base';
import { StyleSheet, View, StyleProp, ModalProps, Dimensions, TouchableOpacity, Pressable } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import RNModal from 'react-native-modal';

interface Props extends ModalProps {}
interface Props {
  type?: 'bottom' | 'middle';
  useNativeDriver?: boolean;
  style?: StyleProp<any>;
  containerStyle?: StyleProp<any>;
  children: React.ReactNode;
  removeKnob?: boolean;
  onClose?: () => void;
  swipeDirection?: any;
  isVisible?: boolean;
  avoidKeyboard?: boolean;
  onLoading?: boolean;
  onShow?: () => void;
  backdropColor?: string;
  backdropOpacity?: number;
}
export interface ModalRef {
  pushModal: (v?: any) => void;
  visible: boolean;
}

const Modal = forwardRef<ModalRef, Props>((props, ref) => {
  const insets = useSafeAreaInsets();
  const [visible, setVisible] = useState(false);
  const deviceHeight = Dimensions.get('window').height * 2;

  useImperativeHandle(ref, () => ({
    pushModal: (status: any) => {
      setVisible(status !== undefined ? status : !visible);
    },
    closeModal: () => {
      onHandleClose();
    },
    visible
  }));

  const onHandleClose = () => {
    setVisible(false);
    if (props.onClose) props.onClose();
  };

  const disableClose = () => {
    return;
  };

  return (
    <Box>
      <RNModal
        isVisible={props.isVisible !== undefined ? props.isVisible : visible}
        style={[styles.modal, props.type === 'middle' && styles.modalMiddle, props.style && props.style]}
        onBackButtonPress={onHandleClose}
        onBackdropPress={props.onLoading ? disableClose : onHandleClose}
        onSwipeComplete={onHandleClose}
        backdropOpacity={props.backdropOpacity || 0.5}
        // customBackdrop={<View style={{ flex: 1,backgroundColor:'rgba(52, 52, 52, 0.8)',width:'100%' }} />}
        swipeDirection={props.swipeDirection || undefined}
        useNativeDriver={true}
        useNativeDriverForBackdrop={true}
        avoidKeyboard={props.avoidKeyboard}
        animationIn={props.type === 'bottom' ? 'slideInUp' : 'fadeIn'}
        animationOut={props.type === 'bottom' ? 'slideOutDown' : 'fadeOut'}
        animationInTiming={300}
        animationOutTiming={300}
        deviceHeight={deviceHeight}
        backdropTransitionOutTiming={0}
        hideModalContentWhileAnimating={true}
        propagateSwipe={true}
        onShow={props.onShow}
        backdropColor={props.backdropColor}
      >
        <Box
          style={[
            styles.container,
            props.type === 'middle' && styles.containerMiddle,
            props.containerStyle && props.containerStyle
          ]}
        >
          {props.type === 'bottom' && props.children && (
            <View style={{ alignSelf: 'center', marginVertical: 8 }}>
              <View style={styles.knob} />
            </View>
          )}
          {props.children}
          {props.type === 'bottom' && <View style={{ marginBottom: insets.bottom || 30 }} />}
        </Box>
      </RNModal>
    </Box>
  );
});

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    bottom: 0,
    marginTop: 0,
    marginBottom: -16, // previous is 0 , but there is a white space so changes to -16
    marginLeft: 0,
    marginRight: 0
  },
  knob: {
    backgroundColor: '#D9D9D9',
    width: 60,
    height: 6,
    borderRadius: 10
  },
  modalMiddle: {
    justifyContent: 'center',
    marginLeft: 10,
    marginRight: 10,
    marginBottom: 16
  },
  container: {
    backgroundColor: '#FFFFFF',
    borderTopRightRadius: 24,
    borderTopLeftRadius: 24
  },
  containerMiddle: {
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
    borderRadius: 16
  }
});

Modal.defaultProps = {
  type: 'bottom',
  useNativeDriver: true,
  removeKnob: false,
  useNativeDriverForBackdrop: true,
  hideModalContentWhileAnimating: true
};

export { Modal };
export default Modal;
