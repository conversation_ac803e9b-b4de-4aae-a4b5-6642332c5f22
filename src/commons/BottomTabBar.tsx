import React from 'react';
import { Box, Text, View } from 'native-base';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import { StyleSheet, Platform } from 'react-native';
import { COLORS } from '@constants';
import { NativeTouchable } from '@commons/NativeTouchable';
import _ from 'lodash';

const BottomTabBar: React.FC<BottomTabBarProps> = ({ state, descriptors, navigation }) => {
  const focusedOptions = descriptors[state.routes[state.index].key].options;
  const tabBarStyle = focusedOptions.tabBarStyle as any;

  return (
    <>
      <View
        style={[
          styles.container,
          {
            paddingBottom: Platform.OS === 'ios' ? 8 : undefined,
            ...tabBarStyle
          }
        ]}
      >
        {state.routes.map((route, index) => {
          const { options } = descriptors[route.key];
          const label = options.tabBarLabel as string;
          const Icon = options.tabBarIcon as any;

          const isFocused = state.index === index;

          const onPress = () => {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
              canPreventDefault: true
            });

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name);
            }
          };

          const tabButton = (
            <NativeTouchable
              accessibilityRole="button"
              accessibilityState={isFocused ? { selected: true } : {}}
              accessibilityLabel={options.tabBarAccessibilityLabel}
              testID={options.tabBarTestID}
              onPress={onPress}
              key={route.key}
              style={styles.button}
            >
              <Box style={[styles.buttonContainer, Platform.OS === 'android' && { flex: 1 }]}>
                <Icon focused={isFocused} />
                {label && (
                  <Text fontSize={10} fontWeight={500} color={isFocused ? COLORS.primary[1] : COLORS.neutrals.gray70}>
                    {label}
                  </Text>
                )}
              </Box>
            </NativeTouchable>
          );

          return tabButton;
        })}
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF',
    // shadowColor: '#4e4e4e',
    // shadowOffset: { width: 0, height: -2 },
    // shadowOpacity: 0.08,
    // shadowRadius: 10,
    width: '100%',
    height: Platform.OS === 'android' ? 65 : 70,
    borderTopWidth: 1,
    borderColor: '#ECECEC'
  },
  button: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10
  },
  buttonContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%'
  }
});

export { BottomTabBar };
