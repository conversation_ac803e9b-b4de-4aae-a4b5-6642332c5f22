import React, { useCallback } from 'react';
import { TouchableOpacity } from 'react-native';
import { Avatar, Box, Divider, HStack, VStack, Text } from 'native-base';
import { Icon } from '@commons';
import { COLORS } from '@src/constants';
import type { ProjectUser } from '@src/modules/DigitalForm/hooks/types';

interface ProjectUserItemProps {
  item: ProjectUser;
  selectedIds: Set<string>;
  onSelect: (item: ProjectUser) => void;
}

export const ProjectUserItem = React.memo(
  ({ item, selectedIds, onSelect }: ProjectUserItemProps) => {
    const isSelected = selectedIds.has(String(item.userId));
    const onPress = useCallback(() => onSelect(item), [item, onSelect]);

    return (
      <Box>
        <TouchableOpacity onPress={onPress}>
          <HStack alignItems="center" space={3}>
            <Avatar size="32px" source={{ uri: item.avatarUrl }} />
            <VStack width="75%">
              <Text style={{ fontWeight: '600', fontSize: 14 }} numberOfLines={1} ellipsizeMode="tail">
                {item.name.toUpperCase()}
              </Text>
              <Text style={{ fontWeight: '400', color: COLORS.neutrals.gray90 }}>{item.email}</Text>
            </VStack>
            {isSelected && <Icon name="tick" fill="#0695D7" />}
          </HStack>
          <Divider my={2} />
        </TouchableOpacity>
      </Box>
    );
  },
  (prev, next) => prev.item.userId === next.item.userId && prev.selectedIds === next.selectedIds
);

ProjectUserItem.displayName = 'ProjectUserItem';
