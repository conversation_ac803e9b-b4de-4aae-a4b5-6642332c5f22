import React from 'react';
import { Box, Circle, Text } from 'native-base';

const EmptyComponent = ({
  source,
  title,
  description,
  iconBg,
  mt
}: {
  source?: any;
  iconBg?: string;
  title: string;
  description?: string;
  mt?: number | string;
}) => {
  const Icon = source;
  return (
    <Box justifyContent="center" alignItems="center" mt={mt}>
      <Circle bg={iconBg || 'primary.light'} size="90px" mb={5}>
        <Icon />
      </Circle>
      <Text variant="h1" fontWeight={800} textAlign="center">
        {title}
      </Text>
      <Text color="neutrals.gray80" textAlign="center">
        {description}
      </Text>
    </Box>
  );
};

export { EmptyComponent };
