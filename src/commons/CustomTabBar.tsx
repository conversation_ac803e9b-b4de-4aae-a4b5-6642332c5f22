import React from 'react';
import { View, Animated, StyleSheet, Text } from 'react-native';
import { DefaultTabBarProps } from 'react-native-scrollable-tab-view';
import _ from 'lodash';
import { COLORS, FONTS } from '@constants';
import { NativeTouchable } from './NativeTouchable';

interface Props extends DefaultTabBarProps {
  // tabs: any;
  // containerWidth: number;
  // activeTab: number;
  // scrollValue: any;
  // onScroll?: any;
  // hasUnderlineIndicator?: boolean;
  // tabBarUnderlineStyle?: any;
  // style?: any;
  // renderTab: (name: any, page: any, isTabActive: any, onPressHandler: any) => void;
  // backgroundColor?: string;
  // goToPage: () => void;
}

const CustomTabBar = (props: Props) => {
  const renderTab = (name: any, page: any, isTabActive: any, onPressHandler: any) => {
    const { activeTextColor, inactiveTextColor, tabStyle, textStyle } = props as any;
    const textColor = isTabActive ? activeTextColor || COLORS.primary[1] : inactiveTextColor || COLORS.neutrals.gray50;
    const fontFamily = isTabActive ? FONTS.interBold : FONTS.interMedium;

    return (
      <NativeTouchable
        style={{ flex: 1 }}
        key={`${name}_${page}`}
        accessible
        accessibilityLabel={name}
        accessibilityTraits="button"
        onPress={() => onPressHandler(page)}
      >
        <View style={[styles.tab, tabStyle]}>
          <Text style={[{ color: textColor, fontFamily }, textStyle]}>{name}</Text>
        </View>
      </NativeTouchable>
    );
  };

  // const setTab = (item, i) => {
  //   props.onChange(item);
  //   updateView(i);
  // };

  const containerWidth = props.containerWidth;
  const numberOfTabs = props.tabs.length;
  const tabUnderlineStyle = {
    position: 'absolute',
    width: containerWidth / numberOfTabs,
    paddingHorizontal: 20,
    bottom: 0
  };

  const translateX = props.scrollValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0, containerWidth / numberOfTabs]
  });

  return (
    <View style={[styles.tabs, { backgroundColor: props.backgroundColor }, props.style]}>
      {props?.tabs.map((name: string, page: number) => {
        const isTabActive = props.activeTab === page;
        const customRenderTab = props.renderTab || renderTab;
        return customRenderTab(name, page, isTabActive, props.goToPage);
      })}
      <Animated.View
        style={[
          tabUnderlineStyle,
          {
            transform: [{ translateX }]
          },
          props.tabBarUnderlineStyle
        ]}
      >
        <View style={[styles.tabUnderline]} />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFF'
    // backgroundColor: '#000'
  },
  tabs: {
    height: 50,
    flexDirection: 'row',
    justifyContent: 'space-around',
    elevation: 1,
    // borderWidth: 1,
    backgroundColor: '#FFF',
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 16,
    shadowOffset: {
      width: 0,
      height: 2
    }
  },
  tabUnderline: {
    height: 2,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: COLORS.primary[1]
  }
});

export { CustomTabBar };
