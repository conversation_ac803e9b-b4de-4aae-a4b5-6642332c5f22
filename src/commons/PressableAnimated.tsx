import React from 'react';
import { Pressable, Box } from 'native-base';

interface Props {
  children: React.ReactChild;
  shrinkIn?: number;
  onPress?: () => void;
  disabled?: boolean;
  mr?: number;
}

const PressableAnimated: React.FC<Props> = ({ children, shrinkIn, disabled, ...props }) => {
  return (
    <Pressable disabled={disabled} {...props}>
      {({ isPressed }: { isPressed: boolean }) => {
        return (
          <Box
            style={[
              {
                transform: [
                  {
                    scale: isPressed ? shrinkIn || 0.85 : 1
                  }
                ]
              }
            ]}
          >
            {children}
          </Box>
        );
      }}
    </Pressable>
  );
};

PressableAnimated.defaultProps = {
  shrinkIn: 0.85
};

export { PressableAnimated };
