import React from 'react';
import FastImage, { ImageStyle } from 'react-native-fast-image';

interface ImageProps {
  width?: number | string;
  height?: number | string;
  borderRadius?: number;
  uri?: string;
  mode?: 'contain' | 'cover' | 'stretch' | 'center';
  source?: any;
  style?: ImageStyle;
}
const Image: React.FC<ImageProps> = ({ width, height, uri, mode, borderRadius, source, style }) => {
  return (
    <FastImage
      style={{ width, height, borderRadius, ...style }}
      source={uri ? { uri, priority: FastImage.priority.high } : source}
      resizeMode={mode}
    />
  );
};

Image.defaultProps = {
  mode: 'contain',
  width: 100,
  height: 100
};

export { Image };
export default Image;
