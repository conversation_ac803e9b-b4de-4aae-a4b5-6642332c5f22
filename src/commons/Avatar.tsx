import React from 'react';
import { Avatar, VStack } from 'native-base';

// Define the props type for the component
type AvatarGroupProps = {
  assignees: Array<{
    avatarUrl: string;
    assigneeNo: string;
    name: string;
  }>;
  maxVisible?: number; // Optional since it is only relevant for groups
  avatarSize?: string;
  type: 'task' | 'workspace';
  group?: boolean; // New prop to indicate if a group should be rendered
  style?: object; // Added style prop for custom styling
};

const colors = ['lightBlue.500'];

const getRandomColor = () => {
  return colors[Math.floor(Math.random() * colors.length)];
};

const AvatarGroupComponent = ({
  assignees,
  maxVisible = 3,
  avatarSize = 'sm',
  type,
  group = false, // Default to single avatar mode
  style = {} // Default style to an empty object
}: AvatarGroupProps) => {
  const avatarSources = assignees.map(assignee => {
    const nameParts = assignee.name?.split?.(' ') || [];
    const initials =
      nameParts.length > 1
        ? `${nameParts[0].charAt(0).toUpperCase()}${nameParts[1].charAt(0).toUpperCase()}`
        : nameParts.length === 1
          ? nameParts[0].charAt(0).toUpperCase()
          : '';

    return {
      ...assignee,
      source: assignee.avatarUrl ? { uri: assignee.avatarUrl } : null,
      initials: initials,
      bgColor: getRandomColor()
    };
  });

  // Default to the first assignee for single avatar mode
  const firstAssignee = avatarSources[0];

  return (
    <VStack style={style}>
      {group
        ? // Render Avatar Group when group mode is enabled
          assignees.length > 0 && (
            <Avatar.Group max={maxVisible} _avatar={{ size: avatarSize }}>
              {avatarSources
                .filter(assignee => (type === 'workspace' ? assignee.assigneeNo !== '1' : true))
                .map((assignee, index) => (
                  <Avatar
                    key={index}
                    size={'sm'}
                    source={assignee.source as any}
                    bg={assignee.source ? undefined : assignee.bgColor}
                  >
                    {!assignee.source && assignee.initials}
                  </Avatar>
                ))}
            </Avatar.Group>
          )
        : // Render Single Avatar when group mode is not enabled
          firstAssignee && (
            <Avatar
              size={avatarSize}
              source={firstAssignee.source as any}
              bg={firstAssignee.source ? undefined : firstAssignee.bgColor}
            >
              {!firstAssignee.source && firstAssignee.initials}
            </Avatar>
          )}
    </VStack>
  );
};

export default React.memo(AvatarGroupComponent);
