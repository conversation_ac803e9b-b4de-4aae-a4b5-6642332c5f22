import React from 'react';
import { Switch as RNSwitch, SwitchProps } from 'react-native-switch';
import { COLORS } from '@constants';

const Switch: React.FC<SwitchProps> = props => {
  return (
    <RNSwitch
      circleSize={22}
      barHeight={26}
      circleBorderWidth={0}
      backgroundActive={COLORS.primary}
      backgroundInactive={COLORS.grayLight}
      circleActiveColor="#FFF"
      circleInActiveColor="#FFF"
      switchLeftPx={3}
      switchRightPx={3}
      switchWidthMultiplier={1.8}
      switchBorderRadius={36.5}
      renderActiveText={false}
      renderInActiveText={false}
      {...props}
    />
  );
};

export { Switch };
