import React from 'react';
import { StyleSheet } from 'react-native';
import { Box, IBoxProps } from 'native-base';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface Props extends IBoxProps {
  children: React.ReactNode;
  style?: Object;
  floating?: boolean;
  hasShadow?: boolean;
  bordered?: boolean;
}

const Footer: React.FC<Props> = ({ children, style, floating, hasShadow, bordered, ...props }) => {
  const insets = useSafeAreaInsets();
  return (
    <Box
      style={[
        floating && { bottom: 0, width: '100%', position: 'absolute' },
        hasShadow && styles.shadow,
        bordered && styles.border,
        style
      ]}
      pt={3}
      px={5}
      pb={`${insets.bottom || 15}px`}
      {...props}
    >
      {children}
    </Box>
  );
};

const styles = StyleSheet.create({
  shadow: {
    shadowColor: '#000',
    backgroundColor: 'white',
    shadowOffset: { width: 0, height: -1 },
    shadowOpacity: 0.08,
    shadowRadius: 16,
    elevation: 2
  },
  border: {
    borderTopWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.08)'
  }
});

export { Footer };
