import React from 'react';
import {
  TouchableOpacity,
  TouchableNativeFeedback,
  Platform,
  TouchableOpacityProps,
  TouchableNativeFeedbackProps
} from 'react-native';

const Button = Platform.OS === 'ios' ? TouchableOpacity : TouchableNativeFeedback;

interface Props extends TouchableOpacityProps, TouchableNativeFeedbackProps {
  children: React.ReactNode;
  rippleOverflow: boolean;
}

const NativeTouchable = ({ children, activeOpacity, rippleOverflow, ...props }: Props) => {
  return (
    // @ts-ignore
    <Button
      {...props}
      activeOpacity={activeOpacity}
      background={TouchableNativeFeedback.Ripple('#d9dddc', rippleOverflow)}
    >
      {children}
    </Button>
  );
};

NativeTouchable.defaultProps = {
  activeOpacity: 0.7,
  rippleOverflow: false
};

export { NativeTouchable };
