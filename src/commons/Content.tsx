import React from 'react';
import { ScrollView, IScrollViewProps } from 'native-base';

interface Props extends IScrollViewProps {
  children: React.ReactNode;
}

const Content: React.FC<Props> = props => {
  return (
    <ScrollView
      flex={1}
      keyboardDismissMode="on-drag"
      contentContainerStyle={[{ paddingHorizontal: 20 }, props.contentContainerStyle]}
      {...props}
    >
      {props.children}
    </ScrollView>
  );
};

export { Content };
export default Content;
