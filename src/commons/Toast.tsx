import React from 'react';
import { AlertNotificationRoot, IConfigToast } from 'react-native-alert-notification';

type Props = {
  children: React.ReactElement;
};

const ToastLayout: React.FC<Props> = props => {
  const toastConfig: IConfigToast = {
    autoClose: 3000,
    titleStyle: {
      fontSize: 14,
      fontWeight: 'bold'
    },
    textBodyStyle: {
      fontSize: 12
    }
  };
  return (
    <AlertNotificationRoot theme="light" toastConfig={toastConfig}>
      {props.children}
    </AlertNotificationRoot>
  );
};

export default ToastLayout;
