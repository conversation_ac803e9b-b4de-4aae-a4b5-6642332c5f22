import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import Modal, { ModalRef as ModalType } from './Modal';
import Carousel, { Pagination } from 'react-native-snap-carousel';
import { Gql } from '@src/api';
import FastImage from 'react-native-fast-image';
import { Linking, ModalProps, useWindowDimensions } from 'react-native';
import { COLORS } from '@src/constants';
import { Button, Text } from 'native-base';
import { pushError } from '@src/configs';

type Props = {
  data: Gql.GetChangeLogsQuery | undefined;
  onDismiss: () => void;
};

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const ChangeLogModal = forwardRef((props: Props, ref) => {
  const modalRef = React.useRef<ModalType>(null);
  const carouselRef = React.useRef<any>(null);
  const layout = useWindowDimensions();
  const [index, setIndex] = useState(0);
  const [buttonName, setButtonName] = useState('');
  const [buttonUrl, setButtonUrl] = useState('');

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal?.();
    },
    isVisible: modalRef?.current?.visible
  }));

  useEffect(() => {
    const nodes = props.data?.changeLogMobiles?.nodes;
    if (nodes && nodes.length > 0) {
      const current = nodes[index];
      setButtonName(current?.buttonName ?? '');
      setButtonUrl(current?.buttonUrl ?? '');
    }
  }, [props.data?.changeLogMobiles?.nodes, index]);

  return (
    <Modal ref={modalRef} onClose={props.onDismiss}>
      <Carousel
        data={(props.data?.changeLogMobiles?.nodes as any) || []}
        itemWidth={layout.width}
        vertical={false}
        style={{ marginTop: 30 }}
        ref={carouselRef}
        layout="stack"
        layoutCardOffset={-18}
        sliderWidth={layout.width}
        renderItem={({ item }: { item: Gql.ChangeLogMobile }) => (
          <FastImage
            source={{
              uri: item?.imageUrl ?? '',
              cache: FastImage.cacheControl.immutable,
              priority: FastImage.priority.normal
            }}
            style={{ width: '100%', height: layout.height - 250, marginTop: 30 }}
            resizeMode="cover"
          />
        )}
        onSnapToItem={setIndex}
      />
      <Button
        style={{ alignSelf: 'center', marginVertical: 20 }}
        onPress={() => {
          if (buttonUrl) {
            Linking.openURL(buttonUrl).catch(err => {
              pushError(err);
            });
          }
        }}
        color={'white'}
      >
        <Text color={'white'}>{buttonName}</Text>
      </Button>
      <Pagination
        dotsLength={props.data?.changeLogMobiles?.nodes?.length || 0}
        tappableDots={true}
        carouselRef={carouselRef}
        activeDotIndex={index}
        containerStyle={{ paddingVertical: 8 }}
        inactiveDotStyle={{
          backgroundColor: '#000'
        }}
        inactiveDotOpacity={0.4}
        inactiveDotScale={0.6}
      />
    </Modal>
  );
});

export default ChangeLogModal;
