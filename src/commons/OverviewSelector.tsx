import React from 'react';
import { TouchableOpacity, StyleSheet } from 'react-native';
import { Icon } from '@src/commons';

const OverviewSelector: React.FC<{
  selectedIcon: string;
  onSelect: (icon: string) => void;
}> = ({ selectedIcon, onSelect }) => {
  return (
    <>
      <TouchableOpacity
        style={[styles.iconButton, { backgroundColor: selectedIcon === 'three-list' ? 'lightgrey' : 'transparent' }]}
        onPress={() => onSelect('three-list')}
      >
        <Icon name="three-list" />
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.iconButton, { backgroundColor: selectedIcon === 'foursquare' ? 'lightgrey' : 'transparent' }]}
        onPress={() => onSelect('foursquare')}
      >
        <Icon name="foursquare" />
      </TouchableOpacity>
    </>
  );
};

const styles = StyleSheet.create({
  iconButton: {
    padding: 5,
    marginTop: 12,
    borderRadius: 8
  }
});

export default OverviewSelector;
