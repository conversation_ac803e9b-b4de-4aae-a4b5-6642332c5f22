import React, { memo, useCallback, useRef, useMemo } from 'react';
import { Alert, RefreshControl, StyleSheet, TouchableOpacity, View } from 'react-native';
import { RowMap, SwipeListView } from 'react-native-swipe-list-view';
import { VStack, Spinner, Box, HStack, Text, Divider } from 'native-base';
import NotFound from '@src/commons/NotFound';
import RenderHiddenItem from './RenderHiddenItem';
import moment from 'moment';
import { getFileIcon } from '@src/commons/FileIcon';
import { COLORS } from '@src/constants/colors';
import { showFileName } from '@src/configs/utils';
import { Gql } from '@src/api';
import _ from 'lodash';
import useCreateOfflineDownload from '@src/mutation/offline-download/useCreateOfflineDownload';
import { pushMessaage } from '@src/configs';
import NetInfo from '@react-native-community/netinfo';
import AvatarComponent from '@commons/Avatar';
import useSaveProjectDocument from '@src/mutation/project-document/useSaveProjectDocument';
import useSyncWithDownload from '@src/hooks/useSyncWithDownload';
import { ProjectDocumentModel } from '@src/database/model/project-document';
import Icon from '@src/commons/Icon';

interface DocumentListProps {
  documents: any[];
  isLoading: boolean;
  loadMoreDocuments: () => void;
  refetch: () => void;
  onItemPress: (item: ProjectDocumentModel) => void;
  onOptionPress: (item: ProjectDocumentModel) => void;
  onAction: boolean;
  setOnAction: (value: boolean) => void;
  modules?: Gql.CategoryType;
  status?: string;
  isFetchingNextPage?: boolean;
}

const DocumentList: React.FC<DocumentListProps> = ({
  documents,
  isLoading,
  loadMoreDocuments,
  refetch,
  onItemPress,
  onOptionPress,
  onAction,
  setOnAction,
  modules,
  status,
  isFetchingNextPage
}) => {
  if (isLoading) {
    return <Spinner color="cyan.500" />;
  }
  const { mutateAsync: queueOfflineDownload, isPending } = useCreateOfflineDownload();
  const { mutateAsync: handleOfflineDownload } = useSaveProjectDocument();
  const netInfo = NetInfo.useNetInfo();
  const isConnected = netInfo.isConnected;
  const onEndReachedCalledDuringMomentum = useRef(false);

  const handleLongPress = useCallback(
    (item: ProjectDocumentModel) => {
      return showFileName(item.type === 'folder' ? 'Folder' : 'File', item.name);
    },
    [documents]
  );

  const renderHiddenItem = (data: { item: ProjectDocumentModel }) => (
    <RenderHiddenItem item={data.item} onAction={onAction} setOnAction={setOnAction} isPending={isPending} />
  );

  const handlePress = useCallback(
    (item: ProjectDocumentModel) => {
      if (item.category !== Gql.CategoryType.AllForm && item.type === 'pdf' && !isConnected) {
        if (item.localFileUrl || !item.remoteId) {
          return onItemPress(item);
        } else {
          return pushMessaage(
            'This is not yet available offline. Please swipe right to download it for offline use',
            'warning'
          );
        }
      }
      onItemPress(item);
    },
    [isConnected, onItemPress]
  );

  const { syncAndDownload } = useSyncWithDownload();

  // Document List item component (memoized for performance)
  const DocumentListItem = memo(
    ({
      item,
      index,
      handlePress,
      handleLongPress,
      onOptionPress,
      modules,
      status
    }: {
      item: any;
      index: number;
      handlePress: (item: any) => void;
      handleLongPress: (item: any) => void;
      onOptionPress: (item: any) => void;
      modules?: Gql.CategoryType;
      status?: string;
    }) => {
      const isUnsynced = item?._status !== 'synced' || item?.isUnsync;
      // Format the date
      const formattedDate = useMemo(() => {
        return moment(item.server_created_at ? item.server_created_at : item.created_at).format('DD MMM YYYY');
      }, [item.server_created_at, item.created_at]);

      const statusComponent = useMemo(() => getStatus(item.status), [item.status]);
      const fileIcon = useMemo(() => getFileIcon(item?.type ?? ''), [item?.type]);

      return (
        <HStack flexDirection="row" bg="white" key={`${item.id}-${index}`}>
          <TouchableOpacity
            style={{ width: '92%' }}
            onPress={() => handlePress(item)}
            onLongPress={() => handleLongPress(item)}
          >
            <Box style={styles.box}>
              <HStack style={{ alignItems: 'center' }}>
                <Box style={{ width: 75, alignItems: 'center' }}>{fileIcon}</Box>
                <VStack w="70%">
                  <Text fontSize={14} numberOfLines={1} ellipsizeMode="tail">
                    {item.name}
                  </Text>
                  {modules !== Gql.CategoryType.AllForm ? (
                    <HStack space={2} alignItems="center">
                      <Text color={COLORS.neutrals.gray90} fontSize={12}>
                        {formattedDate}
                      </Text>
                      {item.fileSystemType !== 'Folder' && (
                        <>
                          <Text fontSize="2xs" color={COLORS.neutrals.gray90}>
                            {'\u2022'}
                          </Text>
                          <Text color={COLORS.neutrals.gray90}>{item.fileSize ?? 0} MB</Text>
                        </>
                      )}
                      {item.localFileUrl || item.isQueued ? (
                        <Icon name={item.localFileUrl ? 'download' : 'download-grey'} width={16} />
                      ) : null}
                    </HStack>
                  ) : (
                    <HStack space={2} alignItems="center">
                      {status !== 'Draft' && (
                        <Text color={COLORS.neutrals.gray90} fontSize={12}>
                          {item.workspaceParent?._raw?.code} - {item.groupCode}
                        </Text>
                      )}
                      {statusComponent}
                      <AvatarComponent
                        style={{ marginLeft: 14 }}
                        assignees={item.workspaceWithAssignees}
                        maxVisible={3}
                        type="task"
                        group
                      />
                      {item.localFileUrl || item.isQueued ? (
                        <Icon name={item.localFileUrl ? 'download' : 'download-grey'} />
                      ) : null}
                    </HStack>
                  )}
                </VStack>
                {item?.workspaceGroupUsers?.length > 0 && (
                  <HStack
                    w="8"
                    h="auto"
                    justifyContent="center"
                    alignItems="center"
                    bgColor="#9B111E"
                    mx={1}
                    borderRadius="2xl"
                  >
                    <Box style={{ marginRight: 1 }}>
                      <Icon name="lock-white" />
                    </Box>
                    <Text color="white" fontSize={12}>
                      {item?.workspaceGroupUsers?.length}
                    </Text>
                  </HStack>
                )}
              </HStack>
              <Divider my={4} mx={10} />
            </Box>
          </TouchableOpacity>
          {isUnsynced && (
            <TouchableOpacity
              onPress={() => onOptionPress(item)}
              style={{ width: '10%', position: 'absolute', right: 35 }}
              hitSlop={10}
            >
              <Box style={{ width: 72 }}>
                <Icon name="unsync" style={{ alignSelf: 'center', marginVertical: '27%' }} />
              </Box>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            onPress={() => onOptionPress(item)}
            style={{ width: '10%', position: 'absolute', right: 15 }}
            hitSlop={10}
          >
            <Box style={{ width: 72 }}>
              <Icon name="option-dot" style={{ alignSelf: 'center', marginVertical: '30%' }} />
            </Box>
          </TouchableOpacity>
        </HStack>
      );
    }
  );

  const renderItem = useCallback(
    ({ item, index }: { item: any; index: number }) => (
      <DocumentListItem
        item={item}
        index={index}
        handlePress={handlePress}
        handleLongPress={handleLongPress}
        onOptionPress={onOptionPress}
        modules={modules}
        status={status}
      />
    ),
    [handlePress, handleLongPress, onOptionPress, modules, status]
  );

  const keyExtractor = useCallback((item: any) => {
    return item.id?.toString?.() || '';
  }, []);

  const onRowOpen = useCallback(
    async (id: string, rowMap: RowMap<any>) => {
      const rowRef = rowMap[id];
      const item = documents.find(doc => doc.id === id);

      if (item && item.fileSystemType === Gql.FileSystemType.Folder) {
        pushMessaage('Folder cannot be downloaded for offline use', 'warning');
        rowRef.closeRow();
        return;
      }

      try {
        await queueOfflineDownload([id]);
        if (isConnected) {
          await handleOfflineDownload({ id: '', dispatchStatus: false });
        }
      } catch (error) {
        Alert.alert('Download Error', 'There was an error downloading the file for offline use.');
      }
    },
    [documents, queueOfflineDownload, isConnected, handleOfflineDownload]
  );

  const handleRefresh = useCallback(async () => {
    await syncAndDownload({
      syncMutateOptions: { dispatchStatus: true },
      offlineDownloadOptions: { id: '', dispatchStatus: true },
      showSyncModal: true
    });
  }, [syncAndDownload]);

  const ListFooterComponent = useMemo(() => {
    if (isFetchingNextPage) {
      return (
        <Box py={4} alignItems="center">
          <Spinner size="lg" color="cyan.500" />
          <Text color="gray.500" mt={2}>
            Loading more items...
          </Text>
        </Box>
      );
    }
    return null;
  }, [isFetchingNextPage]);

  return (
    <View style={{ flex: 1 }}>
      <SwipeListView
        data={documents}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        ListEmptyComponent={<NotFound />}
        refreshControl={<RefreshControl refreshing={isPending} onRefresh={handleRefresh} />}
        onEndReached={() => {
          if (!onEndReachedCalledDuringMomentum.current) {
            loadMoreDocuments();
            onEndReachedCalledDuringMomentum.current = true;
          }
        }}
        onEndReachedThreshold={0.5}
        onMomentumScrollBegin={() => {
          onEndReachedCalledDuringMomentum.current = false;
        }}
        rightOpenValue={-75}
        renderHiddenItem={renderHiddenItem}
        ListFooterComponent={ListFooterComponent}
        onRowOpen={onRowOpen}
        maxToRenderPerBatch={10}
        windowSize={5}
        updateCellsBatchingPeriod={50}
        removeClippedSubviews
        initialNumToRender={10}
        disableVirtualization={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  box: {
    backgroundColor: '#FFFFFF',
    overflow: 'hidden',
    marginVertical: 10
  }
});

const getStatus = (type: string) => _.get(status, type) ?? '';
const status = {
  Approved: (
    <HStack space={2} alignItems="center">
      <Icon name="approved" />
      <Text color={COLORS.neutrals.gray70}>Approved</Text>
    </HStack>
  ),
  Draft: (
    <HStack space={2} alignItems="center">
      <Icon name="draft" />
      <Text color={COLORS.neutrals.gray70}>Draft</Text>
    </HStack>
  ),
  InReview: (
    <HStack space={2} alignItems="center">
      <Icon name="in-review" />
      <Text color={COLORS.neutrals.gray70}>In Review</Text>
    </HStack>
  ),
  Amend: (
    <HStack space={2} alignItems="center">
      <Icon name="amend" />
      <Text color={COLORS.neutrals.gray70}>Amend</Text>
    </HStack>
  ),
  InProgress: (
    <HStack space={2} alignItems="center">
      <Icon name="dynamic-in-progress" />
      <Text color={COLORS.neutrals.gray70}>In Progress</Text>
    </HStack>
  ),
  Pending: (
    <HStack space={2} alignItems="center">
      <Icon name="pending" />
      <Text color={COLORS.neutrals.gray70}>Pending</Text>
    </HStack>
  ),
  Rejected: (
    <HStack space={2} alignItems="center">
      <Icon name="reject" />
      <Text color={COLORS.neutrals.gray70}>Rejected</Text>
    </HStack>
  ),
  Submitted: (
    <HStack space={2} alignItems="center">
      <Icon name="submit" />
      <Text color={COLORS.neutrals.gray70}>Submitted</Text>
    </HStack>
  )
};

export default memo(DocumentList);
