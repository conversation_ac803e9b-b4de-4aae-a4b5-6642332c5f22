import React, { memo, useCallback } from 'react';
import { HStack, Pressable, Spinner, Text, VStack } from 'native-base';
import Icon from '@src/commons/Icon';
import ProjectDocumentModel from '@src/database/model/project-document';

interface RenderHiddenItemProps {
  item: ProjectDocumentModel;
  onAction: boolean;
  setOnAction: (value: boolean) => void;
  isPending: boolean;
}

const RenderHiddenItem: React.FC<RenderHiddenItemProps> = memo(({ item, onAction, setOnAction, isPending }) => {
  // const { mutateAsync: saveDocument, isPending } = useSaveProjectDocument();

  if (item.type === 'folder') {
    return null;
  }

  const handlePress = useCallback(
    async (item: ProjectDocumentModel) => {
      try {
        setOnAction(true);
        // await saveDocument(item.id);
      } catch (error) {
      } finally {
        setOnAction(false);
      }
    },
    [item.id]
  );

  return (
    <HStack alignItems="center" flex={1} mb={7}>
      <Pressable
        height="full"
        w="20"
        ml="auto"
        bg="#68CFFF"
        justifyContent="center"
        onPress={() => handlePress(item)}
        _pressed={{
          opacity: 0.5
        }}
      >
        <VStack alignItems="center">
          {isPending ? (
            <>
              <Spinner size="lg" color="#fff" />
              <Text color="white" fontSize={10}>
                Saving...
              </Text>
            </>
          ) : (
            <>
              <Icon name="offline-cloud" />
              <Text color="white" fontSize={10}>
                Save Offline
              </Text>
            </>
          )}
        </VStack>
      </Pressable>
    </HStack>
  );
});

export default RenderHiddenItem;
