import React from 'react';
import { Field as FormikField } from 'formik';
import { KeyboardType, KeyboardTypeAndroid, KeyboardTypeIOS } from 'react-native';

interface Props {
  name: string;
  label?: string;
  placeholder?: string;
  autoFocus?: boolean;
  maxLength?: number;
  addOnLeft?: any;
  type?: 'username' | 'normal';
  component: JSX.Element;
  validate?: (arg: any[]) => (value: any) => void;
  options?: Option[];
  max?: number;
  column?: number;
  onlyOne?: boolean;
  disabled?: boolean;
  paddingLeft?: number;
  secureTextEntry?: boolean;
  keyboardType?: KeyboardType | KeyboardTypeAndroid | KeyboardTypeIOS;
}

interface Option {
  label: string;
  value: string;
}

const Field: React.FC<Props> = props => {
  return (
    <FormikField
      label={props.label}
      name={props.name}
      placeholder={props.placeholder}
      autoFocus={props.autoFocus}
      maxLength={props.maxLength}
      addOnLeft={props.addOnLeft}
      type={props.type}
      component={props.component}
      validate={props.validate}
      options={props.options}
      max={props.max}
      column={props.column}
      onlyOne={props.onlyOne}
      disabled={props.disabled}
      paddingLeft={props.paddingLeft}
      secureTextEntry={props.secureTextEntry}
      keyboardType={props.keyboardType}
    />
  );
};

export { Field };
export default Field;
