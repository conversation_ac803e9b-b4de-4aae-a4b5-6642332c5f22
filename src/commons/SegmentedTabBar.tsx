import React, { useState } from 'react';
import { Animated, Platform, Pressable, StyleSheet, useWindowDimensions, View } from 'react-native';
import { Box, HStack, Text } from 'native-base';
import { SegmentedControlProps } from 'react-native-collapsible-segmented-view';
import { COLORS, FONTS } from '@src/constants';
// import { CONTROL_HEIGHT, IS_IOS } from './helpers'

const SegmentedTabBar: React.FC<SegmentedControlProps> = ({
  setIndex,
  labels,
  containerStyle,
  selectedIndex,
  floatIndex,
  labelStyle,
  inactiveOpacity,
  indicatorStyle
}) => {
  const windowWidth = useWindowDimensions().width;
  const [currentIndex, setCurrentIndex] = useState(0);

  const onTabPress = React.useCallback(
    nextIndex => {
      setIndex(nextIndex);
      setCurrentIndex(nextIndex);
    },
    [setIndex]
  );

  const [translateX, setTraslateX] = React.useState(
    floatIndex.interpolate({
      inputRange: labels.map((_, i) => i),
      outputRange: labels.map((_, i) => (windowWidth / labels.length) * i)
    })
  );

  React.useEffect(() => {
    setTraslateX(
      floatIndex.interpolate({
        inputRange: labels.map((_, i) => i),
        outputRange: labels.map((_, i) => (windowWidth / labels.length) * i)
      })
    );
  }, [floatIndex, labels, windowWidth]);

  return (
    <View style={[styles.container, containerStyle]}>
      {labels.map((label, index) => {
        const isSelected = index === currentIndex;
        return (
          <Pressable
            key={label}
            onPress={() => onTabPress(index)}
            android_ripple={{
              borderless: true
              //   color: pressColor
            }}
            style={({ pressed }) => [{ opacity: pressed ? 0.4 : 1 }, styles.tab]}
          >
            <Box py={2} px={4}>
              <Text
                fontWeight={600}
                color={isSelected ? '#FFF' : 'neutrals.gray80'}
                style={[
                  {
                    color: isSelected ? COLORS.primary[1] : COLORS.neutrals.gray50,
                    fontFamily: isSelected ? FONTS.interBold : FONTS.interMedium
                  },
                  styles.label
                ]}
              >
                {label}
              </Text>
            </Box>
          </Pressable>
        );
      })}
      <Animated.View
        style={[styles.indicator, indicatorStyle, { transform: [{ translateX }], width: windowWidth / labels.length }]}
      >
        <Box w="100%" bg="primary.1" height="2px" />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    // paddingHorizontal: 20,
    backgroundColor: '#FFF',
    elevation: 0
    // shadowColor: '#000',
    // backgroundColor: 'white',
    // shadowOffset: {
    //   width: 0,
    //   height: 4
    // },
    // shadowOpacity: 0.06,
    // shadowRadius: 4,
    // elevation: 1
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center'
    // paddingHorizontal: 16
    // height: CONTROL_HEIGHT,
  },
  label: {
    fontSize: 14
  },
  indicator: {
    position: 'absolute',
    bottom: 0,
    paddingHorizontal: 25
  }
});

export { SegmentedTabBar };
