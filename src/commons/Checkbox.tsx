import React, { useEffect, useState } from 'react';
import { StyleSheet, Platform } from 'react-native';
import _ from 'lodash';
import CheckBox from '@react-native-community/checkbox';
import { COLORS } from '@constants';

interface Props {
  checked: boolean;
  disabled?: boolean;
  onChange: (val: boolean) => void | undefined;
  title?: string;
}

const Checkbox: React.FC<Props> = ({ checked, disabled, ...props }) => {
  const [toggleCheckBox, setToggleCheckBox] = useState(false);
  const onChange = () => {
    setToggleCheckBox(!toggleCheckBox);
    props.onChange(!toggleCheckBox);
  };

  useEffect(() => {
    setToggleCheckBox(checked);
  }, [checked]);

  return (
    <CheckBox
      boxType="square"
      disabled={disabled}
      value={toggleCheckBox}
      onValueChange={onChange}
      style={styles.checkbox}
      animationDuration={0.2}
      tintColor="#D4D6DB"
      onCheckColor="#FFF"
      onFillColor={COLORS.primary[1]}
      onTintColor={COLORS.primary[1]}
      offAnimationType="fade"
    />
  );
};

const styles = StyleSheet.create({
  checkbox: {
    height: 22,
    width: 22,
    paddingTop: Platform.OS === 'ios' ? 2 : 4,
    paddingLeft: 0,
    paddingRight: Platform.OS === 'ios' ? 5 : 0,
    borderColor: COLORS.neutrals.gray25,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 0
  }
});

Checkbox.defaultProps = {
  onChange: (v: any) => v,
  disabled: false
};

export { Checkbox };
