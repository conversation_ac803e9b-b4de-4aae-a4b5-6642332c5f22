import { Icon } from '@commons/Icon';
import { ICON_NAMES } from '@constants';
import { Gql } from '@src/api';
import ProjectAppBarModal from '@src/modules/App/components/ProjectAppBarModal';
import { useDispatch, useSelector } from '@src/store';
import _ from 'lodash';
import { Box, Button, Flex, HStack, ITextProps, Spinner, Text, View } from 'native-base';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { InteractionManager, Platform, StatusBar, StyleSheet } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import FastImage from 'react-native-fast-image';
import logo1 from '@assets/logo1.png';
import NotificationIcon from '@src/modules/Notification/components/pages/NotificationIcon';
import { projectActions } from '@src/slice/project.slice';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootNavigatorParams } from '@src/types';

type CustomAppBarProps = {
  cancel?: boolean;
  header?: boolean;
  goBack?: boolean;
  more?: boolean;
  onGoBack?: () => void;
  noLeft?: boolean;
  noRight?: boolean;
  background?: string;
  hasShadow?: boolean;
  textAlign?: 'left' | 'center';
  title?: string;
  onlyStatusBar?: boolean;
  transparent?: boolean;
  rightComponent?: any;
  leftIcon?: ICON_NAMES;
  titleOpacity?: number;
  onRightBtn?: () => void;
  isSubmittingRight?: boolean;
  isAbsolute?: boolean;
  leftIconProps?: object;
  hasBorder?: boolean;
  safeAreaTop?: boolean;
  dark?: boolean;
  barStyle?: 'light-content' | 'dark-content';
  titleProps?: ITextProps;
};
const CustomAppBar: React.FC<CustomAppBarProps> = props => {
  const dispatch = useDispatch();
  const projectAppBarModalRef = useRef<any>(null);
  const [showProjectTitle, setShowProjectTitle] = useState<boolean>(false);
  const project = useSelector(state => state.project);
  const projectTitle = useMemo(() => project?.selectedProjectTitle, [project?.selectedProjectTitle]);
  const [getProject] = Gql.useGetProjectLazyQuery();
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();

  const { OFFLINE_MODE } = useSelector(state => state.app);

  const route = useRoute();
  const maxW = props.rightComponent ? '1/3' : '2/3';

  const fetchProject = useCallback(async () => {
    if (!project.projectId) return;
    if (!projectTitle) {
      await getProject({
        variables: {
          id: project?.projectId ?? ''
        }
      }).then(res => {
        dispatch(projectActions.setCurrentProjectTitle(res.data?.project?.title ?? ''));
      });
    }
  }, [projectTitle, project?.projectId]);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      fetchProject();
    });
  }, [fetchProject]);

  const HeaderComponent = useMemo(() => {
    if (!props?.header) return;
    return (
      <Flex direction="row" justifyContent="space-between" width={'full'} alignItems={'center'}>
        {OFFLINE_MODE ? (
          <View ml={3}>
            <Icon name="offline-cloud" fill="#1CE" width={30} height={30} />
          </View>
        ) : route.name !== 'AllProjects' ? (
          <Button
            ml={3}
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            hitSlop={20}
            bg="transparent"
            onPress={() => {
              navigation?.navigate('DashboardNav', {
                screen: 'Dashboard'
              });
            }}
          >
            <Icon name="dashboard" fill="#1CE" width={30} height={30} />
          </Button>
        ) : null}
        <Button
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          bg="transparent"
          justifyContent="flex-start"
          onPress={() => {
            if (projectTitle || route.name === 'AllProjects') {
              projectAppBarModalRef?.current?.pushModal();
              if (projectTitle) {
                setShowProjectTitle(true);
              } else {
                setShowProjectTitle(false);
              }
            }
          }}
          maxW={maxW}
          marginTop="5px"
        >
          <HStack space={1} alignItems="flex-start">
            {route.name === 'AllProjects' ? (
              <Text color="#FFFFFF" numberOfLines={1} ellipsizeMode="tail">
                Projects
              </Text>
            ) : projectTitle ? (
              <>
                <Text
                  color="#FFFFFF"
                  maxW={Platform.OS === 'ios' ? '90%' : '100%'}
                  numberOfLines={1}
                  ellipsizeMode="tail"
                >
                  {projectTitle}
                </Text>
              </>
            ) : (
              <Spinner size="sm" color="#FFFFFF" />
            )}
            <Icon name="chevron-down" />
          </HStack>
        </Button>

        <Box flexDirection={'row'} justifyContent="flex-end">
          {props.rightComponent}
          {!OFFLINE_MODE && <NotificationIcon />}
        </Box>
      </Flex>
    );
  }, [props.header, projectTitle, props.rightComponent, route.name, OFFLINE_MODE]);

  return (
    <Box zIndex={999} style={props.isAbsolute && styles.absolution}>
      <StatusBar
        translucent={false}
        backgroundColor={Platform.OS === 'android' ? '#FFF' : props.transparent ? 'transparent' : props.background}
        barStyle={Platform.OS === 'android' ? 'dark-content' : props.barStyle || 'dark-content'}
      />

      {!props.onlyStatusBar && props.safeAreaTop && (
        <Box safeAreaTop backgroundColor={props.transparent ? 'transparent' : props.background} />
      )}

      {!props.onlyStatusBar && (
        <Box overflow="hidden">
          <HStack
            py={0}
            backgroundColor={props.background}
            justifyContent="space-between"
            alignItems="center"
            borderBottomWidth={props.hasBorder ? 1 : 0}
            borderColor="neutrals.gray10"
            style={[styles.appBarContainer, props.hasShadow && styles.appBarShadow]}
          >
            {HeaderComponent}
            {props.more && (
              <Flex direction="row" justifyContent="space-between" width="100%">
                <Button
                  _pressed={{
                    bg: 'transparent',
                    opacity: 0.8
                  }}
                  bg="transparent"
                  justifyContent="center"
                />

                <Button
                  _pressed={{
                    bg: 'transparent',
                    opacity: 0.8
                  }}
                  bg="transparent"
                  justifyContent="center"
                  onPress={() => {}}
                >
                  <FastImage source={logo1} style={{ width: 110, height: 22 }} />
                </Button>

                <NotificationIcon />
              </Flex>
            )}

            <ProjectAppBarModal ref={projectAppBarModalRef} showProjectTitle={showProjectTitle}></ProjectAppBarModal>

            {!_.isEmpty(props.title) ? (
              <Text
                px={1}
                flex={props.goBack ? 4 : 5}
                textAlign="center"
                fontWeight={500}
                fontSize="16px"
                opacity={props.titleOpacity}
                color={props.dark ? '#FFF' : 'neutrals.black'}
                {...props.titleProps}
              >
                {props.title || ''}
              </Text>
            ) : (
              <Box flex={props.goBack ? 4 : 5} />
            )}
          </HStack>
        </Box>
      )}
    </Box>
  );
};

const styles = StyleSheet.create({
  appBarContainer: {
    width: '100%'
  },
  appBarShadow: {
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 16,
    elevation: 2
  },
  absolution: {
    position: 'absolute',
    width: '100%',
    zIndex: 999,
    top: 0
  },
  logout: {
    color: '#FFFFFF'
  }
});

CustomAppBar.defaultProps = {
  background: '#0695D7',
  goBack: false,
  hasShadow: false,
  textAlign: 'center',
  onlyStatusBar: false,
  transparent: false,
  titleOpacity: 1,
  hasBorder: false,
  safeAreaTop: true,
  dark: false
};

export { CustomAppBar };
export default React.memo(CustomAppBar);
