import React, { useRef, useState } from 'react';
import { HStack, Box, Text, Button, FlatList } from 'native-base';
import { COLORS, DIMENS } from '@constants';
import Icon from './Icon';
import Modal from './Modal';
import { Footer } from './Footer';
import { NativeTouchable } from './NativeTouchable';
import AppBar from './AppBar';
import { StyleSheet } from 'react-native';
import { Checkbox } from './Checkbox';
import _ from 'lodash';

type Props = { py?: number; onSelect?: (id: number) => void };

const data = Array(
  {
    id: 1,
    companyName: 'Del Vino Sdn Bhd'
  },
  {
    id: 2,
    companyName: 'Del Vino Sdn Bhd'
  },
  {
    id: 3,
    companyName: 'Del Vino Sdn Bhd'
  }
);

const CustomerSelection = ({ py, onSelect }: Props) => {
  const modalRef = useRef<any>(null);
  const [selectedId, setSelectedId] = useState<number | null>(0);

  const handleChange = (checked: boolean, id: number) => {
    setSelectedId(id);
    if (onSelect) onSelect(id);
  };
  return (
    <Box>
      <NativeTouchable onPress={() => modalRef?.current?.pushModal()}>
        <HStack
          borderWidth={1}
          borderColor="neutrals.gray25"
          rounded={12}
          px={4}
          py={py || 4}
          justifyContent="space-between"
          alignItems="center"
        >
          <Box>
            <Text variant="caption" color="neutrals.gray50">
              Customer
            </Text>
            <Text variant="body1">Pro Tech Engineering Sdn Bhd</Text>
          </Box>
          <Icon name="chevron-down" fill={COLORS.neutrals.gray80} />
        </HStack>
      </NativeTouchable>
      <Modal ref={modalRef}>
        <AppBar
          safeAreaTop={false}
          noLeft
          hasBorder
          title="Select customer"
          titleProps={{ fontWeight: 700 }}
          rightComponent={
            <Box>
              <Button variant="primaryLink">Add new</Button>
            </Box>
          }
        />
        <Box h={DIMENS.screenHeight * 0.5}>
          <FlatList
            contentContainerStyle={styles.contentContainer}
            data={data}
            renderItem={({ item }) => (
              <HStack py={4}>
                <Checkbox onChange={checked => handleChange(checked, item.id)} checked={selectedId === item.id} />
                <Text ml={3} variant="body1" color="neutrals.gray80">
                  {item.companyName}
                </Text>
              </HStack>
            )}
          />
        </Box>
        <Footer pb={0}>
          <Button variant="primary">Confirm</Button>
        </Footer>
      </Modal>
    </Box>
  );
};

const styles = StyleSheet.create({
  contentContainer: {
    paddingHorizontal: 20,
    paddingTop: 12
  }
});

export { CustomerSelection };
