import React from 'react';
import { Box, HStack, Text } from 'native-base';
import moment from 'moment';
import { NativeTouchable } from '@commons/NativeTouchable';
import { ServiceTeamAvatar } from '@commons/ServiceTeamAvatar';
import * as Gql from '@graphql';

const ServiceTeamItem = ({ item, onPress }: { item: Gql.User; onPress?: () => void }) => {
  return (
    <Box mb={4}>
      <NativeTouchable onPress={onPress}>
        <Box px={4} bg="#FFF" rounded={12}>
          <HStack py={4} justifyContent="space-between">
            <Box mr={4}>
              <ServiceTeamAvatar status="idle" size={70} uri={item.photo ?? ''} />
            </Box>
            <Box flex={1}>
              <Text variant="headline" numberOfLines={1} mb={1}>
                {item.name}
              </Text>

              <Text variant="body2" mb={1}>
                {item.isActive ? 'Available' : 'Not Available'}
              </Text>
              <HStack justifyContent="space-between">
                <Text variant="body2" mb={1}>
                  TNB Pass
                </Text>
                <Text variant="body2" color="semantics.success" mb={1}>
                  {moment(item.engineerDetail?.tnbPassExpiry).format('D MMM YYYY')}
                </Text>
              </HStack>
              <HStack justifyContent="space-between">
                <Text variant="body2" mb={1}>
                  OGSP
                </Text>
                <Text variant="body2" color="semantics.danger" mb={1}>
                  {moment(item.engineerDetail?.OGSPExpiry).format('D MMM YYYY')}
                </Text>
              </HStack>
            </Box>
          </HStack>
        </Box>
      </NativeTouchable>
    </Box>
  );
};

export { ServiceTeamItem };
