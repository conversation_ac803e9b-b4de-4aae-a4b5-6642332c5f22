import { <PERSON>, Divider, Flex, HStack, Text, VStack } from 'native-base';
import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import { Icon } from '@src/commons';
import DeviceInfo from 'react-native-device-info';
import { Gql } from '@src/api';
import { COLORS } from '@src/constants/colors';

type Props = {
  title: string;
  totalCount: number;
  isParent?: boolean;
  item: Gql.WorkspaceGroup;
  onPressChild?: (item: any) => void;
  onLongPressChild?: () => void;
  viewOnly?: boolean;
  handleThreeDots?: (item: any) => void;
};

const Accordion = (props: Props) => {
  const [isOpen, setIsOpen] = useState(false);

  const isFixturesGroup = props?.title === 'Ungroup Documents' || props?.title === 'Site Diary';

  const toggleAccordion = () => {
    setIsOpen(!isOpen);
  };

  return (
    <TouchableOpacity onPress={toggleAccordion} style={{ width: '100%' }}>
      <VStack mx={3}>
        <Box style={styles.box}>
          <HStack alignItems="center" justifyContent={'space-between'}>
            <Box mr={4}>
              {isFixturesGroup ? (
                <Icon name="group" />
              ) : props?.isParent ? (
                <Icon name={isOpen ? 'arrow-down' : 'arrow-right'} />
              ) : null}
            </Box>
            <HStack height={'full'} justifyContent={'space-between'} mr={10}>
              <Text
                fontWeight={600}
                numberOfLines={1}
                ellipsizeMode="tail"
                w={DeviceInfo.getDeviceType() == 'Tablet' ? '90%' : '83%'}
              >
                {props?.title !== 'undefined (undefined)' ? `${props?.title} (${props?.totalCount})` : '...'}
              </Text>
              <Text color={COLORS.neutrals.gray70}>{props.item?.code}</Text>
            </HStack>
            {!props?.viewOnly ? (
              <TouchableOpacity
                onPress={() => {
                  props?.handleThreeDots?.(props.item);
                }}
                style={styles.threeDots}
              >
                <Box style={{ alignSelf: 'center' }}>
                  {props.title !== 'Ungroup Documents' && props.title !== 'Site Diary' ? (
                    <Icon name="option-dot" style={{ marginVertical: '10%' }} />
                  ) : null}
                </Box>
              </TouchableOpacity>
            ) : null}
          </HStack>
        </Box>
        {isOpen
          ? props?.item?.children?.map?.((child: any, index: number) => (
              <TouchableOpacity onPress={() => props?.onPressChild?.(child)} style={{ width: '100%' }} key={index}>
                <Divider my={3} />
                <VStack>
                  <Box style={styles.box}>
                    <HStack alignItems="center">
                      <Box width={'100%'} ml={8}>
                        <Text
                          fontWeight={600}
                          numberOfLines={1}
                          ellipsizeMode="tail"
                          w={DeviceInfo.getDeviceType() == 'Tablet' ? '90%' : '83%'}
                        >
                          {child?.name} ({child?.totalCount})
                        </Text>
                      </Box>
                      {!props?.viewOnly ? (
                        <TouchableOpacity
                          onPress={() => {
                            props?.handleThreeDots?.(child);
                          }}
                          style={styles.threeDots}
                        >
                          <Box style={{ alignSelf: 'center' }}>
                            {child.name !== 'Ungroup Documents' && child.name !== 'Site Diary' ? (
                              <Icon name="option-dot" style={{ marginVertical: '10%' }} />
                            ) : null}
                          </Box>
                        </TouchableOpacity>
                      ) : null}
                    </HStack>
                  </Box>
                </VStack>
              </TouchableOpacity>
            ))
          : null}
        <Divider mt={3} />
      </VStack>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  box: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden'
  },
  addButton: {
    position: 'absolute',
    bottom: 10,
    alignSelf: 'flex-end',
    right: '7%',
    shadowColor: 'rgba(0, 0, 0, 0.5)',
    shadowOpacity: 1,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 6 },
    backgroundColor: '#FFFFFF',
    borderRadius: 9999
  },
  threeDots: {
    width: '10%',
    position: 'absolute',
    right: 0
  }
});

export default Accordion;
