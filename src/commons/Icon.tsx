import { ICONS, ICON_NAMES } from '@constants';
import _ from 'lodash';
import React from 'react';
import { SvgProps } from 'react-native-svg';

interface IconProps extends SvgProps {
  name: ICON_NAMES;
  width?: number;
  height?: number;
  fill?: string;
  style?: any;
}
const Icon = (props: IconProps) => {
  const hasIcon = _.find(ICONS, { name: props.name });
  if (hasIcon) {
    const Comp = hasIcon.component;
    return <Comp {...props} />;
  }
  return null;
};

export { Icon };
export default Icon;
