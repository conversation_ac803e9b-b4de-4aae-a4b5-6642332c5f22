import { useCallback, useEffect } from 'react';
import PushNotification, { PushNotificationObject, ReceivedNotification } from 'react-native-push-notification';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import { Platform } from 'react-native';
import _ from 'lodash';
import useSyncWithDownload from '@src/hooks/useSyncWithDownload';
import { useDeepLinkHandler } from '@src/hooks/useDeepLinkHandler';
import { NOTIFICATION_CHANNEL, NOTIFICATION_CONFIG, NOTIFICATION_SOUND } from '@src/constants/notification.constants';
import {
  NotificationData,
  PushNotificationPayload,
  SyncConfig,
  NotificationPriority
} from '@src/types/notification.types';

const defaultSyncConfig: SyncConfig = {
  showSyncModal: true,
  syncMutateOptions: { dispatchStatus: true },
  offlineDownloadOptions: {
    dispatchStatus: true,
    id: ''
  }
};

const PushNotificationController = () => {
  const { syncAndDownload } = useSyncWithDownload();
  const { handleDeepLink } = useDeepLinkHandler();

  const localNotification = useCallback((notification: PushNotificationPayload) => {
    const notificationData: NotificationData = notification.data || {};
    const message =
      typeof notification.message === 'object' ? JSON.stringify(notification.message) : notification.message;

    return PushNotification.localNotification({
      ...NOTIFICATION_CONFIG,
      title: notification.title || '',
      message,
      soundName: NOTIFICATION_SOUND.DEFAULT,
      channelId: notification.channelId ?? NOTIFICATION_CHANNEL.DEFAULT,
      smallIcon: NOTIFICATION_CHANNEL.ICON,
      userInfo: notificationData,
      priority: 'high' as NotificationPriority
    });
  }, []);

  const handleNotificationInteraction = useCallback(
    async (notification: PushNotificationPayload) => {
      try {
        await syncAndDownload(defaultSyncConfig);

        if (notification.userInteraction && notification.data) {
          await handleDeepLink(notification.data);
        }

        notification.finish?.(PushNotificationIOS.FetchResult.NoData);
      } catch (error) {}
    },
    [handleDeepLink, syncAndDownload]
  );

  // Create Android notification channel
  useEffect(() => {
    if (Platform.OS === 'android') {
      PushNotification.createChannel(
        {
          channelId: NOTIFICATION_CHANNEL.DEFAULT,
          channelName: 'Default Channel',
          channelDescription: 'Default notification channel',
          playSound: true,
          soundName: NOTIFICATION_SOUND.DEFAULT,
          importance: 4, // IMPORTANCE_HIGH
          vibrate: true
        },
        (created: boolean) => {
          if (created) {
          }
        }
      );
    }
  }, []);

  useEffect(() => {
    const handleNotification = (notification: Omit<ReceivedNotification, 'userInfo'>) => {
      const fullNotification = notification as unknown as PushNotificationPayload;

      // Show local notification for both Android and iOS
      if (!fullNotification.userInteraction) {
        localNotification(fullNotification);
      }

      void handleNotificationInteraction(fullNotification);
    };

    // Configure push notification handling
    PushNotification.configure({
      onNotification: handleNotification,
      permissions: {
        alert: true,
        badge: true,
        sound: true
      },
      popInitialNotification: true,
      requestPermissions: true
    });

    // Handle initial notification if app was launched from it
    PushNotification.popInitialNotification(notification => {
      if (notification) {
        void handleNotificationInteraction(notification as unknown as PushNotificationPayload);
      }
    });

    // Cleanup
    return () => {
      PushNotification.unregister();

      // Remove Android channel on cleanup
      if (Platform.OS === 'android') {
        PushNotification.deleteChannel(NOTIFICATION_CHANNEL.DEFAULT);
      }
    };
  }, [handleNotificationInteraction, localNotification]);

  return null;
};

export { PushNotificationController };
