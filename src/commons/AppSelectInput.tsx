import { ISelectProps, Select } from 'native-base';
import React, { FC } from 'react';
import { StyleSheet } from 'react-native';

const AppSelectInput: FC<ISelectProps> = props => {
  return (
    <Select
      color="neutrals.black"
      borderWidth={0}
      borderBottomWidth={0}
      borderRadius={15}
      fontSize={14}
      flex={1}
      bg="#FFF"
      h="55px"
      shadow={1}
      accessibilityLabel="Choose Options"
      placeholder="Choose Options"
      style={[styles.container, props.style]}
      {...props}
    >
      {props.children}
    </Select>
  );
};

const styles = StyleSheet.create({
  container: {
    elevation: 2
  }
});

export default AppSelectInput;
