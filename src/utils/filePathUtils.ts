import { Platform } from 'react-native';

/**
 * Check if path is a mobile local path (strict check for actual device files)
 * This function excludes relative paths that might be cloud storage paths
 */
export const isMobileLocalPath = (filePath: string | null | undefined): boolean => {
  if (!filePath || typeof filePath !== 'string') return false;

  const trimmedPath = filePath.trim();

  // Check for file:// protocol (local file)
  if (trimmedPath.match(/^file:\/\//i)) return true;

  // Check for Android content:// URIs
  if (trimmedPath.match(/^content:\/\//i)) return true;

  // Check for iOS absolute paths
  if (
    Platform.OS === 'ios' &&
    trimmedPath.match(/^\/(?:var|private|Users)\/mobile\/Containers\/Data\/Application\/[A-F0-9-]+\//i)
  )
    return true;

  // Check for Android absolute paths
  if (Platform.OS === 'android' && trimmedPath.match(/^\/(?:data\/(?:data|user)|storage|android_asset|sdcard)\//i))
    return true;

  // Don't treat relative paths as local mobile paths (they're likely cloud storage paths)
  return false;
};
