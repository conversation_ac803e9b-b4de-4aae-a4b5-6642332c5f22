import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { ProjectDocumentApiService } from '@src/api/rest';
import { pushError, pushMessaage } from '@src/configs';
import { Gql } from '@src/api';
import { RootNavigatorParams } from '@src/types';
import NetInfo from '@react-native-community/netinfo';

// Document type definition to match ProjectDocumentModel exactly
interface DocumentItem {
  id?: string | number;
  remoteId?: string | number | null;
  type?: string | null;
  name?: string | null;
  category?: string | null;
  localFileUrl?: string | null;
  fileUrl?: string | null;
  addedBy?: number | null;
}

interface NavigationParams {
  item: DocumentItem;
  role?: string;
  navigation: NativeStackNavigationProp<RootNavigatorParams>;
  isBim?: boolean;
  driveType?: Gql.ProjectDocumentDriveType;
  onRefetch?: () => void;
  modules?: string;
}

// Shared navigation params for sub-folder navigation
interface DrawingSubFolderParams {
  id: number;
  name: string;
  category: string;
  refetch: () => void;
  role: string;
}

interface CloudDocSubFolderParams {
  id: string;
  name: string;
  category: string;
  refetch: () => void;
  role: string;
  driveType?: Gql.ProjectDocumentDriveType;
}

// PdfTron specific params
interface PdfTronParams {
  id: string;
  role: string;
  modules: string;
  fileUrl?: string;
  projectDocumentId?: string;
  localId?: string;
  localRemoteId?: string;
  xfdf?: any;
  revisionData?: any[];
}

const toNavigationId = (id: string | number | undefined | null): string => {
  if (id === null || id === undefined) return '';
  return id.toString();
};

const toNumberId = (id: string | number | undefined | null): number => {
  if (id === null || id === undefined) return 0;
  return typeof id === 'string' ? parseInt(id, 10) : id;
};

const checkBIMStatus = async (urn: string) => {
  try {
    const status = await ProjectDocumentApiService.bimStatus({
      body: { urn: urn }
    }).catch(() => {
      pushError('Failed to get BIM status');
    });
    return status;
  } catch (e) {
    pushError(e);
    return null;
  }
};

const checkNetworkConnection = async (): Promise<boolean> => {
  const netInfo = await NetInfo.fetch();
  return !!netInfo.isConnected;
};

export const handleDocumentNavigation = async (params: NavigationParams) => {
  const { item, role, navigation, isBim, driveType, onRefetch, modules = 'CloudDocs' } = params;

  if (item.type === 'folder') {
    const commonParams = {
      name: item.name ?? '',
      category: item.category ?? '',
      refetch: onRefetch ?? (() => {}),
      role: role ?? ''
    };

    // Different navigation stacks based on module type
    if (modules === 'Drawings') {
      const drawingParams: DrawingSubFolderParams = {
        ...commonParams,
        id: toNumberId(item.remoteId || item.id)
      };

      navigation.navigate('DrawingNav' as any, {
        screen: 'DrawingSubFolder',
        params: drawingParams
      });
    } else if (modules === 'Templates') {
      const cloudDocParams: CloudDocSubFolderParams = {
        ...commonParams,
        id: toNavigationId(item.remoteId || item.id),
        driveType
      };

      navigation.navigate('DigitalFormNav' as any, {
        screen: 'DigitalFormSubFolder',
        params: cloudDocParams
      });
    } else {
      const cloudDocParams: CloudDocSubFolderParams = {
        ...commonParams,
        id: toNavigationId(item.remoteId || item.id),
        driveType
      };

      navigation.navigate('CloudDocsNav' as any, {
        screen: 'CloudDocSubFolder',
        params: cloudDocParams
      });
    }
    return;
  }

  if (item.type === 'pdf') {
    const isConnected = await checkNetworkConnection();

    if (!isConnected && !item.localFileUrl) {
      return pushError('Please connect to internet or download the file for offline viewing');
    }

    const fileUrl = item.localFileUrl || item.fileUrl;

    const pdftronParams: PdfTronParams = {
      id: toNavigationId(item.remoteId || item.id),
      role: role ?? '',
      modules: modules,
      fileUrl: fileUrl ?? undefined,
      projectDocumentId: toNavigationId(item.remoteId || item.id),
      localId: toNavigationId(item.id),
      localRemoteId: toNavigationId(item.remoteId)
    };

    navigation.navigate('PdftronNav' as any, {
      screen: 'Pdftron',
      params: pdftronParams
    });
    return;
  }

  if (['jpg', 'jpeg', 'png', 'svg'].includes(item.type?.toLowerCase() ?? '')) {
    navigation.navigate('CloudDocsNav' as any, {
      screen: 'CloudDocImageViewer',
      params: { id: toNavigationId(item.id) }
    });
    return;
  }

  if (['dwg', 'DWG', 'rvt', 'fbx', 'nwd'].includes(item.type ?? '')) {
    // For BIM files, check network connectivity first
    if (isBim) {
      const isConnected = await checkNetworkConnection();
      if (!isConnected) {
        pushMessaage('Please connect to internet to view the BIM files', 'error', 'No Connection');
        return;
      }

      const res = await checkBIMStatus(item.fileUrl ?? '');
      if (!res) return;

      if (res.progress === 'inprogress') {
        pushMessaage('BIM file processing in progress...', 'warning', 'Processing');
        return;
      }

      if (res.status === 'failed') {
        pushMessaage('Failed to process BIM file', 'error', 'Error');
        return;
      }

      if (res.status === 'success' && res.progress === 'complete') {
        navigation.navigate('DrawingNav' as any, {
          screen: 'DrawingWebView',
          params: { id: toNavigationId(item.remoteId || item.id) }
        });
      }
      return;
    }

    // For non-BIM DWG files
    navigation.navigate('DrawingNav' as any, {
      screen: 'DrawingWebView',
      params: { id: toNavigationId(item.id) }
    });
    return;
  }
};
