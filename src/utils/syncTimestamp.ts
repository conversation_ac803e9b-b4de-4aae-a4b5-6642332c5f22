import AsyncStorage from '@react-native-async-storage/async-storage';

const LAST_SYNC_TIMESTAMP_KEY = '@lastSyncTimestamp';

/**
 * Gets the timestamp of the last successful sync
 * @returns {Promise<number|null>} Timestamp in milliseconds or null if no sync has occurred
 */
export const getLastSyncTimestamp = async (): Promise<number | null> => {
  try {
    const timestamp = await AsyncStorage.getItem(LAST_SYNC_TIMESTAMP_KEY);
    return timestamp ? parseInt(timestamp, 10) : null;
  } catch (error) {
    return null;
  }
};

/**
 * Updates the timestamp of the last successful sync
 * @param {number} timestamp - Timestamp in milliseconds (defaults to current time)
 * @returns {Promise<void>}
 */
export const updateLastSyncTimestamp = async (timestamp: number = Date.now()): Promise<void> => {
  try {
    await AsyncStorage.setItem(LAST_SYNC_TIMESTAMP_KEY, timestamp.toString());
  } catch (error) {}
};

/**
 * Checks if enough time has passed since the last sync
 * @param {number} minInterval - Minimum interval in milliseconds
 * @returns {Promise<boolean>} True if enough time has passed or no previous sync
 */
export const shouldSync = async (minInterval: number): Promise<boolean> => {
  const lastSync = await getLastSyncTimestamp();
  if (lastSync === null) return true;

  const now = Date.now();
  return now - lastSync >= minInterval;
};
