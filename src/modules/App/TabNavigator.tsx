import { BottomTabBar, Icon } from '@commons';
import { COLORS } from '@constants';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { TabNavigatorParams } from '@types';
import React, { useEffect, useState, useMemo } from 'react';
import { InteractionManager, TouchableOpacity } from 'react-native';

import { useSelector } from '@src/store';
import Account from '../Account/pages/Account';
import CloudDoc from '../CloudDocs/pages/CloudDoc';
import DigitalForm from '../DigitalForm/pages/DigitalForm';
import Drawing from '../Drawing/pages/Drawing';
import Tasks from '../Task/pages/Task';
import { ProjectAccess } from '@src/constants/subscription';
import { useProjectSubscriptionPackage } from '@src/hooks/useProjectSubscriptionPackage';

const Tab = createBottomTabNavigator<TabNavigatorParams>();
const TabNavigator: React.FC<any> = ({ navigation }) => {
  const auth = useSelector(state => state.auth);
  const appConfig = useSelector(state => state.app);
  const { OFFLINE_MODE, syncStatus } = appConfig;
  const [navKey, setNavKey] = useState(0);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      auth.isAuthenticated;
    });
  }, []);

  const { projectSubscriptionPackageId, isFeatureAllowed } = useProjectSubscriptionPackage();

  // Memoize tab configuration to avoid unnecessary recalculations
  const tabConfig = useMemo(() => {
    return {
      showTasks: isFeatureAllowed(ProjectAccess.TASK),
      showCloudDocs:
        isFeatureAllowed(ProjectAccess.PROJECT_DOCUMENT) ||
        isFeatureAllowed(ProjectAccess.WORK_PROGRAMME) ||
        isFeatureAllowed(ProjectAccess.CORRESPONDENCE) ||
        OFFLINE_MODE,
      showWorkspace:
        (isFeatureAllowed(ProjectAccess.WORKSPACE_DOCUMENT) || isFeatureAllowed(ProjectAccess.WORKSPACE_TEMPLATE)) &&
        !OFFLINE_MODE,
      showDrawings: isFeatureAllowed(ProjectAccess.DRAWING) || OFFLINE_MODE,
      subscriptionPackageId: projectSubscriptionPackageId
    };
  }, [projectSubscriptionPackageId, isFeatureAllowed, OFFLINE_MODE]);

  // Force navigator re-render only when tab configuration actually changes
  useEffect(() => {
    setNavKey(prev => prev + 1);
  }, [
    tabConfig.subscriptionPackageId,
    tabConfig.showTasks,
    tabConfig.showCloudDocs,
    tabConfig.showWorkspace,
    tabConfig.showDrawings
  ]);

  const getInitialRoute = () => {
    if (tabConfig.showTasks) return 'Tasks';
    else if (tabConfig.showCloudDocs) return 'CloudDoc';
    else if (tabConfig.showWorkspace) return 'DigitalForm';
    else if (tabConfig.showDrawings) return 'Drawings';
    else return 'More';
  };

  return (
    <>
      <Tab.Navigator
        key={navKey}
        initialRouteName={getInitialRoute()}
        screenOptions={{ headerShown: false }}
        tabBar={tabBarProps => <BottomTabBar {...tabBarProps} />}
      >
        {tabConfig.showTasks && (
          <Tab.Screen
            name="Tasks"
            listeners={() => {
              return {
                tabPress: e => {
                  if (OFFLINE_MODE) {
                    // e.preventDefault();
                  }
                }
              };
            }}
            options={{
              tabBarIcon: ({ focused }) => (
                <Icon height={20} width={20} name="tasks" fill={focused ? COLORS.primary[1] : COLORS.neutrals.gray25} />
              ),

              tabBarLabel: 'Tasks'
            }}
            component={Tasks as any}
          />
        )}
        {tabConfig.showCloudDocs && (
          <Tab.Screen
            name="CloudDoc"
            options={{
              tabBarIcon: ({ focused }) => (
                <Icon
                  height={24}
                  width={24}
                  name="cloud-docs"
                  fill={focused ? COLORS.primary[1] : COLORS.neutrals.gray25}
                />
              ),
              tabBarLabel: 'Cloud Docs'
            }}
            component={CloudDoc}
          />
        )}
        {tabConfig.showWorkspace && (
          <Tab.Screen
            name="DigitalForm"
            // listeners={() => {
            //   return {
            //     tabPress: e => {
            //       if (OFFLINE_MODE) {
            //         e.preventDefault();
            //       }
            //     }
            //   };
            // }}
            options={{
              tabBarIcon: ({ focused }) => (
                <Icon
                  height={24}
                  width={24}
                  name="digital-forms"
                  fill={focused ? COLORS.primary[1] : COLORS.neutrals.gray25}
                />
              ),
              tabBarLabel: 'Workspace',
              tabBarTestID: 'digitalFormTab'
            }}
            component={DigitalForm}
          />
        )}
        {tabConfig.showDrawings && (
          <Tab.Screen
            name="Drawings"
            options={{
              tabBarIcon: ({ focused }) => (
                <Icon
                  height={24}
                  width={24}
                  name="drawings"
                  fill={focused ? COLORS.primary[1] : COLORS.neutrals.gray25}
                />
              ),
              tabBarLabel: 'Drawings'
            }}
            component={Drawing}
          />
        )}

        {!OFFLINE_MODE && (
          <Tab.Screen
            name="More"
            options={{
              tabBarIcon: ({ focused }) => (
                <Icon height={24} width={24} name="more" fill={focused ? COLORS.primary[1] : COLORS.neutrals.gray25} />
              ),
              tabBarLabel: 'More'
            }}
            component={Account as any}
          />
        )}
      </Tab.Navigator>
    </>
  );
};

export default TabNavigator;
