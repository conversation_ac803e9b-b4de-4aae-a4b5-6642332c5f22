import React from 'react';
import { Button, Text } from 'native-base';
import { GestureResponderEvent } from 'react-native';

type Project = {
  id: string;
  title: string;
};

interface ModalButtonProps {
  title: string;
  onPress: (event: GestureResponderEvent) => void;
}

const ModalButton: React.FC<ModalButtonProps> = ({ title, onPress }) => (
  <Button style={{ alignItems: 'center', width: '100%', backgroundColor: 'transparent' }} onPress={onPress}>
    <Text>{title}</Text>
  </Button>
);

export default ModalButton;
