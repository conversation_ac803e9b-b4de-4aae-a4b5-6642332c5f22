import React from 'react';
import { Box, HStack, Text } from 'native-base';
import { TouchableOpacity } from 'react-native';
import { FlashList } from '@shopify/flash-list';
import { Icon } from '@src/commons';
import ProjectModel from '@src/database/model/project.model';

type Project = {
  id: string;
  title: string;
};

interface ProjectItemProps {
  item: Project;
  isSelected: boolean;
  onPress: (item: Project) => void;
}

const ProjectItem: React.FC<ProjectItemProps> = ({ item, isSelected, onPress }) => (
  <TouchableOpacity onPress={() => onPress(item)}>
    <Box borderBottomWidth={1} borderColor="#E8E8E8" pl={5} py={5}>
      <HStack justifyContent="space-between" pr={4}>
        <Text numberOfLines={2} ellipsizeMode="tail">
          {item.title}
        </Text>
        {isSelected && <Icon name="tick" />}
      </HStack>
    </Box>
  </TouchableOpacity>
);

interface ProjectListProps {
  data: Project[];
  selectedProjectId: string;
  onProjectSelect: (item: Project) => void;
}

const ProjectList: React.FC<ProjectListProps> = ({ data, selectedProjectId, onProjectSelect }) => (
  <FlashList
    data={data}
    renderItem={({ item }: { item: Project }) => (
      <ProjectItem item={item} isSelected={selectedProjectId === item.id} onPress={onProjectSelect} />
    )}
    keyExtractor={item => item.id}
    estimatedItemSize={50}
  />
);

export default ProjectList;
