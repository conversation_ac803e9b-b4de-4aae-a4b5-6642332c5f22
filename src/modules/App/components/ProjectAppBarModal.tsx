import React, { useCallback, useMemo, useState, forwardRef, useImperativeHandle, useEffect, useRef } from 'react';
import { Divider, Flex, Box } from 'native-base';
import { Alert, BackHandler, ModalProps, Platform, StyleSheet } from 'react-native';
import ModalButton from './ModalButton';
import ProjectList from './ProjectAppBarList';
import { useSelector, useDispatch } from '@src/store';
import { useNavigation } from '@react-navigation/native';
import { pushError } from '@src/configs';
import { projectActions, ProjectActions } from '@src/slice/project.slice';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootNavigatorParams } from '@src/types';
import { Modal } from '@src/commons';

interface Props {
  showProjectTitle?: boolean;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

type Project = {
  id: string;
  title: string;
};

const ProjectAppBarModal = forwardRef<ModalRef, Props>((props, ref) => {
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const projectState = useSelector(state => state.project);
  const states = useSelector(state => state.project);
  const dispatch = useDispatch();
  const modalRef = useRef<any>(null);
  const user = useSelector(state => state.auth.user);

  const [isSubmitting, setIsSubmitting] = useState(false);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const handleProjectSelect = useCallback(
    async (item: Project | null) => {
      try {
        setIsSubmitting(true);

        if (!item) {
          navigation.push('ProjectsNav', { screen: 'AllProjects' });
          return modalRef.current.pushModal();
        }

        modalRef?.current?.closeModal?.();
        dispatch(projectActions.setCurrentProjectTitle(item.title));
        dispatch(ProjectActions.updateProjectId(item.id));
        dispatch(ProjectActions.initialize(parseInt(user?.id ?? '0'), item.id?.toString() ?? ''));
        navigation.push('Tab', { screen: 'Tasks', params: { pageIndex: 0 } });
      } catch (e) {
        pushError(e);
      } finally {
        setIsSubmitting(false);
      }
    },
    [dispatch, navigation]
  );

  const projectData: Project[] = useMemo(
    () =>
      states?.recentProjects?.map?.(item => ({
        id: item.remoteId?.toString?.() ?? '',
        title: item.title
      })) || [],
    [states?.recentProjects]
  );

  return (
    <Modal ref={modalRef} {...props}>
      <Flex direction="row" style={styles.container}>
        <ModalButton title="View All Projects" onPress={() => handleProjectSelect(null)} />
      </Flex>
      <Divider />
      <Box height={Platform.OS === 'ios' ? '50%' : '50%'}>
        <ProjectList
          data={projectData}
          selectedProjectId={projectState.projectId ?? ''}
          onProjectSelect={handleProjectSelect}
        />
      </Box>
    </Modal>
  );
});

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    height: 48,
    alignItems: 'center'
  }
});

ProjectAppBarModal.displayName = 'ProjectAppBarModal';
export default React.memo(ProjectAppBarModal);
