import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps, useFocusEffect } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { Icon } from '@src/commons';
import CustomAppBar from '@src/commons/CustomAppBar';
import NotFound from '@src/commons/NotFound';
import { preventDoubleClick, setProjectId } from '@src/configs';
import { COLORS } from '@src/constants';
import CompanyAppBarModal from '@src/modules/Account/components/CompanyAppBarModal';
import { AuthActions } from '@src/slice/auth.slice';
import { notificationActions } from '@src/slice/notification.slice';
import { ProjectActions, projectActions } from '@src/slice/project.slice';
import { useDispatch, useSelector } from '@src/store';
import { ProjectNavigatorParams, RootNavigatorParams } from '@src/types';
import _ from 'lodash';
import moment from 'moment';
import {
  Alert,
  Box,
  Button,
  Center,
  Circle,
  FlatList,
  Flex,
  HStack,
  Input,
  Pressable,
  Skeleton,
  Spinner,
  Stack,
  Text,
  VStack
} from 'native-base';
import React, { ReactElement, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { InteractionManager, Linking, RefreshControl, StyleSheet, TouchableOpacity } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import FastImage from 'react-native-fast-image';
import SettingsModal from '../Components/SettingsModal';
import ProjectsModal from './ProjectsModal';
import ChangeLogModal from '@src/commons/ChangeLogModal';
import { useGetProject } from '@src/queries/project/useGetProjects';
import useDebouncedSearch from '@src/hooks/useDebounceSearch';
import SearchInput from '@src/commons/SearchInput';
import useSubscription from '@src/hooks/useSubscription';
import useNetworkStatus from '@src/hooks/useNetworkStatus';
import SafeImage from '@src/commons/SafeImage';

type Props = CompositeScreenProps<
  BottomTabScreenProps<ProjectNavigatorParams, 'AllProjects'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const AllProjects: React.FC<Props> = ({ navigation }) => {
  const projectsModalRef = useRef<any>(null);
  const modalRef = useRef<any>(null);
  const [modalMode, setModalMode] = useState<string>('');
  const [selectedProject, setSelectedProject] = useState();
  const [totalCount, setTotalCount] = useState<number>(0);
  const companyAppBarModalRef = useRef<any>(null);
  const dispatch = useDispatch();
  const numColums = DeviceInfo.getDeviceType() == 'Tablet' ? 2 : 1;
  const [alertMessage, setAlertMessage] = useState<ReactElement | null>();
  const { debouncedValue, setInputValue } = useDebouncedSearch();
  const [filteredValue, setFilteredValue] = useState<string | null>(null);
  const { OFFLINE_MODE } = useSelector(state => state.app);
  const isConnected = useNetworkStatus();
  const { user } = useSelector(state => state.auth);

  // const { data: user } = Gql.useGetUserMeQuery({
  //   fetchPolicy: 'cache-and-network'
  // });

  // const [getCompany, { data: companyData }] = Gql.useCompanyLazyQuery({
  //   variables: {
  //     id: user?.getUserMe?.company?.id ?? ''
  //   },
  //   fetchPolicy: 'cache-and-network'
  // });

  // getChangeLog lazy query
  const [getChangeLog, { data: changeLogData }] = Gql.useGetChangeLogsLazyQuery({});
  const [updateUser, { loading: updating }] = Gql.useUpdateUserMeMutation({});
  // const [getCompanySubscription, { data: companySubscription }] = Gql.useCompanySubscriptionsLazyQuery({
  //   variables: {
  //     sorting: {
  //       direction: Gql.SortDirection.Desc,
  //       field: Gql.CompanySubscriptionSortFields.SubscriptionEndDate
  //     },
  //     paging: {
  //       limit: 1
  //     }
  //   },
  //   fetchPolicy: 'cache-and-network'
  // });

  const { checkSubscriptions, isGracePeriod, isSubscriptionActive, isCompanyOwner, company, companySubscription } =
    useSubscription(user);

  useFocusEffect(
    React.useCallback(() => {
      InteractionManager.runAfterInteractions(() => {
        const isReadChangeLog = user?.isReadChangeLogMobile;
        if (!isReadChangeLog && isReadChangeLog !== undefined && !modalRef?.current?.isVisible) {
          getChangeLog();
          modalRef?.current?.pushModal();
        }

        checkSubscriptions();

        // if (!user?.getUserMe?.company?.id && !OFFLINE_MODE) return;

        // const fetchData = async () => {
        //   if (!OFFLINE_MODE) {
        //     const companySubscriptionRes = await getCompanySubscription();
        //     const companySubscriptionData = companySubscriptionRes?.data?.companySubscriptions?.nodes[0];

        //     setIsGracePeriod(companySubscriptionData?.isSubscriptionInGracePeriod ?? false);
        //     setIsSubscriptionActive(companySubscriptionData?.isSubscriptionActive ?? false);

        //     if (!companySubscriptionData) return;
        //     const [companyRes] = await Promise.all([getCompany()]);
        //     const companyData = companyRes?.data?.company as Gql.Company;
        //     dispatch(AuthActions.setCompany(companyData ?? null));
        //   }
        // };

        // fetchData();
        // setHasCheckedSubscriptions(true);
      });
    }, [user, checkSubscriptions])
  );

  useEffect(() => {
    if (isGracePeriod) {
      setAlertMessage(
        <Text px={1}>
          Your subscription has ended on{' '}
          {moment(companySubscription?.companySubscriptions?.nodes[0]?.subscriptionEndDate).format('DD MMM YYYY')}.
          Please{' '}
          <Text textDecorationLine={'underline'} onPress={renewPlan}>
            renew your plan
          </Text>{' '}
          immediately.
        </Text>
      );
    } else if (!isSubscriptionActive) {
      setAlertMessage(
        <Text px={1}>
          You do not have any active subscription now. Please{' '}
          <Text textDecorationLine={'underline'} onPress={renewPlan}>
            subscribe to a new plan
          </Text>
        </Text>
      );
    }
  }, [isGracePeriod, isSubscriptionActive]);

  // const companyOwner = useMemo(() => companyData?.company?.ownerId === user?.getUserMe?.id, [companyData, user]);

  const { data, isLoading, hasNextPage, fetchNextPage } = useGetProject(debouncedValue);

  const sanitizedProjectData = useCallback(() => {
    if (!data || !data.pages) return [];
    const allProjects = data.pages.map(page => page.items).flat();

    const recentProjects = allProjects.slice(0, 4);
    dispatch(projectActions.setRecentProjects(recentProjects as any));

    return allProjects;
  }, [data]);

  const projects = useMemo(() => sanitizedProjectData(), [sanitizedProjectData]);

  const loadMoreProjects = useCallback(() => {
    if (hasNextPage && !isLoading) {
      fetchNextPage();
    }
  }, [hasNextPage, isLoading, fetchNextPage]);

  const onSearchInputChange = (newSearchTerm: string) => {
    setFilteredValue(newSearchTerm);
    setInputValue(newSearchTerm || '');
  };

  const renewPlan = () => {
    Linking.canOpenURL('https://www.bina.cloud/contacts').then(() => {
      Linking.openURL('https://www.bina.cloud/contacts');
    });
  };

  return (
    <Box flex={1} bg={COLORS.neutrals.white}>
      <CustomAppBar header barStyle={'light-content'} />
      {/* All Projects */}
      {alertMessage && (isGracePeriod || !isSubscriptionActive) && (
        <Alert
          status={isGracePeriod ? 'warning' : 'error'}
          w="100%"
          px={2}
          py={2}
          justifyContent="flex-start"
          alignItems="flex-start"
        >
          <HStack alignItems={'flex-start'} px={0}>
            <Alert.Icon mr={1} mt={1} />
            {alertMessage}
          </HStack>
        </Alert>
      )}
      <Box p={4}>
        <Flex flexDirection="row" justifyContent="space-between" alignItems="center">
          <Flex flexDirection="column">
            <Text variant="headlineMuted">Welcome</Text>
            <Text style={styles.titleText}>{company?.company?.name}</Text>
          </Flex>

          {!OFFLINE_MODE && (
            <Button
              variant="outlineGray"
              size="xs"
              onPress={() => {
                companyAppBarModalRef?.current?.pushModal();
              }}
            >
              Switch
            </Button>
          )}
        </Flex>
      </Box>

      <SearchInput
        placeholder="Search for project name"
        filteredValue={filteredValue}
        setFilteredValue={(value: any) => onSearchInputChange(value)}
      />

      {!isSubscriptionActive && projects?.length === 0 && !isLoading ? (
        <Box pt={2}>
          <Center style={styles.center}>
            <Icon name="empty-project" />
            <Text color={COLORS.neutrals.gray100} fontWeight={'600'} fontSize={16} mt={2}>
              No projects here yet
            </Text>
            <Text color={COLORS.neutrals.gray90} fontWeight={'400'} fontSize={14} mt={2}>
              Start by creating project below
            </Text>
          </Center>
        </Box>
      ) : projects?.length === 0 && !isLoading && filteredValue?.length === 0 ? (
        <SkeletonComponent />
      ) : (
        <>
          <FlatList
            testID="allProjectsScreen"
            keyboardShouldPersistTaps="handled"
            contentContainerStyle={{ paddingHorizontal: 10, paddingTop: 20 }}
            data={projects}
            numColumns={numColums}
            keyExtractor={(item: any) => item.id}
            onEndReachedThreshold={0.1}
            ListFooterComponent={totalCount > projects?.length ? <Spinner size={'lg'} /> : null}
            onEndReached={loadMoreProjects}
            ListEmptyComponent={
              isLoading ? (
                <></>
              ) : filteredValue && filteredValue?.length > 0 ? (
                <NotFound />
              ) : (
                <Box pt={2}>
                  <Center style={styles.center}>
                    <Icon name="empty-project" />
                    <Text color={COLORS.neutrals.gray100} fontWeight={'600'} fontSize={16} mt={2}>
                      No projects here yet
                    </Text>
                    <Text color={COLORS.neutrals.gray90} fontWeight={'400'} fontSize={14} mt={2}>
                      Start by creating project below
                    </Text>
                  </Center>
                </Box>
              )
            }
            refreshControl={
              <RefreshControl
                refreshing={isLoading}
                // onRefresh={_.debounce(() => refetch({ paging: { limit: 4, offset: 0 } }), 1000)}
              />
            }
            renderItem={({ item, index }: any) => {
              // Check if we have a meaningful update timestamp (not a recent sync timestamp)
              const now = Date.now();
              const dayInMs = 24 * 60 * 60 * 1000;
              const updateTimestamp = item.server_updated_at || item.updated_at || item.created_at;

              // If timestamp is very recent (within last day) and all projects have similar timestamps,
              // it's likely a sync timestamp, so show creation date instead
              const isLikelySyncTimestamp = updateTimestamp && now - updateTimestamp < dayInMs;
              const displayTimestamp = isLikelySyncTimestamp ? item.created_at || updateTimestamp : updateTimestamp;

              const lastUpdated = displayTimestamp ? moment(displayTimestamp).format('DD MMM YYYY') : 'N/A';
              return (
                <Box style={styles.allProjectBox} key={index}>
                  <TouchableOpacity
                    testID={`projectListItem-${index}`}
                    onPress={() =>
                      preventDoubleClick(async () => {
                        // Handle critical state updates first
                        await Promise.all([
                          dispatch(ProjectActions.updateProjectId(item.remoteId?.toString() ?? '')),
                          dispatch(projectActions.setCurrentProjectTitle(item.title)),
                          dispatch(projectActions.setCurrentProjectImage(item.uri))
                        ]);

                        // Navigate after critical updates
                        navigation.navigate('Tab', {
                          screen: 'DigitalForm',
                          params: { pageIndex: 1 }
                        });

                        // Handle non-critical updates in the background
                        Promise.all([
                          dispatch(notificationActions.getUnreadCount()),
                          dispatch(
                            ProjectActions.initialize(parseInt(user?.id ?? '0'), item.remoteId?.toString() ?? '')
                          )
                        ]).catch(error => {});
                      }, 400)
                    }
                  >
                    <VStack space={1}>
                      {/* <FastImage
                        style={styles.carousel}
                        source={{
                          uri: item.uri,
                        }}
                      /> */}
                      <SafeImage initialUri={item.uri} key={`${index}-${item.uri}`} style={styles.carousel} />
                      <Flex flexDirection="row" alignItems={'center'} justifyContent={'space-between'}>
                        <Text style={styles.projectName} numberOfLines={2}>
                          {item.title}
                        </Text>
                        {!OFFLINE_MODE && (
                          <TouchableOpacity
                            onPress={async () => {
                              projectsModalRef?.current?.pushModal();
                              setModalMode('edit');
                              setProjectId(item.id);
                              setSelectedProject(item);
                            }}
                            style={styles.threeDotsContainer}
                          >
                            <Icon name="three-dots" />
                          </TouchableOpacity>
                        )}
                      </Flex>
                      <Flex flexDirection="row" alignItems="center">
                        <Icon name="assignees" />
                        <Text>{item.users}</Text>
                        <Text color={COLORS.neutrals.gray90} style={{ marginLeft: 8 }}>
                          Last updated: <Text>{lastUpdated}</Text>
                        </Text>
                      </Flex>
                    </VStack>
                  </TouchableOpacity>
                </Box>
              );
            }}
          />
          <ProjectsModal
            ref={projectsModalRef}
            category={Gql.CategoryType?.AllForm}
            refetch={() => {
              // refetch();
            }}
            type={modalMode}
            project={selectedProject}
          />
        </>
      )}

      {isCompanyOwner && isConnected ? (
        <TouchableOpacity
          style={[styles.addButton, { zIndex: 1000 }]}
          onPress={() => {
            if (isGracePeriod || !isSubscriptionActive) return;
            projectsModalRef?.current?.pushModal();
            setModalMode('add');
          }}
          disabled={isLoading}
        >
          <Circle size="65px" bg="#0695D7">
            <Icon name="plus" />
          </Circle>
        </TouchableOpacity>
      ) : null}

      <SettingsModal user={user} />
      <CompanyAppBarModal ref={companyAppBarModalRef} user={user} />
      <ChangeLogModal
        data={changeLogData}
        onDismiss={async () => {
          await updateUser({
            variables: {
              input: {
                isReadChangeLogMobile: true
              }
            }
          });
        }}
        ref={modalRef}
      />
    </Box>
  );
};

const SkeletonComponent: React.FC<any> = () => {
  const SkeletonItem = () => {
    return (
      <VStack mb={5}>
        <Skeleton style={{ height: 190, padding: 14, borderRadius: 12 }} />
        <VStack space={1}></VStack>
      </VStack>
    );
  };
  return (
    <Stack p={5}>
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
    </Stack>
  );
};

const styles = StyleSheet.create({
  titleText: {
    fontWeight: '600',
    fontSize: 16
  },
  box: {
    backgroundColor: COLORS.neutrals.white,
    padding: 14,
    borderRadius: 12,
    marginTop: 10,
    marginLeft: 12
  },
  allProjectBox: {
    backgroundColor: COLORS.neutrals.white,
    borderRadius: 12,
    marginBottom: 26,
    flex: 1 / 2,
    marginHorizontal: 10
  },
  projectName: {
    fontWeight: '600',
    fontSize: 16,
    color: COLORS.neutrals.black,
    width: '92%',
    marginRight: 10
  },
  center: {
    height: 180,
    // width: 360,
    backgroundColor: COLORS.neutrals.white,
    borderRadius: 16,
    marginTop: 4
  },
  threeDotsContainer: {
    paddingVertical: 10
  },
  carousel: {
    height: 158,
    width: '100%',
    borderRadius: 8
  },
  addButton: {
    position: 'absolute',
    bottom: 50,
    alignSelf: 'flex-end',
    right: '7%',
    shadowColor: 'rgba(0, 0, 0, 0.5)',
    shadowOpacity: 1,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 6 },
    backgroundColor: '#FFFFFF',
    borderRadius: 9999
  }
});

export default AllProjects;
