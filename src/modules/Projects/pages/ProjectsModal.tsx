import { Content, Icon, Modal } from '@commons';
import { Gql } from '@src/api';
import UploadFileModal from '@src/commons/UploadFileModal';
import { Button, Center, Flex, HStack, Text, VStack } from 'native-base';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps, Platform, StyleSheet, TouchableOpacity } from 'react-native';
import { Field, Formik, FormikProps } from 'formik';
import { TextInput } from '@src/inputs';
import { COLORS } from '@src/constants';
import { generateRNFile, getProjectId, pushError, pushMessaage, pushSuccess } from '@src/configs';
import _ from 'lodash';
import ImagePicker from 'react-native-image-crop-picker';
import useCreateProject from '@src/mutation/project/useCreateProject';
import useUpdateProject from '@src/mutation/project/useUpdateProject';

interface Props {
  category: string;
  refetch: () => void;
  type: string;
  projectTitle?: string;
  project: any;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const ProjectsModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const uploadFileModalRef = useRef<any>(null);
  const formRef = useRef<FormikProps<FormValues>>(null);
  const [carousel, setCarousel] = useState<any>([]);
  const initialValues = { projectName: '', carousel: [] };
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const { mutateAsync: createProject } = useCreateProject();
  const { mutateAsync: updateProject } = useUpdateProject();

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  useEffect(() => {
    if (props.type === 'edit') {
      formRef.current?.setFieldValue('projectName', props.project?.title);
    }
  }, [props.project]);

  // //? Create new project
  // const [createProject] = Gql.useCreateNewProjectMutation({
  //   onCompleted: project => {
  //     if (carousel?.length > 0) {
  //       createOneProjectCarousel({
  //         variables: {
  //           input: {
  //             fileUrl: carousel[0] as any,
  //             projectId: +project.createNewProject.id
  //           }
  //         }
  //       });
  //     }
  //   }
  // });

  //? Create project cover
  const [createOneProjectCarousel] = Gql.useCreateOneProjectCarouselMutation({
    onCompleted: () => {
      setCarousel([]);
      props.refetch();
    },
    onError: (err: any) => {}
  });

  //? Update project
  // const [updateProject] = Gql.useUpdateProjectMutation({
  //   onCompleted: async () => {
  //     const projectId = (await getProjectId()) ?? '';
  //     if (carousel && carousel.length > 0) {
  //       if (!props.project.uri) {
  //         return createOneProjectCarousel({
  //           variables: {
  //             input: {
  //               fileUrl: carousel[0],
  //               projectId: +projectId
  //             }
  //           }
  //         });
  //       }
  //       const updateCoverProjectVariables = {
  //         variables: {
  //           input: {
  //             id: props.project.uriId,
  //             update: {
  //               fileUrl: carousel[0],
  //               projectId: +projectId
  //             }
  //           }
  //         }
  //       };
  //       return updateCoverProject(updateCoverProjectVariables);
  //     }
  //     props.refetch();
  //   },
  //   onError: (err: any) => {
  //     return pushError(err);
  //   }
  // });

  //? Update project cover
  const [updateCoverProject] = Gql.useUpdateOneProjectCarouselMutation({
    onCompleted: () => {
      props.refetch();
      setCarousel([]);
    },
    onError: (err: any) => {}
  });

  const onSubmit = async (values: FormValues) => {
    const title = values.projectName;
    setIsSubmitting(true);
    try {
      if (props.type === 'add') {
        if (!title) {
          formRef.current?.setFieldError('projectName', 'Please insert project Name');
        }

        if (carousel.length < 1) {
          setIsSubmitting(false);
          return pushMessaage('Select project cover', 'error', 'Group name is required!');
        }

        // await createProject({
        //   variables: {
        //     input: { title }
        //   }
        // });

        await createProject({
          title,
          carousel: carousel[0]?.uri as string
        });

        pushSuccess('Project created successfully');
      } else {
        await updateProject({
          projectId: props.project.id,
          title,
          carousel: carousel[0]?.uri as string
        });

        pushSuccess('Project updated successfully');
      }
      modalRef.current.closeModal();
      props.refetch();
    } catch (e) {
      pushError(e);
    } finally {
      setIsSubmitting(false);
      // setCarousel([]);
    }
  };

  const chooseImage = async () => {
    try {
      const res = await ImagePicker.openPicker({
        width: 500,
        height: 500,
        cropping: true,
        forceJpg: true,
        includeBase64: true
      });
      const newFile = generateRNFile({
        uri: res.path,
        name:
          Platform.OS === 'ios'
            ? (res.filename as string)
            : res.path.replace('_cloud.bina.android', '').substring(res.path.lastIndexOf('/') + 1)
      });
      setCarousel([newFile]);
    } catch (err) {}
  };

  return (
    <Modal
      ref={modalRef}
      type="middle"
      onClose={() => {
        setCarousel([]);
      }}
    >
      <Center height={'372'} marginTop={10}>
        <Text style={styles.title}>{props.type === 'add' ? 'New' : 'Edit'} Project</Text>
        <Formik onSubmit={onSubmit} initialValues={initialValues} innerRef={formRef} validate={validate}>
          {() => {
            return (
              <Content pt={5} width={'full'} px={2}>
                <Field autoFocus name="projectName" label="Project name" component={TextInput} />

                <Text fontWeight={400} color={COLORS.neutrals.gray90}>
                  Upload Project Cover Photo
                </Text>
                <TouchableOpacity onPress={() => chooseImage()} style={styles.uploadContainer}>
                  <Center>
                    {carousel.length > 0 ? (
                      carousel.map((item: any, index: number) => (
                        <Flex key={index} direction="row" alignItems={'center'} justifyContent="flex-start">
                          <Icon name="filetype-image" />
                          <Text ml={3} maxW={'80%'}>
                            {item.name}
                          </Text>
                        </Flex>
                      ))
                    ) : (
                      <Icon name="upload-file" />
                    )}
                  </Center>
                </TouchableOpacity>

                <Flex justifyContent={'flex-end'} direction="row" mt={7}>
                  <Button variant={'ghost'} mx={2} onPress={() => modalRef.current.closeModal()}>
                    <Text style={{ color: COLORS.neutrals.black }}>Cancel</Text>
                  </Button>
                  <Button
                    variant={'ghost'}
                    isLoading={isSubmitting}
                    onPress={() => {
                      formRef.current?.handleSubmit();
                    }}
                  >
                    {props.type === 'add' ? 'Create' : 'Update'}
                  </Button>
                </Flex>
              </Content>
            );
          }}
        </Formik>
      </Center>
      <UploadFileModal
        ref={uploadFileModalRef}
        category={Gql.CategoryType.AllForm}
        onSaved={() => {
          props?.refetch?.();
        }}
      />
    </Modal>
  );
});

const validate = (values: FormValues) => {
  const errors: any = {};
  if (!values.projectName) {
    errors.projectName = 'Required';
  }

  if (values?.projectName?.length > 255) {
    errors.projectName = 'Project name must be less than 255 characters';
  }

  return errors;
};

const styles = StyleSheet.create({
  title: {
    fontSize: 16
  },
  uploadContainer: {
    paddingVertical: 25,
    display: 'flex',
    justifyContent: 'center',
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: COLORS.neutrals.gray50,
    marginTop: 10
  },
  errorMsg: {
    color: 'red',
    fontSize: 12
  }
});

interface FormValues {
  carousel: any;
  projectName: any;
}

ProjectsModal.displayName = 'ProjectsModal';
export default ProjectsModal;
