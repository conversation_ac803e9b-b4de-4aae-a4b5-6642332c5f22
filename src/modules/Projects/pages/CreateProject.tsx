import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { AppBar, Content, Footer } from '@src/commons';
import { pushError, pushSuccess } from '@src/configs';
import { COLORS } from '@src/constants';
import { TextInput } from '@src/inputs';
import apolloClient from '@src/lib/apollo';
import { ProjectNavigatorParams, RootNavigatorParams } from '@src/types';
import { Field, Formik, FormikProps } from 'formik';
import _ from 'lodash';
import { Box, Button } from 'native-base';
import React, { useRef, useState } from 'react';

type Props = CompositeScreenProps<
  BottomTabScreenProps<ProjectNavigatorParams, 'CreateProject'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const CreateProject: React.FC<Props> = ({ navigation, route }) => {
  const formRef = useRef<FormikProps<FormValues>>(null);
  const initialValues = { name: '' };
  const refetch = _.get(route, 'params.refetch', null);
  const refetchAllProject = _.get(route, 'params.allProjectRefetch', null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const onSubmit = async (values: FormValues) => {
    const title = values.name;
    setIsSubmitting(true);
    try {
      await apolloClient.mutate<Gql.CreateNewProjectMutation>({
        mutation: Gql.CreateNewProjectDocument,
        variables: {
          input: {
            title
          }
        }
      });
      pushSuccess('Project created successfully');
      navigation.goBack();
      refetch();
      refetchAllProject();
    } catch (e) {
      pushError(e);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box flex={1} bg={COLORS.neutrals.white}>
      <AppBar goBack barStyle={'dark-content'} title="Create Project" noRight />
      <Formik onSubmit={onSubmit} initialValues={initialValues} innerRef={formRef}>
        {() => {
          return (
            <Box flex={1} bg="#FFFFFF">
              <Content pt={5}>
                <Field autoFocus name="name" label="Project name" component={TextInput} />
              </Content>
              <Footer>
                <Button
                  variant="primary"
                  isLoading={isSubmitting}
                  onPress={() => {
                    formRef.current?.handleSubmit();
                  }}
                >
                  Continue
                </Button>
              </Footer>
            </Box>
          );
        }}
      </Formik>
    </Box>
  );
};

interface FormValues {
  name: string;
}

export default CreateProject;
