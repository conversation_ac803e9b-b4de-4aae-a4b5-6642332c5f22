import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { ProjectNavigatorParams } from '@types';
import AllProjects from './pages/AllProjects';
import CreateProject from './pages/CreateProject';

const ProjectStack = createNativeStackNavigator<ProjectNavigatorParams>();

const ProjectNavigator: React.FC<any> = () => {
  return (
    <ProjectStack.Navigator initialRouteName="AllProjects" screenOptions={{ headerShown: false }}>
      <ProjectStack.Screen name="AllProjects" component={AllProjects} />
      <ProjectStack.Screen name="CreateProject" component={CreateProject} />
    </ProjectStack.Navigator>
  );
};

export default ProjectNavigator;
