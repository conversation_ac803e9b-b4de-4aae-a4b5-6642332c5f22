import { Modal } from '@commons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { COLORS } from '@src/constants';
import CompanyAppBarModal from '@src/modules/Account/components/CompanyAppBarModal';
import { AuthActions } from '@src/slice/auth.slice';
import { projectActions } from '@src/slice/project.slice';
import { useDispatch, useSelector } from '@src/store';
import { RootNavigatorParams } from '@src/types';
import { Button, Divider, Text } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { ModalProps, StyleSheet } from 'react-native';

interface Props {
  user?: any;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const SettingsModal = forwardRef<ModalRef, Props>((props, ref) => {
  const dispatch = useDispatch();
  const project = useSelector(state => state.project);
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const companyModalRef = useRef<any>(null);

  return (
    <Modal isVisible={project.isSettingModalOpen} onClose={() => dispatch(projectActions.setSettingModal(false))}>
      <Button
        _pressed={{
          bg: 'transparent',
          opacity: 0.8
        }}
        my={1}
        bg="transparent"
        onPress={() => {
          dispatch(projectActions.setSettingModal(false));
          navigation.navigate('AccountNav', { screen: 'Settings' });
        }}
      >
        <Text>Settings</Text>
      </Button>

      {/* <Divider my="2" color="#E8E8E8" /> */}

      <Button
        _pressed={{
          bg: 'transparent',
          opacity: 0.8
        }}
        my={1}
        bg="transparent"
        onPress={() => {
          companyModalRef?.current?.pushModal();
        }}
      >
        <Text>Company</Text>
      </Button>

      {/* <Divider my="2" color="#E8E8E8" /> */}

      <Button
        _pressed={{
          bg: 'transparent',
          opacity: 0.8
        }}
        my={1}
        bg="transparent"
        onPress={() => {
          dispatch(projectActions.setSettingModal(false));
          setTimeout(() => {
            dispatch(AuthActions.logout());
          }, 400);
        }}
      >
        <Text>Log Out</Text>
      </Button>
      <CompanyAppBarModal ref={companyModalRef} user={props.user} />
    </Modal>
  );
});

export default SettingsModal;
