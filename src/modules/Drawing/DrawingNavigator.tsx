import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { DrawingNavigatorParams } from '@src/types';
import React from 'react';
import Drawing from './pages/Drawing';
import DrawingDetails from './pages/DrawingDetails';
import DrawingFolder from './pages/DrawingFolder';
import DrawingPdfTron from './pages/DrawingPdfTron_dep';
import DrawingSubFolder from './pages/DrawingSubFolder';
// import DrawingWebView from './pages/DrawingWebView';
import DrawingMovingFile from './pages/DrawingMovingFile';
import DrawingViewRevision from '../DigitalForm/pages/DrawingViewRevision';
import DrawingWebView from './pages/DrawingWebView';

const DrawingStack = createNativeStackNavigator<DrawingNavigatorParams>();

const DrawingNavigator: React.FC<any> = () => {
  return (
    <DrawingStack.Navigator initialRouteName="Drawings" screenOptions={{ headerShown: false }}>
      <DrawingStack.Screen name="Drawings" component={Drawing} />
      <DrawingStack.Screen name="DrawingDetails" component={DrawingDetails} />
      <DrawingStack.Screen name="DrawingFolder" component={DrawingFolder} />
      <DrawingStack.Screen name="DrawingSubFolder" component={DrawingSubFolder} />
      <DrawingStack.Screen name="DrawingPdfTron" component={DrawingPdfTron} />
      <DrawingStack.Screen name="DrawingWebView" component={DrawingWebView} />
      <DrawingStack.Screen name="DrawingMovingFile" component={DrawingMovingFile} />
      <DrawingStack.Screen name="DrawingRevision" component={DrawingViewRevision} />
    </DrawingStack.Navigator>
  );
};

export default DrawingNavigator;
