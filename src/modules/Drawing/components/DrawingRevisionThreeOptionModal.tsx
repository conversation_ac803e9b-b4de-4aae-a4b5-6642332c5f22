import { Modal } from '@commons';
import { pushSuccess, pushError } from '@src/configs';
import { task } from '@src/lib/authority';
import { Button, Center, Text, VStack } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { Alert, ModalProps } from 'react-native';
import DrawingRenameModal from './DrawingRenameModal';
import useDeleteDrawingRevision from '@src/mutation/drawing-revision/useDeleteDrawingRevision';
import useUpdateDrawingRevision from '@src/mutation/drawing-revision/useUpdateDrawingRevision';
interface Props {
  onDelete?: (value: string) => void;
  refetch?: () => void;
  data?: any;
  obj?: any;
  onChange?: (values: string) => void;
  role?: string;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const DrawingRevisionThreeOptionModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const renameDrawingModalRef = useRef<any>(null);
  const deleteDrawingRevisionMutation = useDeleteDrawingRevision();
  const updateDrawingRevisionMutation = useUpdateDrawingRevision();
  // const [revisionData, setRevisionData] = useState<any>([]);

  const onDrawingDelete = async () => {
    try {
      await deleteDrawingRevisionMutation.mutateAsync(props?.data ?? '');
      pushSuccess('Delete drawing successfully');
      props.refetch?.();
    } catch (error: any) {
      pushError(error.message || 'Failed to delete drawing');
    }
  };

  const onUpdate = async (value: any) => {
    if (!value || value === '') {
      pushError('Name is required!');
      modalRef.current.closeModal();
      return;
    }

    try {
      await updateDrawingRevisionMutation.mutateAsync({
        id: props?.data ?? '',
        newDrawingRevision: {
          fileName: value
        } as any
      });
      pushSuccess('Update filename successfully');
      props.refetch?.();
      modalRef?.current?.closeModal();
    } catch (error: any) {
      pushError(error.message || 'Failed to update filename');
    }
  };

  const drawings = task(props.role ?? '');
  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="bottom">
      <VStack>
        <Center>
          {props?.data?.fileType !== 'folder' && (
            <Button
              _pressed={{
                bg: 'transparent',
                opacity: 0.8
              }}
              mt={2}
              mb={4}
              bg="transparent"
              onPress={() => {
                renameDrawingModalRef?.current?.pushModal();
              }}
            >
              <Text>View Details </Text>
            </Button>
          )}

          {/* {props?.data?.fileType !== 'folder' && (
            <Button
              _pressed={{
                bg: 'transparent',
                opacity: 0.8
              }}
              mt={2}
              mb={4}
              bg="transparent"
              onPress={() => {
                if (Platform.OS === 'ios') {
                  Alert.prompt('Drawing Name', 'Please enter drawing name', [
                    {
                      text: 'Cancel',
                      onPress: () => {},
                      style: 'cancel'
                    },
                    {
                      text: 'OK',
                      onPress: (value: any) => {
                        if (!value.endsWith('.pdf')) {
                          value = value + '.pdf';
                        }
                        onUpdate(value);
                      }
                    }
                  ]);
                } else if (Platform.OS === 'android') {
                  prompt('Drawing Name', 'Please enter drawing name', [
                    {
                      text: 'Cancel',
                      onPress: () => {},
                      style: 'cancel'
                    },
                    {
                      text: 'OK',
                      onPress: (value: any) => {
                        if (!value.endsWith('.pdf')) {
                          value = value + '.pdf';
                        }
                        onUpdate(value);
                      }
                    }
                  ]);
                }
              }}
            >
              <Text>Rename</Text>
            </Button>
          )} */}
          {/* {props?.data?.fileType !== 'folder' && (
            <Button
              _pressed={{
                bg: 'transparent',
                opacity: 0.8
              }}
              mt={2}
              mb={4}
              bg="transparent"
              onPress={() => {
                }}
            >
              <Text>View Details</Text>
            </Button>
          )} */}

          {drawings?.overview?.canDelete && (
            <Button
              _pressed={{
                bg: 'transparent',
                opacity: 0.8
              }}
              mt={2}
              mb={4}
              bg="transparent"
              onPress={() => {
                Alert.alert(
                  'Delete',
                  'Are you sure you want to delete this file?',
                  [
                    {
                      text: 'Cancel',
                      onPress: () => {},
                      style: 'cancel'
                    },
                    {
                      text: 'OK',
                      onPress: () => {
                        onDrawingDelete();
                      }
                    }
                  ],
                  { cancelable: false }
                );
              }}
            >
              <Text color="semantics.danger">Delete </Text>
            </Button>
          )}
        </Center>
      </VStack>
      <DrawingRenameModal
        ref={renameDrawingModalRef}
        data={props.obj}
        refetch={() => props?.refetch?.()}
        modalRef={modalRef}
      />
      {/* <ViewRevisionModal ref={viewRevisionModalRef} data={props?.data} refetch={() => props?.refetch?.()} /> */}
    </Modal>
  );
});

DrawingRevisionThreeOptionModal.displayName = 'DrawingRevisionThreeOptionModal';
export default DrawingRevisionThreeOptionModal;
