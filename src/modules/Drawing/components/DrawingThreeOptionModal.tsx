import { Modal } from '@commons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { getDeleteConfirmationMessage, pushError, pushSuccess } from '@src/configs';
import { task } from '@src/lib/authority';
import TemplateRenameModal from '@src/modules/DigitalForm/components/Template/TemplateRenameModal';
import ViewRevisionModal from '@src/modules/DigitalForm/components/Template/ViewRevisionModal_dep';
import { useSelector } from '@src/store';
import { RootNavigatorParams } from '@src/types';
import { Button, Center, Text, VStack } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { Alert, ModalProps, Platform } from 'react-native';
import DocumentPicker from 'react-native-document-picker';
import DrawingRenameModal from './DrawingRenameModal';
import { Gql } from '@src/api';
import useDeleteDrawing from '@src/mutation/drawing/useDeleteDrawing';
import { getLinkId } from '@src/database/utils/numeric';
import RNFS from 'react-native-fs';

interface Props {
  onDelete?: (value: string) => void;
  onChange?: (value: string) => void;
  refetch?: () => void;
  data?: any;
  role?: string;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const DrawingThreeOptionModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const renameTemplateModalRef = useRef<any>(null);
  const viewRevisionModalRef = useRef<any>(null);
  const drawingAddModal = useRef<any>(null);
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const drawings = task(props.role ?? '');
  const { mutateAsync: deleteDocument, isPaused } = useDeleteDrawing();

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const { OFFLINE_MODE } = useSelector(state => state.app);

  const [newFile, setNewFile] = useState<any>(null);

  const chooseDocument = async () => {
    try {
      // Reset newFile state before picking a new document
      setNewFile(null);

      const element = await DocumentPicker.pickSingle({
        type: [DocumentPicker.types.pdf]
      });

      try {
        const realURI: any = Platform.select({
          android: element.uri,
          ios: decodeURI(element.uri)
        });
        // Split the filename to handle extension
        let [baseName, extension] = element.name
          ? element?.name.split(/(?=\.\w+$)/).map(part => part.replace(/\s+/g, '_'))
          : ['', ''];
        let fileName = baseName;
        let fileCounter = 0;

        // Prepare the initial path for the file
        let destPath = `${RNFS.DocumentDirectoryPath}/${fileName}${extension}`;

        // Check if the file exists and append a counter before the extension
        while (await RNFS.exists(destPath)) {
          fileCounter++;
          destPath = `${RNFS.DocumentDirectoryPath}/${fileName}_${fileCounter}${extension}`;
        }

        // Copy the file to the new destination
        await RNFS.copyFile(realURI, destPath);
        const newFileData = {
          uri: destPath,
          name: fileCounter > 0 ? `${fileName}_${fileCounter}${extension}` : `${fileName}${extension}`,
          type: element.type
        };
        setNewFile(newFileData);
        drawingAddModal?.current?.pushModal();
      } catch (e) {
        pushError(e);
      }
    } catch (err) {
      throw err;
    }
  };

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="bottom">
      <VStack>
        <Center>
          {drawings?.overview?.canRename && props?.data?.fileSystemType === Gql.FileSystemType.Folder && (
            <Button
              _pressed={{
                bg: 'transparent',
                opacity: 0.8
              }}
              mt={2}
              mb={4}
              bg="transparent"
              onPress={() => {
                renameTemplateModalRef?.current?.pushModal();
              }}
            >
              <Text>Rename </Text>
            </Button>
          )}

          {props?.data?.fileSystemType === Gql.FileSystemType.Document && props?.data?.remoteId !== 0 ? (
            <Button
              isLoading={false}
              _pressed={{
                bg: 'transparent',
                opacity: 0.8
              }}
              mt={2}
              mb={4}
              bg="transparent"
              onPress={() => {
                chooseDocument();
              }}
            >
              <Text>Add Revision </Text>
            </Button>
          ) : null}

          {props?.data?.fileSystemType !== Gql.FileSystemType.Folder && (
            <Button
              _pressed={{
                bg: 'transparent',
                opacity: 0.8
              }}
              mt={2}
              mb={4}
              bg="transparent"
              onPress={() => {
                modalRef.current.closeModal();
                navigation.navigate('DrawingNav', {
                  screen: 'DrawingRevision',
                  params: {
                    data: props.data,
                    id: props.data?.id ?? ''
                  }
                });
              }}
            >
              <Text>View Revision </Text>
            </Button>
          )}

          {drawings?.overview?.canDelete || OFFLINE_MODE ? (
            <Button
              _pressed={{
                bg: 'transparent',
                opacity: 0.8
              }}
              mt={2}
              mb={4}
              bg="transparent"
              onPress={() => {
                Alert.alert(
                  'Delete',
                  getDeleteConfirmationMessage(OFFLINE_MODE),
                  [
                    {
                      text: 'Cancel',
                      onPress: () => {},
                      style: 'cancel'
                    },
                    {
                      text: 'OK',
                      onPress: async () => {
                        await deleteDocument(props.data?.id);
                        pushSuccess('Delete document successfully');
                        modalRef.current?.closeModal();
                      }
                    }
                  ],
                  { cancelable: false }
                );
              }}
            >
              <Text color="semantics.danger">Delete </Text>
            </Button>
          ) : null}
        </Center>
      </VStack>
      <TemplateRenameModal ref={renameTemplateModalRef} data={props?.data} refetch={() => props?.refetch?.()} />
      <ViewRevisionModal ref={viewRevisionModalRef} data={props?.data} refetch={() => props?.refetch?.()} />
      <DrawingRenameModal
        ref={drawingAddModal}
        newFile={newFile}
        data={props?.data}
        refetch={() => {
          props?.refetch?.();
          setNewFile(null); // Reset newFile after successful operation
        }}
        file={props?.data}
      />
    </Modal>
  );
});

DrawingThreeOptionModal.displayName = 'DrawingThreeOptionModal';
export default DrawingThreeOptionModal;
