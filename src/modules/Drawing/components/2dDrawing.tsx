import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import useDebouncedSearch from '@src/hooks/useDebounceSearch';
import { task } from '@src/lib/authority';
import { drawingsActions } from '@src/slice/drawings.slice';
import { useDispatch, useSelector } from '@src/store';
import { RootNavigatorParams } from '@src/types';
import { Box, Divider, Skeleton, Stack, VStack } from 'native-base';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import DrawingAddModal from './DrawingAddModal';
import DrawingThreeOptionModal from './DrawingThreeOptionModal';
import AddButton from '@src/commons/AddButton';
import SearchInput from '@src/commons/SearchInput';
import DocumentList from '@src/commons/list/DocumentList';
import { useGetDrawings } from '@src/queries/drawing/useGetDrawing';
import { handleDocumentNavigation } from '@src/utils/documentNavigation';
import ProjectDocumentModel from '@src/database/model/project-document';

type NavigationType = NativeStackNavigationProp<RootNavigatorParams>;

const TwoDDrawing: React.FC<any> = () => {
  const navigation = useNavigation<NavigationType>();
  const project = {
    ...useSelector(state => state.project),
    companyName: useSelector(state => state.auth).company?.name
  };
  const drawingAddModalRef = useRef<any>(null);
  const drawingThreeOptionModal = useRef<any>(null);
  const [filteredValue, setFilteredValue] = useState<string | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<any>({});
  const role = project?.projectUserRole;
  const drawings = task(role ?? '');
  const dispatch = useDispatch();

  const { debouncedValue, setInputValue } = useDebouncedSearch();

  const onSearchInputChange = (newSearchTerm: any) => {
    dispatch(drawingsActions.setFilterDrawingRevision(newSearchTerm));
    setFilteredValue(newSearchTerm);
    setInputValue(newSearchTerm);
  };

  const {
    data: getProjectDocuments,
    isLoading,
    fetchNextPage,
    hasNextPage
  } = useGetDrawings(debouncedValue ?? '', {
    projectId: project.projectId,
    limit: 10,
    category: Gql.CategoryType.TwoDDrawings,
    projectDocumentId: null as any,
    pageIndex: 1
  });

  const projectDocuments = useMemo(() => {
    if (!getProjectDocuments) return [];
    return getProjectDocuments.pages.map((page: { results: any[] }) => page.results).flat();
  }, [getProjectDocuments]);

  const loadMoreDocuments = useCallback(() => {
    if (hasNextPage && !isLoading) {
      fetchNextPage();
    }
  }, [hasNextPage, isLoading, fetchNextPage]);

  const onOptionPress = (item: ProjectDocumentModel) => {
    drawingThreeOptionModal?.current?.pushModal();
    setSelectedDocument({ document: item });
  };

  return (
    <Box flex={1} bg="white">
      <Divider />
      {/* Add Button */}
      <AddButton isAllowed={drawings.overview.canCreateRoot} onPress={() => drawingAddModalRef?.current?.pushModal()} />

      {/* Search Input */}
      <SearchInput
        filteredValue={filteredValue}
        setFilteredValue={onSearchInputChange}
        placeholder="Search for drawings"
      />
      {/* List */}
      {isLoading ? (
        <>
          <SkeletonComponent />
        </>
      ) : (
        <DocumentList
          documents={projectDocuments}
          isLoading={isLoading}
          loadMoreDocuments={loadMoreDocuments}
          refetch={() => {}}
          onItemPress={async item => {
            await handleDocumentNavigation({
              item,
              role,
              navigation,
              modules: 'Drawings',
              onRefetch: () => {}
            });
          }}
          onOptionPress={onOptionPress}
          onAction={false}
          setOnAction={() => {}}
        />
      )}

      <DrawingThreeOptionModal
        ref={drawingThreeOptionModal}
        onChange={() => {}}
        data={selectedDocument.document}
        refetch={() => {
          setSelectedDocument({ document: null });
        }}
        role={role ?? ''}
      />
      <DrawingAddModal ref={drawingAddModalRef} category={Gql.CategoryType.TwoDDrawings} />
    </Box>
  );
};

const SkeletonComponent: React.FC<any> = () => {
  const SkeletonItem = () => {
    return (
      <VStack mb={5}>
        <Skeleton style={{ height: 70, padding: 14, borderRadius: 12 }} />
        <VStack space={1}></VStack>
      </VStack>
    );
  };
  return (
    <Stack p={5}>
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
    </Stack>
  );
};

export default TwoDDrawing;
