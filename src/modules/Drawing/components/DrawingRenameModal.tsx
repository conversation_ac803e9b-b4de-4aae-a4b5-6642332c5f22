import { Modal } from '@commons';
import { pushError, pushSuccess } from '@src/configs';
import { Box, Button, Input, Spinner, Text, VStack } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState, useEffect } from 'react';
import { ModalProps } from 'react-native';
import _, { debounce } from 'lodash';
import useCreateDrawingRevision from '@src/mutation/drawing-revision/useCreateDrawingRevision';
import DrawingRevisionModel from '@src/database/model/drawing-revision.model';
import useUpdateDrawingRevision from '@src/mutation/drawing-revision/useUpdateDrawingRevision';

interface Props {
  refetch: () => void;
  data?: any;
  newFile?: any;
  file: any;
  modalRef?: any;
}

interface DrawingData {
  id: string;
  name: string;
  versionName: string;
  description: string;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const DrawingRenameModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const [values, setValues] = useState<DrawingData>(
    props?.data && typeof props.data === 'object' && props.data.fileName
      ? {
          name: props.data.fileName ?? '',
          id: props.data.id ?? '',
          versionName: props.data.versionName ?? '',
          description: props.data.notes ?? ''
        }
      : props?.data?.[1] || props?.file
        ? {
            name: props?.data[1] ?? props?.file?.name ?? '',
            id: props?.data[3] ?? props?.file?.id ?? '',
            versionName: props?.data[4] ?? '',
            description: props?.data[5] ?? ''
          }
        : {
            name: props?.newFile?.name ?? '',
            id: '',
            versionName: '',
            description: ''
          }
  );

  const isEdit =
    (props?.data && typeof props.data === 'object' && props.data.fileName) || props?.data?.[1] ? true : false;

  // Update values when newFile changes
  useEffect(() => {
    if (props?.newFile?.name && !isEdit) {
      setValues(prevValues => ({
        ...prevValues,
        name: props.newFile.name
      }));
    }
  }, [props?.newFile?.name, isEdit]);

  const { mutateAsync: createDrawingRevision } = useCreateDrawingRevision();
  const { mutateAsync: updateDetails, isPending: loading } = useUpdateDrawingRevision();

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  // Debounce the onChangeText handlers
  const debouncedSetName = debounce((name: string) => {
    setValues(prevValues => ({ ...prevValues, name }));
  }, 300);

  const debouncedSetVersionName = debounce((versionName: string) => {
    setValues(prevValues => ({ ...prevValues, versionName }));
  }, 300);

  const debouncedSetDescription = debounce((description: string) => {
    setValues(prevValues => ({ ...prevValues, description }));
  }, 300);

  const onUpdate = async (data: DrawingData) => {
    if (!data || (data.name === '' && isEdit)) {
      pushError('Name is required!');
      modalRef.current.closeModal();
    } else {
      if (isEdit) {
        const update = {
          fileName: data.name,
          versionName: data.versionName,
          notes: data.description
        };
        try {
          await updateDetails({ id: data.id, newDrawingRevision: update as DrawingRevisionModel });
          modalRef?.current?.closeModal();
          props?.modalRef?.current?.closeModal();
          pushSuccess('Update filename successfully');
        } catch (error: any) {
          pushError(error.message || 'Failed to update drawing details');
        }
      } else {
        // change the file name to the values
        if (values?.name?.trim?.() !== '') {
          props.newFile.name = values.name;
        }

        const newDrawingRevision = {
          projectDocumentId: Number(props?.data?.remoteId),
          fileUrl: props?.newFile?.uri,
          versionName: data.versionName || undefined,
          notes: data.description || undefined,
          remoteId: undefined,
          fileName: values.name,
          fileKey: '',
          version: undefined,
          xfdf: undefined,
          status: undefined,
          category: 'TwoDDrawings',
          id: props?.data?.id
        };

        await createDrawingRevision(newDrawingRevision as DrawingRevisionModel);

        pushSuccess('Drawing has been added successfully.');
        return modalRef?.current?.closeModal();
      }
    }
  };

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="middle" avoidKeyboard={true}>
      <Box p={6}>
        <Text textAlign="center" mb={4} fontSize={16}>
          {isEdit ? 'Edit Drawing Details' : 'Add Drawing Details'}
        </Text>

        <VStack>
          <Text>Name</Text>
          <Input
            onChangeText={name => {
              debouncedSetName(name);
            }}
            isRequired={true}
            defaultValue={values.name}
            multiline={true}
            numberOfLines={3}
          />
        </VStack>

        <VStack>
          <Text>Version Name</Text>
          <Input
            onChangeText={versionName => {
              debouncedSetVersionName(versionName);
            }}
            isRequired={true}
            defaultValue={values.versionName}
          />
        </VStack>

        <VStack>
          <Text>Description</Text>
          <Input
            onChangeText={description => {
              debouncedSetDescription(description);
            }}
            isRequired={true}
            defaultValue={values.description}
            multiline={true}
            numberOfLines={3}
          />
        </VStack>

        <Box flexDirection="row" justifyContent="flex-end" mt={5}>
          <Button
            bg="transparent"
            _pressed={{ bg: 'transparent' }}
            onPress={() => {
              modalRef.current.closeModal();
            }}
          >
            <Text color="#0695D7">Cancel</Text>
          </Button>
          {loading ? (
            <Spinner size="lg" />
          ) : (
            <Button
              isLoading={loading}
              _loading={{
                _text: {
                  color: 'black'
                }
              }}
              _spinner={{
                color: 'black'
              }}
              isLoadingText="Submitting"
              bg="transparent"
              _pressed={{ bg: 'transparent' }}
              onPress={() => {
                onUpdate(values);
              }}
            >
              <Text>{isEdit ? 'Update' : 'Add'}</Text>
            </Button>
          )}
        </Box>
      </Box>
    </Modal>
  );
});

DrawingRenameModal.displayName = 'DrawingRenameModal';
export default DrawingRenameModal;
