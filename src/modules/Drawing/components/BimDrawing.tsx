import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import DocumentList from '@src/commons/list/DocumentList';
import { Icon } from '@src/commons';
import useDebouncedSearch from '@src/hooks/useDebounceSearch';
import { useSelector } from '@src/store';
import { RootNavigatorParams } from '@src/types';
import { Box, Divider, Skeleton, Stack, VStack, Text, Center, Image } from 'native-base';
import React, { useCallback, useMemo, useState } from 'react';
import { useGetBimDrawing } from '@src/queries/bim-drawing/useGetBimDrawing';
import SearchInput from '@src/commons/SearchInput';
import { useQueryClient } from '@tanstack/react-query';
import { handleDocumentNavigation } from '@src/utils/documentNavigation';
import useNetworkStatus from '@src/hooks/useNetworkStatus';
import { COLORS } from '@src/constants';

type NavigationType = NativeStackNavigationProp<RootNavigatorParams>;

const BimDrawing: React.FC<any> = () => {
  const navigation = useNavigation<NavigationType>();
  const project = useSelector(state => state.project);
  const [filteredValue, setFilteredValue] = useState('');
  const role = project?.projectUserRole;
  const offlineMode = useSelector(state => state.app.OFFLINE_MODE);

  const { debouncedValue, setInputValue } = useDebouncedSearch();

  const onSearchInputChange = (value: string | null) => {
    const searchValue = value ?? '';
    setFilteredValue(searchValue);
    setInputValue(searchValue);
  };

  const {
    data: getProjectDocuments,
    isLoading,
    fetchNextPage,
    hasNextPage
  } = useGetBimDrawing((debouncedValue as string) || '', {
    projectId: project.projectId?.toString() || '',
    limit: 10,
    category: Gql.CategoryType.BimDrawings,
    projectDocumentId: null as any,
    pageIndex: 1
  });

  const queryClient = useQueryClient();

  const refresh = async () => {
    try {
      await queryClient.invalidateQueries({ queryKey: ['bimDrawing', project.projectId] });
    } catch (e) {}
  };

  const projectDocs = useMemo(() => {
    if (!getProjectDocuments) return [];
    return getProjectDocuments.pages.map((page: { results: any[] }) => page.results).flat();
  }, [getProjectDocuments]);

  const loadMoreDocuments = useCallback(() => {
    if (hasNextPage && !isLoading) {
      fetchNextPage();
    }
  }, [hasNextPage, isLoading, fetchNextPage]);

  if (offlineMode) {
    return <OfflineMessage />;
  }

  return (
    <Box flex={1} bg="white">
      <Divider />
      <SearchInput
        filteredValue={filteredValue}
        setFilteredValue={onSearchInputChange}
        placeholder="Search for models"
      />
      {isLoading ? (
        <SkeletonComponent />
      ) : (
        <DocumentList
          documents={projectDocs}
          isLoading={isLoading}
          loadMoreDocuments={loadMoreDocuments}
          refetch={refresh}
          onItemPress={async item => {
            await handleDocumentNavigation({
              item,
              role,
              navigation,
              isBim: true,
              modules: 'Drawings',
              onRefetch: refresh
            });
          }}
          onOptionPress={() => {}}
          onAction={false}
          setOnAction={() => {}}
        />
      )}
    </Box>
  );
};

const SkeletonComponent: React.FC<any> = () => {
  const SkeletonItem = () => {
    return (
      <VStack mb={5}>
        <Skeleton style={{ height: 70, padding: 14, borderRadius: 12 }} />
        <VStack space={1}></VStack>
      </VStack>
    );
  };
  return (
    <Stack p={5}>
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
    </Stack>
  );
};

const OfflineMessage = () => {
  return (
    <Center flex={1} px={6}>
      <Box bg="gray.50" p={6} rounded="lg" width="100%" shadow={1}>
        <Center mb={4}>
          {/* You can replace this with your actual offline icon */}
          <Icon name="offline" color="#6B7280" />
        </Center>
        <Text fontSize="xl" fontWeight="bold" textAlign="center" mb={2} color="gray.700">
          Internet Connection Required
        </Text>
        <Text textAlign="center" color="gray.600" mb={4}>
          BIM models require an active internet connection to access and view. Please connect to the internet to browse
          and interact with your project models.
        </Text>
        <Text textAlign="center" color={COLORS.primary[1]} fontSize="sm">
          Check your network connection and try again.
        </Text>
      </Box>
    </Center>
  );
};

export default BimDrawing;
