import { Icon, Modal } from '@commons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Gql } from '@src/api';
import UploadFileModal from '@src/commons/UploadFileModal';
import { RootNavigatorParams } from '@src/types';
import { Button, Center, HStack, Spinner, Text, VStack, Box } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { ActivityIndicator, ModalProps, StyleSheet, TouchableOpacity } from 'react-native';

interface Props {
  projectDocumentId?: string;
  localRemoteId?: string;
  category: Gql.CategoryType;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const DrawingAddModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const uploadFileModalRef = useRef<any>(null);
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const category = props?.category;
  const dirId = props?.projectDocumentId;
  const [loading, setLoading] = useState(false);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="bottom">
      <HStack justifyContent={'space-evenly'} px={8}>
        <Button
          width={'50%'}
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          my={6}
          bg="transparent"
          onPress={() => {
            navigation.navigate('DrawingNav', {
              screen: 'DrawingFolder',
              params: {
                projectDocumentId: dirId,
                category: category,
                localRemoteId: dirId
              }
            });
            modalRef?.current?.closeModal();
          }}
          justifyContent={'center'}
        >
          <Box
            alignItems={'center'}
            justifyContent={'center'}
            width={16}
            height={16}
            alignSelf={'center'}
            borderRadius={32}
            borderWidth={2}
            borderColor={'neutrals.gray6'}
          >
            <Icon name="folder-outline" />
          </Box>
          <Text color="#6F6D6D">Create Folder</Text>
        </Button>

        {dirId ? (
          loading ? (
            <ActivityIndicator size={'large'} />
          ) : (
            <Button
              width={'50%'}
              _pressed={{
                bg: 'transparent',
                opacity: 0.8
              }}
              my={6}
              bg="transparent"
              onPress={() => {
                uploadFileModalRef?.current?.pushModal();
                // modalRef?.current?.closeModal();
              }}
              justifyContent={'center'}
            >
              <Box
                alignItems={'center'}
                justifyContent={'center'}
                width={16}
                height={16}
                alignSelf={'center'}
                borderRadius={32}
                borderWidth={2}
                borderColor={'neutrals.gray6'}
              >
                <Icon name="add-file" />
              </Box>
              <Text color="#6F6D6D">Upload Files</Text>
            </Button>
          )
        ) : null}
      </HStack>

      <UploadFileModal
        ref={uploadFileModalRef}
        category={category}
        projectDocumentId={dirId}
        onSaved={() => {
          modalRef?.current?.closeModal();
        }}
        onLoading={loading => {
          setLoading(loading);
        }}
      />
    </Modal>
  );
});

const styles = StyleSheet.create({
  folder: {
    borderWidth: 2,
    borderColor: '#756D6D',
    borderRadius: 100,
    backgroundColor: 'transparent',
    height: 52,
    width: 52,
    justifyContent: 'center',
    alignItems: 'center'
  }
});

DrawingAddModal.displayName = 'DrawingAddModal';
export default DrawingAddModal;
