import { AppBar } from '@commons';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { IntegrationApiService } from '@src/api/rest';
import { pushError } from '@src/configs';
import { COLORS } from '@src/constants';
import { useSelector } from '@src/store';
import { DrawingNavigatorParams, RootNavigatorParams } from '@src/types';
import { Box, Center, Spinner, Text } from 'native-base';
import { env } from 'process';
import React, { useEffect, useState } from 'react';
import { InteractionManager, Platform, StyleSheet } from 'react-native';
import { WebView } from 'react-native-webview';

type Props = CompositeScreenProps<
  BottomTabScreenProps<DrawingNavigatorParams, 'DrawingWebView'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const DrawingWebView: React.FC<Props> = ({ navigation, route }) => {
  const selectedDocumentId = route?.params.id;
  const [token, setToken] = useState<string | null>(null);
  const [loadingWebView, setLoadingWebView] = useState<boolean>(true);
  const { OFFLINE_MODE } = useSelector(state => state.app);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      autodesk();
      getDocument();
    });
  }, [route]);

  const [getDocument, { data, loading }] = Gql.useProjectDocumentLazyQuery({
    variables: {
      id: selectedDocumentId
    },
    onError(error) {
      pushError(error);
    }
  });

  const metadata = data?.projectDocument?.autoDeskMetadata;
  const metadataVal = metadata && JSON?.parse(metadata);
  const urn = data?.projectDocument?.fileUrl;
  const urns = metadataVal?.urns?.map((item: any) => item.urnInBase64);

  const autodesk = async () => {
    try {
      const autodeskToken = await IntegrationApiService.autodeskAuth({});
      setToken(autodeskToken);
    } catch (e) {
      pushError(e);
    }
  };

  const url = `${process.env.PROD_WEB_URL}/drawings/BIM-mobile-view?projectId=71&urn=${
    urns?.length > 0 ? urns : urn
  }&token=${token}`;

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <AppBar goBack barStyle={'dark-content'} title={data?.projectDocument?.name ?? ''} noRight />

      {loadingWebView && (
        <Center flex={1}>
          <Spinner size="lg" />
        </Center>
      )}

      {token && (urn?.length > 0 || urns?.length > 0) && (
        <WebView
          originWhitelist={['*']}
          source={{ uri: url }}
          style={{ flex: 1 }}
          onLoadStart={() => setLoadingWebView(true)} // Start loading
          onLoadEnd={() => setLoadingWebView(false)} // End loading
          onError={syntheticEvent => {
            const { nativeEvent } = syntheticEvent;

            setLoadingWebView(false);
          }}
          javaScriptEnabled={true}
          injectedJavaScript={`window.localStorage.setItem('autodesk_token', '${token}');`}
        />
      )}
    </Box>
  );
};

export default DrawingWebView;
