import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
// import { ScrollableTabBar } from '@src/commons';
import CustomAppBar from '@src/commons/CustomAppBar';
import { COLORS } from '@src/constants';
import DrawingsComponent from '@src/modules/DigitalForm/components/MovingFile/DrawingsComponent';
import { DigitalFormNavigatorParams, DrawingNavigatorParams, RootNavigatorParams } from '@src/types';
import _ from 'lodash';
import { Box, Button, HStack, VStack } from 'native-base';
import React, { useRef } from 'react';
import { ScrollView, StyleSheet, TouchableOpacity } from 'react-native';
// import DocumentComponent from '../components/MovingFile/DocumentComponent';

type Props = CompositeScreenProps<
  BottomTabScreenProps<DrawingNavigatorParams, 'DrawingMovingFile'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const MovingFile: React.FC<Props> = ({ navigation, route }) => {
  const tabBarRef = useRef<any>(null);
  const pageIndex = Number(_.get(route, 'params.pageIndex', '0'));
  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <CustomAppBar header barStyle={'light-content'} />

      <ScrollView
        key={pageIndex}
        // renderTabBar={() => (
        //   <ScrollableTabBar
        //     activeTextColor="#0695D7"
        //     inactiveTextColor={COLORS.neutrals.gray70}
        //     containerStyle={styles.tabContainer}
        //     ref={tabBarRef}
        //   />
        // )}
      >
        {/* <DocumentComponent tabLabel="Documents" /> */}
        <DrawingsComponent
          docId={_.get(route, 'params.docId', '')}
          category={_.get(route, 'params.category', '')}
          refetch={_.get(route, 'params.refetch', '')}
        />
      </ScrollView>
    </Box>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    backgroundColor: '#FFFFFF'
  }
});

export default MovingFile;
