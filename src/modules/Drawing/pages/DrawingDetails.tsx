import { AppBar, Icon } from '@commons';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { getFileIcon } from '@src/commons/FileIcon';
import { COLORS } from '@src/constants';
import { DrawingNavigatorParams, RootNavigatorParams } from '@src/types';
import moment from 'moment';
import { Avatar, Box, Button, Circle, Divider, FlatList, Flex, HStack, Text, View, VStack } from 'native-base';
import React, { useEffect, useState } from 'react';
import { InteractionManager, StyleSheet } from 'react-native';

type Props = CompositeScreenProps<
  BottomTabScreenProps<DrawingNavigatorParams, 'DrawingDetails'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const DrawingDetails: React.FC<Props> = ({ navigation, route }) => {
  const selectedDocumentId = route?.params.id;

  const [isRefresh, setIsRefresh] = useState(false);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getDocumentDetails();
      getTask();
    });
  }, []);

  const [getDocumentDetails, { data, refetch }] = Gql.useProjectDocumentLazyQuery({
    variables: { id: selectedDocumentId }
  });

  const [getTask, { data: taskData }] = Gql.useGetTasksLazyQuery({
    variables: {
      filter: {
        documents: {
          id: {
            eq: selectedDocumentId
          }
        }
      }
    }
  });

  const getTaskData = taskData?.tasks?.nodes.map((document: any) => {
    return {
      id: document.id,
      title: document.title,
      lastModified: moment(document.updatedAt).format('DD MMM YYYY, hh:mma')
    };
  });

  const fetchData = () => {
    getTaskData;
    setIsRefresh(false);
  };

  const onRefresh = () => {
    setIsRefresh(true);
    fetchData();
  };

  const myListEmpty = () => {
    return (
      <View style={{ alignItems: 'center' }}>
        <Text>No data found</Text>
      </View>
    );
  };

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <AppBar
        header
        goBack
        title="Document Details"
        rightComponent={
          <HStack space={5}>
            <Button
              _pressed={{
                bg: 'transparent',
                opacity: 0.8
              }}
              bg="transparent"
              onPress={() => {}}
              p={0}
              justifyContent="flex-start"
            >
              <Icon name="three-dots" />
            </Button>
            <Button
              _pressed={{
                bg: 'transparent',
                opacity: 0.8
              }}
              bg="transparent"
              onPress={() => {}}
              p={0}
              justifyContent="flex-start"
            >
              <Icon name="cancel" />
            </Button>
          </HStack>
        }
      />
      <Box py={2} px={7} bg="#FFFFFF">
        <Flex direction="column">
          <HStack space={2} mb={4}>
            {getFileIcon(data?.projectDocument?.type ?? '')}
            <Text style={style.titleText}>{data?.projectDocument?.name}</Text>
          </HStack>
          <Divider />
          <HStack space={12} mt={4} mb={4}>
            <Text style={style.text}>Added by</Text>
            <HStack alignItems="center" space={2}>
              <Avatar
                size="20px"
                source={{
                  uri: data?.projectDocument?.owner?.avatar ?? ''
                }}
              />
              <Text style={style.detailsText}>{data?.projectDocument?.owner?.name}</Text>
            </HStack>
          </HStack>
          <Divider />
          <HStack space={6} mt={4} mb={4}>
            <Text style={style.text}>Last modified</Text>
            <Text pl={2} style={style.detailsText}>
              {moment(data?.projectDocument?.updatedAt).format('DD MMMM YYYY')}
            </Text>
          </HStack>
          <Divider />
          <Button
            variant="primary"
            mt={3}
            onPress={() => {
              navigation.push('DrawingNav', {
                screen: 'DrawingPdfTron',
                params: { id: selectedDocumentId ?? '' }
              });
            }}
          >
            Annotate file
          </Button>
        </Flex>
      </Box>

      <Box py={2} px={7} height="full">
        <Flex direction="row" justifyContent="space-between" alignItems="center">
          <Text fontWeight={600} fontSize={16}>
            Tasks
          </Text>
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            bg="transparent"
          >
            <Text color={COLORS.primary[1]} onPress={onRefresh}>
              Refresh
            </Text>
          </Button>
        </Flex>
        <FlatList
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={{ paddingHorizontal: 20, paddingTop: 20 }}
          data={getTaskData}
          ListEmptyComponent={myListEmpty}
          onRefresh={onRefresh}
          keyExtractor={item => item.id}
          refreshing={isRefresh}
          renderItem={({ item, index }: any) => (
            <Box style={style.taskBox}>
              <VStack space={2}>
                <HStack space={3}>
                  <Circle size="24px" bg={COLORS.primary.light}>
                    <Text color={COLORS.primary[1]} fontWeight={600}>
                      {index + 1}
                    </Text>
                  </Circle>
                  <Text fontWeight={600}>{item.title}</Text>
                </HStack>
                <Text color={COLORS.neutrals.gray70}>{item.lastModified}</Text>
              </VStack>
            </Box>
          )}
        />
      </Box>
    </Box>
  );
};

const style = StyleSheet.create({
  taskBox: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 12,
    marginBottom: 20
  },
  text: {
    color: COLORS.neutrals.gray90,
    fontWeight: '400',
    fontSize: 14
  },
  titleText: {
    fontWeight: '600',
    fontSize: 16
  },
  detailsText: {
    color: COLORS.neutrals.gray100
  }
});

export default DrawingDetails;
