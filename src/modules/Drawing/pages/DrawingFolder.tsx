import { AppBar, Content, Footer } from '@commons';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { pushError, pushSuccess } from '@src/configs';
import { COLORS } from '@src/constants';
import { TextInput } from '@src/inputs';
import apolloClient from '@src/lib/apollo';
import useCreateDrawing from '@src/mutation/drawing/useCreateDrawing';
import { useSelector } from '@src/store';
import { DrawingNavigatorParams, RootNavigatorParams } from '@src/types';
import { Field, Formik, FormikProps } from 'formik';
import _ from 'lodash';
import { Box, Button } from 'native-base';
import React, { useRef, useState } from 'react';
import { StyleSheet } from 'react-native';

//latest version
type Props = CompositeScreenProps<
  BottomTabScreenProps<DrawingNavigatorParams, 'DrawingFolder'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const DrawingFolder: React.FC<Props> = ({ route, navigation }) => {
  const formRef = useRef<FormikProps<FormValues>>(null);
  const pageIndex = Number(_.get(route, 'params.pageIndex', '0'));
  const projectDocumentId = route.params.projectDocumentId;
  const localRemoteId = route.params.localRemoteId;
  const [isSubmitting, setSubmitting] = useState<boolean>(false);
  const projectId = useSelector(state => state.project.projectId);

  const { mutateAsync: createFolder, isPending } = useCreateDrawing();

  const initialValues = {
    name: ''
  };

  const onSubmit = async (values: FormValues) => {
    if (values.name.trim() === '') {
      pushError('Please enter folder names');
      return;
    }
    const { name } = values;
    const fileSystemType = Gql.FileSystemType.Folder;
    const type = 'folder';
    const category = route.params.category as Gql.CategoryType;
    setSubmitting(true);
    try {
      if (!projectId) {
        throw new Error('Project Id is missing');
      }

      const newFolder = {
        name,
        category,
        fileSystemType,
        type,
        projectDocumentId: projectDocumentId ? parseInt(projectDocumentId) : null,
        localRemoteId: localRemoteId,
        projectId: parseInt(projectId)
      };

      await createFolder([newFolder]);

      pushSuccess('Folder added successfully');
      navigation.goBack();
    } catch (e) {
      pushError(e);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <AppBar
        goBack
        barStyle={'dark-content'}
        title={'Add Folder'}
        // title={id ? 'Edit folder' : 'Add Folder'}
        noRight
      />

      <Formik onSubmit={onSubmit} initialValues={initialValues} innerRef={formRef}>
        {() => {
          return (
            <Box flex={1} bg="#FFFFFF">
              <Content pt={5}>
                <Field autoFocus name="name" label="Folder name" component={TextInput} />
              </Content>
              <Footer>
                <Button
                  isLoading={isSubmitting}
                  variant="primary"
                  onPress={() => {
                    formRef.current?.handleSubmit();
                  }}
                >
                  Confirm
                </Button>
              </Footer>
            </Box>
          );
        }}
      </Formik>
    </Box>
  );
};

interface FormValues {
  name: string;
}

const styles = StyleSheet.create({});

export default DrawingFolder;
