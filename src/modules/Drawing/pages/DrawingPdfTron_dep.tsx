import { Config, DocumentView, RNPdftron } from 'react-native-pdftron';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { AppBar, Footer } from '@src/commons';
import { pushError, pushSuccess } from '@src/configs';
import { COLORS } from '@src/constants';
import apolloClient from '@src/lib/apollo';
import { DrawingNavigatorParams, RootNavigatorParams } from '@src/types';
import _ from 'lodash';
import { Box, Button } from 'native-base';
import React, { useEffect, useRef, useState } from 'react';
import { Alert, BackHandler, InteractionManager, Platform } from 'react-native';

type Props = CompositeScreenProps<
  BottomTabScreenProps<DrawingNavigatorParams, 'DrawingPdfTron'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const DrawingPdfTron: React.FC<Props> = ({ route, navigation }) => {
  const id = route.params.id;
  const role = route.params.role;
  const viewerRef = useRef<any>();
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  let view = [] as any;
  //Custom Toolbars
  const AnnotateToolbar = {
    [Config.CustomToolbarKey.Id]: 'Annotate',
    [Config.CustomToolbarKey.Name]: 'Annotate',
    [Config.CustomToolbarKey.Icon]: Config.ToolbarIcons.Annotate,
    [Config.CustomToolbarKey.Items]: [
      Config.Tools.annotationCreateFreeText,
      Config.Tools.annotationCreateRectangle,
      Config.Tools.annotationCreateFreeHand,
      Config.Tools.annotationCreateFreeHighlighter,
      Config.Tools.annotationCreatePolygonCloud,
      Config.Tools.annotationCreateArrow,
      Config.Tools.annotationCreateRubberStamp,
      Config.Buttons.undo
    ]
  };

  if (role === 'CloudCoordinator' || role === 'ProjectOwner' || role === 'CanEdit') {
    view = [Config.DefaultToolbars.View, AnnotateToolbar, Config.DefaultToolbars.Measure];
  } else {
    view = [Config.DefaultToolbars.View];
  }

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getDoc();
    });
    RNPdftron.initialize(
      'Bina Cloudtech sdn bhd (bina.cloud):OEM:Bina::IA:AMS(20230928):D7863732B60C5D58B999E101404F3D38DD43F51255922C4C9BD65ABAB6F5C7'
    );
    RNPdftron.enableJavaScript(true);
  }, []);

  const [getDoc, { data }] = Gql.useProjectDocumentLazyQuery({ variables: { id: id } });

  const path = data?.projectDocument?.fileUrl ?? '';
  const fileName = data?.projectDocument?.name;
  const file = data?.projectDocument;
  if (!path) return null;

  const onLeadingNavButtonPressed = () => {
    if (Platform.OS === 'ios') {
      Alert.alert('App', 'onLeadingNavButtonPressed', [{ text: 'OK', onPress: () => {} }], {
        cancelable: true
      });
    } else {
      navigation.goBack();
    }
  };

  // save the signature / any xfdf
  const onSavedXfdf = async (xfdf: string) => {
    setIsSubmitting(true);
    try {
      await apolloClient.mutate<Gql.UpdateOneProjectDocumentMutation>({
        mutation: Gql.UpdateOneProjectDocumentDocument,
        variables: {
          input: {
            id: _.toString(id),
            update: {
              xfdf
            }
          }
        }
      });
      pushSuccess('Saved successfully');
    } catch (e) {
      pushError(e);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <AppBar
        goBack={Platform.OS === 'ios' ? true : undefined}
        barStyle={'dark-content'}
        title={fileName}
        noRight={Platform.OS === 'ios' ? true : undefined}
        onPressTitle={() => null}
      />
      {path && (
        <DocumentView
          ref={viewerRef}
          hideToolbarsOnTap={false}
          bottomToolbarEnabled={false}
          annotationToolbars={view}
          document={path}
          // leadingNavButtonIcon={Platform.OS === 'ios' ? 'ic_close_black_24px.png' : 'ic_arrow_back_white_24dp'}
          onLeadingNavButtonPressed={onLeadingNavButtonPressed}
          onDocumentLoaded={() => {
            viewerRef?.current?.importAnnotations(file?.xfdf);
          }}
        />
      )}
      <Footer>
        <Button
          variant="primary"
          isLoading={isSubmitting}
          onPress={() => {
            viewerRef?.current.exportAnnotations().then((filePath: any) => {
              onSavedXfdf(filePath);
            });
          }}
        >
          Save
        </Button>
      </Footer>
    </Box>
  );
};

export default DrawingPdfTron;
