import { CompositeScreenProps, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Gql } from '@src/api';
import DrawingAddModal from '@src/modules/Drawing/components/DrawingAddModal';
import DrawingThreeOptionModal from '@src/modules/Drawing/components/DrawingThreeOptionModal';
import { manageFiles, task } from '@src/lib/authority';
import { useDispatch, useSelector } from '@src/store';
import { DrawingNavigatorParams, RootNavigatorParams } from '@src/types';
import { Box, Divider, Skeleton, Stack, VStack } from 'native-base';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { useRoute } from '@react-navigation/native';
import useDebouncedSearch from '@src/hooks/useDebounceSearch';
import { drawingsActions } from '@src/slice/drawings.slice';
import AddButton from '@src/commons/AddButton';
import SearchInput from '@src/commons/SearchInput';
import DocumentList from '@src/commons/list/DocumentList';
import { handleDocumentNavigation } from '@src/utils/documentNavigation';
import ProjectDocumentModel from '@src/database/model/project-document';
import { useGetDrawings } from '@src/queries/drawing/useGetDrawing';
import { AppBar } from '@src/commons';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { NativeStackNavigationProp, NativeStackScreenProps } from '@react-navigation/native-stack';

type Props = CompositeScreenProps<
  BottomTabScreenProps<DrawingNavigatorParams, 'DrawingSubFolder'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

interface RouteParams {
  id: string | number;
  name?: string;
  category?: string;
  role?: string;
  refetch?: () => void;
  localRemoteId?: string;
}

type NavigationType = NativeStackNavigationProp<RootNavigatorParams>;

const DrawingSubFolder: React.FC<Props> = ({ route }) => {
  const navigation = useNavigation<NavigationType>();
  const params = route.params as RouteParams;
  const id = params?.id;
  const category = params?.category;
  const role = params?.role;
  const drawingAddModalRef = useRef<any>(null);
  const drawingThreeOptionModal = useRef<any>(null);
  const [filteredValue, setFilteredValue] = useState<string | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<any>({});
  const project = {
    ...useSelector(state => state.project),
    companyName: useSelector(state => state.auth).company?.name
  };
  const drawings = task(role ?? '');
  const dispatch = useDispatch();

  const { debouncedValue, setInputValue } = useDebouncedSearch();

  const {
    data: getProjectDocuments,
    isLoading,
    fetchNextPage,
    hasNextPage
  } = useGetDrawings(debouncedValue ?? '', {
    projectId: project.projectId,
    category: category as any,
    limit: 10,
    projectDocumentId: typeof id === 'string' ? parseInt(id, 10) : id,
    pageIndex: 1
  });

  const projectDocuments = useMemo(() => {
    if (!getProjectDocuments) return [];
    return getProjectDocuments.pages.map((page: { results: any[] }) => page.results).flat();
  }, [getProjectDocuments]);

  const onSearchInputChange = (newSearchTerm: any) => {
    dispatch(drawingsActions.setFilterDrawingRevision(newSearchTerm));
    setFilteredValue(newSearchTerm);
    setInputValue(newSearchTerm);
  };

  const loadMoreDocuments = useCallback(() => {
    if (hasNextPage && !isLoading) {
      fetchNextPage();
    }
  }, [hasNextPage, isLoading, fetchNextPage]);

  const onOptionPress = (item: ProjectDocumentModel) => {
    drawingThreeOptionModal?.current?.pushModal();
    setSelectedDocument({ document: item });
  };

  return (
    <Box height="full" bg="white" flex={1}>
      <AppBar goBack barStyle={'dark-content'} title={'test'} noRight onPressTitle={() => null} />

      {/* Add Button - Hidden for BIM Models */}
      {category !== Gql.CategoryType.BimDrawings && (
        <AddButton
          isAllowed={drawings.overview.canCreateRoot}
          onPress={() => drawingAddModalRef?.current?.pushModal()}
        />
      )}
      <SearchInput
        filteredValue={filteredValue}
        setFilteredValue={onSearchInputChange}
        placeholder="Search for drawings"
      />
      {/* List */}
      {isLoading ? (
        <>
          <Skeleton />
        </>
      ) : (
        <DocumentList
          documents={projectDocuments}
          isLoading={isLoading}
          loadMoreDocuments={loadMoreDocuments}
          refetch={() => {}}
          onItemPress={async (item: ProjectDocumentModel) => {
            await handleDocumentNavigation({
              item,
              role,
              navigation,
              modules: 'Drawings',
              onRefetch: () => {}
            });
          }}
          onOptionPress={onOptionPress}
          onAction={false}
          setOnAction={() => {}}
        />
      )}

      <DrawingThreeOptionModal
        ref={drawingThreeOptionModal}
        onChange={() => {}}
        data={selectedDocument.document}
        refetch={() => {
          setSelectedDocument({ document: null });
        }}
        role={role ?? ''}
      />
      {category !== Gql.CategoryType.BimDrawings && (
        <DrawingAddModal
          ref={drawingAddModalRef}
          category={category as Gql.CategoryType}
          projectDocumentId={id.toString()}
        />
      )}
    </Box>
  );
};

export default DrawingSubFolder;
