import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import CustomAppBar from '@src/commons/CustomAppBar';
import { COLORS } from '@src/constants';
import { DrawingNavigatorParams, RootNavigatorParams } from '@src/types';
import _ from 'lodash';
import { Box } from 'native-base';
import React, { useRef } from 'react';
import { StyleSheet } from 'react-native';
import ScrollableTabView, { DefaultTabBar } from 'react-native-scrollable-tab-view';
import TwoDDrawing from '../components/2dDrawing';
import BimDrawing from '../components/BimDrawing';
import { useDeviceOrientation } from '@react-native-community/hooks';

type Props = CompositeScreenProps<
  BottomTabScreenProps<DrawingNavigatorParams, 'Drawings'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const Drawing: React.FC<Props> = ({ navigation, route }) => {
  const tabBarRef = useRef<any>(null);
  const pageIndex = Number(_.get(route, 'params.pageIndex', '0'));
  // const { landscape, portrait } = useDeviceOrientation();

  // const renderTabBar = useCallback(() => <ScrollableTabBar
  //   style={{ height: 40 }}
  //   tabsContainerStyle={{ justifyContent: 'center' }}
  // />, [landscape, portrait])

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <CustomAppBar header barStyle={'light-content'} />

      <ScrollableTabView
        key={pageIndex}
        initialPage={0}
        style={{ backgroundColor: '#FFFFFF' }}
        tabBarInactiveTextColor="#000"
        tabBarActiveTextColor="#0695D7"
        tabBarUnderlineStyle={{ backgroundColor: '#0695D7', height: 2 }}
        scrollWithoutAnimation={true}
        locked={true}
        renderTabBar={() => <DefaultTabBar style={{ height: 40, marginTop: 10 }} tabStyle={{ height: 40 }} />}
      >
        <TwoDDrawing tabLabel="Drawings" />
        <BimDrawing tabLabel="BIM Models" />
      </ScrollableTabView>
    </Box>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    backgroundColor: '#FFFFFF'
  }
});

export default Drawing;
