import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { AppBar, Content } from '@src/commons';
import { pushError, pushSuccess } from '@src/configs';
import { COLORS } from '@src/constants';
import { SelectInput, TextInput } from '@src/inputs';
import { CalendarPicker } from '@src/inputs/CalendarPicker';
import apolloClient from '@src/lib/apollo';
import { OverviewNavigatorParams, RootNavigatorParams } from '@src/types';
import { Field, Formik, FormikProps } from 'formik';
import _ from 'lodash';
import moment, { Moment } from 'moment';
import { Avatar, Box, Button, HStack, Text, VStack } from 'native-base';
import React, { useEffect, useRef, useState } from 'react';
import { InteractionManager } from 'react-native';

type Props = CompositeScreenProps<
  BottomTabScreenProps<OverviewNavigatorParams, 'EditOverviewProjectDetails'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const EditOverviewProjectDetails: React.FC<Props> = ({ route }) => {
  const formRef = useRef<FormikProps<FormValues>>(null);
  const id = route?.params?.id;
  const refetch = _.get(route, 'params.refetch', null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getProject();
    });
  }, []);

  const [getProject, { data, loading }] = Gql.useProjectLazyQuery({
    variables: {
      id
    }
  });
  if (loading) return null;

  const initialValues = {
    status: data?.project?.status ?? '',
    managedBy: data?.project?.managedBy ?? '',
    deputySuperintendent: data?.project?.deputySuperintendent ?? '',
    client: data?.project?.client ?? '',
    contractor: data?.project?.contractor ?? '',
    startDate: moment(data?.project?.startDate).format('DD MMMM YYYY') ?? '',
    completionDate: moment(data?.project?.completionDate).format('DD MMMM YYYY') ?? '',
    cnsConsultant: data?.project?.cnsConsultant ?? '',
    mneConsultant: data?.project?.mneConsultant ?? '',
    qsConsultant: data?.project?.qsConsultant ?? '',
    environmentConsultant: data?.project?.environmentConsultant ?? ''
  };

  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true);
    const {
      status,
      managedBy,
      deputySuperintendent,
      client,
      contractor,
      startDate,
      completionDate,
      cnsConsultant,
      mneConsultant,
      qsConsultant,
      environmentConsultant
    } = values;

    try {
      await apolloClient.mutate<Gql.UpdateProjectInputDto>({
        mutation: Gql.UpdateProjectDocument,
        variables: {
          id: id,
          input: {
            status,
            managedBy,
            deputySuperintendent,
            client,
            contractor,
            startDate,
            completionDate,
            cnsConsultant,
            mneConsultant,
            qsConsultant,
            environmentConsultant
          }
        }
      });
      pushSuccess('Project details updated');
      refetch();
    } catch (e) {
      pushError(e);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box flex={1} bg={COLORS.neutrals.white}>
      <AppBar goBack barStyle={'dark-content'} title="Project Details" noRight />

      <Formik onSubmit={onSubmit} initialValues={initialValues as any} innerRef={formRef}>
        {() => {
          return (
            <Box flex={1}>
              <Content>
                {/* <Field
                  autoFocus
                  name="status"
                  label="Status"
                  options={_.map(STATUS, o => ({ label: o, value: o }))}
                  component={SelectInput}
                /> */}
                {/* <Box mb={3}>
                  <VStack space={1}>
                    <Text fontWeight={400} color={COLORS.neutrals.gray90}>
                      Project Owner
                    </Text>
                    <HStack alignItems="center" space={3}>
                      <Avatar
                        size="24px"
                        source={{
                          uri: data?.project?.owner?.avatar ?? ''
                        }}
                      />
                      <Text>{data?.project?.owner?.name}</Text>
                    </HStack>
                  </VStack>
                </Box> */}
                <Field autoFocus name="managedBy" label="Project Director" component={TextInput} />
                <Field autoFocus name="deputySuperintendent" label="Deputy Superintendent" component={TextInput} />
                <Field autoFocus name="client" label="Client" component={TextInput} />
                <Field autoFocus name="contractor" label="Contractor" component={TextInput} />
                <Field autoFocus name="startDate" label="Start Date" component={CalendarPicker} />
                <Field autoFocus name="completionDate" label="Completion Date" component={CalendarPicker} />
                <Field autoFocus name="cnsConsultant" label="C&S consultant" component={TextInput} />
                <Field autoFocus name="mneConsultant" label="M&E consultant" component={TextInput} />
                <Field autoFocus name="qsConsultant" label="QS consultant" component={TextInput} />
                <Field autoFocus name="environmentConsultant" label="Environment Consultant" component={TextInput} />

                <Button
                  variant="primary"
                  isLoading={isSubmitting}
                  isEnabled={!isSubmitting}
                  onPress={() => {
                    formRef.current?.handleSubmit();
                  }}
                >
                  Save
                </Button>
              </Content>
            </Box>
          );
        }}
      </Formik>
    </Box>
  );
};

interface FormValues {
  status: string;
  managedBy: string;
  deputySuperintendent: string;
  client: string;
  contractor: string;
  startDate: Moment;
  completionDate: Moment;
  cnsConsultant: string;
  mneConsultant: string;
  qsConsultant: string;
  environmentConsultant: string;
}

const STATUS = ['Completed', 'InProgress', 'Overdue'];

export default EditOverviewProjectDetails;
