import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MoreHeaderBar, ScrollableTabBar } from '@src/commons';
import { COLORS } from '@src/constants';
import { OverviewNavigatorParams, RootNavigatorParams } from '@src/types';
import { Box, Divider, HStack, Icon, Text } from 'native-base';
import React, { useRef } from 'react';
import { StyleSheet } from 'react-native';
import OverviewProjectDetails from '../components/OverviewProjectDetails';

type Props = CompositeScreenProps<
  BottomTabScreenProps<OverviewNavigatorParams, 'Overview'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const Overview: React.FC<Props> = ({}) => {
  const tabBarRef = useRef<any>(null);

  return (
    <Box flex={1} bg={COLORS.neutrals.white}>
      <MoreHeaderBar goBack rightComponent={<></>} barStyle={'dark-content'} title="Overview" />

      <Divider />
      {/* <AppBar goBack barStyle={'dark-content'} title="Overview" noRight /> */}
      {/* <ScrollableTabView
        renderTabBar={() => (
          <ScrollableTabBar
            activeTextColor="#2A64F9"
            inactiveTextColor={COLORS.neutrals.gray70}
            containerStyle={styles.tabContainer}
            ref={tabBarRef}
          />
        )}
      > */}
      {/* <OverviewComment tabLabel="Comments" /> */}
      {/* <OverviewActivityLog tabLabel="Activity Log" /> */}
      <OverviewProjectDetails tabLabel="Project Details" />
      {/* </ScrollableTabView> */}
    </Box>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    backgroundColor: '#FFFFFF'
  }
});

export default Overview;
