import { Gql } from '@src/api';
import { CustomBoxComponent } from '@src/commons/CustomBoxComponent';
import { COLORS, DIMENS } from '@src/constants';
import { useSelector } from '@src/store';
import { Box, Divider, ScrollView, Skeleton, Stack, Text, VStack } from 'native-base';
import React, { useEffect } from 'react';
import { InteractionManager, StyleSheet } from 'react-native';

const OverviewProjectDetails: React.FC<any> = props => {
  const project = useSelector(state => state.project);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getOverviews();
    });
  }, []);

  const [getOverviews, { data: overviews, loading: overviewsLoading }] = Gql.useProjectOverviewsLazyQuery({
    variables: {
      filter: {
        projectId: { eq: project.projectId }
      },
      paging: {
        limit: 50,
        offset: 0
      },
      sorting: {
        field: Gql.ProjectOverviewSortFields.Sequence,
        direction: Gql.SortDirection.Asc
      }
    }
  });

  return (
    <ScrollView>
      <Box height="full">
        {overviewsLoading ? (
          <SkeletonComponent />
        ) : (
          <>
            <Box style={styles.box} pb={2} pl={4}>
              <CustomBoxComponent
                key={0}
                title="Project Name"
                description={<Text>{project.selectedProjectTitle}</Text>}
              />
              <Divider />

              {overviews?.projectOverviews?.nodes?.map((element, index) => {
                return (
                  <>
                    <CustomBoxComponent
                      key={element.id ?? index}
                      title={element.key ?? '-'}
                      description={<Text>{element.value}</Text>}
                    />
                    <Divider />
                  </>
                );
              })}
            </Box>
          </>
        )}
      </Box>
    </ScrollView>
  );
};

const SkeletonComponent: React.FC<any> = () => {
  const SkeletonItem = () => {
    return (
      <VStack mb={5}>
        {/* <Skeleton style={{ width: DIMENS.screenWidth , height: 22 }} mb={2} />
        <Skeleton style={{ width: DIMENS.screenWidth , height: 22 }} mb={2} />
        <Skeleton style={{ width: DIMENS.screenWidth , height: 22 }} mb={4} /> */}
        {/* <Skeleton style={{height:600,backgroundColor: 'white',}} mb={3} >

        </Skeleton> */}
        <Skeleton style={{ width: DIMENS.screenWidth, height: 80, marginTop: 5 }} />
        <Skeleton style={{ width: DIMENS.screenWidth, height: 80, marginTop: 5 }} />
        <Skeleton style={{ width: DIMENS.screenWidth, height: 80, marginTop: 5 }} />
        <Skeleton style={{ width: DIMENS.screenWidth, height: 80, marginTop: 5 }} />
        <Skeleton style={{ width: DIMENS.screenWidth, height: 80, marginTop: 5 }} />
        <Skeleton style={{ width: DIMENS.screenWidth, height: 80, marginTop: 5 }} />
        <Skeleton style={{ width: DIMENS.screenWidth, height: 80, marginTop: 5 }} />
        <Skeleton style={{ width: DIMENS.screenWidth, height: 80, marginTop: 5 }} />
        <Skeleton style={{ width: DIMENS.screenWidth, height: 80, marginTop: 5 }} />
      </VStack>
    );
  };
  return (
    <Stack mt={5}>
      <SkeletonItem />
    </Stack>
  );
};

const styles = StyleSheet.create({
  box: {
    // borderRadius: 12,
    backgroundColor: COLORS.neutrals.white
  },
  text: {
    color: COLORS.neutrals.gray90,
    fontWeight: '400',
    fontSize: 14
  }
});

export default OverviewProjectDetails;
