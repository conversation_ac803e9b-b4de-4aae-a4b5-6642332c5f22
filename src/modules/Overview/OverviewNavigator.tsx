import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { OverviewNavigatorParams } from '@types';
import Overview from './pages/Overview';
import EditOverviewProjectDetails from './pages/EditOverviewProjectDetails';

const OverviewStack = createNativeStackNavigator<OverviewNavigatorParams>();

const OverviewNavigator: React.FC<any> = () => {
  return (
    <OverviewStack.Navigator initialRouteName="Overview" screenOptions={{ headerShown: false }}>
      <OverviewStack.Screen name="Overview" component={Overview} />
      <OverviewStack.Screen name="EditOverviewProjectDetails" component={EditOverviewProjectDetails} />
    </OverviewStack.Navigator>
  );
};

export default OverviewNavigator;
