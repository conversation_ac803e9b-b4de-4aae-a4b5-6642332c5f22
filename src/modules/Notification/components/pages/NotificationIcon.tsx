import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Gql } from '@src/api';
import { Icon } from '@src/commons';
import { useDispatch, useSelector } from '@src/store';
import { RootNavigatorParams } from '@src/types';
import { Avatar, Box, Button } from 'native-base';
import React, { memo, useEffect, useMemo, useState } from 'react';
import { projectActions } from '@src/slice/project.slice';

type Props = {};

const NotificationIcon = (props: Props) => {
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const { unreadCount } = useSelector(state => state.notifications);
  const route = useRoute();
  const dispatch = useDispatch();
  const user = useSelector(state => state.auth.user);
  const notificationCount = useMemo(() => unreadCount, [unreadCount]);

  return (
    <>
      {route.name === 'AllProjects' && user ? (
        <Button
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          bg="transparent"
          justifyContent="flex-end"
          onPress={() => dispatch(projectActions.setSettingModal(true))}
        >
          <Avatar
            size="sm"
            source={{
              uri: user?.avatar ?? '',
              cache: 'force-cache'
            }}
          />
        </Button>
      ) : (
        <>
          {!!notificationCount ? (
            <Box
              zIndex={1}
              width={3}
              height={3}
              borderRadius={'full'}
              position="absolute"
              top={2.5}
              right={3}
              bg={'red.500'}
            />
          ) : null}
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            hitSlop={20}
            bg="transparent"
            justifyContent="center"
            onPress={() => navigation.push('NotificationsNav', { screen: 'Notifications' })}
          >
            <Icon name="bell-notification" width={25} height={25} />
          </Button>
        </>
      )}
    </>
  );
};

export default memo(NotificationIcon);
