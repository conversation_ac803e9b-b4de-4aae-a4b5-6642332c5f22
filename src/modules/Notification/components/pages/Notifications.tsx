import { AppBar } from '@commons';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Gql } from '@src/api';
import { NotificationApiService } from '@src/api/rest';
import { pushError } from '@src/configs';
import TaskModel from '@src/database/model/task.model';
import { ProjectActions, projectActions } from '@src/slice/project.slice';
import { useDispatch, useSelector } from '@src/store';
import { Notification, RootNavigatorParams } from '@src/types';
import moment from 'moment';
import { Box, FlatList, HStack, Skeleton, Spinner, Stack, Text, VStack } from 'native-base';
import React, { useEffect, useState, useCallback } from 'react';
import { Linking, RefreshControl, StyleSheet, TouchableOpacity } from 'react-native';
import { getLastPulledAt } from '@nozbe/watermelondb/sync/impl';
import database from '@src/database/index.native';
import useSyncWithDownload from '@src/hooks/useSyncWithDownload';
import ProjectDocumentModel from '@src/database/model/project-document';

const ActivityLog = () => {
  const [notifications, setNotifications] = useState([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [totalNotification, setTotalNotification] = useState(0);
  const [isFetching, setIsFetching] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const { syncAndDownload } = useSyncWithDownload();

  const dispatch = useDispatch();
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const project = useSelector(state => state.project);
  const auth = useSelector(state => state.auth);

  const [switchCompany] = Gql.useSwitchCompanyMutation({
    onCompleted: () => {
      navigation.push('ProjectsNav', { screen: 'AllProjects' });
    }
  });

  const getNotifications = useCallback(async (page: number = 0) => {
    setIsFetching(true);
    try {
      const res = await NotificationApiService.getNotifications({ body: { page } });
      if (res) {
        const { data, totalCount, hasMore } = res;
        const notificationLog = data.map((notification: any) => ({
          id: notification?._id,
          avatar: notification?.payload?.user?.avatar,
          name: notification?.payload?.user?.title,
          createdAt: {
            raw: notification?.createdAt,
            formatted: moment(notification?.createdAt).format('D MMM YYYY, h:mma')
          },
          actionName: typeof notification?.payload?.body === 'object' ? '' : notification?.payload?.body,
          actionType: notification?.payload?.head,
          deepLink: notification?.payload?.link?.mobile,
          read: notification?.read,
          link: notification?.payload?.link?.web,
          title: notification?.payload?.title,
          tail: notification?.payload?.tail,
          bodyColon: notification?.payload?.bodyColon,
          headColon: notification?.payload?.headColon,
          ...notification?.payload
        }));
        // @ts-ignore
        setNotifications(prevNotifications => [...prevNotifications, ...notificationLog]);
        setTotalNotification(totalCount);
        setHasMore(hasMore);
      }
    } catch (err) {
    } finally {
      setIsFetching(false);
    }
  }, []);

  useFocusEffect(
    useCallback(() => {
      getNotifications(0);
    }, [getNotifications])
  );

  const onLoadedMore = () => {
    if (hasMore) {
      getNotifications(currentPage + 1);
      setCurrentPage(prevPage => prevPage + 1);
    }
  };

  const extractValueFromLink = (link: string | undefined, key: string): string | null => {
    if (!link) return null;
    const match = link.match(new RegExp(`${key}=([^&]*)`));
    return match ? match[1] : null;
  };

  const normalizeDeepLink = (deepLink: string | undefined): string => {
    if (!deepLink) return '';

    let normalizedLink = deepLink.replace(/:/g, '');
    if (normalizedLink.includes('/0/')) {
      normalizedLink = normalizedLink.replace(/\/0\//g, '/1/');
    }
    return normalizedLink;
  };

  const handleDeepLinkSpecialCases = async (deepLink: string): Promise<string> => {
    if (deepLink?.includes('dynamicDocumentDetail/')) {
      return await replaceRemoteIdWithLocalId(deepLink, /dynamicDocumentDetail\/(\d+)\/(.+)/, ProjectDocumentModel);
    }
    // if (deepLink?.includes('documentDetail/')) {
    //   return await replaceRemoteIdWithLocalId(deepLink, /documentDetail\/(\d+)\/(.+)/, ProjectDocumentModel);
    // }
    if (deepLink?.includes('digital-form/1/')) {
      return await replaceRemoteIdWithLocalId(deepLink, /digital-form\/1\/(\d+)\/(.+)/, ProjectDocumentModel);
    }
    if (deepLink?.includes('task/1/')) {
      return await replaceRemoteIdWithLocalId(deepLink, /task\/1\/(\d+)\/(.+)/, TaskModel);
    }
    return deepLink;
  };

  const replaceRemoteIdWithLocalId = async (deepLink: string, pattern: RegExp, model: any): Promise<string> => {
    // Extract the prefix dynamically
    const prefixMatch = deepLink.match(/^([^/]+)/);
    let prefix = prefixMatch ? prefixMatch[1] : '';

    // Change prefix if it matches 'task'
    if (prefix === 'task') {
      prefix = 'EditTask';
    }

    const match = deepLink?.match(pattern);
    if (match && match[1] && match[2]) {
      const remoteId = parseInt(match[1]);
      const localId = await model.getLocalId(remoteId);
      if (localId) {
        return `${prefix}/${localId}/${match[2]}`;
      }
    }
    throw new Error('Invalid deep link');
  };

  const markNotificationAsRead = async (item: any) => {
    try {
      const res = await NotificationApiService.markAsRead({ body: { messageId: item.id } });
      if (res) {
        // @ts-ignore
        setNotifications((prevNotifications: any[]) =>
          prevNotifications.map((notification: any) =>
            notification.id === item.id ? { ...notification, read: true } : notification
          )
        );
      }
    } catch (err) {
      pushError(err);
    }
  };

  const handleNotificationPress = async (item: Notification) => {
    try {
      const lastPulledAt = await getLastPulledAt(database as any);
      const sanitizedNotificationCreatedAt = new Date(item.createdAt.raw).getTime();

      // check if the notification created at is more than lastPullAt, then sync the data
      if (lastPulledAt && sanitizedNotificationCreatedAt > lastPulledAt) {
        await syncAndDownload({
          offlineDownloadOptions: {
            dispatchStatus: true,
            id: ''
          },
          syncMutateOptions: {
            dispatchStatus: true
          },
          showSyncModal: true
        });
      }

      if (item?.actionName?.includes?.('Happy collaborating!')) return;
      const projectId = extractValueFromLink(item?.link?.redirect, 'projectId');

      if (project?.projectId !== projectId) {
        dispatch(projectActions.setCurrentProjectTitle(''));
      }

      const normalizedDeepLink = normalizeDeepLink(item?.deepLink);
      if (!normalizedDeepLink) {
        throw new Error('Invalid deep link');
      }

      const deepLinkWithProjectId = await handleDeepLinkSpecialCases(normalizedDeepLink);

      dispatch(ProjectActions.updateProjectId(projectId as any));
      Linking.openURL(`bina://${deepLinkWithProjectId}`);

      if (!item.read) {
        await markNotificationAsRead(item);
      }
    } catch (err) {
      pushError(err);
    }
  };

  const renderNotificationItem = ({ item }: { item: any }) => (
    <TouchableOpacity onPress={() => handleNotificationPress(item)}>
      <Box style={styles.notificationBox}>
        <HStack space={2}>
          <VStack>
            <Box flexDirection="column" flex="1" flexWrap="wrap" mb={2} mr={2}>
              <HStack w="full" style={{ alignItems: 'center' }}>
                {item.header && (
                  <Box flex={1}>
                    <Text color="black" fontSize={13} numberOfLines={2} bold>
                      {item.header}
                    </Text>
                  </Box>
                )}
                {!item.read && (
                  <Box
                    width={2}
                    height={2}
                    borderRadius={'full'}
                    marginLeft={'auto'}
                    marginRight={2}
                    bg={'primary.1'}
                  />
                )}
              </HStack>
              {item.title && (
                <Box w={'98%'}>
                  <Text fontSize={13} color="#585657" numberOfLines={2} bold>
                    {item.title}
                  </Text>
                </Box>
              )}
              <Text color="neutrals.gray90" style={styles.dateText} numberOfLines={2}>
                {item.createdAt.formatted}
              </Text>
            </Box>
            <Box style={{ margin: 0, padding: 0 }}>
              <Text style={styles.titleText} ellipsizeMode="tail" mr={10}>
                <Text bold>
                  {item.actionType}
                  {item.headColon && ':'}
                </Text>
                <Text>
                  {' '}
                  {item.actionName}
                  {item.bodyColon && ':'}
                </Text>
                <Text bold color="#0695D7">
                  {''} {item.tail}
                </Text>
              </Text>
            </Box>
          </VStack>
        </HStack>
      </Box>
    </TouchableOpacity>
  );

  return (
    <Box flex={1} bg="#ECEEF0">
      <AppBar goBack barStyle={'dark-content'} title="Notifications" noRight />
      {isFetching && notifications.length === 0 ? (
        <SkeletonComponent />
      ) : (
        <FlatList
          style={{ marginTop: 5 }}
          contentContainerStyle={{ paddingHorizontal: 0, paddingTop: 0 }}
          data={notifications}
          keyExtractor={item => item.id}
          onEndReached={onLoadedMore}
          ListFooterComponent={totalNotification > notifications.length ? <Spinner size={'lg'} /> : null}
          refreshControl={
            <RefreshControl
              refreshing={isFetching}
              onRefresh={() => {
                getNotifications(0);
                setNotifications([]);
              }}
            />
          }
          renderItem={renderNotificationItem}
        />
      )}
    </Box>
  );
};

const SkeletonComponent = () => (
  <Stack p={5}>
    {[...Array(8)].map((_, i) => (
      <VStack mb={5} key={i}>
        <Skeleton style={styles.skeleton} />
      </VStack>
    ))}
  </Stack>
);

const styles = StyleSheet.create({
  notificationBox: {
    paddingLeft: 14,
    paddingRight: 8,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#0695D7',
    marginLeft: 10,
    marginRight: 10,
    marginTop: 10,
    marginBottom: 6,
    borderRadius: 12
  },
  titleText: {
    fontSize: 12
  },
  dateText: {
    fontSize: 12,
    margin: 0,
    padding: 0
  },
  skeleton: {
    height: 90,
    padding: 14,
    borderRadius: 12
  }
});

export default ActivityLog;
