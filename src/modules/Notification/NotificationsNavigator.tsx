import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { NotificationsNavigatorParams } from '@types';
import Notifications from './components/pages/Notifications';

const NotificationsStack = createNativeStackNavigator<NotificationsNavigatorParams>();

const NotificationNavigator: React.FC<any> = () => {
  return (
    <NotificationsStack.Navigator initialRouteName="Notifications" screenOptions={{ headerShown: false }}>
      <NotificationsStack.Screen name="Notifications" component={Notifications} />
    </NotificationsStack.Navigator>
  );
};

export default NotificationNavigator;
