import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { PhotosNavigatorParams } from '@src/types';
import Photos from './pages/Photo';
import Folder from './pages/Folder';
import DirFolder from './pages/DirFolder';
import PhotoImageViewer from './pages/PhotoImageViewer';
import MovingFile from './pages/MovingFile';

const PhotosStack = createNativeStackNavigator<PhotosNavigatorParams>();

const PhotosNavigator: React.FC<any> = () => {
  return (
    <PhotosStack.Navigator initialRouteName="Photos" screenOptions={{ headerShown: false }}>
      <PhotosStack.Screen name="Photos" component={Photos} />
      <PhotosStack.Screen name="Folder" component={Folder} />
      <PhotosStack.Screen name="DirFolder" component={DirFolder} />
      <PhotosStack.Screen name="PhotoImageViewer" component={PhotoImageViewer} />
      <PhotosStack.Screen name="MovingFile" component={MovingFile} />
    </PhotosStack.Navigator>
  );
};

export default PhotosNavigator;
