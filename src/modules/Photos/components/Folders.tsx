import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Gql } from '@src/api';
import { Icon } from '@src/commons';
import { COLORS } from '@src/constants';
import { useSelector } from '@src/store';
import { RootNavigatorParams } from '@src/types';
import moment from 'moment';
import { Box, FlatList, HStack, Skeleton, Stack, Text, VStack } from 'native-base';
import React, { useEffect } from 'react';
import { InteractionManager, StyleSheet, TouchableOpacity } from 'react-native';

const Folders: React.FC<any> = props => {
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const project = useSelector(state => state.project);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getFolder();
    });
  }, []);

  const [getFolder, { data, refetch, loading, fetchMore }] = Gql.useProjectDocumentsLazyQuery({
    variables: {
      filter: {
        projectId: { eq: project.projectId ?? '' },
        category: { eq: Gql.CategoryType.Photo },
        fileSystemType: { eq: Gql.FileSystemType.Folder }
      },
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.ProjectDocumentSortFields.UpdatedAt
        }
      ],
      paging: {
        limit: 20,
        offset: 0
      }
    },
    fetchPolicy: 'cache-and-network'
  });

  const folders = data?.projectDocuments?.nodes.map(folder => {
    return {
      id: folder.id,
      name: folder.name,
      updatedAt: moment(folder.updatedAt).format('DD MMMM YYYY')
    };
  });

  const onLoaded = () => {
    if (!data?.projectDocuments.pageInfo.hasNextPage) return;
    fetchMore({
      variables: {
        paging: {
          offset: data?.projectDocuments.nodes.length,
          limit: 20
        }
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;
        const result = Object.assign({}, prev, {
          projectDocuments: {
            ...fetchMoreResult.projectDocuments,
            nodes: [...prev.projectDocuments.nodes, ...fetchMoreResult.projectDocuments.nodes]
          }
        });
        return result;
      }
    });
  };

  return (
    <Box height="full" bg={COLORS.primary.light}>
      <Box>
        {loading ? (
          <SkeletonComponent />
        ) : (
          <FlatList
            keyboardShouldPersistTaps="handled"
            contentContainerStyle={{ paddingHorizontal: 20, paddingTop: 20 }}
            data={folders}
            keyExtractor={item => item.id}
            onEndReachedThreshold={0.3}
            onEndReached={onLoaded}
            renderItem={({ item }: any) => (
              <TouchableOpacity
                onPress={() => {
                  navigation.push('PhotosNav', {
                    screen: 'DirFolder',
                    params: { id: item.id, name: item.name, refetch }
                  });
                }}
              >
                <Box style={styles.box}>
                  <HStack space={3}>
                    <Box pt={3}>
                      <Icon name="folder" />
                    </Box>

                    <VStack>
                      <Text fontWeight={600}>{item.name}</Text>
                      <Text color={COLORS.neutrals.gray90}>{item.updatedAt}</Text>
                    </VStack>
                  </HStack>
                </Box>
              </TouchableOpacity>
            )}
          />
        )}
      </Box>
    </Box>
  );
};

const SkeletonComponent: React.FC<any> = () => {
  const SkeletonItem = () => {
    return (
      <VStack mb={5}>
        <Skeleton style={{ height: 70, padding: 14, borderRadius: 12 }} />
        <VStack space={1}></VStack>
      </VStack>
    );
  };
  return (
    <Stack p={5}>
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
    </Stack>
  );
};

const styles = StyleSheet.create({
  box: {
    padding: 14,
    backgroundColor: '#FFFFFF',
    marginBottom: 10,
    borderRadius: 12
  }
});

export default Folders;
