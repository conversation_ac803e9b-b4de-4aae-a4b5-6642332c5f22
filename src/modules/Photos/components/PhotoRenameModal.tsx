import { Modal } from '@commons';
import { Gql } from '@src/api';
import { pushError, pushSuccess, pushMessaage } from '@src/configs';
import { useUpdatePhotos } from '@src/mutation/photos/useUpdatePhotos';
import { photosActions } from '@src/slice/photos.slice';
import { useSelector } from '@src/store';
import _ from 'lodash';
import { Box, Button, Input, Text, VStack } from 'native-base';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps } from 'react-native';
import { useDispatch } from 'react-redux';

interface Props {
  refetch: () => void;
  data?: any;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const PhotoRenameModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const [value, setValue] = useState(props?.data?.name);
  const states = useSelector(state => state.photos);
  const dispatch = useDispatch();
  const { mutate: updateName, isPending } = useUpdatePhotos();

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  useEffect(() => {
    setValue(props?.data?.name);
  }, [props?.data?.name]);

  // const [updateName, { loading: isSubmit }] = Gql.useUpdateOneProjectDocumentMutation({
  //   onCompleted: () => {
  //     pushSuccess('Media name updated successfully');
  //     props.refetch();
  //     setValue('');
  //     dispatch(photosActions.closeAll());
  //   },
  //   onError: (err: any) => {
  //     pushError(err.message);
  //   }
  // });

  // Create group Function
  const onFinish = async () => {
    let name = value;
    if (!value.endsWith(props?.data?.type) && props?.data?.type !== 'folder') {
      name = value + '.' + props?.data?.type;
    }
    if (!value || value === '') {
      pushMessaage('Error on group name', 'error', 'Name is required!');
      modalRef.current.closeModal();
    } else if (props?.data?.id) {
      try {
        updateName({
          id: props?.data?.id,
          name
        });
        pushSuccess('Media name updated successfully');
        setValue('');
        dispatch(photosActions.closeAll());
      } catch (e: any) {
        pushError(e.message);
      }
    }
  };

  return (
    <Modal isVisible={states.isPhotoRenameModalOpen} ref={modalRef} style={{ marginTop: 40 }} type="middle">
      <Box p={6}>
        <Text textAlign="center" mb={4} fontSize={16}>
          Update new name
        </Text>

        <VStack>
          <Text>New name</Text>
          <Input
            onChangeText={value => {
              setValue(value);
            }}
            isRequired={true}
            defaultValue={value}
          />
        </VStack>

        <Box flexDirection="row" justifyContent="flex-end" mt={5}>
          <Button
            bg="transparent"
            _pressed={{ bg: 'transparent' }}
            onPress={() => {
              // setValue('')
              dispatch(photosActions.resetAll());
            }}
          >
            <Text>Cancel</Text>
          </Button>
          <Button
            variant={'ghost'}
            onPress={() => {
              onFinish();
            }}
            isLoading={isPending}
          >
            <Text color="#0695D7">Update</Text>
          </Button>
        </Box>
      </Box>
    </Modal>
  );
});

PhotoRenameModal.displayName = 'PhotoRenameModal';
export default PhotoRenameModal;
