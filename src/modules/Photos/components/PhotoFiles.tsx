import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Gql } from '@src/api';
import { Icon } from '@src/commons';
import NotFound from '@src/commons/NotFound';
import { pushError, pushSuccess, showFileName } from '@src/configs';
import apolloClient from '@src/lib/apollo';
import { photosActions } from '@src/slice/photos.slice';
import { useDispatch } from '@src/store';
import { RootNavigatorParams } from '@src/types';
import _ from 'lodash';
import moment from 'moment';
import { Box, Center, Flex, Skeleton, Spinner, Text, VStack, View, HStack } from 'native-base';
import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, Image, RefreshControl, Platform } from 'react-native';
import Share from 'react-native-share';
import { FlatGrid } from 'react-native-super-grid';
import RNFetchBlob from 'rn-fetch-blob';
import PhotoOptionModal from './PhotoOptionModal';
import DeviceInfo from 'react-native-device-info';
import { ImageFormat } from '@src/constants';
import FastImage from 'react-native-fast-image';
import useDeletePhotos from '@src/mutation/photos/useDeletePhotos';
import useSyncWithDownload from '@src/hooks/useSyncWithDownload';

interface Props {
  refetch: () => void;
  loadMore: () => void;
  data: any | undefined;
  loading: boolean;
  fetchMoreLoading: boolean;
  photosData: any | [];
  searchLoading: boolean;
  filterValue: string;
  spacingBottom?: boolean;
  disableThreeDots?: boolean;
  // used in linked to
  onClick?: (item: any) => void;
}

const PhotoFiles: React.FC<Props> = props => {
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const [selectedImage, setSelectedImage] = useState<any>({});
  const dispatch = useDispatch();
  const maxItemsPerRow = DeviceInfo.getDeviceType() == 'Tablet' ? 5 : 2;
  const { mutateAsync: deletePhoto } = useDeletePhotos();

  const folders = props.data?.map?.((folder: any) => {
    return {
      id: folder?.id || folder?._raw?.id,
      remoteId: folder?.remoteId || folder?._raw?.remoteId,
      name: folder?.name || folder?._raw?.name,
      date: moment(folder?.createdAt || folder?._raw?.createdAt).format('DD MMMM YYYY'),
      time: moment(folder?.createdAt || folder?._raw?.createdAt).format('h:mm:ss A'),
      type: folder?.type || folder?._raw?.type,
      uri: folder?._raw?.localFileUrl || folder?.localFileUrl || folder?.fileUrl,
      updatedBy: folder?.owner?.name || folder?._raw?.owner?.name,
      description: folder?.description || folder?._raw?.description,
      uploadAddress: folder?.uploadAddress || folder?._raw?.uploadAddress,
      uploadLatitude: folder?.uploadLatitude || folder?._raw?.uploadLatitude,
      uploadLongitude: folder?.uploadLongitude || folder?._raw?.uploadLongitude,
      videoThumbnail: folder?.videoThumbnail || folder?._raw?.videoThumbnail,
      projectDocumentId: folder?.projectDocumentId || folder?._raw?.projectDocumentId,
      localFileUrl: folder?._raw?.localFileUrl || folder?.localFileUrl,
      isQueued: folder?._raw?.isQueued || folder?.isQueued
    };
  });

  const onDownload = async (id: string) => {
    const file = _.find(props.data?.getProjectDocuments.nodes, (data: { id: string }) => data.id === id);
    if (!file) return;

    const dir = `${Platform.OS === 'ios' ? RNFetchBlob.fs.dirs.DownloadDir : RNFetchBlob.fs.dirs.DCIMDir}` + file.name;

    await RNFetchBlob.config({
      fileCache: true,
      addAndroidDownloads: {
        path: dir,
        mediaScannable: true,
        useDownloadManager: true,
        notification: true,
        title: file?.name,
        mime: file.type ?? ''
      },
      path: dir + '/' + file?.name + '.' + file?.type,
      appendExt: file?.type
    })
      .fetch('GET', file.fileUrl ?? '', {})
      .then(res => {
        try {
          if (Platform.OS === 'ios') {
            Share.open({
              title: file.name,
              message: file.name,
              url: res.path(),
              filename: file.name,
              showAppsToView: true
            })
              .then(res => {
                if (res.success) pushSuccess('Successfully downloaded.');
              })
              .catch(err => {
                pushError(err);
              });
          }
        } catch (e) {
          pushError(e);
        }
      });

    // try {
    //   const dir = `${RNFetchBlob.fs.dirs.DCIMDir}/${file?.name}`;
    //   await RNFetchBlob.config({
    //     fileCache: true,
    //     addAndroidDownloads: {
    //       path: dir,
    //       mediaScannable: true,
    //       useDownloadManager: true,
    //       notification: true,
    //       title: file?.name,
    //       mime: file.type ?? ''
    //     }
    //   }).fetch('GET', file.fileUrl ?? '', {});
    //   pushSuccess('Download successfully');
    // } catch (e) {
    //   pushError(e);
    // }
  };

  // Delete function
  const onDelete = async (id: string) => {
    if (id) {
      await deletePhoto(id).then(() => {
        pushSuccess('Folder/File deleted successfully');
        dispatch(photosActions.closeAll());
      });
      // try {
      //   await apolloClient.mutate<Gql.DeleteOneProjectDocumentMutation>({
      //     mutation: Gql.DeleteOneProjectDocumentDocument,
      //     variables: {
      //       id: id
      //     }
      //   });
      //   dispatch(photosActions.closeAll());
      //   pushSuccess('Delete document successfully');
      //   props.refetch();
      // } catch (e) {
      //   pushError(e);
      // }
    }
  };

  const keyExtractor = (item: any, index: number) => `photos-${index}`;

  const handleLongPress = (title: string, type: 'folder' | 'pdf') => {
    return showFileName(type === 'folder' ? 'Folder' : 'File', title);
  };

  const { syncAndDownload } = useSyncWithDownload();

  return (
    <Box height="full" paddingBottom={props.spacingBottom ? 10 : 0}>
      <Box>
        {/* {(props.loading || props.fetchMoreLoading || props.searchLoading) && <Spinner size="lg" />} */}

        {!props.data || props.searchLoading ? (
          <SkeletonComponent />
        ) : (
          <FlatGrid
            style={{ height: '100%' }}
            contentContainerStyle={{ paddingHorizontal: 0, paddingTop: 20 }}
            data={folders}
            keyExtractor={keyExtractor}
            maxItemsPerRow={maxItemsPerRow}
            onEndReachedThreshold={0.3}
            onEndReached={props.loadMore}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={props.filterValue.length > 0 ? <NotFound /> : <></>}
            spacing={20}
            ListFooterComponent={
              props?.data?.getProjectDocuments?.pageInfo?.hasNextPage && props.filterValue === '' ? (
                <Spinner paddingBottom={10} size={'lg'} />
              ) : null
            }
            refreshControl={
              <RefreshControl
                refreshing={false}
                onRefresh={async () => {
                  await syncAndDownload({
                    syncMutateOptions: { dispatchStatus: true },
                    offlineDownloadOptions: { id: '', dispatchStatus: true },
                    showSyncModal: true
                  });
                }}
              />
            }
            renderItem={({ item, index }: any) => {
              if (item.type === 'folder') {
                return (
                  <TouchableOpacity
                    key={index}
                    onPress={() => {
                      if (props.onClick) {
                        props.onClick(item);
                        return;
                      }
                      navigation.push('PhotosNav', {
                        screen: 'DirFolder',
                        params: {
                          id: item.remoteId ? item.remoteId : (item.id as any),
                          name: item.name,
                          refetch: props.refetch
                        }
                      });
                    }}
                    onLongPress={() => handleLongPress(item.name, item.type)}
                  >
                    <Box style={styles.box}>
                      <VStack>
                        <Center>
                          <Icon name="folder" width={75} height={80} />
                        </Center>
                      </VStack>
                    </Box>
                    <Flex direction="row" justifyContent={'space-between'}>
                      <Text textAlign={'center'} width={'90%'} ellipsizeMode={'tail'} numberOfLines={1}>
                        {item.name}
                      </Text>
                      {!props.disableThreeDots && (
                        <TouchableOpacity
                          onPress={() => {
                            dispatch(
                              photosActions.openPhotosOptionModal({
                                id: item.id,
                                fileUrl: item.uri,
                                type: 'folder',
                                data: item
                              })
                            );
                          }}
                          style={{
                            width: 30,
                            height: 30
                          }}
                        >
                          <Icon name="three-dots-v" style={styles.iconContainer} />
                        </TouchableOpacity>
                      )}
                    </Flex>
                  </TouchableOpacity>
                );
              }
              return (
                <>
                  <TouchableOpacity
                    key={index}
                    onPress={() => {
                      if (props.onClick) {
                        props.onClick(item);
                        return;
                      }

                      if (ImageFormat.includes(item?.type)) {
                        navigation.navigate('PhotosNav', {
                          screen: 'PhotoImageViewer',
                          params: { id: item.remoteId || item.id }
                        });
                      } else {
                        navigation.navigate('VideoNav', {
                          screen: 'VideoViewer',
                          params: {
                            id: item.id ?? '',
                            fileUrl: item.uri ?? '',
                            type: item.type ?? '',
                            name: item.name ?? ''
                          }
                        });
                      }
                    }}
                    onLongPress={() => handleLongPress(item.name, item.type)}
                  >
                    {ImageFormat.includes(item?.type) ? (
                      item.localFileUrl || item.uri ? (
                        <FastImage
                          source={{
                            uri: item.localFileUrl || item.uri,
                            cache: FastImage?.cacheControl?.immutable
                          }}
                          style={styles.carousel}
                        />
                      ) : (
                        <Box
                          style={[
                            styles.carousel,
                            { backgroundColor: '#f0f0f0', justifyContent: 'center', alignItems: 'center' }
                          ]}
                        >
                          <Icon name="image" />
                          <Text fontSize="xs">Image unavailable for offline</Text>
                        </Box>
                      )
                    ) : (
                      <>
                        <View>
                          <Image source={{ uri: item?.videoThumbnail }} style={styles.carousel} />
                          <View style={styles.playButtonContainer}>
                            <Icon name="play-button" />
                          </View>
                        </View>
                      </>
                    )}
                  </TouchableOpacity>
                  <Flex direction="row" justifyContent={'space-between'} alignItems="center">
                    <VStack width={'70%'}>
                      <Text textAlign={'left'} ellipsizeMode={'tail'} numberOfLines={1}>
                        {item.name}
                      </Text>
                      {item.localFileUrl || item.isQueued ? (
                        <HStack alignItems="center" mt={1}>
                          <Icon name={item.localFileUrl ? 'download' : 'download-grey'} width={12} height={12} />
                          <Text fontSize={10} color="gray.500" ml={1}>
                            {item.localFileUrl ? 'Available offline' : 'Queued for offline'}
                          </Text>
                        </HStack>
                      ) : (
                        <Box height={6} />
                      )}
                    </VStack>
                    <TouchableOpacity
                      onPress={() => {
                        setSelectedImage(item);
                        dispatch(
                          photosActions.openPhotosOptionModal({
                            id: item.id,
                            fileUrl: item.uri,
                            type: 'photos',
                            data: item
                          })
                        );
                      }}
                      style={{
                        width: 30,
                        height: 30
                      }}
                    >
                      <Icon name="three-dots-v" style={styles.iconContainer} />
                    </TouchableOpacity>
                  </Flex>
                </>
              );
            }}
          />
        )}
        {/* <Center>{props.fetchMoreLoading && <Spinner />}</Center> */}
        <PhotoOptionModal
          refetch={props.refetch}
          selectedImage={selectedImage}
          onDelete={value => onDelete(value)}
          onChange={value => onDownload(value)}
        />
      </Box>
    </Box>
  );
};

const SkeletonComponent: React.FC<any> = () => {
  const SkeletonItem = () => {
    return (
      <VStack mb={5}>
        <Skeleton style={{ height: 100, padding: 14, borderRadius: 12 }} />
        <VStack space={1}></VStack>
      </VStack>
    );
  };
  return (
    <Box mt={5}>
      <FlatGrid
        data={Array.from({ length: 8 }, (_, i) => i)}
        keyExtractor={(item, index) => `${item}-${index}`}
        renderItem={() => <SkeletonItem />}
      />
    </Box>
  );
};

const styles = StyleSheet.create({
  box: {
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    marginBottom: 25,
    height: 80
  },
  carousel: {
    height: 100,
    width: '100%',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    marginBottom: 10
  },
  addButton: {
    position: 'absolute',
    bottom: 70,
    alignSelf: 'flex-end',
    left: 290
  },
  iconContainer: {
    zIndex: 100,
    padding: 8,
    marginLeft: 10,
    height: 40,
    width: 40
  },
  playButtonContainer: {
    position: 'absolute',
    top: '50%', // Center the icon vertically
    left: '50%', // Center the icon horizontally
    transform: [{ translateX: -25 }, { translateY: -25 }], // Center the icon (adjust these values based on the icon size)
    zIndex: 1 // Make sure the icon is displayed on top of the image
  }
});

export default PhotoFiles;
