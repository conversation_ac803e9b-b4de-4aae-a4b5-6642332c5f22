import { Modal } from '@commons';
import _ from 'lodash';
import { Box, Button, Input, Text, VStack } from 'native-base';
import React, { forwardRef, useCallback, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps } from 'react-native';

interface Props {
  refetch: () => void;
  onFinish?: (text: string) => void;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const PhotosAddNameModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const [error, setError] = useState('');
  const [value, setValue] = useState('');

  const validate = () => {
    if (!value) {
      setError('Name is required');
      return false;
    }
    return true;
  };

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const onSubmit = useCallback(() => {
    if (validate()) {
      let finalName = value;
      if (!value.endsWith('.png') && !value.endsWith('.jpg') && !value.endsWith('.jpeg')) {
        finalName = `${value}.png`;
      }
      return props?.onFinish?.(finalName);
    }
  }, [value]);

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="middle">
      <Box p={6}>
        <Text textAlign="center" mb={4} fontSize={16}>
          Photo name
        </Text>

        <VStack>
          <Text>Name</Text>
          <Input onChangeText={value => setValue(value)} isRequired={true} />
          {error ? <Text color={'red.400'}>{error}</Text> : null}
        </VStack>

        <Box flexDirection="row" justifyContent="flex-end" mt={5}>
          <Button
            bg="transparent"
            _pressed={{ bg: 'transparent' }}
            onPress={() => {
              modalRef?.current?.closeModal();
            }}
          >
            <Text>Cancel</Text>
          </Button>
          <Button variant={'ghost'} onPress={onSubmit}>
            <Text color="#0695D7">Create</Text>
          </Button>
        </Box>
      </Box>
    </Modal>
  );
});

PhotosAddNameModal.displayName = 'PhotosAddNameModal';
export default PhotosAddNameModal;
