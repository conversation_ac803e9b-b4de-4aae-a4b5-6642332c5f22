import { Modal } from '@commons';
import { photosActions } from '@src/slice/photos.slice';
import { useDispatch, useSelector } from '@src/store';
import { Button, Text } from 'native-base';
import React, { FC } from 'react';
import PhotoDetailModal from './PhotoDetailModal';
import { photo } from '@src/lib/authority';
import PhotoRenameModal from './PhotoRenameModal';
import PhotoDescModal from './PhotoDescModal';
import { Alert } from 'react-native';
import useCreateOfflineDownload from '@src/mutation/offline-download/useCreateOfflineDownload';
import { pushMessaage } from '@src/configs';
import NetInfo from '@react-native-community/netinfo';
import useSaveProjectDocument from '@src/mutation/project-document/useSaveProjectDocument';

interface Props {
  onChange?: (value: string) => void;
  onDelete?: (value: string) => void;
  selectedImage: any;
  refetch: () => void;
}

const PhotoOptionModal: FC<Props> = props => {
  const dispatch = useDispatch();
  const states = useSelector(state => state.photos);
  const project = useSelector(state => state.project);
  const perm = photo(project.projectUserRole);
  const { mutateAsync: queueOfflineDownload, isPending } = useCreateOfflineDownload();
  const { mutateAsync: handleOfflineDownload } = useSaveProjectDocument();
  const netInfo = NetInfo.useNetInfo();
  const isConnected = netInfo.isConnected;

  const handleOfflineDownloadPress = async () => {
    if (states.data?.type === 'folder') {
      pushMessaage('Folder cannot be downloaded for offline use', 'warning');
      return;
    }

    if (!states.selectedDocumentId) {
      pushMessaage('Unable to download: Document ID not found', 'error');
      return;
    }

    // Check if already downloaded or queued
    if (states.data?.localFileUrl) {
      // If we have localFileUrl but the image isn't loading, try to re-download
      if (!states.data?.uri && isConnected) {
        try {
          await handleOfflineDownload({ id: states.selectedDocumentId, dispatchStatus: false });
          pushMessaage('Photo has been refreshed', 'success');
        } catch (error) {
          pushMessaage('Failed to refresh photo', 'error');
        }
      } else {
        pushMessaage('This photo is already available offline', 'info');
      }
      dispatch(photosActions.resetAll());
      return;
    }

    if (states.data?.isQueued) {
      pushMessaage('This photo is already queued for offline download', 'info');
      dispatch(photosActions.resetAll());
      return;
    }

    try {
      await queueOfflineDownload([states.selectedDocumentId]);
      if (isConnected) {
        await handleOfflineDownload({ id: '', dispatchStatus: false });
      }
      dispatch(photosActions.resetAll());
    } catch (error) {
      Alert.alert('Download Error', 'There was an error downloading the file for offline use.');
    }
  };

  // sharing image function
  // const shareImage = async (url: string) => {
  //   RNFetchBlob.config({
  //     fileCache: true,
  //     addAndroidDownloads: {
  //       useDownloadManager: true,
  //       notification: true,
  //       path: RNFetchBlob.fs.dirs.DownloadDir + '/' + props?.selectedImage?.name,
  //       // path: RNFetchBlob.fs.dirs.DownloadDir + '/image-1.png',
  //       mime: 'image/png',
  //       mediaScannable: true,
  //       description: 'png will be downloaded'
  //     }
  //   })
  //     .fetch('GET', url)
  //     .then(res => {
  //       //   return res.readFile('base64');
  //       // })
  //       // .then(base64Data => {

  //       // let shareOptions = {
  //       //   title: 'Share via',
  //       //   message: 'Sharing from Bina Cloud',
  //       //   url: `file://,${res.data}`,
  //       //   // url: `data:image/png;base64,${base64Data}`,
  //       //   subject: 'Share Link', //  for email
  //       //   type: 'application/pdf'
  //       // };
  //       let shareOptions = {
  //         title: 'Share via',
  //         message: 'Sharing from Bina Cloud',
  //         url: `file://${res.path()}`,
  //         subject: 'Share Link', // for email
  //         type: 'application/pdf'
  //       };
  //       Share.open(shareOptions)
  //         .then(() => {
  //           pushSuccess('Image shared successfully');
  //         })
  //         .catch((err: any) => {
  //           // err && pushError({ message: 'Cancel sharing' });
  //         });
  //     });
  // };

  return (
    <>
      <Modal
        isVisible={states.isPhotoOptionModalOpen}
        onClose={() => {
          dispatch(photosActions.resetAll());
        }}
        type="bottom"
        style={{ paddingBottom: 10 }}
      >
        {/* {perm.canShare && states.type == 'photos' && (
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            // style={states.type === 'folder' ? { display: 'none' } : { display: 'flex' }}
            my={1}
            bg="transparent"
            onPress={() => {
              // shareImage(states.files);
            }}
          >
            <Text>Share</Text>
          </Button>
        )} */}

        {perm.canDownload && states.type == 'photos' && states.data?.type !== 'folder' && (
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            my={1}
            bg="transparent"
            isLoading={isPending}
            onPress={handleOfflineDownloadPress}
          >
            <Text>
              {states.data?.localFileUrl
                ? 'Available offline'
                : states.data?.isQueued
                  ? 'Queued for offline'
                  : 'Download for offline'}
            </Text>
          </Button>
        )}

        {perm.canRename && (
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            my={1}
            bg="transparent"
            onPress={() => {
              dispatch(photosActions.closeAll());
              setTimeout(() => {
                dispatch(photosActions.setIsPhotosRenameModalOpen(true));
              }, 500);
            }}
          >
            <Text>Rename</Text>
          </Button>
        )}

        {/* <Button
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          my={2}
          bg="transparent"
          onPress={() => {
            dispatch(photosActions.closeAll())
            navigation.navigate('PhotosNav', {
              screen: 'MovingFile',
              params: {
                id: '',
                refetch: props.refetch
              }
            });
          }}
        >
          <Text>Move</Text>
        </Button> */}

        {/* <Button
          _pressed={{
            bg: 'transparent'
          }}
          mb={4}
          bg="transparent"
          onPress={() => {
            dispatch(photosActions.closeAll());

            setTimeout(() => {
              dispatch(photosActions.setIsPhotosDetailsModalOpen(true));
            }, 1000);
          }}
        >
          <Text>Details</Text>
        </Button> */}
        {states?.data?.type === 'folder' ? null : (
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            my={1}
            bg="transparent"
            onPress={() => {
              dispatch(photosActions.closeAll());
              setTimeout(() => {
                dispatch(photosActions.setIsPhotosDescModalOpen(true));
              }, 500);
            }}
          >
            <Text>Details</Text>
          </Button>
        )}

        {perm.canDelete && (
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            my={1}
            bg="transparent"
            onPress={() => {
              Alert.alert('Delete Folder/File', 'Are you sure you want to delete this?', [
                {
                  text: 'Cancel',
                  onPress: () => {
                    return;
                  },
                  style: 'cancel'
                },
                {
                  text: 'Delete',
                  onPress: () => {
                    props.onDelete?.(states.selectedDocumentId!);
                  }
                }
              ]);
            }}
          >
            <Text color="semantics.danger">Delete</Text>
          </Button>
        )}
      </Modal>
      <PhotoDetailModal refetch={props.refetch} />
      <PhotoRenameModal refetch={props.refetch} data={states.data} />
      <PhotoDescModal refetch={props.refetch} data={states.data} />
    </>
  );
};

PhotoOptionModal.displayName = 'PhotoOptionModal';
export default PhotoOptionModal;
