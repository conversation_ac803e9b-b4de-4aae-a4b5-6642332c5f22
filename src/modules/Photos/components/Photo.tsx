import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@src/commons';
import { COLORS } from '@src/constants';
import { Box, Button, Circle, Flex, HStack, Input, Pressable } from 'native-base';
import React, { useEffect, useRef, useState } from 'react';
import { Animated, StyleSheet, TouchableOpacity } from 'react-native';
import PhotoFiles from '../components/PhotoFiles';
import PhotosAddModal from '../components/PhotosAddModal';
import { useDispatch, useSelector } from '@src/store';
import { photosActions } from '@src/slice/photos.slice';
import PhotosAddFolders from '../components/PhotosAddFolder';
import _ from 'lodash';
import { photo } from '@src/lib/authority';
import { Gql } from '@src/api';

interface Props {
  data: any;
  refetch?: any;
  loading: any;
  photosData?: any;
  searchLoading: any;
  onLoaded: any;
  fetchMoreLoading: any;
  tabLabel: string;

  filteredValue: string | null;
  date: Date | null;

  setFilteredValue: (value: string) => void;
  setDate: (date: Date | null) => void;
}

const Photos = (props: Props) => {
  const photosAddModalRef = useRef<any>(null);
  const project = useSelector((state: any) => state.project);
  const dispatch = useDispatch();
  const [showSearch, setShowSearch] = useState<boolean>(false);
  const [showDate, setShowDate] = useState<boolean>(false);
  const perm = photo(project.projectUserRole);

  const isEditor = project.projectUserRole === 'CanEdit';

  const canEdit = project.projectUserRole === 'CloudCoordinator' || project.projectUserRole === 'ProjectOwner';

  const [slideAnim] = useState(new Animated.Value(0));

  useEffect(() => {
    if (showSearch) {
      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 250,
        useNativeDriver: true
      }).start();
    } else if (showDate) {
      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 250,
        useNativeDriver: true
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true
      }).start();
    }
  }, [showSearch, showDate]);

  return (
    <>
      <Box flex={1} bg={COLORS.neutrals.white}>
        <MoreHeaderBar
          goBack
          modules={Gql.CategoryType.Photo}
          rightComponent={
            <Flex direction="row">
              <Button
                _pressed={{
                  bg: 'transparent',
                  opacity: 0.8
                }}
                bg="transparent"
                onPress={() => setShowSearch(!showSearch)}
                p={0}
                mr={2}
              >
                <Icon name="search" />
              </Button>
              {/* <Button
                _pressed={{
                  bg: 'transparent',
                  opacity: 0.8
                }}
                ml={2}
                bg="transparent"
                onPress={() => setShowDate(!showDate)}
                p={0}
              >
                <CalendarPickerIcon selectedDate={props.date} setSelectedDate={props.setDate} />
              </Button> */}
            </Flex>
          }
          barStyle={'dark-content'}
          title="Photos"
        />

        {showSearch && (
          <Animated.View
            style={{
              transform: [
                {
                  translateY: slideAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [-50, 0]
                  })
                }
              ]
            }}
          >
            <HStack width="full" height="48px" bg="white" alignItems="center" px={2} mt={1} space={2}>
              <Input
                flexGrow={1}
                backgroundColor={COLORS.neutrals.white}
                placeholder="Search for photos"
                width="full"
                borderRadius="8"
                fontSize="14"
                InputLeftElement={
                  <Box pl="2" py="6">
                    <Icon name="search" />
                  </Box>
                }
                InputRightElement={
                  <Pressable px="4" py="6" onPress={() => props.setFilteredValue('')}>
                    {props.filteredValue !== '' && <Icon name="cancel" />}
                  </Pressable>
                }
                // value={props.filteredValue ?? ''}
                onChangeText={value => {
                  props.setDate(null);
                  props.setFilteredValue(value);
                }}
                onFocus={() => setShowSearch(true)}
                onBlur={() => setShowSearch(false)}
              />
            </HStack>
          </Animated.View>
        )}
        <PhotoFiles
          loading={props.loading}
          photosData={props.data}
          data={props.data}
          loadMore={props.onLoaded}
          refetch={props.refetch}
          fetchMoreLoading={props.fetchMoreLoading}
          searchLoading={props.searchLoading}
          filterValue={props.filteredValue ?? ''}
          spacingBottom={true}
        />
        {canEdit && (
          <TouchableOpacity
            style={[styles.addButton, { zIndex: 1000 }]}
            onPress={() => {
              dispatch(photosActions.openPhotosAddModal());
            }}
          >
            <Circle size="65px" bg="#0695D7">
              <Icon name="plus" />
            </Circle>
          </TouchableOpacity>
        )}

        <PhotosAddModal ref={photosAddModalRef} category={props?.tabLabel} refetch={props.refetch} />
        <PhotosAddFolders refetch={props.refetch} />
      </Box>
    </>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    backgroundColor: '#FFFFFF'
  },
  box: {
    paddingTop: 10,
    paddingHorizontal: 20
  },
  title: {
    fontSize: 34,
    lineHeight: 34
  },
  addButton: {
    position: 'absolute',
    bottom: 71,
    alignSelf: 'flex-end',
    right: 36,
    shadowColor: 'rgba(0, 0, 0, 0.5)',
    shadowOpacity: 1,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 6 },
    backgroundColor: '#FFFFFF',
    borderRadius: 9999
  }
});

export default Photos;
