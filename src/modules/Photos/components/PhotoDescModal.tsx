import { Modal } from '@commons';
import { useDispatch, useSelector } from '@src/store';
import { Box, Button, HStack, Input, Text, TextArea, VStack } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { Linking, ModalProps } from 'react-native';
import { photosActions } from '@src/slice/photos.slice';
import ProjectDocumentModel from '@src/database/model/project-document';
import { pushMessaage, pushSuccess } from '@src/configs';
import { useQueryClient } from '@tanstack/react-query';

interface Props {
  data?: any;
  refetch: () => void;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const PhotoDescModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const dispatch = useDispatch();
  const states = useSelector(state => state.photos);
  const [loading, setLoading] = useState(false);
  const [desc, setDesc] = useState('');
  const queryClient = useQueryClient();
  const user = useSelector(state => state.auth.user);

  const onSubmit = async () => {
    try {
      setLoading(true);
      await ProjectDocumentModel.updateProjectDocs({
        id: states?.data?.id,
        description: desc
      });
      queryClient.invalidateQueries({ queryKey: ['photos'] });
      queryClient.invalidateQueries({ queryKey: ['projectDocuments'] });
      setLoading(false);
      pushSuccess('Saved successfully');
      dispatch(photosActions.resetAll());
    } catch (e: any) {
      pushMessaage(e.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  return (
    <Modal isVisible={states.isPhotosDescModalOpen} ref={modalRef} style={{ marginTop: 40 }} type="middle">
      <Box p={6}>
        <VStack flexDirection={'row'} justifyContent="space-between" alignItems="center" mt={1}>
          <Text fontSize={16}>Photo Details</Text>
          <HStack>
            <Button
              bg="transparent"
              _pressed={{ bg: 'transparent' }}
              onPress={() => {
                dispatch(photosActions.resetAll());
              }}
            >
              <Text>Cancel</Text>
            </Button>
            <Button
              variant={'ghost'}
              onPress={() => {
                onSubmit();
              }}
              isLoading={loading}
            >
              <Text color="#0695D7">Update</Text>
            </Button>
          </HStack>
        </VStack>

        <VStack>
          <Text mt={4}>Description</Text>
          <Input multiline numberOfLines={6} onChangeText={e => setDesc(e)} height={60}>
            {states?.data?.description}
          </Input>
        </VStack>

        <VStack>
          <Text mt={4}>Date</Text>
          <Text>{states?.data?.date}</Text>
        </VStack>

        <VStack>
          <Text mt={4}>Time</Text>
          <Text>{states?.data?.time}</Text>
        </VStack>

        <VStack>
          <Text mt={4}>Location</Text>
          <Text
            onPress={
              states?.data?.uploadAddress
                ? () => Linking.openURL(`https://maps.google.com/?q=${encodeURIComponent(states.data.uploadAddress)}`)
                : undefined
            }
            color={states?.data?.uploadAddress ? '#0695D7' : '#000'}
          >
            {states?.data?.uploadAddress ? states?.data?.uploadAddress : 'Not Provided by User'}
          </Text>
        </VStack>

        <VStack>
          <Text mt={4}>Uploaded By</Text>
          <Text>{user?.name || 'Unknown User'}</Text>
        </VStack>
      </Box>
    </Modal>
  );
});

PhotoDescModal.displayName = 'PhotoDescModal';
export default PhotoDescModal;
