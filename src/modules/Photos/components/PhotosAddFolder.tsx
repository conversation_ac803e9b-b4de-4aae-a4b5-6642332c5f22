import { Modal } from '@commons';
import { Gql } from '@src/api';
import { pushError, pushSuccess } from '@src/configs';
import { COLORS } from '@src/constants';
import { getLinkId } from '@src/database/utils/numeric';
import useCreatePhotos from '@src/mutation/photos/useCreatePhotos';
import { photosActions } from '@src/slice/photos.slice';
import { useDispatch, useSelector } from '@src/store';
import _ from 'lodash';
import { Box, Button, Input, Text, VStack } from 'native-base';
import React, { forwardRef, useState } from 'react';
import { ModalProps } from 'react-native';

interface Props {
  refetch: () => void;
  group?: any;
  documentId?: string;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const PhotosAddFolders = forwardRef<ModalRef, Props>(props => {
  const states = useSelector(state => state.photos);
  const dispatch = useDispatch();
  const [name, setName] = useState<string>('');
  const project = useSelector(state => state.project);
  const { mutate: createFolder, isPending } = useCreatePhotos();

  // const [createFolder, { loading: createFolderLoading }] = Gql.useCreateOneProjectDocumentMutation({
  //   onCompleted: () => {
  //     pushSuccess('Folder added successfully');
  //     props.refetch();
  //     // formRef.current?.resetForm();
  //     dispatch(photosActions.closeAll());
  //   },
  //   onError: (err: any) => {
  //     dispatch(photosActions.closeAll());
  //     pushError(err);
  //   }
  // });

  const onFinish = async () => {
    if (name.trim() === '') {
      pushError('Please enter folder names');
      return;
    }
    const category = Gql.CategoryType.Photo;
    const fileSystemType = Gql.FileSystemType.Folder;
    const type = 'folder';
    const linkId = getLinkId(props.documentId);
    const newDocs: CreateProjectDocumentInput = {
      name: name,
      projectId: parseInt(project?.projectId ?? '0'),
      category: category,
      fileSystemType: fileSystemType,
      type: type,
      ...linkId
    };

    createFolder([newDocs], {
      onSuccess: () => {
        pushSuccess('Folder created successfully');
        setName('');
        dispatch(photosActions.closeAll());
      },
      onError: (error: any) => {
        pushError(error);
      }
    });
  };

  return (
    <Modal
      style={{ marginTop: 40 }}
      type="middle"
      isVisible={states.isCrateFolderModalOpen}
      onClose={() => {
        dispatch(photosActions.closeAll());
      }}
    >
      <Box py={6} height={200}>
        <Text textAlign="center" mb={4} fontSize={16}>
          Create Folder
        </Text>
        {/* <ScrollView keyboardShouldPersistTaps='handled'> */}
        <VStack mx={7}>
          <Text>Folder name</Text>
          <Input
            onChangeText={value => {
              setName(value);
            }}
            isRequired={true}
          />
          <Box flexDirection="row" justifyContent="flex-end" mt={5}>
            <Button variant={'ghost'} mx={2} onPress={() => dispatch(photosActions.closeAll())}>
              <Text style={{ color: COLORS.neutrals.black }}>Cancel</Text>
            </Button>
            <Button variant={'ghost'} isLoading={isPending} onPress={onFinish}>
              Create
            </Button>
          </Box>
        </VStack>
        {/* </ScrollView> */}
      </Box>
    </Modal>
  );
});

PhotosAddFolders.displayName = 'PhotosAddFolders';
export default PhotosAddFolders;
