import { Icon, Modal } from '@commons';
import { Gql } from '@src/api';
import { pushError, pushSuccess } from '@src/configs';
import { COLORS } from '@src/constants';
import { photosActions } from '@src/slice/photos.slice';
import { useDispatch, useSelector } from '@src/store';
import _ from 'lodash';
import moment from 'moment';
import { Avatar, Box, Button, Divider, HStack, Input, ScrollView, Text, VStack } from 'native-base';
import React, { FC, useEffect, useState } from 'react';
import { InteractionManager, StyleSheet, TouchableOpacity } from 'react-native';

interface Props {
  refetch: () => void;
}

const PhotoDetailModal: FC<Props> = props => {
  const [title, setTitle] = useState<string>('');

  const dispatch = useDispatch();
  const states = useSelector(state => state.photos);

  const [getDoc, { data, refetch }] = Gql.useProjectDocumentLazyQuery({
    variables: { id: states.selectedDocumentId ?? '' }
  });

  const [updatePhotos, { loading }] = Gql.useUpdateOneProjectDocumentMutation({
    onCompleted: () => {
      props.refetch();
      pushSuccess('Document Successfully updated');
      dispatch(photosActions.resetAll());
      setTitle('');
    },
    onError: e => {
      pushError(e);
      dispatch(photosActions.resetAll());
      setTitle('');
    }
  });

  const onSubmit = async () => {
    if (!data && !title) return;

    updatePhotos({
      variables: {
        input: { id: data?.projectDocument?.id ?? '', update: { name: title } }
      }
    });
  };

  useEffect(() => {
    setTitle(data?.projectDocument?.name ?? '');
  }, [data]);

  const clearInput = () => setTitle('');

  return (
    <Modal
      style={{ marginTop: 40 }}
      type="bottom"
      isVisible={states.isPhotosDetailsModalOpen}
      onClose={() => {
        dispatch(photosActions.resetAll());
      }}
      onShow={getDoc}
    >
      <ScrollView height="80%" keyboardShouldPersistTaps="handled">
        <Box>
          <Box flex={1} flexDirection="row" alignItems="center" justifyContent={'space-between'} paddingX={5}>
            <Text>Title</Text>
            <Input
              width={'70%'}
              value={title}
              borderWidth={'0'}
              backgroundColor={'white'}
              onChangeText={text => setTitle(text)}
            />
            <TouchableOpacity onPress={() => clearInput()}>
              <Icon name="x-circle" />
            </TouchableOpacity>
          </Box>
          <Divider width={'90%'} alignSelf={'center'} />
          <Box
            flex={1}
            paddingY={3}
            paddingX={5}
            mt={8}
            flexDirection="row"
            backgroundColor={COLORS.neutrals.gray40}
            alignItems="center"
          >
            <Text color="neutrals.gray90">Details</Text>
          </Box>
          <HStack mt={4} mb={4} flex={1} justifyContent={'space-between'} alignItems={'center'} paddingX={5}>
            <Box flex={1} flexDirection="row" alignItems="center">
              <Text color="neutrals.gray90">Added by</Text>
            </Box>
            <Box>
              <TouchableOpacity>
                <VStack>
                  <HStack space={2} flexWrap="wrap" alignItems="center">
                    <Avatar
                      size="22px"
                      source={{
                        uri: data?.projectDocument?.updatedBy?.avatar ?? ''
                      }}
                    />
                    <Text>
                      {data?.projectDocument?.updatedBy?.name ?? data?.projectDocument?.owner?.name ?? 'Unknown User'}
                    </Text>
                  </HStack>
                </VStack>
              </TouchableOpacity>
            </Box>
          </HStack>
          <Divider width={'90%'} alignSelf={'center'} />
          <HStack mt={4} mb={4} paddingX={5} justifyContent={'space-between'}>
            <Box flex={1} flexDirection="row" alignItems="center">
              <Text color="neutrals.gray90">Taken on</Text>
            </Box>
            <Box>
              <Text>{data?.projectDocument?.takenOn ?? 'Unavailable'}</Text>
            </Box>
          </HStack>

          <Divider width={'90%'} alignSelf={'center'} />

          <HStack mt={4} mb={4} paddingX={5} justifyContent={'space-between'}>
            <Box flex={1} flexDirection="row" alignItems="center">
              <Text color="neutrals.gray90"> Added on</Text>
            </Box>
            <Box>
              <Text color="neutrals.gray90">{`${moment(data?.projectDocument?.createdAt).format(
                `DD MMM YYYY`
              )} at ${moment(data?.projectDocument?.createdAt).format(`h:mm a`)}`}</Text>
            </Box>
          </HStack>

          <Divider width={'90%'} alignSelf={'center'} />

          <HStack mt={4} mb={4} paddingX={5} justifyContent={'space-between'}>
            <Box flex={1} flexDirection="row" alignItems="center">
              <Text color="neutrals.gray90"> GPS Location</Text>
            </Box>
            <Box>
              <Text>{data?.projectDocument?.location ?? 'Unavailable'}</Text>
            </Box>
          </HStack>

          <Divider width={'90%'} alignSelf={'center'} />
          <Box flexDirection="row" mt={6} justifyContent="flex-end" mr={5}>
            <Button
              variant="outlineGray"
              h={38}
              isLoading={loading}
              onPress={() => {
                onSubmit();
              }}
            >
              Save
            </Button>
          </Box>
        </Box>
      </ScrollView>
    </Modal>
  );
};

// based on status to show icon
const getStatus = (type: string) => _.get(status, type) ?? '';
const status = {
  Approved: (
    <HStack space={2} alignItems="center">
      <Icon name="approved" />
      <Text color={COLORS.neutrals.gray70}>Approved</Text>
    </HStack>
  ),
  Draft: (
    <HStack space={2} alignItems="center">
      <Icon name="draft" />
      <Text color={COLORS.neutrals.gray70}>Draft</Text>
    </HStack>
  ),
  InReview: (
    <HStack space={2} alignItems="center">
      <Icon name="in-review" />
      <Text color={COLORS.neutrals.gray70}>In Review</Text>
    </HStack>
  ),
  Rejected: (
    <HStack space={2} alignItems="center">
      <Icon name="reject" />
      <Text color={COLORS.neutrals.gray70}>Rejected</Text>
    </HStack>
  )
};

const styles = StyleSheet.create({
  box: {
    borderWidth: 1,
    borderColor: '#E8E8E8',
    borderRadius: 36,
    width: 114,
    height: 38,
    alignItems: 'center'
  }
});

PhotoDetailModal.displayName = 'PhotoDetailModal';
export default PhotoDetailModal;
