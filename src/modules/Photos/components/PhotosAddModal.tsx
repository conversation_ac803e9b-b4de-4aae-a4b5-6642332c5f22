import { Icon, Modal } from '@commons';
import { Gql } from '@src/api';
import { Box, Button, HStack, Spinner, Text } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { Alert, ModalProps } from 'react-native';
import ImagePicker from 'react-native-image-crop-picker';
import { generateRNFile, pushSuccess } from '@src/configs';
import { useDispatch, useSelector } from '@src/store';
import { photosActions } from '@src/slice/photos.slice';
import GetLocation from 'react-native-get-location';
import PhotosAddNameModal from './PhotoNameModal';
import { getLinkId } from '@src/database/utils/numeric';
import useCreatePhotos from '@src/mutation/photos/useCreatePhotos';
import { createThumbnail } from 'react-native-create-thumbnail';

interface Props {
  projectDocumentId?: string;
  category: string;
  refetch: () => void;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const PhotosAddModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const states = useSelector(state => state.photos);
  const project = useSelector(state => state.project);
  const user = useSelector(state => state.auth.user);
  const [loadingLibrary, setLoadingLibrary] = useState(false);
  const [loadingCamera, setLoadingCamera] = useState(false);
  const dispatch = useDispatch();
  const [photos, setPhotos] = useState({
    path: '',
    size: '',
    type: '',
    address: '',
    latitude: '',
    longitude: ''
  });
  const addModalRef = useRef<any>(null);
  const { mutate: createPhoto, isPending } = useCreatePhotos();

  async function getLocation(type: any) {
    if (type === 'Library') {
      setLoadingLibrary(true);
    } else if (type === 'Camera') {
      setLoadingCamera(true);
    }

    await GetLocation.getCurrentPosition({ enableHighAccuracy: true, timeout: 30000, maximumAge: 60000 })
      .then(data => {
        const apiUrl = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${data.latitude},${data.longitude}&key=${process.env.GOOGLE_MAPS_API_KEY}`;
        fetch(apiUrl)
          .then(response => response.json())
          .then(geocodeData => {
            // if (geocodeData.status === 'OK' && geocodeData.results.length > 0) {
            const formattedAddress = geocodeData?.results[0]?.formatted_address;
            const latitude = data.latitude; // Use original GPS coordinates
            const longitude = data.longitude; // Use original GPS coordinates
            if (type === 'Camera') {
              openCamera(formattedAddress, latitude, longitude);
            } else if (type === 'Library') {
              Alert.alert('Choose Media', 'Which file would you want to upload?', [
                {
                  text: 'Cancel',
                  onPress: () => {
                    modalRef?.current?.pushModal();
                  },
                  style: 'default'
                },
                {
                  text: 'Video',
                  onPress: () => {
                    openVideoLibrary(formattedAddress, latitude, longitude);
                  },
                  style: 'default'
                },
                {
                  text: 'Photos',
                  onPress: () => {
                    openPhotoLibrary(formattedAddress, latitude, longitude);
                  },
                  style: 'default'
                }
              ]);
            }
            // }
          })
          .catch(error => {
            setLoadingCamera(false);
            setLoadingLibrary(false);
          })
          .finally(() => {
            setLoadingCamera(false);
            setLoadingLibrary(false);
          });
      })
      .catch(error => {
        // Continue without location data
        if (type === 'Camera') {
          openCamera('Location not available', null, null);
        } else if (type === 'Library') {
          Alert.alert('Choose Media', 'Which file would you want to upload?', [
            {
              text: 'Cancel',
              onPress: () => {
                modalRef?.current?.pushModal();
              },
              style: 'default'
            },
            {
              text: 'Video',
              onPress: () => {
                openVideoLibrary('Location not available', null, null);
              },
              style: 'default'
            },
            {
              text: 'Photos',
              onPress: () => {
                openPhotoLibrary('Location not available', null, null);
              },
              style: 'default'
            }
          ]);
        }
      })
      .finally(() => {
        setLoadingCamera(false);
        setLoadingLibrary(false);
      });
  }

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const openCreateFolderModal = () => {
    dispatch(photosActions.closePhotosAddModal());

    setTimeout(() => {
      dispatch(photosActions.openCreateFolderModal());
    }, 1000);
  };

  // Photos picker
  const openPhotoLibrary = async (formattedAddress: any, latitude: any, longitude: any) => {
    let arr = [] as any;
    try {
      const images = await ImagePicker.openPicker({
        width: 500,
        height: 500,
        cropping: true,
        multiple: true,
        maxFiles: 0,
        forceJpg: true,
        includeBase64: true
      });

      if (images.length > 0) {
        images.forEach(item => {
          let imageName = item.path.split('/').pop();
          const o = generateRNFile({ uri: item.path, name: imageName as any });
          arr.push(o);
        });

        arr.forEach(async (element: any) => {
          try {
            const linkId = getLinkId(props.projectDocumentId);
            const fileSizeInMB = ((element?.size as any) / 1048576).toFixed(2);
            const fileType = element.type.split('/')[1];

            const newDocs: CreateProjectDocumentInput = {
              name: element?.name as string,
              projectId: parseInt(project?.projectId ?? '0'),
              category: Gql.CategoryType.Photo,
              fileSystemType: Gql.FileSystemType.Document,
              type: fileType,
              fileUrl: element.uri,
              fileSize: parseFloat(fileSizeInMB),
              updatedBy: user?.id,
              uploadLatitude: latitude,
              uploadLongitude: longitude,
              uploadAddress: formattedAddress,
              ...linkId
            };
            await createPhoto([newDocs], {
              onSuccess: () => {
                pushSuccess('File upload successfully');
              }
            });
          } catch (error) {}
        });
      }
    } catch (error) {
      setLoadingCamera(false);
      setLoadingLibrary(false);
    }
  };

  const openVideoLibrary = async (formattedAddress: any, latitude: any, longitude: any) => {
    try {
      const videoPickerResponses = await ImagePicker.openPicker({
        mediaType: 'video',
        includeBase64: true,
        multiple: true
      });

      if (videoPickerResponses) {
        const uploadPromises = videoPickerResponses.map(async videoPickerResponse => {
          if (videoPickerResponse.size > 104857600) {
            Alert.alert('Reminder', 'Video size must be less than 100MB per upload');
            return;
          }

          let videoPath = videoPickerResponse.path;
          let videoName = videoPath.split('/').pop();
          if (!videoName?.endsWith('.mp4')) {
            videoName += '.mp4';
          }
          const image = await createThumbnail({ url: videoPath, timeStamp: 1000, format: 'jpeg' });
          const fileType = videoPickerResponse.mime.split('/')[1];
          const linkId = getLinkId(props.projectDocumentId);
          const fileSizeInMB = (videoPickerResponse.size / 1048576).toFixed(2);

          const newDocs: CreateProjectDocumentInput = {
            name: videoName as string,
            projectId: parseInt(project?.projectId ?? '0'),
            category: Gql.CategoryType.Photo,
            fileSystemType: Gql.FileSystemType.Document,
            type: fileType,
            fileUrl: videoPath,
            fileSize: parseFloat(fileSizeInMB),
            videoThumbnail: image.path,
            updatedBy: user?.id,
            uploadLatitude: latitude,
            uploadLongitude: longitude,
            uploadAddress: formattedAddress,
            ...linkId
          };

          return await createPhoto([newDocs as any], {
            onSuccess: () => {
              pushSuccess('Video upload successfully');
            }
          });
        });

        await Promise.all(uploadPromises);
      }
    } catch (error) {
      setLoadingCamera(false);
      setLoadingLibrary(false);
    } finally {
      setLoadingCamera(false);
      setLoadingLibrary(false);
    }
  };

  const openCamera = async (formattedAddress: any, latitude: any, longitude: any) => {
    try {
      await ImagePicker.openCamera({
        width: 500,
        height: 500,
        cropping: true,
        forceJpg: true,
        includeBase64: true
      }).then(async (image: any) => {
        setPhotos({
          path: image.path,
          size: image.size,
          type: image.mime,
          address: formattedAddress,
          latitude: latitude,
          longitude: longitude
        });
        addModalRef?.current?.pushModal();
      });
    } catch (error) {
      setLoadingCamera(false);
      setLoadingLibrary(false);
    }
  };

  const addPhotoName = async (
    value: any,
    path: any,
    address: any,
    latitude: any,
    longitude: any,
    size: any,
    type: any
  ) => {
    const linkId = getLinkId(props.projectDocumentId);
    const fileSizeInMB = ((size as any) / 1048576).toFixed(2);
    const fileType = type.split('/')[1];

    const newDocs: CreateProjectDocumentInput = {
      name: value,
      projectId: parseInt(project?.projectId ?? '0'),
      category: Gql.CategoryType.Photo,
      fileSystemType: Gql.FileSystemType.Document,
      type: fileType,
      fileUrl: path,
      fileSize: parseFloat(fileSizeInMB),
      updatedBy: user?.id,
      uploadLatitude: latitude,
      uploadLongitude: longitude,
      uploadAddress: address,
      ...linkId
    };

    await createPhoto([newDocs], {
      onSuccess: () => {
        pushSuccess('File upload successfully');
        dispatch(photosActions.closeAll());
      }
    });
  };

  return (
    <Modal
      style={{ marginTop: 40 }}
      type="bottom"
      isVisible={states.isPhotosAddModalOpen}
      onClose={() => {
        dispatch(photosActions.closeAll());
      }}
    >
      {/* <Center height={209}> */}
      <HStack justifyContent={'space-evenly'}>
        {/* Create Folder */}
        {props.category === 'Folder' && (
          <Button
            width={'33%'}
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            my={6}
            bg="transparent"
            onPress={() => {
              openCreateFolderModal();
            }}
            justifyContent={'center'}
          >
            <Box
              alignItems={'center'}
              justifyContent={'center'}
              width={16}
              height={16}
              alignSelf={'center'}
              borderRadius={32}
              borderWidth={2}
              borderColor={'neutrals.gray6'}
            >
              <Icon name="folder-outline" />
            </Box>
            <Text color="#6F6D6D">Create Folder</Text>
          </Button>
        )}

        {/* Upload File */}
        <Button
          width={'33%'}
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          my={6}
          bg="transparent"
          onPress={async () => {
            await getLocation('Library');
          }}
          justifyContent={'center'}
        >
          {loadingLibrary ? (
            <Spinner size="lg" />
          ) : (
            <>
              <Box
                alignItems={'center'}
                justifyContent={'center'}
                width={16}
                height={16}
                alignSelf={'center'}
                borderRadius={32}
                borderWidth={2}
                borderColor={'neutrals.gray6'}
              >
                <Icon name="upload" />
              </Box>
              <Text color="#6F6D6D">Upload File</Text>
            </>
          )}
        </Button>

        {/* Upload Folder */}
        <Button
          width={'33%'}
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          my={6}
          bg="transparent"
          onPress={async () => {
            await getLocation('Camera');
          }}
          justifyContent={'center'}
        >
          {loadingCamera ? (
            <Spinner size="lg" />
          ) : (
            <>
              <Box
                alignItems={'center'}
                justifyContent={'center'}
                width={16}
                height={16}
                alignSelf={'center'}
                borderRadius={32}
                borderWidth={2}
                borderColor={'neutrals.gray6'}
              >
                <Icon name="use-camera" />
              </Box>
              <Text color="#6F6D6D">Use Camera</Text>
            </>
          )}
        </Button>
      </HStack>
      {/* </Center> */}
      <PhotosAddNameModal
        ref={addModalRef}
        refetch={props.refetch}
        onFinish={(name: string) => {
          addPhotoName(name, photos.path, photos.address, photos.latitude, photos.longitude, photos.size, photos.type);
        }}
      />
    </Modal>
  );
});

PhotosAddModal.displayName = 'PhotosAddModal';
export default PhotosAddModal;
