import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ScrollableTabBar } from '@src/commons';
import CustomAppBar from '@src/commons/CustomAppBar';
import { COLORS } from '@src/constants';
import { DigitalFormNavigatorParams, PhotosNavigatorParams, RootNavigatorParams } from '@src/types';
import _ from 'lodash';
import { Box, Button, HStack, VStack } from 'native-base';
import React, { useRef } from 'react';
import { ScrollView, StyleSheet, TouchableOpacity } from 'react-native';
import TemplateComponent from '../components/MovingFile/TemplateComponent';

type Props = CompositeScreenProps<
  BottomTabScreenProps<PhotosNavigatorParams, 'MovingFile'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const MovingFile: React.FC<Props> = ({ route }) => {
  const pageIndex = Number(_.get(route, 'params.pageIndex', '0'));

  return (
    <Box flex={1} bg={COLORS.neutrals.white}>
      <CustomAppBar header barStyle={'light-content'} />

      <ScrollView key={pageIndex}>
        <TemplateComponent docId={_.get(route, 'params.id', '')} />
      </ScrollView>
    </Box>
  );
};

export default MovingFile;
