import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { AppBar, Icon } from '@src/commons';
import { COLORS } from '@src/constants';
import { PhotosNavigatorParams, RootNavigatorParams } from '@src/types';
import { Box, Button, Circle, Flex, HStack, Input, Pressable, Text } from 'native-base';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Animated, StyleSheet, TouchableOpacity } from 'react-native';
import PhotoFiles from '../components/PhotoFiles';
import { useDispatch, useSelector } from '@src/store';
import { photosActions } from '@src/slice/photos.slice';
import PhotosAddFolders from '../components/PhotosAddFolder';
import PhotosAddModal from '../components/PhotosAddModal';
import { useGetPhotos } from '@src/queries/photos/useGetPhotos';
import useDebouncedSearch from '@src/hooks/useDebounceSearch';
import { getLinkId } from '@src/database/utils/numeric';

type Props = CompositeScreenProps<
  BottomTabScreenProps<PhotosNavigatorParams, 'DirFolder'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const DirFolder: React.FC<Props> = ({ navigation, route }) => {
  const id = route.params.id;
  const linkId = getLinkId(id);
  const folderName = route.params.name;
  const [filteredValue, setFilteredValue] = useState<string | null>(null);
  const [showSearch, setShowSearch] = useState<boolean>(false);
  const dispatch = useDispatch();
  const [fetchMoreLoading, setFetchMoreLoading] = useState<boolean>(false);
  const [date, setDate] = useState<null | Date>(null);
  const project = useSelector((state: any) => state.project);
  const canView = project.projectUserRole === 'CanView';
  const { debouncedValue, setInputValue } = useDebouncedSearch();

  const {
    data: photo,
    isLoading,
    fetchNextPage,
    hasNextPage
  } = useGetPhotos(debouncedValue ?? '', {
    category: Gql.CategoryType.Photo,
    projectId: project.projectId,
    limit: 10,
    ...linkId
    // sorting: pageIndex === 0 ? 'server_created_at' : undefined,
    // exclude: pageIndex === 0 ? ['fileSystemType:Folder'] : []
  });

  const photos = useMemo(() => {
    if (!photo) return [];
    return photo.pages.map(page => page.results).flat();
  }, [photo]);

  const loadMoreDocuments = useCallback(() => {
    if (hasNextPage && !isLoading) {
      fetchNextPage();
    }
  }, [hasNextPage, isLoading, fetchNextPage]);

  // const [getPhotos, { data, refetch, loading, fetchMore }] = Gql.useGetProjectDocumentsLazyQuery({
  //   variables: {
  //     filter: {
  //       projectId: { eq: project.projectId ?? '' },
  //       category: { eq: Gql.CategoryType.Photo },
  //       ...(date && {
  //         createdAt: {
  //           between: {
  //             lower: moment(date).startOf('day').toISOString(),
  //             upper: moment(date).endOf('day').toISOString()
  //           }
  //         }
  //       }),
  //       projectDocumentId: { eq: id }
  //     },

  //     paging: {
  //       limit: 20,
  //       offset: 0
  //     }
  //   },
  //   fetchPolicy: 'cache-and-network'
  // });

  // useFocusEffect(
  //   useCallback(() => {
  //     InteractionManager.runAfterInteractions(() => {
  //       getPhotos();
  //     });
  //   }, [])
  // );

  // const onLoaded = async () => {
  //   if (!data?.getProjectDocuments.pageInfo.hasNextPage) return;
  //   setFetchMoreLoading(true);
  //   await fetchMore({
  //     variables: {
  //       paging: {
  //         offset: data?.getProjectDocuments.nodes.length,
  //         limit: 20
  //       }
  //     },
  //     updateQuery: (prev, { fetchMoreResult }) => {
  //       if (!fetchMoreResult) return prev;
  //       setFetchMoreLoading(false);
  //       const result = Object.assign({}, prev, {
  //         getProjectDocuments: {
  //           ...fetchMoreResult?.getProjectDocuments,
  //           nodes: [...prev?.getProjectDocuments.nodes, ...fetchMoreResult?.getProjectDocuments.nodes]
  //         }
  //       });
  //       return result;
  //     }
  //   });
  // };

  // const filter = {
  //   category: { eq: Gql.CategoryType.Photo },
  //   projectId: { eq: project.projectId ?? '' },
  //   projectDocumentId: { eq: id },
  //   ...(date && {
  //     createdAt: {
  //       between: {
  //         lower: moment(date).startOf('day').toISOString(),
  //         upper: moment(date).endOf('day').toISOString()
  //       }
  //     }
  //   })
  // };

  // useEffect(() => {
  //   if (filteredValue === null) return;
  //   debouceRequest(filteredValue);
  // }, [filteredValue, date]);

  // const debounceSearchDeals = debounce(keyword => {
  //   const filterCopy = { ...filter };
  //   if (filterCopy.projectDocumentId) {
  //     delete (filterCopy as Partial<typeof filter>).projectDocumentId;
  //   }

  //   getPhotos({
  //     variables: {
  //       filter: {
  //         ...filterCopy,
  //         name: {
  //           like: `%${keyword}%`
  //         }
  //       }
  //     }
  //   });
  // }, 700);

  // const debouceRequest = useCallback((value: any) => debounceSearchDeals(value), [date]);

  const [slideAnim] = useState(new Animated.Value(0));

  useEffect(() => {
    if (showSearch) {
      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 250,
        useNativeDriver: true
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true
      }).start();
    }
  }, [showSearch]);

  const setDateFilter = (date: Date | null) => {
    setFilteredValue(filteredValue !== null ? '' : null);
    setDate(date);
  };

  return (
    <>
      <AppBar
        barStyle={'dark-content'}
        title={'Photos'}
        titleProps={{ marginLeft: 20 }}
        rightComponent={
          <>
            <Button
              _pressed={{
                bg: 'transparent',
                opacity: 0.8
              }}
              bg="transparent"
              onPress={() => {
                // searchModalRef?.current?.pushModal();
              }}
              p={0}
              justifyContent="flex-start"
            >
              <Flex direction="row" alignItems={'center'}>
                <Icon name="more-menu" />
                {/*@ts-ignore */}
                <TouchableOpacity onPress={() => navigation.navigate('More', { screen: 'Account' })}>
                  <Text style={{ marginLeft: 5, color: COLORS.primary[1] }}> More </Text>
                </TouchableOpacity>
              </Flex>
            </Button>
          </>
        }
        onPressTitle={() => null}
      />
      <Box flex={1} bg={COLORS.neutrals.white} style={styles.box}>
        {/* <SearchPhotoModal ref={searchModalRef} /> */}
        <Flex direction="row" justifyContent={'space-between'} alignItems={'center'}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <HStack>
              <Icon name="chevron-left" fill={COLORS.neutrals.gray70} />
              <Text ml={2}>{folderName}</Text>
            </HStack>
          </TouchableOpacity>
          <HStack>
            <Button
              _pressed={{
                bg: 'transparent',
                opacity: 0.8
              }}
              bg="transparent"
              onPress={() => setShowSearch(!showSearch)}
              p={0}
              mr={2}
              justifyContent="flex-start"
            >
              <Icon name="search" />
            </Button>
            <Button
              _pressed={{
                bg: 'transparent',
                opacity: 0.8
              }}
              ml={2}
              bg="transparent"
              p={0}
            >
              {/* <CalendarPickerIcon selectedDate={date} setSelectedDate={setDateFilter} /> */}
            </Button>
          </HStack>
        </Flex>
        {showSearch && (
          <Animated.View
            style={{
              transform: [
                {
                  translateY: slideAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [-50, 0]
                  })
                }
              ]
            }}
          >
            <HStack>
              <Input
                backgroundColor={COLORS.neutrals.gray40}
                placeholder="Search for a photos"
                mt="2"
                width="full"
                borderRadius="8"
                fontSize="14"
                InputLeftElement={
                  <Box pl="2" py="6">
                    <Icon name="search" />
                  </Box>
                }
                InputRightElement={
                  <Pressable
                    px="4"
                    py="6"
                    onPress={() => {
                      setDate(null);
                      setInputValue('');
                    }}
                  >
                    {debouncedValue !== '' && <Icon name="cancel" />}
                  </Pressable>
                }
                // value={debouncedValue ?? ''}
                onChangeText={value => {
                  setDate(null);
                  setInputValue(value ?? '');
                }}
              />
            </HStack>
          </Animated.View>
        )}
        <PhotoFiles
          loading={isLoading}
          data={photos}
          refetch={() => null}
          loadMore={loadMoreDocuments}
          fetchMoreLoading={fetchMoreLoading}
          photosData={null}
          searchLoading={isLoading}
          filterValue={filteredValue ?? ''}
        />
        {!canView && (
          <TouchableOpacity
            style={[styles.addButton, { zIndex: 1000 }]}
            onPress={() => {
              dispatch(photosActions.openPhotosAddModal());
            }}
          >
            <Circle size="65px" bg="#0695D7">
              <Icon name="plus" />
            </Circle>
          </TouchableOpacity>
        )}

        <PhotosAddModal category={'Folder'} refetch={() => null} projectDocumentId={id} />

        <PhotosAddFolders refetch={() => null} documentId={id} />
      </Box>
    </>
  );
};

const styles = StyleSheet.create({
  box: {
    paddingTop: 10,
    paddingHorizontal: 20
  },
  title: {
    fontSize: 34,
    lineHeight: 34
  },
  addButton: {
    position: 'absolute',
    bottom: 71,
    alignSelf: 'flex-end',
    right: 36,
    shadowColor: 'rgba(0, 0, 0, 0.5)',
    shadowOpacity: 1,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 6 },
    backgroundColor: '#FFFFFF',
    borderRadius: 9999
  }
});

export default DirFolder;
