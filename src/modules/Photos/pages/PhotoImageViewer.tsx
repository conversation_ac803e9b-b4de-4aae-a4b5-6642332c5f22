import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { AppBar, Icon, Image } from '@src/commons';
import { COLORS } from '@src/constants';
import ImageThreeDotsModal from '@src/modules/CloudDocs/components/ImageThreeDotsModal';
import { PhotosNavigatorParams, RootNavigatorParams } from '@src/types';
import { Box, Button, View } from 'native-base';
import React, { useEffect, useRef, useState } from 'react';
import { InteractionManager, Modal, StyleSheet, Text } from 'react-native';
import ImageViewer from 'react-native-image-zoom-viewer';

type Props = CompositeScreenProps<
  BottomTabScreenProps<PhotosNavigatorParams, 'PhotoImageViewer'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const PhotoImageViewer: React.FC<Props> = ({ route }) => {
  const id = route.params.id;
  const imageThreeDotsModalRef = useRef<any>(null);
  const [getDoc, { data }] = Gql.useProjectDocumentLazyQuery({ variables: { id: id } });
  const uri = data?.projectDocument?.fileUrl ?? '';
  const fileName = data?.projectDocument?.name;
  const [images, setImages] = useState<Array<any>>([]);
  const [valueChange, setValueChange] = useState<boolean>(false);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      setTimeout(() => {
        getDoc();
      }, 400);
    });
  }, []);

  useEffect(() => {
    if (uri?.length > 0) {
      let arr = [
        {
          url: uri
        }
      ];
      setImages(arr);
      setValueChange(!valueChange);
    }
  }, [uri]);

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <AppBar goBack barStyle={'dark-content'} title={fileName} noRight />
      {images?.length > 0 && <ImageViewer imageUrls={images} />}
    </Box>
  );
};

export default PhotoImageViewer;
