import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps, useFocusEffect } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ScrollableTabBar } from '@src/commons';
import { COLORS } from '@src/constants';
import { PhotosNavigatorParams, RootNavigatorParams } from '@src/types';
import { Box } from 'native-base';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Animated, StyleSheet } from 'react-native';
import { Gql } from '@src/api';
import { useSelector } from '@src/store';
import _ from 'lodash';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import PhotosComponent from '../components/Photo';
import { useGetPhotos } from '@src/queries/photos/useGetPhotos';
import useDebouncedSearch from '@src/hooks/useDebounceSearch';

type Props = CompositeScreenProps<
  BottomTabScreenProps<PhotosNavigatorParams, 'Photos'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const Photos: React.FC<Props> = () => {
  const project = useSelector((state: any) => state.project);
  const [filteredValue, setFilteredValue] = useState<string | null>(null);
  const [showSearch, setShowSearch] = useState<boolean>(false);
  const [showDate, setShowDate] = useState<boolean>(false);
  const [fetchMoreLoading, setFetchMoreLoading] = useState<boolean>(false);
  const [date, setDate] = useState<null | Date>(null);
  const [pageIndex, setPageIndex] = useState<number>(0);
  const { debouncedValue, setInputValue } = useDebouncedSearch();

  // request for photos from the watermelon db
  const {
    data: photo,
    isLoading,
    fetchNextPage,
    hasNextPage
  } = useGetPhotos(debouncedValue ?? '', {
    category: Gql.CategoryType.Photo,
    projectId: project.projectId,
    limit: 10,
    projectDocumentId: pageIndex === 1 ? null : (undefined as any),
    sorting: pageIndex === 0 ? 'server_created_at' : undefined,
    exclude: pageIndex === 0 ? ['fileSystemType:Folder'] : []
  });

  const photos = useMemo(() => {
    if (!photo) return [];
    return photo.pages.map(page => page.results).flat();
  }, [photo]);

  const loadMoreDocuments = useCallback(() => {
    if (hasNextPage && !isLoading) {
      fetchNextPage();
    }
  }, [hasNextPage, isLoading, fetchNextPage]);

  const [slideAnim] = useState(new Animated.Value(0));

  useEffect(() => {
    if (showSearch) {
      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 250,
        useNativeDriver: true
      }).start();
    } else if (showDate) {
      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 250,
        useNativeDriver: true
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true
      }).start();
    }
  }, [showSearch, showDate]);

  const setDateFilter = (date: Date | null) => {
    setFilteredValue(filteredValue !== null ? '' : null);
    setDate(date);
  };

  return (
    <>
      <Box paddingTop={10} pb={10} flex={1} bg={COLORS.neutrals.white}>
        <ScrollableTabView
          key={pageIndex}
          initialPage={pageIndex}
          renderTabBar={() => (
            <ScrollableTabBar
              activeTextColor="#0695D7"
              inactiveTextColor={COLORS.neutrals.gray70}
              containerStyle={styles.tabContainer}
              currentTab={pageIndex}
              onChange={(index: number) => {
                setTimeout(() => {
                  setPageIndex(index);
                  setFilteredValue(filteredValue !== null ? '' : null);
                }, 100);
              }}
            />
          )}
        >
          <PhotosComponent
            tabLabel="Recent"
            data={photos}
            loading={isLoading}
            onLoaded={loadMoreDocuments}
            fetchMoreLoading={fetchMoreLoading}
            // refetch={refetch}
            searchLoading={isLoading}
            filteredValue={debouncedValue}
            setFilteredValue={setInputValue}
            date={date}
            setDate={setDateFilter}
          />
          <PhotosComponent
            tabLabel="Folder"
            data={photos}
            loading={isLoading}
            onLoaded={loadMoreDocuments}
            fetchMoreLoading={fetchMoreLoading}
            // refetch={refetch}
            searchLoading={isLoading}
            filteredValue={debouncedValue}
            setFilteredValue={setInputValue}
            date={date}
            setDate={setDateFilter}
          />
        </ScrollableTabView>
      </Box>
    </>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    backgroundColor: '#FFFFFF'
  }
});

export default Photos;
