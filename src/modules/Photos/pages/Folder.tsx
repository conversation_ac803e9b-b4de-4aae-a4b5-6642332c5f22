import { AppBar, Content, Footer } from '@commons';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { pushError, pushSuccess } from '@src/configs';
import { COLORS } from '@src/constants';
import { TextInput } from '@src/inputs';
import apolloClient from '@src/lib/apollo';
import { PhotosNavigatorParams } from '@src/types';
import { Field, Formik, FormikProps } from 'formik';
import _ from 'lodash';
import { Box, Button } from 'native-base';
import React, { useRef, useState } from 'react';

type Props = NativeStackScreenProps<PhotosNavigatorParams, 'Folder'>;

const Folder: React.FC<Props> = ({ route, navigation }) => {
  const id = route?.params?.id;
  const projectDocumentId = route?.params?.projectDocumentId;
  const refetch = _.get(route, 'params.refetch', null);
  const formRef = useRef<FormikProps<FormValues>>(null);
  const { data, loading } = Gql.useProjectDocumentQuery({ variables: { id: id ?? '' } });
  const [isSubmitting, setSubmitting] = useState<boolean>(false);

  const initialValues = {
    name: data?.projectDocument?.name ?? ''
  };

  if (loading) return null;

  const onSubmit = async (values: FormValues) => {
    if (values.name.trim() === '') {
      pushError('Please enter folder names');
      return;
    }
    const { name } = values;
    const category = Gql.CategoryType.Photo;
    const fileSystemType = Gql.FileSystemType.Folder;
    const type = 'folder';
    setSubmitting(true);
    try {
      if (id) {
        await apolloClient.mutate<Gql.UpdateOneProjectDocumentMutation>({
          mutation: Gql.UpdateOneProjectDocumentDocument,
          variables: {
            input: {
              id: id,
              update: {
                name: values.name
              }
            }
          }
        });
        pushSuccess('Folder name updated successfully');
        refetch();
      } else {
        await apolloClient.mutate<Gql.CreateOneProjectDocumentMutation>({
          mutation: Gql.CreateOneProjectDocumentDocument,
          variables: {
            input: {
              projectDocument: {
                name,
                category,
                fileSystemType,
                type,
                projectDocumentId
              }
            }
          }
        });
        pushSuccess('Folder added successfully');
        refetch();
      }
    } catch (e) {
      pushError(e);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <AppBar goBack barStyle={'dark-content'} title={id ? 'Edit folder' : 'Add Folder'} noRight />

      <Formik onSubmit={onSubmit} initialValues={initialValues} innerRef={formRef}>
        {() => {
          return (
            <Box flex={1} bg="#FFFFFF">
              <Content pt={5}>
                <Field autoFocus name="name" label="Folder name" component={TextInput} />
              </Content>
              <Footer>
                <Button
                  variant="primary"
                  isLoading={isSubmitting}
                  onPress={() => {
                    formRef.current?.handleSubmit();
                  }}
                >
                  Confirm
                </Button>
              </Footer>
            </Box>
          );
        }}
      </Formik>
    </Box>
  );
};

interface FormValues {
  name: string;
}

export default Folder;
