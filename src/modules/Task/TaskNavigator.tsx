import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { TaskNavigatorParams } from '@types';
import AddTask from './pages/AddTask';
import EditTask from './pages/EditTask/Index';
import AttachPdftron from './pages/AttachPdfTron_dep';
import PhotoPdftron from './pages/PhotoPdfTron_dep';
import AddAssignee from './pages/AddAssignee';
import AddCc from './pages/AddCc';
import CreateMemo from './pages/CreateMemo';

const TaskStack = createNativeStackNavigator<TaskNavigatorParams>();

const TaskNavigator: React.FC<any> = () => {
  return (
    <TaskStack.Navigator initialRouteName="Tasks" screenOptions={{ headerShown: false }}>
      {/* <TaskStack.Screen name="Tasks" component={Tasks} /> */}
      <TaskStack.Screen name="AddTask" component={AddTask} />
      <TaskStack.Screen name="EditTask" component={EditTask} />
      <TaskStack.Screen name="AttachPdftron" component={AttachPdftron} />
      <TaskStack.Screen name="PhotoPdftron" component={PhotoPdftron} />
      <TaskStack.Screen name="AddAssignee" component={AddAssignee} />
      <TaskStack.Screen name="AddCc" component={AddCc} />
      <TaskStack.Screen name="CreateMemo" component={CreateMemo} />
      {/* <TaskStack.Screen name="AddAssignee" component={AddAssignee}/> */}
    </TaskStack.Navigator>
  );
};

export default TaskNavigator;
