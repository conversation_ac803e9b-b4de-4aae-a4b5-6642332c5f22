import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps, useFocusEffect } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { ProjectDocument } from '@src/api/graphql';
import LinkedToModal from '@src/commons/LinkedToModal';
import { pushError, pushSuccess } from '@src/configs';
import useDeleteConfirmation from '@src/hooks/useDeleteConfirmation';
import DescriptionInput from '@src/inputs/DescriptionInput';
import { task as taskAuth } from '@src/lib/authority';
import { taskActions } from '@src/slice/tasks.slice';
import { useDispatch, useSelector } from '@src/store';
import { RootNavigatorParams, TaskNavigatorParams } from '@src/types';
import { ReactNativeFile } from 'apollo-upload-client';
import _, { isEqual } from 'lodash';
import { Box, Button, Divider, HStack, ScrollView, Text } from 'native-base';
import React, { lazy, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Alert, Platform, RefreshControl, StyleSheet, TouchableOpacity } from 'react-native';
import DocumentPicker from 'react-native-document-picker';
import TaskGroupModal from '../../components/TaskGroupModal';
import ActivityComponent from '../../components/Tasks/ActivityComponent';
import CommentComponent from '../../components/Tasks/CommentComponent';
import SelectActivityModal from '../../components/Tasks/SelectActivityModal';
import TaskDescriptionModal from '../../components/Tasks/TaskDescriptionModal';
import TaskPhotosModal from '../../components/Tasks/TaskPhotosModal';
import TaskStatusModal from '../../components/Tasks/TaskStatusModal';
import Assignee from './Assignee';
import Attachment from './Attachment';
import Calendar from './Calendar';
import Cc from './Cc';
import CreatedBy from './CreatedBy';
import Group from './Group';
import Header from './Header';
import LinkedTo from './LinkedTo';
import Media from './Media';
import Status from './Status';
import { COLORS } from '@src/constants';
import { Icon } from '@src/commons';
import { withObservables } from '@nozbe/watermelondb/react';
import database from '@src/database/index.native';
import TasksMediaModel from '@src/database/model/task-media.model';
import TasksAttachmentModel from '@src/database/model/task-attachment.model';
import { FlashList } from '@shopify/flash-list';
import useUpdateTask from '@src/mutation/task/useUpdateTask';
import useDeleteTaskMedia from '@src/mutation/task-media/useDeleteTaskMedia';
import useDeleteTaskAttachment from '@src/mutation/task-attachment/useTaskAttachment';
import ProjectDocumentModel from '@src/database/model/project-document.model';
import RNFS from 'react-native-fs';

type Props = CompositeScreenProps<
  BottomTabScreenProps<TaskNavigatorParams, 'EditTask'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const EditTask: React.FC<Props> = ({ navigation, route, task }) => {
  const selectActivityRef = useRef<any>(null);
  const taskGroupModalRef = useRef<any>(null);
  const addAttachmentPhotoRef = useRef<any>(null);
  const taskDescriptionRef = useRef<any>(null);
  const statusRef = useRef<any>(null);
  const linkedToRef = useRef<any>(null);
  const [chosedAttachments, setChosedAttachments] = useState<any[]>([]);
  const [chosedPhotos, setChosedPhotos] = useState<any[]>([]);
  const [type, setType] = useState('Comments');
  const [remoteId, setRemoteId] = useState<number | null>(null);
  const redirect = _.get(route, 'params.redirect', null);
  const scrollViewRef = useRef<any>(null);

  const dispatch = useDispatch();
  const states = useSelector(state => state.tasks, isEqual);

  const { mutateAsync: updateTask } = useUpdateTask();
  const { mutateAsync: deleteTaskMedia } = useDeleteTaskMedia();
  const { mutateAsync: deleteTaskAttachment } = useDeleteTaskAttachment();

  const deleteConfirmation = useDeleteConfirmation();

  const proposedStatus = task?.proposedStatus;

  const [uploadTaskAttachments] = Gql.useCreateManyTasksAttachmentsMutation();
  const [deleteOneAttachment] = Gql.useDeleteOneAttachmentMutation();

  const authData = useSelector(state => state.auth);
  const userMeData = useMemo(() => authData?.user, [authData?.user]);
  const isTaskOwner = useMemo(
    () => states?.task?.createdBy === userMeData?.id,
    [states?.task?.createdBy, userMeData?.id]
  );
  const project = useSelector(state => state.project);
  const role = taskAuth(project.projectUserRole ?? '');
  const TaskAssignees = states.task?.assignee?.map((ass: any) => ass?.remoteId) ?? [];
  const isAssigned = TaskAssignees.includes(userMeData?.id);
  const assignees = states?.task?.assignee;
  const disableValidateStatus =
    !isTaskOwner && project.projectUserRole !== 'CloudCoordinator' && project.projectUserRole !== 'ProjectOwner';
  const disableProposeStatus = disableValidateStatus && !isAssigned;

  const loadTaskDetail = useCallback(async () => {
    if (!task) return;
    try {
      const {
        group,
        assignees,
        copies,
        attachments: detailedAttachments,
        media: detailedMedia,
        owner,
        documents
      } = await task.getTaskDetails();

      const status = task?.status;
      setRemoteId(task?.remoteId);

      dispatch(
        taskActions.initialState({
          id: task?.id,
          title: task?.title,
          description: task?.description,
          dueDate: task?.dueDate,
          status: status as Gql.TaskStatusType,
          linkedDocument: documents,
          createdBy: owner,
          group: group,
          assignee: assignees,
          cc: copies,
          attachment: detailedAttachments,
          medias: detailedMedia
        })
      );
    } catch (error) {}
  }, [task, dispatch]);

  useEffect(() => {
    if (states?.setInitialState) {
      loadTaskDetail();
    }
  }, [states?.setInitialState, task]);

  const updateStatusUponView = useCallback(async () => {
    if (states.task?.status !== Gql.TaskStatusType.Open) return;
    const isUserAssigned = assignees?.some(item => item?.userId?.toString?.() === userMeData?.id);

    if (isUserAssigned) {
      dispatch(taskActions?.initialState({ ...states?.task, status: Gql.TaskStatusType.InProgress } as any));
      try {
        await updateTask({
          id: route?.params?.id,
          newTask: {
            status: Gql.TaskStatusType.InProgress
          },
          newAttachment: [],
          newMedias: []
        });
      } catch (error) {
        pushError(error);
      }
    }
  }, [assignees, states.task?.status, userMeData?.id, route?.params?.id]);

  const uploadPhotos = async (photos: ReactNativeFile[]) => {
    if (states?.task?.medias && states?.task?.medias?.length > 10) {
      return Alert.alert('Warning', 'Photos must be less than 10');
    }

    const sanitizedPhotos = photos?.map?.((photo: any) => ({
      id: photo.id,
      taskId: remoteId,
      uri: photo?.uri,
      name: photo.name,
      userId: parseInt(userMeData?.id ?? '0')
    }));

    const existingPhotos = states?.task?.medias?.map?.((photo: any) => ({
      id: photo.id,
      taskId: remoteId,
      name: photo.name,
      userId: parseInt(userMeData?.id ?? '0')
    }));

    try {
      return dispatch(
        taskActions?.initialState({
          ...states?.task,
          medias: [...sanitizedPhotos, ...(existingPhotos ?? [])]
        } as any)
      );
    } catch (error) {
      setChosedPhotos(chosedPhotos.filter(p => !photos.includes(p)));
    }
  };

  useEffect(() => {
    if (route?.params?.id) {
      updateStatusUponView();
    }
  }, [route?.params?.id, updateStatusUponView]);

  const onSubmit = async () => {
    if (states?.task?.assignee?.length === 0) {
      return Alert.alert('Assignee is Required');
    }

    const groupId = states?.task?.group?.remoteId;
    const document = states.task?.linkedDocument?.map?.((doc: any) => (doc?.remoteId ? doc?.remoteId : doc.id)) ?? [];
    const assignees = states?.task?.assignee?.map?.((user: any) => user?.remoteId);
    const ccs = states?.task?.cc?.map?.((user: any) => user?.remoteId);
    const createdBy = states?.task?.createdBy?.id;
    const title = states?.task?.title;
    const dueDate = states?.task?.dueDate?.toString();
    const status = states?.task?.status;
    const description = states?.task?.description;

    const newAttachment = states?.task?.attachment
      ?.filter?.((attachment: any) => !attachment?.id)
      ?.map?.((attachment: any) => {
        return {
          ...attachment,
          localTaskId: route?.params?.id
        };
      });
    const newMedias = states?.task?.medias
      ?.filter?.((media: any) => !media?.id)
      ?.map?.((media: any) => {
        return {
          ...media,
          localTaskId: route?.params?.id
        };
      });

    const newTask = {
      assignees,
      copies: ccs,
      documents: document,
      groupId,
      createdBy,
      title,
      dueDate,
      status,
      description
    };

    await updateTask(
      {
        id: route?.params?.id,
        newTask,
        newAttachment,
        newMedias
      },
      {
        onSuccess: () => {
          navigation.pop();
          pushSuccess('Successful update task');
          setChosedAttachments([]);
          setChosedPhotos([]);
          dispatch(taskActions.setInitialState(true));
        },
        onError: error => {
          pushError(error);
        }
      }
    );
  };

  const removeLinkedDocument = async (files: any) => {
    try {
      const removeDocument = states?.task?.linkedDocument?.filter((document: any) => document !== files);
      dispatch(taskActions?.initialState({ ...states?.task, linkedDocument: removeDocument } as any));
    } catch (error) {}
  };

  const chooseDocument = async () => {
    try {
      if (states?.task?.attachment && states?.task?.attachment?.length > 10) {
        return Alert.alert('Warning', 'Attachments must be less than 10');
      }

      if (!userMeData?.id) {
        return pushError('User not found');
      }

      const res = await DocumentPicker.pick({
        type: [DocumentPicker.types.pdf],
        allowMultiSelection: true
      });

      const destPaths = new Map(); // Store the destination paths for each document

      // Handle each document to ensure it is prepared for uploading
      for (const element of res) {
        try {
          const realURI: any = Platform.select({
            android: element.uri,
            ios: decodeURI(element.uri)
          });

          let [baseName, extension]: any = element?.name?.split(/(?=\.\w+$)/).map(part => part.replace(/\s+/g, '_'));
          let fileName = baseName;
          let fileCounter = 0;

          // Prepare the initial path for the file
          let destPath = `${RNFS.DocumentDirectoryPath}/${fileName}${extension}`;

          // Check if the file exists and append a counter before the extension
          while (await RNFS.exists(destPath)) {
            fileCounter++;
            destPath = `${RNFS.DocumentDirectoryPath}/${fileName}_${fileCounter}${extension}`;
          }

          // Copy the file to the new destination
          await RNFS.copyFile(realURI, destPath);

          // Store the new path in the map
          destPaths.set(realURI, destPath);
        } catch (fileError) {
          continue; // Skip this file if there is an error processing it
        }
      }

      // Now create attachment array after processing each file
      const newAttachments = res.map(doc => ({
        ...doc,
        uri: destPaths.get(doc.uri), // Replace doc.uri with the new destination path
        taskId: remoteId,
        userId: parseInt(userMeData?.id)
      }));

      // Check if adding all new attachments exceeds the limit
      const newAttachmentsCount = states?.task?.attachment ? states.task.attachment.length + res.length : res.length;
      if (newAttachmentsCount > 10) {
        return Alert.alert('Warning', 'Attachments must be less than 10');
      }

      dispatch(
        taskActions?.initialState({
          ...states?.task,
          attachment: [...(states?.task?.attachment ?? []), ...newAttachments]
        } as any)
      );
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
        // Handle the case where the user cancels the document picker
      } else {
        // Propagate the error if it's not a cancellation
        throw err;
      }
    }
  };

  const deleteTaskAttachments = async (files: Gql.TasksAttachment) => {
    return deleteConfirmation.showDeleteConfirmation({
      fileName: files?.name,
      action: async () => {
        try {
          files.id && (await deleteTaskAttachment(files.id));
          const removedAttachments = states?.task?.attachment?.filter((attachment: any) => attachment !== files);

          dispatch(
            taskActions?.initialState({
              ...states?.task,
              attachment: removedAttachments
            } as any)
          );
        } catch (error) {
          pushError(error.message);
        }
      }
    });
  };

  const deleteTaskMedias = async (files: Gql.TasksMedia) => {
    return deleteConfirmation.showDeleteConfirmation({
      fileName: files?.name,
      action: async () => {
        try {
          files.id && (await deleteTaskMedia(files.id));
          const removedPhotos = states?.task?.medias?.filter((photo: any) => photo !== files);

          dispatch(
            taskActions?.initialState({
              ...states?.task,
              medias: removedPhotos
            } as any)
          );
        } catch (error) {
          pushError(error.message);
        }
      }
    });
  };

  const handleInput = (value: any, name: string) => {
    dispatch(taskActions?.initialState({ ...states?.task, [name]: value } as any));
  };

  const goBack = () => {
    dispatch(taskActions?.closeAll());
    navigation.goBack();
  };

  const navigateToAssignee = useCallback(
    () =>
      navigation.push('TasksNav', {
        screen: 'AddAssignee',
        params: {
          ass: states?.task?.assignee ?? ([] as any)
        }
      }),
    [states?.task?.assignee]
  );

  const navigationToCc = useCallback(() => {
    return navigation.push('TasksNav', {
      screen: 'AddCc',
      params: {
        ass: states?.task?.cc ?? []
      }
    });
  }, [states?.task?.cc]);

  const navigationToPdftron = useCallback((attachment: TasksAttachmentModel) => {
    return navigation.navigate('PdftronNav', {
      screen: 'Pdftron',
      params: {
        id: attachment?.remoteId?.toString?.() ?? '',
        modules: 'TaskAttachment',
        ...(!attachment.remoteId && { fileUrl: attachment.fileUrl }),
        onPressBack: () => {
          dispatch(taskActions.OpenTaskDetailModal(task?.remoteId));
        },
        ...(!attachment.remoteId && attachment.fileUrl ? { fileUrl: attachment.fileUrl } : undefined)
      }
    });
  }, []);

  const navigationMediaToPdfTron = (files: TasksMediaModel) => {
    const params: any = {
      id: files?.remoteId?.toString?.() ?? '',
      name: files.name ?? '',
      onPressBack: () => {
        dispatch(taskActions.OpenTaskDetailModal(task?.remoteId));
      },
      ...(!files.remoteId && files.fileUrl ? { fileUrl: files.fileUrl } : undefined),
      modules: 'TaskMedia'
    };

    if (files.type === 'mp4' || files.type === 'MOV') {
      return navigation.navigate('VideoNav', {
        screen: 'VideoViewer',
        params: params
      });
    } else {
      return navigation.navigate('PdftronNav', {
        screen: 'Pdftron',
        params: params
      });
    }
  };

  const navigationLinkedTo = (item: ProjectDocumentModel) => {
    dispatch(taskActions?.setInitialState(false));
    return navigation.navigate('PdftronNav', {
      screen: 'Pdftron',
      params: {
        id: item.remoteId?.toString?.() ?? '',
        modules: 'CloudDocs',
        ...(item.localFileUrl && { fileUrl: item?.localFileUrl as any }),
        ...(!item.remoteId && { fileUrl: item?.fileUrl as any }),
        addedBy: item?.addedBy as number
      }
    });
  };

  const redirectHandler = useCallback(() => {
    if (!redirect || states?.isFetchingComments) return;

    if (redirect === 'comment') {
      scrollViewRef?.current?.scrollToEnd({ animated: true });
    }
  }, [redirect, states?.isFetchingComments]);

  useEffect(() => {
    redirectHandler();
  }, [redirectHandler]);

  function syncAndDownload(arg0: {
    syncMutateOptions: { dispatchStatus: boolean };
    offlineDownloadOptions: { id: string; dispatchStatus: boolean };
  }) {
    throw new Error('Function not implemented.');
  }

  return (
    <FlashList
      data={[1]}
      estimatedItemSize={1}
      renderItem={({ item }) => {
        return (
          <Box style={styles.container} ref={scrollViewRef}>
            <Header goBack={goBack} role={project.projectUserRole ?? ''} onSubmit={onSubmit} />
            <HStack py={4} px={8} justifyContent="center" alignItems="center">
              <Button
                isDisabled={proposedStatus ? disableValidateStatus : disableProposeStatus}
                ml={2}
                shadow={2}
                onPress={() => {
                  statusRef?.current?.pushModal();
                }}
                backgroundColor={COLORS.neutrals.white}
              >
                <HStack alignItems={'center'} justifyContent={'center'}>
                  <Text color={'#0695D7'} mr={2}>
                    Action
                  </Text>
                  <Icon name="chevron-down-primary-light" />
                </HStack>
              </Button>
              {task?.memoUrl && (
                <Button
                  variant="primary"
                  // isLoading={allLoading}
                  ml={2}
                  onPress={() => {
                    navigation.navigate('PdftronNav', {
                      screen: 'Pdftron',
                      params: {
                        id: task?.remoteId ?? '',
                        modules: 'Task',
                        // addedBy: item?.addedBy,
                        role: project.projectUserRole as string,
                        assigneeIds: null
                      }
                    });
                  }}
                >
                  Memo
                </Button>
              )}

              <Box flex={1}>
                {project.projectUserRole !== 'CanView' && (
                  <Box flexDirection="row" justifyContent="flex-end">
                    <Button onPress={onSubmit} variant="primary">
                      Save
                    </Button>
                  </Box>
                )}
              </Box>
            </HStack>

            {/* Upper Part */}
            {/* Body */}
            <Box p={6}>
              <DescriptionInput
                value={states?.task?.title}
                onChangeText={(value: string) => handleInput(value, 'title')}
                isTaskOwner={isTaskOwner}
                role={role?.tasks?.canEdit}
                textType={'Title'}
              />
              <DescriptionInput
                value={states?.task?.description}
                onChangeText={(value: string) => handleInput(value, 'description')}
                isTaskOwner={isTaskOwner}
                role={role?.tasks?.canEdit}
                textType={'Description'}
                placeholder="Add your description here..."
              />
              <Divider style={styles?.divider} />
              <CreatedBy data={states.task?.createdBy as any}></CreatedBy>
              <Divider style={styles?.divider} />
              <Calendar
                date={states?.task?.dueDate ?? ('' as any)}
                setDate={date => handleInput(date, 'dueDate')}
                role={role?.tasks?.canEdit}
                isTaskOwner={isTaskOwner}
              />
              <Divider style={styles?.divider} />
              <Assignee
                loadingAssignee={false}
                isTaskOwner={isTaskOwner}
                role={role?.tasks?.canEdit}
                navigationToAssignee={navigateToAssignee}
                assignees={states?.task?.assignee as any}
                // loading={allLoading}
              />
              <Divider style={styles?.divider} />
              <Cc
                cc={states?.task?.cc as any}
                loadingCc={false}
                isTaskOwner={isTaskOwner}
                role={role?.tasks?.canEdit}
                navigationToCc={navigationToCc}
                // loading={allLoading}
              />
              <Divider style={styles?.divider} />
              <Status
                isTaskOwner={isTaskOwner}
                role={role?.tasks?.canEdit}
                proposedStatus={proposedStatus}
                status={states?.task?.status as Gql.TaskStatusType}
                statusRef={statusRef}
                // loading={allLoading}
                ownerId={states?.task?.createdBy?.id ?? ''}
                userMeData={userMeData as any}
              />
              <Divider style={styles?.divider} />
              <Group
                taskGroupModalRef={taskGroupModalRef}
                // allLoading={allLoading}
                isTaskOwner={isTaskOwner}
                role={role?.tasks?.canEdit}
                groupName={states?.task?.group?.title ?? ''}
              />
              <Divider style={styles?.divider} />
              <Attachment
                // allLoading={allLoading}
                isTaskOwner={isTaskOwner}
                role={role?.tasks?.canEdit}
                chooseDocument={chooseDocument}
                deleteTaskAttachment={deleteTaskAttachments}
                attachment={states?.task?.attachment ?? []}
                chosedAttachments={chosedAttachments}
                isAssigned={isAssigned}
                navigationToPdftron={navigationToPdftron}
              />
              <Divider style={styles?.divider} />
              <Media
                addAttachmentPhotoRef={addAttachmentPhotoRef}
                // allLoading={allLoading}
                isTaskOwner={isTaskOwner}
                role={role.tasks?.canEdit}
                deleteTaskMedias={deleteTaskMedias}
                isAssigned={isAssigned}
                navigationMediaToPdfTron={navigationMediaToPdfTron}
                photos={states?.task?.medias ?? []}
              />
              <Divider style={styles?.divider} />
              <LinkedTo
                // allLoading={allLoading}
                isTaskOwner={isTaskOwner}
                role={role?.tasks?.canEdit}
                navigateToPdfTron={navigationLinkedTo}
                removeLinkedDocument={removeLinkedDocument}
                linkedDocument={states?.task?.linkedDocument ?? []}
                isAssigned={isAssigned}
                linkedToRef={linkedToRef}
              />
              <Box flexDirection="row" justifyContent="space-between" mt={5}>
                <Text fontWeight={600}>Activity</Text>
                <Box>
                  <HStack alignItems="center">
                    <Text color="neutrals.gray90"> Show: </Text>
                    <TouchableOpacity
                      onPress={() => {
                        selectActivityRef?.current?.pushModal();
                      }}
                      style={styles.box}
                    >
                      <HStack alignItems="center">
                        <Text lineHeight="34">{type}</Text>
                        {/* <Icon name="chevron-down-grey" /> */}
                      </HStack>
                    </TouchableOpacity>
                  </HStack>
                </Box>
              </Box>
              {type === 'Comments' ? (
                <CommentComponent selectedTaskId={task?.remoteId} projectId={project.projectId} />
              ) : (
                <ActivityComponent selectedTaskId={task?.remoteId} />
              )}
              <SelectActivityModal ref={selectActivityRef} onChange={value => setType(value)} />
              <TaskGroupModal ref={taskGroupModalRef} action="editTask" projectId={project?.projectId} />
              <TaskPhotosModal
                ref={addAttachmentPhotoRef}
                chosedAttachment={(photo: any) => {
                  uploadPhotos(photo);
                }}
                state="editTask"
              />
              <TaskStatusModal
                ref={statusRef}
                proposedStatus={task?.proposedStatus}
                onSaved={async () => {
                  loadTaskDetail();
                }}
              />
              <LinkedToModal
                ref={linkedToRef}
                setLinkedDocument={file => {
                  const newLinkedDocument = [...(states.task?.linkedDocument ?? []), file];
                  dispatch(taskActions.initialState({ ...states.task, linkedDocument: newLinkedDocument } as any));
                }}
              />
              <TaskDescriptionModal
                ref={taskDescriptionRef}
                value={states?.task?.description ?? ''}
                isTaskOwner={isTaskOwner}
                role={role?.tasks?.canEdit}
                onChange={(value: string) =>
                  dispatch(
                    taskActions?.initialState({
                      ...states?.task,
                      description: value
                    } as any)
                  )
                }
                textType={'Description'}
              />
            </Box>
          </Box>
        );
      }}
      refreshControl={
        <RefreshControl
          refreshing={false}
          onRefresh={async () => {
            syncAndDownload({
              syncMutateOptions: { dispatchStatus: true },
              offlineDownloadOptions: { id: '', dispatchStatus: true }
            });
          }}
        />
      }
    ></FlashList>
  );
};

const EnhancedEditTask = withObservables(['task', 'route'], ({ task, route }: any) => ({
  task: database.collections.get('tasks').findAndObserve(route?.params?.id)
}))(EditTask);

const styles = StyleSheet.create({
  box: {
    borderWidth: 1,
    borderColor: '#E8E8E8',
    borderRadius: 36,
    width: 114,
    height: 38,
    alignItems: 'center'
  },
  container: {
    backgroundColor: '#FFF',
    flex: 1
  },
  divider: {
    marginVertical: 14,
    color: '#E8E8E8'
  },
  button: {
    height: 38,
    width: 68,
    alignSelf: 'flex-end',
    marginRight: 25,
    marginTop: 1
  }
});

export default EnhancedEditTask;
