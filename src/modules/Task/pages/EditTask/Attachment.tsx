import { FlashList } from '@shopify/flash-list';
import { Icon } from '@src/commons';
import { Box, HStack, Text } from 'native-base';
import React, { memo, useMemo } from 'react';
import { TouchableOpacity } from 'react-native';
import { CreateTaskAttachmentInput, TaskAttachment } from 'task-attachment';

type Props = {
  attachment: TaskAttachment[];
  chosedAttachments: any;

  navigationToPdftron: (attachment: CreateTaskAttachmentInput, module: 'TaskAttachment' | 'TaskMedia') => void;
  isTaskOwner?: boolean;
  role: any;
  allLoading?: boolean;

  deleteTaskAttachment: (files: any) => void;
  chooseDocument: () => void;
  isAssigned: boolean;
};

const Attachment = ({
  attachment,
  chosedAttachments,
  navigationToPdftron,
  allLoading,
  deleteTaskAttachment,
  isTaskOwner,
  role,
  chooseDocument,
  isAssigned
}: Props) => {
  const AttachmentItem = useMemo(
    () =>
      ({ files, onPress, onDelete }: any) => (
        <HStack alignItems={'center'} justifyContent={'space-between'}>
          <Text
            numberOfLines={1}
            ellipsizeMode="tail"
            w={'80%'}
            color={!files.fileUrl ? 'black' : 'blue.500'}
            onPress={onPress}
            disabled={allLoading}
          >
            {files.name}
          </Text>
          <TouchableOpacity onPress={onDelete} disabled={!isTaskOwner && !role}>
            <Icon name="garbage" />
          </TouchableOpacity>
        </HStack>
      ),
    []
  );

  return (
    <HStack>
      <Box flex={1} flexDirection="row" alignItems="center">
        <Icon name="attached" />
        <Text color="neutrals.gray90"> Attached</Text>
      </Box>
      <Box flex={2}>
        <FlashList
          data={attachment}
          estimatedItemSize={10}
          estimatedFirstItemOffset={0}
          renderItem={({ item: files, index }) => (
            <AttachmentItem
              files={files}
              onPress={() => navigationToPdftron(files as any, 'TaskAttachment')}
              onDelete={() => deleteTaskAttachment(files)}
              key={index}
            />
          )}
          keyExtractor={(_, index) => `attachment_${index}`}
        />

        {chosedAttachments.length + attachment.length > 10 && <Text color={'red.500'}>Attachments exceeded 10</Text>}

        <TouchableOpacity onPress={chooseDocument} disabled={(!isTaskOwner && !role && !isAssigned) || allLoading}>
          {(!isTaskOwner && !role && !isAssigned) || allLoading ? (
            <Text color="gray.400"> + Add attachment</Text>
          ) : (
            <Text color={!role && !isTaskOwner ? 'gray.400' : 'neutrals.gray90'}> + Add attachment</Text>
          )}
        </TouchableOpacity>
      </Box>
    </HStack>
  );
};

export default memo(Attachment);
