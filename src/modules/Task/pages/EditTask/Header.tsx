import AppBar from '@src/commons/AppBar';
import { Box, Button } from 'native-base';
import React, { memo } from 'react';
import { StyleSheet } from 'react-native';

type Props = {
  goBack: () => void;
  loading?: boolean;
  role: string;
  onSubmit: () => Promise<any>;
};

const Header = ({ goBack, loading, role, onSubmit }: Props) => {
  return (
    <>
      <AppBar goBack onGoBack={goBack} barStyle={'dark-content'} title="Task Details" noRight onPressTitle={() => {}} />
      {/* <Box flex={1}>
        {role !== 'CanView' && (
          <Box flexDirection="row" mt={4} justifyContent="flex-end">
            <Button
              onPress={onSubmit}
              isLoading={loading}
              variant="primary"
              style={styles.button}
            >
              Save
            </Button>
          </Box>
        )}
      </Box> */}
    </>
  );
};

const styles = StyleSheet.create({
  button: {
    height: 38,
    width: 68,
    alignSelf: 'flex-end',
    marginRight: 25,
    marginTop: 1
  }
});

export default memo(Header);
