import React, { memo, useMemo } from 'react';
import { Box, HStack, Text, VStack } from 'native-base';
import { Icon } from '@src/commons';
import { FlashList } from '@shopify/flash-list';
import { TouchableOpacity } from 'react-native';
import ProjectDocumentModel from '@src/database/model/project-document.model';

type DocumentItemProps = {
  document: any;
  onNavigate: () => void;
  onRemove: () => void;
  isDisabled: boolean;
};

type LinkedToProps = {
  linkedDocument: any[];
  linkedToRef: any;
  removeLinkedDocument: (doc: any) => void;
  allLoading?: boolean;
  isTaskOwner?: boolean;
  role: any;
  isAssigned: boolean;
  navigateToPdfTron: (item: ProjectDocumentModel) => void;
};

const LinkedTo = ({
  linkedDocument,
  linkedToRef,
  removeLinkedDocument,
  allLoading,
  isTaskOwner,
  role,
  isAssigned,
  navigateToPdfTron
}: LinkedToProps) => {
  const LinkedDocumentItem = useMemo(
    () =>
      ({ document, onNavigate, onRemove, isDisabled }: DocumentItemProps) => (
        <HStack alignItems={'center'} justifyContent={'space-between'}>
          <TouchableOpacity onPress={onNavigate} disabled={isDisabled}>
            <Text numberOfLines={1} ellipsizeMode="tail" maxWidth={'90%'} color={'blue.500'}>
              {document?.name}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={onRemove} disabled={isDisabled}>
            {isDisabled ? null : <Icon name="garbage" />}
          </TouchableOpacity>
        </HStack>
      ),
    []
  );

  return (
    <HStack>
      <Box flex={1} flexDirection="row" alignItems="center">
        <Icon name="link-document" />
        <Text color="neutrals.gray90"> Linked to</Text>
      </Box>
      <Box flex={2}>
        <VStack>
          <FlashList
            data={linkedDocument}
            estimatedItemSize={60}
            renderItem={({ item }) => (
              <LinkedDocumentItem
                document={item}
                onNavigate={() => navigateToPdfTron(item)}
                onRemove={() => removeLinkedDocument(item)}
                isDisabled={allLoading || (!isTaskOwner && !role)}
              />
            )}
            keyExtractor={item => item.id.toString()}
          />

          {linkedDocument.length < 5 && (
            <TouchableOpacity
              onPress={() => linkedToRef?.current?.pushModal()}
              disabled={!isTaskOwner && !role && !isAssigned}
            >
              {<Text color={!role && !isTaskOwner ? 'gray.400' : 'neutrals.gray90'}> + Add Linked document</Text>}
            </TouchableOpacity>
          )}
        </VStack>
      </Box>
    </HStack>
  );
};

export default memo(LinkedTo);
