import { TaskStatusType, User } from '@src/api/graphql';
import { Icon } from '@src/commons';
import { taskActions } from '@src/slice/tasks.slice';
import { useDispatch } from '@src/store';
import { get } from 'lodash';
import { Box, HStack, Text } from 'native-base';
import React, { memo, useMemo } from 'react';
import { TouchableOpacity } from 'react-native';

type Props = {
  isTaskOwner: boolean;
  role: any;
  loading?: boolean;
  userMeData: User;
  statusRef: any;
  ownerId: string;
  status: TaskStatusType;
  proposedStatus?: any;
};

const Status = ({ isTaskOwner, role, loading, proposedStatus, ownerId, statusRef, status }: Props) => {
  const dispatch = useDispatch();
  const color = proposedStatus === 'Hold' ? 'in-review' : 'approved';
  let proposedIconName: any;

  const getStatus = (type: string) => useMemo(() => get(statusComponent, type), [status]);

  if (proposedStatus === 'Hold') {
    proposedIconName = 'propose-onHold';
  } else if (proposedStatus === 'Completed') {
    proposedIconName = 'propose-closed';
  }

  const statusComponent = {
    Completed: (
      <HStack space={2} alignItems="center">
        <Icon name={proposedStatus ? proposedIconName : 'approved'} />
        <Text color={'#000'}>
          {proposedStatus ? `Propose > ${proposedStatus === 'Completed' ? 'Closed' : proposedStatus}` : 'Closed'}
        </Text>
      </HStack>
    ),
    Hold: (
      <HStack space={2} alignItems="center">
        <Icon name={proposedIconName ? proposedIconName : 'in-review'} />
        <Text color={'#000'}>{proposedStatus ? `Propose > ${proposedStatus}` : 'Hold'}</Text>
      </HStack>
    ),
    InProgress: (
      <HStack space={2} alignItems="center">
        <Icon name={proposedStatus !== null ? color : 'in-progress'} />
        <Text color={'#000'}>{proposedStatus ? `Propose > ${proposedStatus}` : 'In Progress'}</Text>
      </HStack>
    ),
    Open: (
      <HStack space={2} alignItems="center">
        <Icon name={proposedStatus !== null ? color : 'reject'} />
        <Text color={'#000'}>{proposedStatus ? `Propose > ${proposedStatus}` : 'Open'}</Text>
      </HStack>
    )
  };

  if (loading) return;

  return (
    <HStack>
      <Box flex={1} flexDirection="row" alignItems="center">
        <Icon name="check-square" />
        <Text color="neutrals.gray90"> Status</Text>
      </Box>
      <Box flex={2}>
        <TouchableOpacity
          disabled={(!isTaskOwner && !role) || loading}
          onPress={() => {
            // if (proposedStatus !== null) return;

            statusRef?.current?.pushModal();
            // dispatch(taskActions.setInitialState(true));
          }}
        >
          <Text> {getStatus(proposedStatus || status)}</Text>
        </TouchableOpacity>
      </Box>
    </HStack>
  );
};

export default memo(Status);
