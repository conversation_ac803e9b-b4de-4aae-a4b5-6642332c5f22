import { FlashList } from '@shopify/flash-list';
import { Icon } from '@src/commons';
import { Box, HStack, Text } from 'native-base';
import { UserDetail } from 'project-user';
import React, { memo, useMemo } from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import Avatar from '@src/commons/Avatar';

type Props = {
  navigationToCc: () => void;
  isTaskOwner?: boolean;
  role: any;
  loading?: boolean;
  cc: UserDetail[] | [];
  loadingCc: boolean;
  type: 'add' | 'edit';
};

const Cc = ({ navigationToCc, isTaskOwner, role, loading, cc, loadingCc, type }: Props) => {
  const CcItem = useMemo(
    () =>
      ({ user }: { user: UserDetail }) => (
        <HStack key={user?.id} space={2} flexWrap="wrap" alignItems="center" mt={1}>
          <Avatar
            assignees={[{ assigneeNo: '1', name: user.name, avatarUrl: user.avatarUrl }]}
            maxVisible={0}
            type={'task'}
          />
          <Text numberOfLines={1} ellipsizeMode="tail" maxWidth={'80%'} ml={1}>
            {user.name}
          </Text>
        </HStack>
      ),
    []
  );

  const canAddCc = useMemo(() => {
    return type === 'edit' ? (!isTaskOwner && !role) || loading : false; // Added return statement
  }, [type, isTaskOwner, role, loading]);

  return (
    <HStack>
      <Box style={styles?.container}>
        <Icon name="assignee" />
        <Text color="neutrals.gray90"> Cc</Text>
      </Box>
      <Box flex={2}>
        <TouchableOpacity onPress={navigationToCc} disabled={canAddCc}>
          <FlashList
            data={cc}
            estimatedItemSize={20}
            renderItem={({ item: user }) => <CcItem user={user} />}
            keyExtractor={(item, index) => `${item.id}_${index}`}
          />
          {<Text color={canAddCc ? 'gray.400' : 'neutrals.gray90'}> + Add cc</Text>}
        </TouchableOpacity>
      </Box>
    </HStack>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center'
  }
});

export default memo(Cc);
