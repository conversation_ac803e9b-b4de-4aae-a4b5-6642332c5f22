import React, { memo, useMemo } from 'react';
import { Text, HStack, Box, VStack } from 'native-base';
import { FlashList } from '@shopify/flash-list';
import { CreateTaskMediaInput, TaskMedia } from 'task-media';
import { TouchableOpacity } from 'react-native';
import { Icon } from '@src/commons';

type Props = {
  photos: TaskMedia[];
  navigationMediaToPdfTron: (files: CreateTaskMediaInput, module: 'TaskAttachment' | 'TaskMedia') => void;
  deleteTaskMedias: (files: any) => void;
  addAttachmentPhotoRef: any;
  isTaskOwner?: boolean;
  role: any;
  isAssigned: boolean;
  allLoading?: boolean;
};

type MediaItemProps = {
  files: TaskMedia;
  onPress: () => void;
  onDelete: () => void;
};

const Media = ({
  photos,
  addAttachmentPhotoRef,
  allLoading,
  deleteTaskMedias,
  isAssigned,
  isTaskOwner,
  navigationMediaToPdfTron,
  role
}: Props) => {
  const MediaItem = useMemo(
    () =>
      ({ files, onPress, onDelete }: MediaItemProps) => (
        <HStack alignItems={'center'} justifyContent={'space-between'}>
          <Text
            numberOfLines={1}
            ellipsizeMode="tail"
            w={'80%'}
            color={!files.fileUrl ? 'black' : 'blue.500'}
            onPress={onPress}
          >
            {files?.name}
          </Text>
          <HStack alignItems={'flex-end'}>
            {files.type === 'mp4' || files.type === 'MOV' ? <Icon name="video" /> : null}
            <TouchableOpacity onPress={onDelete}>
              <Icon name="garbage" />
            </TouchableOpacity>
          </HStack>
        </HStack>
      ),
    []
  );

  return (
    <HStack>
      <Box flex={1} flexDirection="row" alignItems="center">
        <Icon name="image" />
        <Text color="neutrals.gray90" ml={1}>
          Media
        </Text>
      </Box>
      <Box flex={2}>
        <VStack>
          <FlashList
            data={photos}
            estimatedItemSize={60}
            renderItem={({ item }) => (
              <MediaItem
                files={item}
                onPress={() => navigationMediaToPdfTron(item, 'TaskMedia')}
                onDelete={() => deleteTaskMedias(item)}
              />
            )}
            keyExtractor={(item, index) => `media_${index}`}
          />
          {photos?.length > 20 && <Text color={'red.500'}>Photos exceeded 20</Text>}

          <TouchableOpacity
            onPress={() => {
              addAttachmentPhotoRef?.current?.pushModal();
            }}
            disabled={(!isTaskOwner && !role && !isAssigned) || allLoading}
          >
            {<Text color={!role && !isTaskOwner ? 'gray.400' : 'neutrals.gray90'}> + Add photos</Text>}
          </TouchableOpacity>
        </VStack>
      </Box>
    </HStack>
  );
};

export default memo(Media);
