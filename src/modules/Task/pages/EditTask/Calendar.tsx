import { Icon } from '@src/commons';
import { CalendarPicker } from '@src/inputs/CalendarPicker';
import { Box, HStack, Text } from 'native-base';
import React, { memo } from 'react';

type Props = {
  date: string;
  setDate: (date: Date) => void;
  role?: boolean;
  isTaskOwner?: boolean;
};

const Calendar = ({ date, setDate, role, isTaskOwner }: Props) => {
  return (
    <HStack alignItems="center">
      <HStack flex={1} alignItems="center">
        <Icon name="calendar-grey" />
        <Text color="neutrals.gray90" ml={1.5} flex={1}>
          Due date
        </Text>
      </HStack>
      <Box flex={2}>
        <CalendarPicker setSelectedDate={setDate} date={date} role={role} isTaskOwner={isTaskOwner} />
      </Box>
    </HStack>
  );
};

export default memo(Calendar);
