import { Icon } from '@src/commons';
import { Box, HStack, Text } from 'native-base';
import React, { memo } from 'react';
import Avatar from '@src/commons/Avatar';
import ProjectUserModel from '@src/database/model/project-user.model';

type Props = {
  data: {
    id: string;
    name: string;
    _raw: ProjectUserModel;
  };
};

const CreatedBy: React.FC<Props> = ({ data }) => {
  const user = data?._raw;

  return (
    <HStack mt={1} mb={1} alignItems="center">
      {/* Created By Label */}
      <Box flex={1} flexDirection="row" alignItems="center">
        <Icon name="assignee" />
        <Text ml={1} fontSize="sm">
          Created By
        </Text>
      </Box>

      {/* User Information */}
      <Box flex={2}>
        <HStack key={data?.id} space={2} flexWrap="wrap" alignItems="center" mt={1}>
          <Avatar
            assignees={[
              {
                avatarUrl: user?.avatarUrl || '',
                assigneeNo: '1',
                name: user?.name || 'Unknown User'
              }
            ]}
            maxVisible={0}
            type="task"
          />
          <Text numberOfLines={1} ellipsizeMode="tail" maxWidth="80%" fontSize="sm" ml={1}>
            {user?.name || 'Unknown User'}
          </Text>
        </HStack>
      </Box>
    </HStack>
  );
};

export default memo(CreatedBy);
