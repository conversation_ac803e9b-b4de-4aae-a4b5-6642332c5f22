import { Icon } from '@src/commons';
import { Box, HStack, Text, VStack } from 'native-base';
import React, { memo } from 'react';
import { TouchableOpacity } from 'react-native';

type Props = {
  isTaskOwner: boolean;
  role: any;
  allLoading?: boolean;
  taskGroupModalRef: any;
  groupName: string;
};

const Group = ({ isTaskOwner, role, allLoading, taskGroupModalRef, groupName }: Props) => {
  return (
    <HStack>
      <Box flex={1} flexDirection="row" alignItems="center">
        <Icon name="group" />
        <Text color="neutrals.gray90"> Group</Text>
      </Box>
      <Box flex={2}>
        <TouchableOpacity
          disabled={(!isTaskOwner && !role) || allLoading}
          onPress={() => taskGroupModalRef?.current?.pushModal?.()}
        >
          <VStack>
            <Text color={!role && !isTaskOwner ? 'gray.400' : 'black'}>{groupName ?? ''}</Text>
          </VStack>
        </TouchableOpacity>
      </Box>
    </HStack>
  );
};

export default memo(Group);
