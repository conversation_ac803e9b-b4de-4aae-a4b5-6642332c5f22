import { Icon } from '@src/commons';
import { Box, HStack, Text } from 'native-base';
import React, { memo, useMemo } from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import { UserDetail } from 'project-user';
import { FlashList } from '@shopify/flash-list';
import Avatar from '@src/commons/Avatar';

type Props = {
  navigationToAssignee: () => void;
  isTaskOwner?: boolean;
  role: any;
  loading?: boolean;
  assignees: UserDetail[] | [];
  loadingAssignee: boolean;
  type?: 'add' | 'edit';
};

const Assignee = ({ navigationToAssignee, isTaskOwner, role, loading, assignees, loadingAssignee, type }: Props) => {
  const AssigneeItem = useMemo(
    () =>
      ({ requestedUser }: { requestedUser: UserDetail }) => (
        <HStack key={requestedUser?.id} space={2} flexWrap="wrap" alignItems="center" mt={1}>
          <Avatar
            assignees={[{ assigneeNo: '1', name: requestedUser.name, avatarUrl: requestedUser.avatarUrl }]}
            maxVisible={0}
            type={'task'}
          />
          <Text numberOfLines={1} ellipsizeMode="tail" maxWidth={'80%'} ml={1}>
            {requestedUser.name}
          </Text>
        </HStack>
      ),
    []
  );

  const canAddAssignee = useMemo(() => {
    return type === 'edit' ? (!isTaskOwner && !role) || loading : false; // Added return statement
  }, [type, isTaskOwner, role, loading]);

  return (
    <HStack>
      <Box style={styles?.container}>
        <Icon name="assignee" />
        <Text color="neutrals.gray90"> Assignee</Text>
      </Box>
      <Box flex={2}>
        <TouchableOpacity onPress={navigationToAssignee} disabled={canAddAssignee}>
          <FlashList
            data={assignees}
            estimatedItemSize={20}
            renderItem={({ item: requestedUser }) => <AssigneeItem requestedUser={requestedUser} />}
            keyExtractor={(item, index) => item.id || index.toString()}
          />
          {<Text color={canAddAssignee ? 'gray.400' : 'neutrals.gray90'}> + Add assignee</Text>}
        </TouchableOpacity>
      </Box>
    </HStack>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center'
  }
});

export default memo(Assignee);
