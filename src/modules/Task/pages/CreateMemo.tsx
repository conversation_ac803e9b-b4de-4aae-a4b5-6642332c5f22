import { AppBar, Checkbox, Content, Footer, Modal } from '@commons';

import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps, useFocusEffect } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { LanguageType } from '@src/api/graphql';
import { pushError, pushSuccess } from '@src/configs';
import { COLORS } from '@src/constants';
import { AvatarPicker, SelectInput, TextInput } from '@src/inputs';
import { useDispatch, useSelector } from '@src/store';
import { RootNavigatorParams, TaskNavigatorParams } from '@src/types';
import { Field, Formik, FormikProps } from 'formik';
import { Box, Button, HStack, ScrollView, Text, VStack } from 'native-base';
import React, { useCallback, useRef, useState } from 'react';
import { Image, InteractionManager, Platform, StyleSheet } from 'react-native';
import { required, composeValidators } from '@configs/utils';
import RNFS from 'react-native-fs';

import CollapsibleAttachmentList from '../components/CollapsibleAttachmentList';
import PdfThumbnail from 'react-native-pdf-thumbnail';

type Props = CompositeScreenProps<
  BottomTabScreenProps<TaskNavigatorParams, 'CreateMemo'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const CreateMemo: React.FC<Props> = ({ route, navigation }) => {
  const modalRef = useRef<any>(null);
  const id = route.params?.id;
  const formRef = useRef<FormikProps<FormValues>>(null);

  const [attachmentIds, setAttachmentIds] = useState<string[]>([]);
  const [documentIds, setDocumentIds] = useState<string[]>([]);
  const [mediaIds, setMediaIds] = useState<string[]>([]);
  const [isPreview, setIsPreview] = useState(false);
  const [thumbnail, setThumbnail] = useState<string>('');

  const { data, loading: fetchingMe } = Gql.useGetUserMeQuery({});

  useFocusEffect(
    useCallback(() => {
      InteractionManager.runAfterInteractions(async () => {});
    }, [])
  );

  //generate memo mutation
  const [generateMemo, { loading: generating, data: memoData }] = Gql.useGenerateMemoMutation({
    onError: e => {
      pushError(e);
    },
    onCompleted: res => {
      if (!isPreview) {
        pushSuccess('Memo has been successfully sent.');
        navigation.goBack();
      } else {
        // onPreview();
        navigation.navigate('PdftronNav', {
          screen: 'Pdftron',
          params: {
            id: res?.generateMemo?.id ?? '',
            modules: 'Task',
            isPreview: true
          }
        });
      }
    }
  });

  //get task data
  const {
    data: taskData,
    refetch,
    loading: fetchingTask
  } = Gql.useGetTaskQuery({
    fetchPolicy: 'cache-and-network',
    skip: !id,
    variables: {
      id: id
    },
    onError: e => {
      pushError(e);
    }
  });

  const attachments = taskData?.task?.attachments?.nodes || [];
  const medias = taskData?.task?.medias?.nodes || [];
  const documents = taskData?.task?.documents?.nodes || [];

  const onSubmit = async (values: any) => {
    // const { refNo, logoUrl, languageType } = values;
    const url = typeof logoUrl === 'string' ? 'logoUrl' : 'customUrl';

    await generateMemo({
      variables: {
        input: {
          taskId: route.params?.id,
          [url]: logoUrl,
          companyName: values.companyName,
          refNo: refNo,
          additionalDetails: values.additionalDetails,
          attachmentIds,
          languageType: languageType,
          mediaIds,
          documentIds,
          isPreview: isPreview,
          hasStamp: values?.stamp,
          hasSignature: values?.signature
        }
      }
    });
  };

  const downloadFileAndConvertToContentUri = async (httpsUrl: any) => {
    try {
      // Create a unique file path in the app's cache directory
      const filePath = `${RNFS.CachesDirectoryPath}/${Math.random().toString(36).substring(7)}.pdf`;

      // Download the file from the HTTPS URL
      const response = await RNFS.downloadFile({
        fromUrl: httpsUrl,
        toFile: filePath
      }).promise;

      if (response.statusCode === 200) {
        // Get the content URI for the downloaded file
        const contentUri = `file://${filePath}`;
        return contentUri;
      } else {
        return null;
      }
    } catch (error) {
      return null;
    }
  };

  const generateThumbnail = async (url: string) => {
    if (!url) return;
    const page = 0;
    if (Platform.OS === 'android') {
      const contentUri = (await downloadFileAndConvertToContentUri(url)) as any;
      const { uri } = await PdfThumbnail.generate(contentUri, page);
      return uri;
    } else if (Platform.OS === 'ios') {
      const { uri } = await PdfThumbnail.generate(url, page);
      return uri;
    }
  };

  const onPreview = async () => {
    modalRef?.current?.pushModal();

    await generateThumbnail(memoData?.generateMemo?.previewMemoUrl as string).then((uri: any) => {
      setThumbnail(uri);
    });
  };

  if (!data) return null;

  const initialValues = {
    logoUrl: data?.getUserMe?.company?.logoUrl,
    companyName: data?.getUserMe?.company?.name,
    stamp: true,
    signature: true,
    languageType: LanguageType.BahasaMelayu
  };

  const loading = generating || fetchingMe || fetchingTask;

  return (
    <Box flex={1} bg="#FFFFFF">
      <AppBar
        onPressTitle={() => null}
        header
        goBack
        onGoBack={() => {
          navigation.goBack();
        }}
        title="Generate Memo"
        noRight
      />
      <Box flex={1} bg="#FFFFFF">
        <ScrollView w={['100%', '100%']} h="80">
          <Box flex={1} bg="#FFFFFF">
            <Formik onSubmit={onSubmit} initialValues={initialValues} innerRef={formRef}>
              {() => {
                return (
                  <Box flex={1} height={600}>
                    <Content pt={5}>
                      <Field
                        avatarContainer={{ flexDirection: 'row', marginTop: 10 }}
                        autoFocus
                        name="logoUrl"
                        label="Company Logo"
                        component={AvatarPicker}
                      />
                      <Field autoFocus name="companyName" label="Company Name" component={TextInput} />
                      <Field
                        autoFocus
                        name="refNo"
                        label="Reference No."
                        component={TextInput}
                        validate={composeValidators(required)}
                      />
                      <Field
                        name="languageType"
                        label="Language"
                        placeholder=""
                        options={[
                          {
                            label: LanguageType.English,
                            value: LanguageType.English
                          },
                          {
                            label: 'Bahasa Melayu',
                            value: LanguageType.BahasaMelayu
                          }
                        ]}
                        component={SelectInput}
                      />
                      <Field autoFocus label="Additional Details" name="additionalDetails" component={TextInput} />

                      <HStack space={5} alignItems={'center'}>
                        <Field
                          name="stamp"
                          component={Checkbox}
                          checked={true}
                          onChange={(val: boolean) => formRef.current?.setFieldValue('stamp', val)}
                        />
                        <Text flex={1} variant="bodyReg" mb={2}>
                          Stamp
                        </Text>

                        <Field
                          name="signature"
                          component={Checkbox}
                          checked={true}
                          onChange={(val: boolean) => formRef.current?.setFieldValue('signature', val)}
                        />
                        <Text flex={1} variant="bodyReg" mb={2}>
                          Signature
                        </Text>
                      </HStack>
                    </Content>
                  </Box>
                );
              }}
            </Formik>

            {attachments && attachments.length >= 1 && (
              <CollapsibleAttachmentList
                headerTitle="Attachments"
                data={taskData?.task?.attachments?.nodes || []}
                loading={loading}
                refetch={refetch}
                itemIds={attachmentIds}
                setItemIds={setAttachmentIds}
              />
            )}

            {medias && medias.length >= 1 && (
              <CollapsibleAttachmentList
                headerTitle="Photos"
                data={taskData?.task?.medias?.nodes?.filter(obj => obj.type !== 'mp4' && obj.type !== 'mov') || []}
                loading={loading}
                refetch={refetch}
                itemIds={mediaIds}
                setItemIds={setMediaIds}
              />
            )}

            {documents && documents?.length >= 1 && (
              <CollapsibleAttachmentList
                headerTitle="Linked to Documents"
                data={taskData?.task?.documents?.nodes.filter(obj => obj.type === 'pdf') || []}
                loading={loading}
                refetch={refetch}
                itemIds={documentIds}
                setItemIds={setDocumentIds}
              />
            )}
          </Box>
        </ScrollView>

        <Footer flexDirection={'row'}>
          <Button
            mr={2}
            w="50%"
            isLoading={loading}
            variant="outline"
            onPress={async () => {
              setIsPreview(true);
              await formRef.current?.handleSubmit();
            }}
          >
            Preview
          </Button>

          <Button
            // mx={2}
            w="50%"
            isLoading={loading}
            variant="primary"
            onPress={() => {
              formRef.current?.handleSubmit();
            }}
          >
            Generate
          </Button>
        </Footer>
      </Box>

      <Modal style={styles.modal} type="middle" ref={modalRef} onClose={() => setIsPreview(false)}>
        <Box justifyItems={'center'} alignItems={'center'}>
          <Image source={{ uri: thumbnail }} style={{ width: 300, height: 300 }} />
        </Box>
      </Modal>
    </Box>
  );
};

const styles = StyleSheet.create({
  memberBox: {
    borderWidth: 1,
    borderColor: COLORS.neutrals.gray40,
    borderRadius: 8,
    paddingLeft: 10,
    alignItem: 'center'
  },
  modal: {
    width: '100%',
    height: '100%',
    backgroundColor: 'white'
  },

  flatList: {
    borderColor: COLORS.neutrals.gray40,
    padding: 8

    // backgroundColor: '#E6F1FF'
  },
  container: {
    flex: 1,
    justifyContent: 'center'
  },
  horizontal: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 10
  }
});

interface FormValues {
  companyName?: string;
  logoUrl?: any;
  languageType?: LanguageType;
}

export default CreateMemo;
