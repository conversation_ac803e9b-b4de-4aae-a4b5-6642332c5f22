import { AppBar, Content, Footer, Icon } from '@commons';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { pushError, pushSuccess } from '@src/configs';
import { generateRNFile } from '@src/configs/utils';
import DescriptionInput from '@src/inputs/DescriptionInput';
import { task } from '@src/lib/authority';
import { taskActions } from '@src/slice/tasks.slice';
import { useDispatch, useSelector } from '@src/store';
import { RootNavigatorParams, TaskNavigatorParams } from '@src/types';
import _ from 'lodash';
import moment from 'moment';
import { Box, Button, Divider, HStack, Text, VStack } from 'native-base';
import React, { useCallback, useRef, useState } from 'react';
import { Alert, StyleSheet, TouchableOpacity } from 'react-native';
import DocumentPicker from 'react-native-document-picker';
import TaskGroupModal from '../components/TaskGroupModal';
import TaskPhotosModal from '../components/Tasks/TaskPhotosModal';
import LinkedToModal from '@src/commons/LinkedToModal';
import Calendar from './EditTask/Calendar';
import Assignee from './EditTask/Assignee';
import Cc from './EditTask/Cc';
import Attachment from './EditTask/Attachment';
import Media from './EditTask/Media';
import LinkedTo from './EditTask/LinkedTo';
import { CreateTaskInput } from 'task';
import { ReactNativeFile } from 'apollo-upload-client';
import useCreateTask from '@src/mutation/task/useCreateTask';
import { CreateTaskAttachmentInput, TaskAttachment } from 'task-attachment';
import { CreateTaskMedia } from 'task-media';

type Props = CompositeScreenProps<
  BottomTabScreenProps<TaskNavigatorParams, 'AddTask'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const AddTask: React.FC<Props> = ({ navigation, route }) => {
  const taskGroupedModalRef = useRef<any>(null);
  const addAttachmentPhotoRef = useRef<any>(null);
  const linkedDocumentRef = useRef<any>(null);
  const [validation, setValidation] = useState(false);
  const [isSubmitting, setSubmitting] = useState<boolean>(false);
  const [group, setGroup] = useState('');
  const project = useSelector(state => state.project);
  const role = task(project.projectUserRole ?? '');
  const userMeData = useSelector(state => state.auth.user);

  const { mutate: submitTask, isPending } = useCreateTask();

  const dispatch = useDispatch();
  const states = useSelector(state => state.tasks);

  const onSubmit = async () => {
    setSubmitting(true);
    setValidation(true);

    const groupId = states?.task?.group?.remoteId;
    const assignees = states?.task?.assignee?.map?.((user: any) => user?.remoteId);
    const copies = states?.task?.cc?.map?.((user: any) => user?.remoteId);

    if (!group || assignees?.length === 0 || !groupId) return setSubmitting(false);

    const document = states.task?.linkedDocument?.map((doc: any) => parseInt(doc?.remoteId));

    try {
      if (states?.task?.title === '' || !project.projectId || !userMeData) return;

      const newTask: CreateTaskInput = {
        title: states?.task?.title ?? '',
        description: states?.task?.description,
        dueDate: moment(states?.task?.dueDate).format('YYYY-MM-DD') ?? moment().format('YYYY-MM-DD'),
        assignees,
        linkedTo: states?.task?.linkedDocument,
        groupId: parseInt(groupId ?? '0'),
        copies,
        projectId: parseInt(project.projectId),
        status: 'Open',
        ownerId: parseInt(userMeData.id),
        documents: document,
        medias: states?.task?.medias,
        attachments: states?.task?.attachment
      };

      await submitTask(newTask);
      setValidation(false);
    } catch (e) {
      pushError(e);
      setGroup('');
      dispatch(taskActions.resetAll());
    } finally {
      setSubmitting(false);
      dispatch(taskActions.setInitialState(true));

      pushSuccess('Task created successfully');

      navigation.pop();
    }
  };

  const chooseDocument = async () => {
    try {
      const res = await DocumentPicker.pickMultiple({
        type: [DocumentPicker.types.pdf]
      });

      const newFiles = res.map(file => {
        const createdFile = generateRNFile({
          uri: file.uri,
          name: file.name as string,
          type: file.type as string
        });

        return {
          ...createdFile,
          userId: parseInt(userMeData?.id ?? '0')
        };
      });

      dispatch(
        taskActions?.initialState({
          ...states?.task,
          attachment: [...(states.task?.attachment ?? []), ...newFiles]
        } as any)
      );
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
      } else {
        throw err;
      }
    }
  };

  const uploadPhotos = async (photos: ReactNativeFile[]) => {
    if (states?.task?.medias && states?.task?.medias?.length > 10) {
      return Alert.alert('Warning', 'Photos must be less than 10');
    }

    const sanitizedPhotos = photos?.map?.((photo: any) => ({
      id: photo.id,
      uri: photo.uri,
      name: photo.name,
      userId: parseInt(userMeData?.id ?? '0')
    }));

    const existingPhotos = states?.task?.medias?.map?.((photo: any) => ({
      id: parseInt(photo?.id),
      name: photo.name,
      userId: parseInt(userMeData?.id ?? '0')
    }));

    try {
      return dispatch(
        taskActions?.initialState({
          ...states?.task,
          medias: [...sanitizedPhotos, ...(existingPhotos ?? [])]
        } as any)
      );
    } catch (error) {}
  };

  const removeChosedPhotos = async (files: any) => {
    try {
      const removedPhotos = states.task?.medias?.filter?.((photo: any) => photo !== files);
      dispatch(taskActions?.initialState({ ...states?.task, medias: removedPhotos } as any));
    } catch (error) {}
  };

  const removeLinkedDocument = async (files: any) => {
    try {
      const removeDocument = states.linkedTo?.filter((document: any) => document !== files);
      dispatch(taskActions?.initialState({ ...states?.task, linkedDocument: removeDocument } as any));
    } catch (error) {}
  };

  const removeAttachment = async (files: any) => {
    try {
      const removeAttachment = states.task?.attachment?.filter((document: any) => document !== files);
      dispatch(taskActions?.initialState({ ...states?.task, attachment: removeAttachment } as any));
    } catch (error) {}
  };

  const handleInput = (value: any, name: string) => {
    dispatch(taskActions?.initialState({ ...states?.task, [name]: value } as any));
  };

  const navigateToAssignee = () => {
    navigation.push('TasksNav', {
      screen: 'AddAssignee',
      params: {
        ass: states?.task?.assignee ?? ([] as any)
      }
    });
  };

  const navigationToCc = () => {
    return navigation.push('TasksNav', {
      screen: 'AddCc',
      params: {
        ass: states?.task?.cc ?? []
      }
    });
  };

  const navigationToPdftron = useCallback((file: CreateTaskAttachmentInput, module: 'TaskAttachment' | 'TaskMedia') => {
    return navigation.navigate('PdftronNav', {
      screen: 'Pdftron',
      params: {
        id: file.id ?? '',
        modules: module,
        onPressBack: () => {
          file.remoteId && dispatch(taskActions.OpenTaskDetailModal(file?.remoteId));
        },
        ...(!file.remoteId && file.uri ? { fileUrl: file.uri } : undefined)
      }
    });
  }, []);

  return (
    <Box flex={1} bg="#FFFFFF">
      <AppBar goBack barStyle={'dark-content'} title="Add Task" noRight onPressTitle={() => {}} />
      <Box flex={1}>
        <Content pt={5}>
          <DescriptionInput
            value={states?.task?.title}
            onChangeText={(value: string) => handleInput(value, 'title')}
            loading={isPending}
            role={true}
            textType={'Title'}
            isTaskOwner={false}
            placeholder="Task name"
          />
          {validation && states?.task?.title === '' && <Text color={'red.500'}>Please enter task title</Text>}
          <DescriptionInput
            value={states?.task?.description}
            onChangeText={(value: string) => handleInput(value, 'description')}
            loading={isPending}
            role={true}
            isTaskOwner={false}
            textType={'Description'}
            placeholder="Add your description here..."
          />
          <Divider style={styles.divider} />
          <Calendar
            date={states?.task?.dueDate ?? ('' as any)}
            setDate={(date: any) => handleInput(date, 'dueDate')}
            role={role?.tasks?.canAdd}
            isTaskOwner={true}
          />
          <Divider style={styles.divider} />

          <HStack alignItems="center">
            <Box flex={2}>
              <VStack mt={2}>
                <Assignee
                  loadingAssignee={false}
                  role={role?.tasks?.canEdit}
                  navigationToAssignee={navigateToAssignee}
                  assignees={states?.task?.assignee as any}
                  loading={false}
                />
              </VStack>
            </Box>
          </HStack>

          <Divider style={styles.divider} />

          <HStack alignItems="center">
            <Box flex={2}>
              <VStack mt={2}>
                <Cc
                  cc={states?.task?.cc as any}
                  loadingCc={false}
                  role={role?.tasks?.canEdit}
                  navigationToCc={navigationToCc}
                  loading={false}
                />
              </VStack>
            </Box>
          </HStack>

          <Divider style={styles.divider} />

          <HStack alignItems="center">
            <HStack flex={1} alignItems="center">
              <Icon name="check-square" />
              <Text color="neutrals.gray90" ml={1.5} flex={1}>
                Status
              </Text>
            </HStack>
            <Box flex={2} mt={2}>
              <HStack space={2} alignItems="center">
                <Icon name="reject" />
                <Text>Open</Text>
              </HStack>
            </Box>
          </HStack>

          <Divider style={styles.divider} />

          <HStack alignItems="center">
            <HStack flex={1} alignItems="center">
              <Icon name="group" />
              <Text color="neutrals.gray90" ml={1.5} flex={1}>
                Group
              </Text>
            </HStack>
            <Box flex={2}>
              <TouchableOpacity
                onPress={() => {
                  taskGroupedModalRef.current.pushModal();
                }}
                disabled={!role.tasks.canAdd}
              >
                <HStack alignItems="center">
                  {states.task?.group?.id && states.task?.group.title ? (
                    <VStack>
                      <Text color={!role.tasks.canAdd ? 'gray.400' : 'neutrals.gray90'}>
                        {states.task?.group?.title}
                      </Text>
                    </VStack>
                  ) : (
                    <>
                      <VStack>
                        <HStack alignItems={'center'}>
                          <Icon name="add" />
                          <Text color="neutrals.gray90" ml={1.5}>
                            Add Group
                          </Text>
                        </HStack>
                        <HStack>{validation && !group && <Text color={'red.500'}>Please select a group</Text>}</HStack>
                      </VStack>
                    </>
                  )}
                </HStack>
              </TouchableOpacity>
            </Box>
          </HStack>

          <Divider style={styles.divider} />

          <Attachment
            allLoading={false}
            role={true}
            chooseDocument={chooseDocument}
            deleteTaskAttachment={removeAttachment}
            attachment={states?.task?.attachment ?? []}
            chosedAttachments={chooseDocument}
            isAssigned={true}
            navigationToPdftron={navigationToPdftron}
          />

          <Divider style={styles.divider} />

          <Media
            addAttachmentPhotoRef={addAttachmentPhotoRef}
            allLoading={false}
            role={role.tasks?.canEdit}
            deleteTaskMedias={removeChosedPhotos}
            isAssigned={true}
            navigationMediaToPdfTron={navigationToPdftron}
            photos={states?.task?.medias ?? []}
          />

          <Divider style={styles.divider} />

          <LinkedTo
            allLoading={false}
            role={true}
            navigateToPdfTron={() => {}}
            removeLinkedDocument={removeLinkedDocument}
            linkedDocument={states?.task?.linkedDocument ?? []}
            isAssigned={true}
            linkedToRef={linkedDocumentRef}
          />

          <Divider style={styles.divider} />
        </Content>
        <Footer>
          <Button isLoading={isPending} variant="primary" onPress={onSubmit} isDisabled={!role.tasks.canAdd}>
            Save
          </Button>
        </Footer>
      </Box>
      <TaskGroupModal ref={taskGroupedModalRef} setGroup={setGroup} action="addTask" />
      <TaskPhotosModal
        ref={addAttachmentPhotoRef}
        chosedAttachment={(photo: any) => {
          uploadPhotos(photo);
        }}
        state="addTask"
      />
      <LinkedToModal
        ref={linkedDocumentRef}
        setLinkedDocument={(file: Gql.ProjectDocument) => {
          //@ts-ignore
          file.documentId = file?.id ?? '';
          dispatch(
            taskActions?.initialState({
              ...states?.task,
              linkedDocument: [...(states.task?.linkedDocument ?? []), file]
            } as any)
          );
        }}
      />
    </Box>
  );
};

const styles = StyleSheet.create({
  divider: {
    marginVertical: 14,
    color: '#E8E8E8'
  }
});

interface FormValues {
  title: string;
  description: string;
  dueDate: string;
  assignees: [];
  attachment: [];
  linkedTo: [];
}

export default AddTask;
