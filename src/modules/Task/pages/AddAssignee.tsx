import { App<PERSON><PERSON>, Footer, Icon } from '@commons';
import { Q } from '@nozbe/watermelondb';
import { withObservables } from '@nozbe/watermelondb/react';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { COLORS } from '@src/constants';
import database from '@src/database/index.native';
import { taskActions } from '@src/slice/tasks.slice';
import { useDispatch, useSelector } from '@src/store';
import { TaskNavigatorParams, RootNavigatorParams } from '@src/types';
import _, { debounce } from 'lodash';
import { Avatar, Box, Button, Divider, FlatList, HStack, Input, Pressable, Text, VStack } from 'native-base';
import { SearchProjectUserViewProps, UserDetail } from 'project-user';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { ScrollView, StyleSheet, TouchableOpacity } from 'react-native';
import { useGetProjectUser } from '@src/queries/project-user/useGetProjectUser';

type Props = CompositeScreenProps<
  BottomTabScreenProps<TaskNavigatorParams, 'AddAssignee'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const AddAssignee: React.FC<Props> = ({ route, navigation }) => {
  const [selectedUsers, setSelectedUsers] = useState<UserDetail[]>(route.params.ass as any);
  const [filteredValue, setFilteredValue] = useState<string | null>(null);
  const [search, setSearch] = useState('');
  const project = useSelector(states => states.project);

  const { data: projectUserDatas } = useGetProjectUser('');

  const projectUserData = useMemo(() => {
    if (!projectUserDatas) return [];
    return projectUserDatas.pages.map(page => page.items).flat();
  }, [projectUserDatas]);

  const dispatch = useDispatch();
  const states = useSelector(state => state?.tasks);

  const recentlyJoinedUsers = route?.params?.ass;

  useEffect(() => {
    setSelectedUsers(route.params.ass as UserDetail[]);
  }, [route?.params?.ass]);

  useEffect(() => {
    if (filteredValue === null || filteredValue?.trim?.() === '') return;
    handleSearch(filteredValue);
  }, [filteredValue]);

  const debounceSearch = debounce(keyword => {
    setSearch(keyword);
  }, 150);

  const handleSearch = useCallback((value: string) => debounceSearch(value), []);

  const ProjectUserView = () =>
    useMemo(() => {
      if (selectedUsers.length === 0) {
        return (
          <FlatList
            keyboardShouldPersistTaps="handled"
            data={projectUserData as any}
            keyExtractor={(item: UserDetail) => String(item.userId)}
            renderItem={({ item }) => (
              <Box>
                <TouchableOpacity
                  onPress={() => {
                    setSelectedUsers(pre => [...pre, item]);
                    setFilteredValue(null);
                    return setSearch('');
                  }}
                >
                  <HStack alignItems="center" space={3}>
                    <Avatar
                      size="32px"
                      source={{
                        uri: item?.avatarUrl ?? ''
                      }}
                    />
                    <VStack width="75%">
                      <Text style={{ fontWeight: '600', fontSize: 14 }} numberOfLines={1} ellipsizeMode="tail">
                        {item?.name?.toUpperCase?.()}
                      </Text>
                      <Text style={{ fontWeight: '400', color: COLORS.neutrals.gray90 }}>{item.email}</Text>
                    </VStack>
                  </HStack>
                  <Divider my={2} />
                </TouchableOpacity>
              </Box>
            )}
          />
        );
      } else {
        return (
          <FlatList
            keyboardShouldPersistTaps="handled"
            data={selectedUsers as any}
            keyExtractor={(item: UserDetail) => String(item.userId)}
            // ListEmptyComponent={recentlyJoinedUsers?.length === 0 && loading ? <ActivityIndicator size="large" /> : null}
            renderItem={({ item }) => (
              <Box>
                {/* <TouchableOpacity> */}
                <HStack alignItems="center" space={3}>
                  <Avatar
                    size="32px"
                    source={{
                      uri: item?.avatarUrl ?? ''
                    }}
                  />
                  <VStack width="75%">
                    <Text style={{ fontWeight: '600', fontSize: 14 }} numberOfLines={1} ellipsizeMode="tail">
                      {item?.name?.toUpperCase?.()}
                    </Text>
                    <Text style={{ fontWeight: '400', color: COLORS.neutrals.gray90 }}>{item.email}</Text>
                  </VStack>
                  {selectedUsers?.length > 0 && (
                    <TouchableOpacity
                      onPress={() => {
                        const index = selectedUsers.findIndex(preId => preId === item);
                        const newSelectedUsers = [...selectedUsers];
                        newSelectedUsers.splice(index, 1);
                        setSelectedUsers(newSelectedUsers);
                      }}
                    >
                      <Icon name="cancel" fill="#0695D7" />
                    </TouchableOpacity>
                  )}
                </HStack>
                <Divider my={2} />
                {/* </TouchableOpacity> */}
              </Box>
            )}
          />
        );
      }
    }, [recentlyJoinedUsers]);

  const SearchProjectUserView: React.FC<SearchProjectUserViewProps> = ({ searchedUsers }) => {
    return (
      <FlatList
        keyboardShouldPersistTaps="handled"
        data={searchedUsers}
        keyExtractor={(item: UserDetail) => String(item.userId)}
        renderItem={({ item, index }) => (
          <Box style={styles.flatList}>
            {index === 0 && (
              <>
                <Text color={COLORS.neutrals.black}>Search results</Text>
                <Divider my={1} />
              </>
            )}
            <TouchableOpacity
              onPress={() => {
                setSelectedUsers(pre => [...pre, item]);
                setFilteredValue(null);
                return setSearch('');
              }}
            >
              <HStack alignItems="center" space={3} mt={4}>
                <Avatar
                  size="32px"
                  source={{
                    uri: item.avatarUrl ?? ''
                  }}
                />

                <VStack width="76%">
                  <Text numberOfLines={1} ellipsizeMode="tail" style={{ fontWeight: '600', fontSize: 14 }}>
                    {item?.name?.toUpperCase?.()}
                  </Text>
                  <Text style={{ fontWeight: '400', color: COLORS.neutrals.gray90 }}>{item.email}</Text>
                </VStack>

                {selectedUsers.find(preId => preId === item) && <Icon name="tick" fill="#0695D7" />}
              </HStack>
            </TouchableOpacity>
          </Box>
        )}
      />
    );
  };

  const fetchFilteredUsers = useCallback(
    (searchTerm: string) => {
      return database.collections
        .get('project_users')
        .query(
          Q.and(
            Q.where('name', Q.like(`%${Q.sanitizeLikeString(searchTerm)}%`)),
            Q.and(
              Q.where('projectId', parseInt(project.projectId ?? '0')),
              Q.where('id', Q.notIn(selectedUsers.map(user => user.id) as any))
            )
          )
        );
    },
    [project.projectId, selectedUsers]
  );

  const SearchProjectUserViewObservable = withObservables(['filteredValue'], ({ filteredValue }) => ({
    searchedUsers: fetchFilteredUsers(filteredValue)
  }))(SearchProjectUserView as any);

  const ShouldRenderSeach = useCallback(() => {
    if (filteredValue === null || filteredValue?.trim?.() === '') return null;
    return <SearchProjectUserViewObservable filteredValue={search} />;
  }, [search]);

  return (
    <Box flex={1} bg="#FFFFFF">
      <AppBar
        onPressTitle={() => null}
        header
        goBack
        onGoBack={() => {
          navigation.goBack();
          route.params.onPressBack?.();
        }}
        title="Add Assignee"
        noRight
      />
      <Box flex={1} bg="#FFFFFF">
        <ScrollView keyboardShouldPersistTaps="handled" style={{ paddingHorizontal: 20 }}>
          <Text mb={4} color={COLORS.neutrals.gray90}>
            Add Assignee
          </Text>
          <Divider mb={4} />
          <Input
            placeholder="Enter a member's name"
            width="full"
            borderRadius="4"
            fontSize="14"
            onChangeText={text => setFilteredValue(text)}
            value={filteredValue ?? ''}
            {...(!!filteredValue && {
              InputRightElement: (
                <Pressable px="4" py="6" onPress={() => setFilteredValue(null)}>
                  <Icon name="cancel" />
                </Pressable>
              )
            })}
          />
          {/* <SearchProjectUserViewObservable filteredValue={search} /> */}
          <ShouldRenderSeach />
          <Box>
            <Text mb={2} mt={6} color={COLORS.neutrals.gray90}>
              Recently joined
            </Text>
            <Divider mb={4} />
            <ProjectUserView />
          </Box>
        </ScrollView>
        <Footer>
          <Button
            variant="primary"
            onPress={() => {
              dispatch(taskActions?.setInitialState(false));
              dispatch(taskActions?.initialState({ ...states?.task, assignee: selectedUsers } as any));
              navigation.goBack();
              route.params.onPressBack?.();
            }}
          >
            Add Assignee
          </Button>
        </Footer>
      </Box>
    </Box>
  );
};

const styles = StyleSheet.create({
  memberBox: {
    borderWidth: 1,
    borderColor: COLORS.neutrals.gray40,
    borderRadius: 8,
    paddingLeft: 10,
    alignItem: 'center'
  },
  flatList: {
    borderWidth: 1,
    borderColor: COLORS.neutrals.gray40,
    padding: 12,
    backgroundColor: '#E6F1FF'
  },
  container: {
    flex: 1,
    justifyContent: 'center'
  },
  horizontal: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 10
  }
});

export default React.memo(AddAssignee);
