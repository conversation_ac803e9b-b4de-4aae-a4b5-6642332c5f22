import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps, useFocusEffect } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { COLORS } from '@src/constants';
import { RootNavigatorParams, TaskNavigatorParams } from '@src/types';
import _ from 'lodash';
import { Box, Button } from 'native-base';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { Dimensions, InteractionManager, StyleSheet, View } from 'react-native';
import { Icon } from '@src/commons';
import ScrollableTabView, { DefaultTabBar, ScrollableTabBar } from 'react-native-scrollable-tab-view';
import { taskActions } from '@src/slice/tasks.slice';
import { useDispatch, useSelector } from '@src/store';

import CustomAppBar from '@src/commons/CustomAppBar';
import Tasks from '../components/Tasks/Task';
import TasksOverview from '../components/Overview/TasksOverview';
import { useDeviceOrientation } from '@react-native-community/hooks';

type Props = CompositeScreenProps<
  BottomTabScreenProps<TaskNavigatorParams, 'Tasks'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const Task: React.FC<Props> = ({ navigation, route }) => {
  const scrollableTabViewRef = useRef<ScrollableTabView>(null); // Create a ref for ScrollableTabView
  const [currentTab, setCurrentTab] = useState(0);
  const dispatch = useDispatch();
  const { filterItems } = useSelector(state => state.tasks);
  const pageIndex = useMemo(() => Number(_.get(route, 'params.pageIndex', '0')), [route?.params]);
  const { landscape, portrait } = useDeviceOrientation();
  const WINDOW_WIDTH = useMemo(() => Dimensions.get('window').width, [landscape, portrait]);

  // Check if filters have any value
  const dot = useMemo(() => {
    const filterValues = Object.values(filterItems);
    const hasFilter = filterValues?.some?.(value => {
      if (Array.isArray(value)) {
        return value.length > 0;
      }
      return !!value;
    });

    if (hasFilter) {
      return (
        <Box
          zIndex={1}
          width={3}
          height={3}
          borderRadius={'full'}
          position="absolute"
          top={2.5}
          right={3}
          bg={'red.500'}
        />
      );
    } else {
      return null;
    }
  }, [filterItems]);

  // Set up the right component for the custom app bar
  const rightComponent = useMemo(() => {
    if (currentTab === 1) {
      return (
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            bg="transparent"
            onPress={() => dispatch(taskActions.OpenTaskFilterModal())}
          >
            <Icon name="filtering" />
          </Button>
          {dot}
        </View>
      );
    } else {
      return null;
    }
  }, [currentTab, filterItems]);

  // Handle tab change to keep track of the current tab index
  const handleTabChange = useCallback((index: number) => {
    setCurrentTab(index);
  }, []);

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <CustomAppBar header barStyle={'light-content'} rightComponent={rightComponent} />

      <ScrollableTabView
        ref={scrollableTabViewRef} // Attach the ref here
        key={pageIndex}
        initialPage={1}
        style={{ backgroundColor: '#FFFFFF' }}
        tabBarInactiveTextColor="#000"
        tabBarActiveTextColor="#0695D7"
        tabBarUnderlineStyle={{ backgroundColor: '#0695D7', height: 2 }}
        scrollWithoutAnimation={true}
        renderTabBar={() => <DefaultTabBar style={{ height: 40, marginTop: 10 }} tabStyle={{ height: 40 }} />}
        onChangeTab={({ i }) => handleTabChange(i)} // Handle tab changes
      >
        <TasksOverview tabLabel="Overview" />
        <Tasks tabLabel="Tasks" />
      </ScrollableTabView>
    </Box>
  );
};

export default Task;
