import { Modal } from '@commons';
import { But<PERSON>, Text } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { Alert, ModalProps, Platform } from 'react-native';
import TaskGroupAddRename from './TaskGroupAddRename';
import { useSelector } from '@src/store';
import { task } from '@src/lib/authority';
import prompt from 'react-native-prompt-android';
import { pushError, pushSuccess } from '@src/configs';
import { Gql } from '@src/api';
import { ProjectGroupApiService } from '@src/api/rest';
import ProjectGroupModel from '@src/database/model/project-group.model';
import database from '@src/database/index.native';
import { attemptImmediateSync } from '@src/database/utils/dataSync';
import useDeleteTaskGroup from '@src/mutation/task-group/useDeleteTaskGroup';
import useCreateTaskGroup from '@src/mutation/task-group/useCreateTaskGroup';

interface Props {
  onDelete?: (values: string) => void;
  refetch?: () => void;
  data?: any;
  action?: string;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any, type?: 'parent' | 'child', hasChildren?: boolean) => void;
}

const TaskGroupOptions = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const addModalRef = useRef<any>(null);
  const project = useSelector(state => state.project);
  const perm = task(project.projectUserRole);

  const { mutateAsync: deleteGroup } = useDeleteTaskGroup();
  const { mutateAsync: createGroup } = useCreateTaskGroup();

  const [type, setType] = useState('');
  const [hasChildren, setHasChildren] = useState<boolean>(false);

  useImperativeHandle(ref, () => ({
    pushModal: ({ type, hasChildren }) => {
      setType(type);
      setHasChildren(hasChildren);
      modalRef?.current?.pushModal();
    }
  }));

  const [updateGroup] = Gql.useUpdateOneProjectGroupMutation({
    onCompleted: () => {
      pushSuccess('Create group successfully');
    },
    onError: err => {
      pushError(err?.message);
    }
  });

  const updateNewGroup = async (value: string) => {
    try {
      await updateGroup({
        variables: {
          input: {
            id: props?.data?.id,
            update: {
              title: value
            }
          }
        }
      });
    } catch (e) {
      pushError(e);
    }
  };

  const createNewGroup = async (value: string) => {
    if (!project.projectId) {
      return pushError('Project not found');
    }

    const projectGroupId = props?.data?.remoteId
      ? {
          projectGroupId: props?.data?.remoteId
        }
      : {
          localGroupId: props?.data?.id
        };

    await createGroup({
      title: value,
      projectId: parseInt(project.projectId),
      ...projectGroupId
    });

    // const createGroupLocally = () => {
    //   const projectGroup = new ProjectGroupModel(database.get('project_groups'), {} as any);
    //   return projectGroup.createProjectGroup(value, parseInt(project.projectId), props?.data?.remoteId?.toString());
    // };

    // const syncToServer = async (): Promise<ProjectGroupModel> => {
    //   return ProjectGroupApiService.create({
    //     body: {
    //       title: value,
    //       projectGroupId: Number(props?.data?.remoteId)
    //     }
    //   }).then(response => {
    //     pushSuccess('Created group successfully');
    //     if (props?.refetch) props.refetch();

    //     return response;
    //   });
    // };

    // return await attemptImmediateSync();
  };

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="bottom">
      {perm.overview.canRename && (
        <Button
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          mb={4}
          bg="transparent"
          onPress={() => {
            addModalRef?.current?.pushModal();
          }}
        >
          <Text>Rename</Text>
        </Button>
      )}

      {type === 'parent' && (
        <Button
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          mb={4}
          bg="transparent"
          onPress={() => {
            if (Platform.OS === 'ios') {
              Alert.prompt('Create New Group', 'Enter name', [
                {
                  text: 'Cancel',
                  style: 'cancel'
                },
                {
                  text: 'OK',
                  onPress: (name: any) => {
                    createNewGroup(name);
                  }
                }
              ]);
            } else {
              prompt('Create New Group', 'Enter name', [
                {
                  text: 'Cancel',
                  style: 'cancel'
                },
                {
                  text: 'OK',
                  onPress: (name: any) => {
                    createNewGroup(name);
                  }
                }
              ]);
            }
          }}
        >
          <Text>Add Sub Group</Text>
        </Button>
      )}

      {perm.overview.canDelete && (type === 'child' || !hasChildren) && (
        <Button
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          mb={4}
          bg="transparent"
          onPress={() => {
            Alert.alert('Delete Group', 'Are you sure you want to delete this group?', [
              {
                text: 'Cancel',
                onPress: () => {},
                style: 'cancel'
              },
              {
                text: 'Delete',
                onPress: async () => deleteGroup(props?.data?.id)
              }
            ]);
          }}
        >
          <Text color="semantics.danger">Delete Group</Text>
        </Button>
      )}
      <TaskGroupAddRename
        ref={addModalRef}
        refetch={() => {
          props?.refetch?.();
        }}
        data={props?.data}
        action={props?.action}
      />
    </Modal>
  );
});

TaskGroupOptions.displayName = 'TaskGroupOptions';
export default TaskGroupOptions;
