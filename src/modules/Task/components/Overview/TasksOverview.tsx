import { Icon } from '@src/commons';
import { pushError, pushSuccess, showFileName } from '@src/configs';
import _ from 'lodash';
import { Box, Circle } from 'native-base';
import React, { lazy, useCallback, useMemo, useRef, useState } from 'react';
import { Alert, Platform, StyleSheet, TouchableOpacity } from 'react-native';

import TaskGroupDetails from './TaskGroupDetails';
import TaskGroupOptions from './TaskGroupOptions';
import { useSelector } from '@src/store';
import { task } from '@src/lib/authority';
import prompt from 'react-native-prompt-android';
import { ProjectGroupApiService } from '@src/api/rest';
import OverviewList from './OverviewList';
import useDebouncedSearch from '@src/hooks/useDebounceSearch';
import { useGetTaskGroups } from '@src/queries/task-gorup/useGetTaskGroups';
import useCreateTaskGroup from '@src/mutation/task-group/useCreateTaskGroup';

const SearchInput = lazy(() => import('@src/commons/SearchInput'));

const TasksOverview: React.FC<any> = () => {
  const [selectedGroupId, setSelectedGroupId] = useState<any>('');
  const [selectedGroupName, setSelectedGroupName] = useState<any>('');
  const [selectedGroup, setSelectedGroup] = useState<any>({});
  const [groups, setGroups] = useState<any>([]);
  const [openAccordion, setOpenAccordion] = useState(true);
  const [filteredValue, setFilteredValue] = useState<string | null>(null);

  const overviewOptionModalRef = useRef<any>(null);
  const groupDetailsRef = useRef<any>(null);
  const [modalMutateAction, setModalMutateAction] = useState('create');

  const project = useSelector(state => state.project);
  const perm = useMemo(() => task(project.projectUserRole), [project.projectUserRole]);
  const viewOnly = project.projectUserRole === 'CanView';

  const { debouncedValue, setInputValue } = useDebouncedSearch();
  const { data, fetchNextPage, hasNextPage, isLoading } = useGetTaskGroups(debouncedValue ?? '');
  const { mutateAsync: createProjectGroup } = useCreateTaskGroup();

  const datas = useMemo(() => {
    if (!data) return [];
    return data.pages.map(page => page.items).flat();
  }, [data]);

  const onSearchInputChange = (newSearchTerm: string) => {
    setFilteredValue(newSearchTerm);
    setInputValue(newSearchTerm || '');
  };

  const handleEndReached = useCallback(() => {
    if (hasNextPage && !isLoading) {
      fetchNextPage();
    }
  }, [hasNextPage, isLoading, fetchNextPage]);

  const onDelete = async (id: string) => {
    if (id) {
      await ProjectGroupApiService.delete({
        body: {
          id: +id
        }
      })
        .then(() => {
          pushSuccess('Deleted group successfully');
          // refetch();
        })
        .catch(e => {
          pushError(e);
        });
    }
  };

  const createNewGroup = async (value: string) => {
    try {
      if (!project.projectId) {
        return pushError('Project not found');
      }

      return await createProjectGroup({
        title: value,
        projectId: parseInt(project.projectId),
        projectGroupId: null
      }).then(() => {
        pushSuccess('Created group successfully');
      });

      // const createGroupLocally = () => {
      //   const projectGroup = new ProjectGroupModel(database.get('project_groups'), {} as any);
      //   return projectGroup.createProjectGroup(value, parseInt(project.projectId), null);
      // };

      // const syncToServer = async (): Promise<ProjectGroupModel> => {
      //   return ProjectGroupApiService.create({
      //     body: {
      //       title: value
      //     }
      //   })
      //     .then(response => {
      //       pushSuccess('Created group successfully');

      //       return response;
      //     })
      //     .catch((err: any) => {
      //       return pushError(`${err?.response?.data?.message}. Will rollback in 5 seconds.`);
      //     });
      // };

      // const rollback: any = async (id: string) => {
      //   const projectGroup = new ProjectGroupModel(database.get('project_groups'), {} as any);
      //   return await projectGroup.deleteProjectGroup(id);
      // };

      // return await attemptImmediateSync();
    } catch (e) {
      pushError(e);
    }
  };

  const handleLongPress = (title: string) => {
    if (title === 'Ungroup Tasks') return;
    return showFileName('Group', title);
  };

  const handleOnPress = (item: any) => {
    setSelectedGroupId(item.remoteId);
    setSelectedGroupName(item.title);
    setGroups(item);
    groupDetailsRef.current.pushModal();
  };

  const handleOverview = (item: any, type: string, hasChildren: boolean) => {
    setModalMutateAction('update');
    setSelectedGroup(item);
    overviewOptionModalRef.current.pushModal({ type: type, hasChildren });
  };

  const createButton = useMemo(
    () => (
      <TouchableOpacity
        style={[styles.addButton, { zIndex: 1000 }]}
        onPress={() => {
          if (Platform.OS === 'ios') {
            Alert.prompt('Create New Group', 'Enter name', [
              {
                text: 'Cancel',
                style: 'cancel'
              },
              {
                text: 'OK',
                onPress: (name: any) => {
                  createNewGroup(name);
                }
              }
            ]);
          } else {
            prompt('Create New Group', 'Enter name', [
              {
                text: 'Cancel',
                style: 'cancel'
              },
              {
                text: 'OK',
                onPress: (name: any) => {
                  createNewGroup(name);
                }
              }
            ]);
          }
        }}
      >
        <Circle size="65px" bg="#0695D7">
          <Icon name="plus" />
        </Circle>
      </TouchableOpacity>
    ),
    [perm.overview.canCreate]
  );

  return (
    <Box bg="white" height="full">
      {createButton}
      <SearchInput
        placeholder="Search for group name."
        filteredValue={filteredValue}
        onClose={() => setOpenAccordion(true)}
        setFilteredValue={(value: any) => onSearchInputChange(value)}
      />
      <OverviewList
        handleLongPress={handleLongPress}
        handleOnPress={handleOnPress}
        handleOverview={handleOverview}
        openAccordion={openAccordion}
        viewOnly={viewOnly}
        filteredValue={debouncedValue}
        projectId={project?.projectId}
        key={project?.projectId}
        item={datas}
        handleReachedEnd={handleEndReached}
      />
      <TaskGroupDetails
        ref={groupDetailsRef}
        selectedGroupId={selectedGroupId}
        group={groups}
        selectedGroupName={selectedGroupName}
      />
      <TaskGroupOptions
        ref={overviewOptionModalRef}
        data={selectedGroup}
        refetch={() => {
          // refetch();
        }}
        action={modalMutateAction}
        onDelete={values => onDelete(values)}
      />
    </Box>
  );
};

const styles = StyleSheet.create({
  box: {
    backgroundColor: '#FFFFFF',
    paddingLeft: 0,
    borderRadius: 12,
    overflow: 'hidden'
  },
  addButton: {
    position: 'absolute',
    bottom: 10,
    alignSelf: 'flex-end',
    right: '7%',
    shadowColor: 'rgba(0, 0, 0, 0.5)',
    shadowOpacity: 1,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 6 },
    backgroundColor: '#FFFFFF',
    borderRadius: 9999
  },
  tabContainer: {
    backgroundColor: '#FFFFFF'
  }
});

export default TasksOverview;
