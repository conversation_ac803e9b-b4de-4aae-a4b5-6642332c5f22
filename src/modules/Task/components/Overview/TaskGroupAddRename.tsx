import { Modal } from '@commons';
import { ProjectGroupApiService } from '@src/api/rest';
import { pushError, pushSuccess } from '@src/configs';
import { DIMENS } from '@src/constants';
import useUpdateTaskGroup from '@src/mutation/task-group/useUpdateTaskGroup';
import _ from 'lodash';
import { Box, Button, Input, Text, VStack, Spinner } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps } from 'react-native';

interface Props {
  refetch: () => void;
  addPhotoName?: (value: any) => void;
  data?: any;
  action?: string;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const TaskGroupAddRename = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const [value, setValue] = useState(props?.data?.taskGroupName ?? '');
  const [loading, setLoading] = useState<boolean>(false);

  const { mutateAsync: updateGroup } = useUpdateTaskGroup();

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const createOrUpdateGroup = async (value: any) => {
    setLoading(true);
    if (props?.action == 'create') {
      // const title = data;
      ProjectGroupApiService.create({ body: { title: value } })
        .then((res: any) => {
          pushSuccess(res.message);
          props?.refetch?.();
          modalRef?.current?.closeModal();
          setLoading(false);
        })
        .catch((err: any) => {
          pushError(err?.response?.data?.message);
          setLoading(false);
        });
    } else if (props?.action == 'update') {
      await updateGroup({ title: value, projectGroupId: props?.data?.id });

      // const existingGroup = await database.get('project_groups').find(props?.data?.id);
      // const snapshot: any = _.cloneDeep(existingGroup);

      // const rollback = async () => {
      //   const projectGroup = new ProjectGroupModel(database.get('project_groups'), {} as any);
      //   return await projectGroup.updateProjectGroup(props?.data?.id, snapshot?.title);
      // };

      // const updateLocally = async () => {
      //   const projectGroup = new ProjectGroupModel(database.get('project_groups'), {} as any);
      //   return await projectGroup.updateProjectGroup(props?.data?.id, value);
      // };

      // const syncToServer = async (): Promise<ProjectGroupModel> => {
      //   return ProjectGroupApiService.update({ body: { id: props?.data?.remoteId, title: value } })
      //     .then((res: any) => {
      //       pushSuccess(res.message);
      //       props?.refetch?.();

      //       return res;
      //     })
      //     .catch((err: any) => {
      //       pushError(`${err?.response?.data?.message}. Will rollback in 5 seconds.`);
      //       modalRef?.current?.closeModal();
      //       setLoading(false);
      //     });
      // };

      // await attemptImmediateSync();

      modalRef?.current?.closeModal();
      return setLoading(false);
    }
  };

  const addPhotoMethod = (value: any) => {
    props?.addPhotoName?.(value);
    modalRef?.current?.closeModal();
  };

  return (
    <Modal ref={modalRef} style={{ marginTop: 40, alignItems: 'center' }} type="middle">
      <Box p={6} style={{ width: DIMENS.screenWidth - 45 }}>
        <Text textAlign="center" mb={4} fontSize={16}>
          {props?.action == 'create'
            ? 'Create new group'
            : props?.action == 'photo'
              ? 'Add photo name'
              : 'Update new name'}
        </Text>

        <VStack>
          <Text>Enter name</Text>
          <Input
            onChangeText={value => {
              let name = value;
              if (!value.includes('.jpg') && !value.includes('.png') && props.action === 'Photo') {
                name = value + '.jpg';
              }
              setValue(name);
            }}
            isRequired={true}
            defaultValue={props?.data?.title}
          />
        </VStack>

        <Box flexDirection="row" justifyContent="flex-end" mt={5}>
          <Button
            bg="transparent"
            _pressed={{ bg: 'transparent' }}
            onPress={() => {
              modalRef.current.closeModal();
            }}
          >
            <Text color="#0695D7">Cancel</Text>
          </Button>
          {loading ? (
            <Spinner size="lg" />
          ) : (
            <Button
              bg="transparent"
              _pressed={{ bg: 'transparent' }}
              onPress={() => {
                props?.action == 'photo' ? addPhotoMethod(value) : createOrUpdateGroup(value);
              }}
            >
              <Text>{props?.action == 'create' ? 'Create' : props?.action == 'photo' ? 'Add' : 'Update'}</Text>
            </Button>
          )}
        </Box>
      </Box>
    </Modal>
  );
});

TaskGroupAddRename.displayName = 'TaskGroupAddRename';
export default TaskGroupAddRename;
