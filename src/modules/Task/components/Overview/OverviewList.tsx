import React, { memo, useCallback, useMemo, useState } from 'react';
import { FlatList, RefreshControl, StyleSheet, TouchableOpacity } from 'react-native';
import { Box, Divider, HStack, Pressable, Text, VStack } from 'native-base';
import { Icon } from '@src/commons';
import NotFound from '@src/commons/NotFound';
import DeviceInfo from 'react-native-device-info';
import useSyncWithDownload from '@src/hooks/useSyncWithDownload';
import { FIXTURES_GROUP_TASKS } from '@src/database/utils/constant';

type Props = {
  item: any;
  handleOnPress: (item: any) => void;
  handleLongPress: (title: string) => void;
  viewOnly?: boolean;
  handleOverview: (item: any, type: string, hasChildren: boolean) => void;
  openAccordion?: boolean;
  filteredValue: string | null;
  projectId: string | undefined;
  handleReachedEnd: () => void;
};

const styles = StyleSheet.create({
  box: {
    backgroundColor: '#FFFFFF',
    paddingLeft: 0,
    borderRadius: 12,
    overflow: 'hidden'
  }
});

const ProjectGroupItem = ({
  item,
  handleLongPress,
  handleOverview,
  handleOnPress,
  isOpenChild,
  toggleOpenChild,
  children,
  viewOnly
}: any) => {
  return item.projectGroupId ? null : (
    <HStack flexDirection="row" alignItems={'center'} py={1}>
      <TouchableOpacity
        onPress={() => {
          if (FIXTURES_GROUP_TASKS.includes(item.title)) {
            return handleOnPress(item);
          }
          toggleOpenChild(item.id);
        }}
        style={{ width: '100%' }}
        onLongPress={() => handleLongPress(item.title)}
      >
        <VStack>
          <Box style={styles.box}>
            <VStack>
              <HStack alignItems="center">
                <Pressable
                  onPress={e => {
                    e.stopPropagation();
                    toggleOpenChild(item.id);
                  }}
                >
                  <Box>
                    {item?.title === 'Ungroup Tasks' ? (
                      <Icon name="group" style={{ marginRight: 15 }} />
                    ) : isOpenChild ? (
                      <Icon name="arrow-down" />
                    ) : (
                      <Icon name="arrow-right" />
                    )}
                  </Box>
                </Pressable>
                <HStack direction={'row'}>
                  <Text
                    my={1}
                    fontWeight={600}
                    numberOfLines={1}
                    ml={3}
                    ellipsizeMode="tail"
                    w={DeviceInfo.getDeviceType() == 'Tablet' ? '90%' : '83%'}
                  >
                    {item?.title !== 'undefined (undefined)' ? (
                      <>
                        {item?.title} <Text style={{ marginLeft: 4 }}>({item?.totalCount ?? 0})</Text>
                      </>
                    ) : (
                      '...'
                    )}
                  </Text>
                </HStack>
              </HStack>
              <Divider my={2} />
            </VStack>
          </Box>
          {item?.children?.length > 0 && isOpenChild
            ? item?.children?.map?.((child: any, index: number) => {
                const { tasks, title, id } = child;
                const group = { ...child, id, title };

                return (
                  <TouchableOpacity onPress={() => handleOnPress(child)} key={child?.id}>
                    <Box style={styles.box}>
                      <HStack alignItems="center">
                        <HStack ml={10} my={1} flex={1} direction={'row'}>
                          <Text
                            fontWeight={600}
                            numberOfLines={1}
                            ellipsizeMode="tail"
                            w={DeviceInfo.getDeviceType() == 'Tablet' ? '90%' : '83%'}
                          >
                            {title} ({child?.totalCount ?? 0})
                          </Text>
                          <Pressable p={2} ml={8} onPress={() => handleOverview(child, 'child', false)}>
                            <Icon name="option-dot" />
                          </Pressable>
                        </HStack>
                      </HStack>
                      <Divider mb={2} />
                    </Box>
                  </TouchableOpacity>
                );
              })
            : null}
        </VStack>
      </TouchableOpacity>
      {item._status !== 'synced' ? (
        <Icon name="unsync" style={{ width: '10%', position: 'absolute', right: 20, marginLeft: 'auto', top: 12.5 }} />
      ) : null}
      {!viewOnly ? (
        <TouchableOpacity
          onPress={() => handleOverview(item, 'parent', children?.length > 0)}
          style={{ width: '10%', position: 'absolute', right: -10, marginLeft: 'auto', top: 15 }}
        >
          <HStack alignSelf="center">{item?.title !== 'Ungroup Tasks' ? <Icon name="option-dot" /> : null}</HStack>
        </TouchableOpacity>
      ) : null}
    </HStack>
  );
};

const OverviewList = ({
  item,
  handleLongPress,
  handleOverview,
  handleOnPress,
  viewOnly,
  filteredValue,
  handleReachedEnd
}: Props) => {
  const [isOpenChild, setIsOpenChild] = useState<string[]>([]);

  const { syncAndDownload } = useSyncWithDownload();

  const toggleOpenChild = useCallback(
    (id: string) => {
      if (isOpenChild.includes(id)) {
        setIsOpenChild(isOpenChild.filter(item => item !== id));
      } else {
        setIsOpenChild([...isOpenChild, id]);
      }
    },
    [isOpenChild]
  );

  const renderItem = useCallback(
    ({ item }: any) => {
      return (
        <ProjectGroupItem
          item={item}
          handleLongPress={handleLongPress}
          handleOverview={handleOverview}
          handleOnPress={handleOnPress}
          isOpenChild={isOpenChild.includes(item.id)}
          toggleOpenChild={toggleOpenChild}
          children={item.children}
          viewOnly={viewOnly}
        />
      );
    },
    [handleLongPress, handleOverview, handleOnPress, isOpenChild, toggleOpenChild, item]
  );

  return (
    <>
      {item?.length === 0 && filteredValue && filteredValue.length > 0 ? (
        <NotFound />
      ) : (
        <FlatList
          data={item}
          keyExtractor={item => item?.id?.toString?.()}
          renderItem={renderItem}
          initialNumToRender={20}
          maxToRenderPerBatch={20}
          windowSize={10}
          contentContainerStyle={{ paddingHorizontal: 20, paddingTop: 20 }}
          refreshControl={
            <RefreshControl
              refreshing={false}
              onRefresh={async () => {
                syncAndDownload({
                  syncMutateOptions: { dispatchStatus: true },
                  offlineDownloadOptions: { id: '', dispatchStatus: true },
                  showSyncModal: true
                });
              }}
            />
          }
          onEndReached={handleReachedEnd}
        />
      )}
    </>
  );
};

export default memo(OverviewList);
