import { Icon, Modal } from '@commons';
import { WorkspaceGroupApiService } from '@src/api/rest';
import { Box, Divider, HStack, Text, View } from 'native-base';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps, StyleSheet, Touchable, TouchableOpacity } from 'react-native';
import { useDispatch, useSelector } from '@src/store';
import { taskActions } from '@src/slice/tasks.slice';
import { TaskStatusType } from '@src/api/graphql';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootNavigatorParams } from '@src/types';

interface Props {
  selectedGroupId?: string;
  selectedGroupName?: string;
  group?: any;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const TaskGroupDetails = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const { filter } = useSelector(state => state.tasks);
  const dispatch = useDispatch();

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="bottom">
      <Box p={3}>
        <Box p={5}>
          <HStack alignItems="center" space={3} pl={0}>
            <Icon name="group" />
            <Text fontSize={19}>{props.selectedGroupName ?? '-'}</Text>
          </HStack>
          <Divider mt={4} mb={4} />

          <TouchableOpacity
            onPress={() => {
              dispatch(
                taskActions.setFilterItems({
                  ...filter,
                  status: [TaskStatusType.Open],
                  group: { id: props.selectedGroupId as string, title: props.selectedGroupName as string }
                })
              );
              dispatch(
                taskActions.setFilter({
                  ...filter,
                  status: [TaskStatusType.Open],
                  group: { id: props.selectedGroupId as string, title: props.selectedGroupName as string }
                })
              );
              modalRef.current.closeModal();
              navigation.push('Tab', { screen: 'Tasks', params: { pageIndex: 1 } });
            }}
          >
            <Box flexDirection="row" justifyContent="space-between">
              <HStack space={2}>
                <View style={[style.dot, { backgroundColor: '#F40000', alignSelf: 'center' }]} />
                <Text fontSize={18} color="#8F8989">
                  Open
                </Text>
              </HStack>
              <Box>
                <Text fontSize={18} color="#8F8989">
                  {`(${props?.group?.openCount ?? 0})`}
                </Text>
              </Box>
            </Box>
          </TouchableOpacity>

          <Divider mt={4} mb={4} />

          <TouchableOpacity
            onPress={() => {
              dispatch(
                taskActions.setFilterItems({
                  ...filter,
                  status: [TaskStatusType.InProgress],
                  group: { id: props.selectedGroupId as string, title: props.selectedGroupName as string }
                })
              );
              dispatch(
                taskActions.setFilter({
                  ...filter,
                  status: [TaskStatusType.InProgress],
                  group: { id: props.selectedGroupId as string, title: props.selectedGroupName as string }
                })
              );
              modalRef.current.closeModal();
              navigation.push('Tab', { screen: 'Tasks', params: { pageIndex: 1 } });
            }}
          >
            <Box flexDirection="row" justifyContent="space-between">
              <HStack space={2}>
                <View style={[style.dot, { backgroundColor: '#3E78CF', alignSelf: 'center' }]} />
                <Text fontSize={18} color="#8F8989">
                  In Progress
                </Text>
              </HStack>
              <Box>
                <Text fontSize={18} color="#8F8989">
                  {`(${props?.group?.inProgressCount ?? 0})`}
                </Text>
              </Box>
            </Box>
          </TouchableOpacity>

          <Divider mt={4} mb={4} />

          <TouchableOpacity
            onPress={() => {
              dispatch(
                taskActions.setFilterItems({
                  ...filter,
                  status: [TaskStatusType.Hold],
                  group: { id: props.selectedGroupId as string, title: props.selectedGroupName as string }
                })
              );
              dispatch(
                taskActions.setFilter({
                  ...filter,
                  status: [TaskStatusType.Hold],
                  group: { id: props.selectedGroupId as string, title: props.selectedGroupName as string }
                })
              );
              modalRef.current.closeModal();
              navigation.push('Tab', { screen: 'Tasks', params: { pageIndex: 1 } });
            }}
          >
            <Box flexDirection="row" justifyContent="space-between">
              <HStack space={2}>
                <View style={[style.dot, { backgroundColor: '#F29100', alignSelf: 'center' }]} />
                <Text fontSize={18} color="#8F8989">
                  Hold
                </Text>
              </HStack>
              <Box>
                <Text fontSize={18} color="#8F8989">
                  {`(${props?.group?.holdCount ?? 0})`}
                </Text>
              </Box>
            </Box>
          </TouchableOpacity>

          <Divider mt={4} mb={4} />

          <TouchableOpacity
            onPress={() => {
              dispatch(
                taskActions.setFilterItems({
                  ...filter,
                  status: [TaskStatusType.Completed],
                  group: { id: props.selectedGroupId as string, title: props.selectedGroupName as string }
                })
              );
              dispatch(
                taskActions.setFilter({
                  ...filter,
                  status: [TaskStatusType.Completed],
                  group: { id: props.selectedGroupId as string, title: props.selectedGroupName as string }
                })
              );
              modalRef.current.closeModal();
              navigation.push('Tab', { screen: 'Tasks', params: { pageIndex: 1 } });
            }}
          >
            <Box flexDirection="row" justifyContent="space-between">
              <HStack space={2}>
                <View style={[style.dot, { backgroundColor: '#18A601', alignSelf: 'center' }]} />
                <Text fontSize={18} color="#8F8989">
                  Closed
                </Text>
              </HStack>
              <Box>
                <Text fontSize={18} color="#8F8989">
                  {`(${props?.group?.closedCount ?? 0})`}
                </Text>
              </Box>
            </Box>
          </TouchableOpacity>
        </Box>
      </Box>
    </Modal>
  );
});

const style = StyleSheet.create({
  dot: {
    width: 10,
    height: 10,
    borderRadius: 5
  }
});

TaskGroupDetails.displayName = 'TaskGroupDetails';
export default TaskGroupDetails;
