import { Content, Footer, Icon, Modal } from '@commons';
import { Gql } from '@src/api';
import { pushError } from '@src/configs';
import { COLORS } from '@src/constants';
import { taskActions } from '@src/slice/tasks.slice';
import { useDispatch, useSelector } from '@src/store';
import _ from 'lodash';
import {
  Avatar,
  Box,
  Button,
  Divider,
  FlatList,
  HStack,
  Input,
  KeyboardAvoidingView,
  Pressable,
  Text,
  VStack
} from 'native-base';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { ActivityIndicator, ModalProps, StyleSheet, TouchableOpacity, View } from 'react-native';
import DeviceInfo from 'react-native-device-info';

interface Props {
  selectedTaskId?: string;
  selectedUser?: string[];
  onSubmitted?: (v: ConfirmAssigneesProps) => void;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

export type ConfirmAssigneesProps = { addAssignees: string[]; deleteAssignees: string[] };

const TaskAssigneeModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const isTablet = DeviceInfo.isTablet();
  const dispatch = useDispatch();
  const states = useSelector(state => state.tasks);

  const [selectedUsers, setSelectedUsers] = useState<string[]>(states.addedAssignee ?? []);
  const [filteredValue, setFilteredValue] = useState<string>();

  const { data: projectUserData, loading: projectDataLoading } = Gql.useProjectUsersQuery({
    variables: {
      filter: {
        role: { neq: Gql.ProjectUserRoleType.CanView },
        user: { name: { like: `%${filteredValue}%` } }
      },
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.ProjectUserSortFields.CreatedAt
        }
      ]
    }
  });

  const { data: recentlyJoinedData } = Gql.useProjectUsersQuery({
    variables: {
      filter: {
        role: { neq: Gql.ProjectUserRoleType.CanView }
        // user: { name: { like: `%${filteredValue}%` } }
      },
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.ProjectUserSortFields.CreatedAt
        }
      ]
    }
  });

  const { data: addedUserData } = Gql.useProjectUsersQuery({
    variables: {
      filter: {
        user: {
          id: selectedUsers.length > 0 ? { in: selectedUsers } : { is: null }
        }
      }
    }
  });

  const inviteProjectUser = projectUserData?.projectUsers?.nodes.map((u: any) => {
    return {
      name: u.user.name,
      email: u.user.email,
      id: u.user.id,
      value: u.user.id,
      avatar: u.user.avatar
    };
  });

  const recentlyJoinedUsers = recentlyJoinedData?.projectUsers?.nodes.map((u: any) => {
    return {
      name: u.user.name,
      email: u.user.email,
      id: u.user.id,
      value: u.user.id,
      avatar: u.user.avatar
    };
  });

  const addedUser = addedUserData?.projectUsers?.nodes?.map((u: any) => {
    return {
      name: u.user.name,
      email: u.user.email,
      id: u.user.id,
      avatar: u.user.avatar
    };
  });

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  // const onConfirm = () => {
  //   setIsSubmitting(true);
  //   try {
  //     props.onSubmitted(confirmAssignees);
  //     modalRef?.current?.closeModal();
  //   } catch (e) {
  //     pushError(e);
  //   } finally {
  //     setIsSubmitting(false);
  //   }
  // };

  return (
    <Modal ref={modalRef}>
      <KeyboardAvoidingView behavior="height">
        <Box bg="#FFFFFF" height={350}>
          <Content pt={5} height={350}>
            <Text mb={4} color={COLORS.neutrals.gray90}>
              Add Assignees
            </Text>

            <Divider mb={4} />

            <Input
              placeholder="Enter a member's name"
              width="full"
              borderRadius="4"
              fontSize="14"
              InputRightElement={
                <Pressable pr="2" py="6" onPress={() => setFilteredValue('')}>
                  <Icon name="cancel" />
                </Pressable>
              }
              // onChangeText={_.debounce(value => {
              //   setFilteredValue(value);
              // }, 300)}
              onChangeText={value => {
                setFilteredValue(value);
              }}
              value={filteredValue}
            />

            {!!filteredValue ? (
              <>
                {projectDataLoading && (
                  <View style={[styles.container, styles.horizontal]}>
                    <ActivityIndicator />
                  </View>
                )}
                <FlatList
                  keyboardShouldPersistTaps="handled"
                  data={inviteProjectUser}
                  keyExtractor={item => item.id}
                  renderItem={({ item, index }) => (
                    <Box style={styles.flatList}>
                      {index === 0 && (
                        <>
                          <Text color={COLORS.neutrals.black}>Search results</Text>
                          <Divider my={1} />
                        </>
                      )}
                      <TouchableOpacity
                        onPress={() => {
                          setSelectedUsers(pre => {
                            const findPreId = pre.find(preId => preId === item.id);
                            if (findPreId) {
                              return pre.filter(preId => preId !== findPreId);
                            } else {
                              // setFilteredValue('');
                              return [...pre, item.id];
                            }
                          });
                        }}
                      >
                        <HStack alignItems="center" space={3} mt={4}>
                          <Avatar
                            size="32px"
                            source={{
                              uri: item.avatar
                            }}
                          />

                          <VStack width="76%">
                            <Text numberOfLines={1} ellipsizeMode="tail" style={{ fontWeight: '600', fontSize: 14 }}>
                              {item.name}
                            </Text>
                            <Text style={{ fontWeight: '400', color: COLORS.neutrals.gray90 }}>{item.email}</Text>
                          </VStack>

                          {selectedUsers.find(preId => preId === item.id) && <Icon name="tick" fill="#0695D7" />}
                        </HStack>
                      </TouchableOpacity>
                    </Box>
                  )}
                />
              </>
            ) : null}

            {addedUser ? (
              <>
                {/* Current add assignee */}
                <Box mt={4}>
                  {/* <Text color={COLORS.neutrals.gray90}>
                  Assigned users
                </Text>
                <Divider mt={1} /> */}
                  <FlatList
                    keyboardShouldPersistTaps="handled"
                    data={addedUser}
                    keyExtractor={item => item.id}
                    renderItem={({ item }) => (
                      <Box>
                        <HStack alignItems="center" space={3} mt={4}>
                          <Avatar
                            size="32px"
                            source={{
                              uri: item.avatar
                            }}
                          />
                          <VStack width={isTablet ? '90%' : '78%'}>
                            <Text style={{ fontWeight: '600', fontSize: 14 }}>{item.name}</Text>
                            <Text style={{ fontWeight: '400', color: COLORS.neutrals.gray90 }}>{item.email}</Text>
                          </VStack>
                          <TouchableOpacity
                            onPress={() => {
                              setSelectedUsers(pre => {
                                const findPreId = pre.find(preId => preId === item.id);
                                if (findPreId) {
                                  return pre.filter(preId => preId !== findPreId);
                                } else {
                                  return [...pre, item.id];
                                }
                              });
                            }}
                          >
                            <Box alignItems="flex-end">
                              <Icon name="cancel" fill="#0695D7" />
                            </Box>
                          </TouchableOpacity>
                        </HStack>
                      </Box>
                    )}
                  />
                </Box>
              </>
            ) : (
              <Box>
                <Text mb={2} mt={6} color={COLORS.neutrals.gray90}>
                  Recently joined
                </Text>

                {projectDataLoading && (
                  <View style={[styles.container, styles.horizontal]}>
                    <ActivityIndicator />
                  </View>
                )}
                <Divider mb={4} />
                <FlatList
                  keyboardShouldPersistTaps="handled"
                  data={recentlyJoinedUsers}
                  keyExtractor={item => item.id}
                  renderItem={({ item }) => (
                    <Box>
                      <TouchableOpacity
                        onPress={() => {
                          setSelectedUsers(pre => {
                            const findPreId = pre.find(preId => preId === item.id);
                            if (findPreId) {
                              return pre.filter(preId => preId !== findPreId);
                            } else {
                              return [...pre, item.id];
                            }
                          });
                        }}
                      >
                        <HStack alignItems="center" space={3}>
                          <Avatar
                            size="32px"
                            source={{
                              uri: item.avatar
                            }}
                          />

                          <VStack width="85%">
                            <Text style={{ fontWeight: '600', fontSize: 14 }}>{item.name}</Text>
                            <Text style={{ fontWeight: '400', color: COLORS.neutrals.gray90 }}>{item.email}</Text>
                          </VStack>
                        </HStack>
                        <Divider my={2} />
                      </TouchableOpacity>
                    </Box>
                  )}
                />
              </Box>
            )}
          </Content>
          <Footer>
            <Button
              variant="primary"
              onPress={() => {
                dispatch(taskActions.AddAssignee(selectedUsers as any));
                modalRef.current.closeModal();
              }}
            >
              Add Assignee
            </Button>
          </Footer>
        </Box>
      </KeyboardAvoidingView>
    </Modal>
  );
});

const styles = StyleSheet.create({
  memberBox: {
    borderWidth: 1,
    borderColor: COLORS.neutrals.gray40,
    borderRadius: 8,
    paddingLeft: 10,
    alignItem: 'center'
  },
  flatList: {
    borderWidth: 1,
    borderColor: COLORS.neutrals.gray40,
    padding: 12
  },
  container: {
    flex: 1,
    justifyContent: 'center'
  },
  horizontal: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 10
  }
});

TaskAssigneeModal.displayName = 'TaskAssigneeModal';
export default TaskAssigneeModal;
