import { Gql } from '@src/api';
import { Icon } from '@src/commons';
import CommentModal from '@src/modules/Task/components/Tasks/CommentModal';
import { useSelector } from '@src/store';
import _, { assign } from 'lodash';
import moment from 'moment';
import { Box, FlatList, HStack, Text, VStack } from 'native-base';
import React, { memo, useCallback, useEffect, useMemo, useRef } from 'react';
import { Alert, Pressable, StyleSheet, TouchableOpacity } from 'react-native';
import ParsedText from 'react-native-parsed-text';
import TasksCommentModel from '@src/database/model/task-comment.model';
import { TaskComment } from 'task-comment';
import { FlashList } from '@shopify/flash-list';
import Avatar from '@src/commons/Avatar';

type Props = {
  selectedTaskId: string;
  taskId: string;
};

interface CommentItemProps {
  item: TaskComment;
  isUser: boolean;
  canDelete: boolean;
}

export const CommentComponent: React.FC<Props> = props => {
  const commentRef = useRef<any>(null);
  const { user } = useSelector(state => state.auth);
  const project = useSelector(state => state.project);
  const role = project.projectUserRole;
  const [comments, setComments] = React.useState<any[]>([]);

  const canDelete = useMemo(
    () => role === Gql.ProjectUserRoleType.CloudCoordinator || role === Gql.ProjectUserRoleType.ProjectOwner,
    [role]
  );

  const fetchComments = useCallback(async () => {
    const comments = await TasksCommentModel.getTaskComment(parseInt(props.selectedTaskId));
    setComments(comments);
  }, [props.selectedTaskId]);

  useEffect(() => {
    fetchComments();
  }, [props.selectedTaskId]);

  const deleteComment = useCallback(async (commentId: string) => {
    try {
      await TasksCommentModel.deleteComment(commentId);
      fetchComments();
    } catch (error) {}
  }, []);

  const regexPattern = (name: string) => new RegExp('@' + name);

  const CommentItem: React.FC<CommentItemProps> = memo(({ item, isUser, canDelete }) => {
    const regexArr = useMemo(() => {
      return (item?.message.match(/\[(.*?)\]/g) || []).map(match => {
        const name = match.replace(/[\[\]]/g, '');
        return {
          pattern: regexPattern(name),
          style: { color: '#2a64f9' }
        };
      });
    }, [item]);

    return (
      <Box pl={['0', '4']} pr={['0', '5']} py="2">
        <HStack space={[2, 3]} justifyContent="space-between">
          <Avatar
            assignees={[{ assigneeNo: '1', name: item.user?.name, avatarUrl: item.user?.avatarUrl }]}
            maxVisible={0}
            type={'task'}
          />
          <VStack flex={1}>
            <HStack space={2}>
              <Text
                _dark={{ color: 'warmGray.50' }}
                color="coolGray.800"
                bold
                ellipsizeMode="tail"
                numberOfLines={1}
                maxWidth={'50%'}
              >
                {item.user?.name}
              </Text>
              <Text fontSize="xs" _dark={{ color: 'warmGray.50' }} color="coolGray.800" alignSelf="flex-start">
                {moment(item.createdAt).format('D MMM YYYY, h:mma')}
              </Text>
            </HStack>
            <ParsedText parse={regexArr} childrenProps={{ allowFontScaling: false }}>
              {item?.message.replace(/ *\([^)]*\)*/g, '')}
            </ParsedText>
          </VStack>
          {!isUser && canDelete && (
            <Pressable
              onPress={() =>
                Alert.alert('Delete Comment', 'Are you sure you want to delete this comment?', [
                  { text: 'Cancel', style: 'cancel' },
                  { text: 'Delete', style: 'destructive', onPress: () => deleteComment(item.id as string) }
                ])
              }
            >
              <Box flex={1} justifyContent={'center'} alignItems={'center'}>
                <Icon name="delete-bin" width={15} height={15} />
              </Box>
            </Pressable>
          )}
        </HStack>
      </Box>
    );
  });

  return (
    <Box>
      <Box style={{ flex: 1 }}>
        <FlashList
          data={comments}
          renderItem={({ item }) => <CommentItem item={item} isUser={false} canDelete={canDelete} />}
          keyExtractor={item => item.id}
          estimatedItemSize={5}
        />
      </Box>
      <Box flexDirection="row" mt={4}>
        <Box flex={0.7}>
          <Avatar
            assignees={[{ assigneeNo: '1', name: user?.name ?? '', avatarUrl: user?.avatar ?? '' }]}
            maxVisible={0}
            type={'task'}
          />
        </Box>
        <Box flex={6}>
          <TouchableOpacity onPressIn={() => commentRef.current?.pushModal()} style={styles.mentionInput} />
        </Box>
      </Box>
      <CommentModal refetchComments={fetchComments} ref={commentRef} module="task" id={props.selectedTaskId} />
    </Box>
  );
};

const styles = StyleSheet.create({
  mentionInput: {
    borderWidth: 1,
    borderColor: 'neutrals.gray50',
    borderRadius: 5,
    padding: 5,
    textAlignVertical: 'top',
    height: 70
  }
});

export default memo(CommentComponent);
