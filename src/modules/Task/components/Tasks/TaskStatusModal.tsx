import { Icon, Modal } from '@commons';
import { Gql } from '@src/api';
import { ProposeAction, TaskStatusType } from '@src/api/graphql';
import { pushError, pushSuccess } from '@src/configs';
import useUpdateTask from '@src/mutation/task/useUpdateTask';
import { taskActions } from '@src/slice/tasks.slice';
import { useDispatch, useSelector } from '@src/store';
import { Box, Divider, Flex, HStack, Text, View } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { Alert, ModalProps, StyleSheet, TouchableOpacity } from 'react-native';

interface Props {
  proposedStatus?: any;
  onSaved?: () => void;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const TaskStatusModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const dispatch = useDispatch();
  const { task } = useSelector(state => state.tasks);
  const authData = useSelector(state => state.auth);

  const hasProposedStatus = !!props?.proposedStatus;
  const type = hasProposedStatus ? 'respond' : 'status';
  const proposedStatus = props?.proposedStatus === 'Completed' ? 'Closed' : props?.proposedStatus;
  const { mutateAsync: updateTask } = useUpdateTask();

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  // const [updateTask, { loading: updateLoading }] = Gql.useUpdateOneTaskMutation({
  //   onCompleted: () => {
  //     pushSuccess('Task status proposed successfully.');
  //   },
  //   onError: (err: any) => {
  //     pushError(err.message);
  //   }
  // });

  const [changeStatus, { loading: updatingStatus }] = Gql.useUpdateTaskStatusMutation({
    onCompleted: () => {
      pushSuccess('Task status updated successfully.');
      if (props?.onSaved) props?.onSaved();
    },
    onError: (err: any) => {
      pushError(err.message);
    }
  });

  const validateStatus = async (status: any, type: 'status' | 'respond', proposedStatus: any) => {
    if (type === 'status') {
      // update proposed status
      await updateTask({
        id: task?.id as string,
        newTask: {
          proposedStatus: status
        }
      });
    } else {
      await updateTask({
        id: task?.id as string,
        newTask: {
          ...(status === ProposeAction.Validate
            ? { status: proposedStatus === 'Closed' ? TaskStatusType.Completed : proposedStatus }
            : {}),
          proposedStatus: null
        }
      });
    }
  };

  const onStatusChange = async (status: any, type: 'status' | 'respond') => {
    let title = `Respond as ${status}`;
    let content = 'Confirm respond to proposed status as ' + proposedStatus + '?';

    if (type === 'status') {
      title = `Change status to ${status === TaskStatusType.Completed ? 'closed' : status}`;
      content = `Confirm propose change status to ${status === TaskStatusType.Completed ? 'closed' : status}?`;
    }

    try {
      Alert.alert(
        title,
        content,
        [
          {
            text: 'No',
            onPress: () => {},
            style: 'cancel'
          },
          {
            text: 'Yes',
            onPress: () => validateStatus(status, type, proposedStatus)
          }
        ],
        { cancelable: false }
      );
    } catch (err: any) {
      pushError(err.message);
    }
  };

  // const loading = updateLoading || updatingStatus;

  return (
    <Modal ref={modalRef} type="bottom">
      <View>
        <Flex direction="column" p={5} mb={5}>
          <TouchableOpacity
            // disabled={loading}
            onPress={() => {
              // dispatch the set status action

              // modalRef?.current?.closeModal();
              const val = hasProposedStatus ? ProposeAction.Validate : TaskStatusType.Hold;
              onStatusChange(val, type);
            }}
          >
            <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
              <HStack space={2}>
                <Icon name="flag" fill={hasProposedStatus ? '#53cf30' : '#ffc266'} />
                <Text fontSize="16px" color="#8F8989">
                  {hasProposedStatus ? 'Validate' : 'Propose > On Hold'}
                </Text>
              </HStack>
              {/* {task?.status?.includes?.(TaskStatusType.Hold) ? <Icon name="tick" /> : <></>} */}
            </Box>
          </TouchableOpacity>

          <Divider mt={4} />
          <TouchableOpacity
            // disabled={loading}
            onPress={() => {
              // dispatch the set status action

              const val = hasProposedStatus ? ProposeAction.Reject : TaskStatusType.Completed;
              onStatusChange(val, type);
              // dispatch(taskActions.initialState({ ...task, status: TaskStatusType.Completed } as any));
              // modalRef?.current?.closeModal();
            }}
          >
            <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
              <HStack space={2}>
                <Icon name="flag" fill={hasProposedStatus ? '#f50000' : '#9ce255'} />
                <Text fontSize="16px" color={'#8F8989'}>
                  {hasProposedStatus ? 'Reject' : 'Propose > Closed'}
                </Text>
              </HStack>
              {/* {task?.status?.includes?.(TaskStatusType.Completed) ? <Icon name="tick" /> : <></>} */}
            </Box>
          </TouchableOpacity>
        </Flex>
      </View>
    </Modal>
  );
});

const style = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    height: 48,
    justifyContent: 'space-between',
    padding: 6,
    alignItems: 'center'
  },
  filterButton: {
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20
  }
});

TaskStatusModal.displayName = 'TaskStatusModal';
export default TaskStatusModal;
