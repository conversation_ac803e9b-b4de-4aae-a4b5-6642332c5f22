import { Icon } from '@src/commons';
import { COLORS } from '@src/constants/colors';
import database from '@src/database/index.native';
import ProjectUserModel from '@src/database/model/project-user.model';
import { taskActions } from '@src/slice/tasks.slice';
import { useDispatch } from '@src/store';
import moment from 'moment';
import { Avatar, Box, Divider, Flex, HStack, Row, Text, VStack } from 'native-base';
import React, { memo, useEffect, useMemo, useState } from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import AvatarComponent from '@src/commons/Avatar';

type Props = {
  item: any;
  role: any;
  auth: any;
  navigateToTaskDetail: (id: string) => void;
  handleLongPress: (title: string) => void;
};

const TaskList = ({ item, auth, role, navigateToTaskDetail, handleLongPress }: Props) => {
  const dispatch = useDispatch();
  const isProposed = !!item?.proposedStatus;

  let proposedColor = '';

  if (item?.proposedStatus === 'Hold') {
    proposedColor = '#ffc266';
  } else if (item?.proposedStatus === 'Completed') {
    proposedColor = '#9ce255';
  }

  const tasksStatus: any = {
    Open: { status: isProposed ? 'Proposed' : 'Open', color: isProposed ? proposedColor : '#F40000' },
    InProgress: { status: isProposed ? 'Proposed' : 'In Progress', color: isProposed ? proposedColor : '#3E78CF' },
    Hold: { status: isProposed ? 'Proposed' : 'On Hold', color: isProposed ? proposedColor : '#F29100' },
    Completed: { status: 'Closed', color: '#18A601' }
  };

  const date = useMemo(
    () => (moment(item.dueDate).isBefore(moment()) && item.status !== 'Completed' ? '#FF2020' : '#585757'),
    [item.dueDate, item.status]
  );

  return (
    <HStack style={style.box} py={3} key={item}>
      <TouchableOpacity
        style={{ width: '100%' }}
        onPress={() => navigateToTaskDetail(item.id)}
        onLongPress={() => handleLongPress(item?.title)}
      >
        <VStack>
          {/* <Flex direction="row" justifyItems="center" alignItems="center" height={6}> */}
          <HStack space={4}>
            <Text numberOfLines={1} ellipsizeMode="tail" fontSize={'16'}>
              {item.title}
            </Text>
            <Text>{item?.memoUrl && <Icon name="memo" width={20} height={20} />}</Text>
            <Text>{item?.isUrgent && <Icon name="urgent-flag" width={20} height={20} />}</Text>
          </HStack>
          {/* </Flex> */}
          <HStack flex={1} alignItems={'center'}>
            <Text
              numberOfLines={2}
              ellipsizeMode="tail"
              color={COLORS.neutrals.gray90}
              fontWeight={'light'}
              width={'92%'}
            >
              {item.description ?? ''}
            </Text>
            <TouchableOpacity
              onPress={() => {
                dispatch(taskActions.OpenTaskOptionModal({ taskId: item.id, ownerId: item.ownerId }));
              }}
              style={{ width: '10%' }}
              hitSlop={30}
            >
              <HStack alignSelf="center" width={100}>
                {item._status !== 'synced' ? <Icon name="unsync" /> : null}
                {(auth?.user?.id === item.ownerId || role.tasks.canDelete) && (
                  <Icon name="option-dot" style={{ marginLeft: 12, marginRight: 4 }} />
                )}
              </HStack>
            </TouchableOpacity>
          </HStack>
          <HStack style={{ justifyContent: 'space-around', alignItems: 'center' }} flex={1} marginTop={2}>
            <Text style={style.descriptionText} color="neutrals.gray70" flex={0.1}>
              #{item.taskCode}
            </Text>
            <Row textAlign="start" alignItems={'center'} flex={0.25}>
              <Box height={5} width={1.5} backgroundColor={tasksStatus?.[item.status]?.color} mr={2} />
              <Text style={style.descriptionText}>{tasksStatus?.[item.status]?.status}</Text>
            </Row>
            <Flex direction="row" justifyContent="center" flex={0.25}>
              <AvatarComponent assignees={item.assignees} maxVisible={3} type="task" group />
            </Flex>
            <Row flex={0.25}>
              <Text color="neutrals.gray90" style={style.descriptionText}>
                Due:
              </Text>
              {!item.dueDate ? (
                <Text> - </Text>
              ) : (
                <Text
                  style={{
                    marginLeft: 4,
                    color: date
                  }}
                >
                  {moment(item.dueDate).format('D MMM')}
                </Text>
              )}
            </Row>
          </HStack>
        </VStack>
        <Divider mt={5} />
      </TouchableOpacity>
    </HStack>
  );
};

const style = StyleSheet.create({
  box: {
    backgroundColor: '#FFFFFF',
    paddingLeft: 0,
    borderRadius: 12,
    overflow: 'hidden'
  },
  descriptionText: {
    fontWeight: '400',
    fontSize: 14
  }
});

export default memo(TaskList);
