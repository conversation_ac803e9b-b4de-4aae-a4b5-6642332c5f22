import { Modal } from '@commons';
import { Gql } from '@src/api';
import { ProjectUser } from '@src/api/graphql';
import { pushError } from '@src/configs';
import database from '@src/database/index.native';
import TasksCommentModel from '@src/database/model/task-comment.model';
import { useSelector } from '@src/store';
import _, { debounce, isEqual } from 'lodash';
import { Box, Button, Pressable, Spinner, Text, VStack } from 'native-base';
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { Alert, FlatList, ModalProps, StyleSheet } from 'react-native';
import { MentionInput } from 'react-native-controlled-mentions';

interface Props {
  data?: any;
  action?: string;
  value?: string;
  module: 'task' | 'projectDocument' | 'drawingLink';
  id?: string;
  drawingLinkId?: number;
  createComment?: (message: string) => Promise<void>;
  refetchComments?: () => void;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const CommentModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const documentStates = useSelector(state => state.documentWorkspace, isEqual);
  const [message, setMessage] = useState<string>('');
  const [mention, setMention] = useState<string | null>(null);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const [getProjectsUsers, { data: recentlyJoinedData, fetchMore, loading }] = Gql.useProjectUsersLazyQuery({
    variables: {
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.ProjectUserSortFields.CreatedAt
        }
      ],
      paging: {
        limit: 10
      }
    },
    fetchPolicy: 'cache-and-network'
  });

  const data = useSelector(state => state.auth);

  const onLoadMore = async () => {
    if (!recentlyJoinedData?.projectUsers?.pageInfo?.hasNextPage) return;
    await fetchMore({
      variables: {
        paging: {
          offset: recentlyJoinedData?.projectUsers.nodes.length,
          limit: 10
        }
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;
        const result = Object.assign({}, prev, {
          projectUsers: {
            ...fetchMoreResult.projectUsers,
            nodes: [...prev.projectUsers.nodes, ...fetchMoreResult.projectUsers.nodes]
          }
        });
        return result;
      }
    });
  };

  const sanitizedProjectUsers = (projectUsers: any) => {
    if (!projectUsers || loading) return [];

    return projectUsers?.map((u: ProjectUser) => {
      return {
        name: u.user.name,
        email: u.user.email,
        id: u.user.id,
        value: u.user.id,
        avatar: u.user.avatar
      };
    });
  };

  const debouncedGetProjectsUsers = useCallback(
    debounce((mentionValue: any) => {
      getProjectsUsers({
        variables: {
          filter: {
            ...(mentionValue && mentionValue.length > 0 && { user: { name: { like: `%${mentionValue}%` } } })
          }
        }
      });
    }, 1000),
    []
  );

  useEffect(() => {
    if (mention === null) return;
    debouncedGetProjectsUsers(mention);
  }, [mention]);

  const recentlyJoinedUsers = useMemo(
    () => sanitizedProjectUsers(recentlyJoinedData?.projectUsers.nodes),
    [recentlyJoinedData?.projectUsers.nodes, loading]
  );

  const onSubmit = async () => {
    if (message?.length > 1)
      try {
        if (props.module === 'task') {
          const TaskAttachment = new TasksCommentModel(database.get('task_comments'), {} as any);
          await TaskAttachment.createComment({
            taskId: Number(props.id),
            userId: Number(data?.user?.id),
            message: message
          }).then(() => {
            props.refetchComments?.();
          });
        } else {
          await props?.createComment?.(message);
        }
      } catch (e) {
        pushError(e);
      } finally {
        setMessage('');
        modalRef?.current?.closeModal();
      }
  };

  const [createProjectDocumentComment, { loading: documentCommentLoading }] =
    Gql.useCreateProjectDocumentCommentMutation({
      onCompleted: () => {
        setMessage('');
        modalRef?.current?.closeModal();
      },
      onError: (err: any) => {
        pushError(err);
      }
    });

  const memorizeSuggestion = useCallback(
    () =>
      ({ keyword, onSuggestionPress }: any) => {
        if (keyword === undefined) return null;
        setMention(keyword);
        if (loading) return <Spinner size={'lg'} />;
        return (
          <Box borderWidth={0.7} borderColor={'border.gray'} bgColor={'neutrals.white'} mt={0}>
            <FlatList
              keyboardShouldPersistTaps="handled"
              data={recentlyJoinedUsers}
              onEndReachedThreshold={0.1}
              onEndReached={onLoadMore}
              ListFooterComponent={
                recentlyJoinedData?.projectUsers.pageInfo.hasNextPage ? <Spinner size={'lg'} /> : null
              }
              renderItem={({ item, index }) => (
                <Pressable
                  key={index}
                  onPress={() => {
                    onSuggestionPress(item);
                  }}
                  pl={5}
                  pr={5}
                  pt={3}
                  pb={3}
                  borderBottomColor={'border.gray'}
                  borderBottomWidth={1}
                >
                  <Text>{item?.name}</Text>
                </Pressable>
              )}
              keyExtractor={item => item.id}
            />
          </Box>
        );
      },
    [recentlyJoinedUsers]
  );

  const renderSuggestions = useMemo(() => memorizeSuggestion(), [memorizeSuggestion]);

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="middle">
      <Box p={6}>
        <Text textAlign="center" mb={4} fontSize={16}>
          Insert Comment
        </Text>

        <VStack>
          <MentionInput
            value={message}
            multiline
            placeholder="Leave a comment here..."
            onChange={value => {
              setMessage(value);
            }}
            style={{ ...styles.mentionInput, height: 70 }}
            partTypes={[
              {
                trigger: '@',
                renderSuggestions,
                isBottomMentionSuggestionsRender: true,
                isInsertSpaceAfterMention: true,
                textStyle: { color: 'black.1', backgroundColor: 'neutrals.gray5' }
              }
            ]}
          />
        </VStack>

        <Box flexDirection="row" justifyContent="flex-end" mt={5}>
          <Button
            bg="transparent"
            _pressed={{ bg: 'transparent' }}
            onPress={() => {
              modalRef.current.closeModal();
            }}
          >
            <Text color="#0695D7">Cancel</Text>
          </Button>
          <Button
            bg="transparent"
            _pressed={{ bg: 'transparent' }}
            onPress={onSubmit}
            // isLoading={taskCommentLoading || documentCommentLoading || drawingLinkLoading}
            spinner={<Spinner size="sm" color="#0695D7" />}
          >
            <Text>Send</Text>
          </Button>
        </Box>
      </Box>
    </Modal>
  );
});
const styles = StyleSheet.create({
  mentionInput: {
    borderWidth: 1,
    borderColor: 'neutrals.gray50',
    borderRadius: 5,
    padding: 5,
    textAlignVertical: 'top',
    color: 'black'
  }
});

CommentModal.displayName = 'CommentModal';
export default CommentModal;
