import { Icon, Modal } from '@commons';
import { COLORS } from '@src/constants';
import { taskActions } from '@src/slice/tasks.slice';
import { useDispatch, useSelector } from '@src/store';
import { Box, Button, Divider, Flex, HStack, Spacer, Switch, Text, View } from 'native-base';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps, StyleSheet, TouchableOpacity } from 'react-native';

export type TaskFilterType = {
  isAssignToMe: boolean;
  isHideCompletedTask: boolean;
};
interface Props {
  onFilter?: (values: TaskFilterType) => void;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const QuickFilter = forwardRef<ModalRef, Props>((props, ref) => {
  const { onFilter } = props;
  const modalRef = useRef<any>(null);

  const { filter } = useSelector(state => state.tasks);
  const dispatch = useDispatch();

  const [isAssignToMe, setIsAssignToMe] = useState<boolean>(false);
  const [isHideCompletedTask, setIsHideCompletedTask] = useState<boolean>(false);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  useEffect(() => {
    setIsAssignToMe(filter.assignToMe);
    setIsHideCompletedTask(filter.hideCompleted);
  }, [filter]);

  const onSubmit = () => {
    dispatch(taskActions.setFilter({ assignToMe: isAssignToMe, hideCompleted: isHideCompletedTask }));
    modalRef.current?.closeModal();
  };

  return (
    <Modal ref={modalRef}>
      <View>
        <Flex direction="row" style={style.container} width="100%">
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            bg="transparent"
            onPress={() => modalRef.current?.closeModal()}
          >
            <HStack alignItems={'center'} space={1}>
              <Icon name="chevron-left" fill={COLORS.primary[1]} />
              <Text color={COLORS.primary[1]}>Back</Text>
            </HStack>
          </Button>
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            bg="transparent"
            onPress={() => {
              setIsAssignToMe(false);
              setIsHideCompletedTask(false);
              dispatch(taskActions.setFilter({ assignToMe: false, hideCompleted: false }));
              modalRef.current?.closeModal();
            }}
          >
            <Text color={COLORS.primary[1]}>Clear</Text>
          </Button>
        </Flex>
        <Divider color="#E8E8E8" />
        <Flex direction="column" p={5}>
          <TouchableOpacity onPress={() => setIsAssignToMe(!isAssignToMe)}>
            <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
              <HStack space={2}>
                <Text fontSize="16px">Assigned to me</Text>
              </HStack>
              {isAssignToMe ? <Icon name="tick" /> : <></>}
            </Box>
          </TouchableOpacity>

          <Divider mt={4} />

          <TouchableOpacity onPress={() => setIsHideCompletedTask(!isHideCompletedTask)}>
            <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={6}>
              <HStack space={2}>
                <Text fontSize="16px">Hide completed tasks</Text>
              </HStack>
              {isHideCompletedTask ? <Icon name="tick" /> : <></>}
            </Box>
          </TouchableOpacity>

          <Spacer my={4} />

          <Button
            variant="primary"
            onPress={() => {
              onSubmit();
              modalRef?.current?.closeModal();
            }}
          >
            Apply
          </Button>
        </Flex>
      </View>
    </Modal>
  );
});

const style = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    height: 48,
    justifyContent: 'space-between',
    padding: 6,
    alignItems: 'center'
  },
  filterButton: {
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20
  }
});

export default QuickFilter;
