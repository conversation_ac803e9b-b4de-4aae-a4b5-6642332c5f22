import { Modal } from '@commons';
import { COLORS } from '@src/constants';
import { taskActions } from '@src/slice/tasks.slice';
import { useDispatch, useSelector } from '@src/store';
import { Box, Button, Divider, Flex, Input, Text, View } from 'native-base';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps, StyleSheet } from 'react-native';

interface Props {}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const IDFilter = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const [filteredValue, setFilteredValue] = useState<string>('');
  const [selectedId, setSelectedId] = useState<number[]>([]);

  const { filter } = useSelector(state => state.tasks);
  const dispatch = useDispatch();

  const handleButtonPress = () => {
    if (filteredValue !== '') {
      dispatch(taskActions.setFilter({ ...filter, id: filteredValue }));
      modalRef.current.closeModal();
    }
  };

  useEffect(() => {
    if (filter.id) {
      setFilteredValue(filter.id);
    }
  }, [filter.id]);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  return (
    <Modal ref={modalRef} type="bottom">
      <View h={400}>
        <Flex direction="row" style={style.container} paddingBottom={1} width="100%" justifyContent="space-between">
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            bg="transparent"
            onPress={() => {
              //   props.onAllFormCode(selectedId);
              modalRef.current.closeModal();
            }}
          >
            <Text color="#0695D7">Back</Text>
          </Button>

          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            bg="transparent"
            onPress={() => {
              setFilteredValue('');
              dispatch(taskActions.setFilter({ ...filter, id: undefined }));
              modalRef.current.closeModal();
            }}
          >
            <Text color="#0695D7">Clear</Text>
          </Button>
        </Flex>
        <View bg="#F0F0F0" h="40px" justifyContent="center">
          <Text fontSize="16" fontWeight="600px" pl={5}>
            Filter
          </Text>
        </View>

        <Box p={5}>
          <Text mb={4} color={COLORS.neutrals.gray90}>
            Add ID: {selectedId.join(', ')}
          </Text>

          <Divider mb={4} />

          <Input
            placeholder="Enter an ID"
            width="full"
            borderRadius="4"
            fontSize="14"
            maxLength={5}
            keyboardType="numeric"
            value={filteredValue}
            onChangeText={value => {
              setFilteredValue(value);
            }}
          />

          <Button
            _pressed={{
              bg: 'blue.200',
              opacity: 0.8
            }}
            mt={5}
            bg="blue.300"
            onPress={() => handleButtonPress()}
          >
            <Text>Select ID</Text>
          </Button>
        </Box>
      </View>
    </Modal>
  );
});

const style = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    height: 48,
    justifyContent: 'space-between',
    padding: 6,
    alignItems: 'center'
  },
  filterButton: {
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20
  },
  flatList: {
    borderWidth: 1,
    borderColor: COLORS.neutrals.gray40,
    padding: 12
  }
});

IDFilter.displayName = 'IDFilter';
export default IDFilter;
