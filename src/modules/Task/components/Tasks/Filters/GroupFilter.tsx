import { Icon, Modal } from '@commons';
import { Gql } from '@src/api';
import { ProjectGroup } from '@src/api/graphql';
import { ProjectGroupApiService } from '@src/api/rest';
import { pushError } from '@src/configs';
import { COLORS } from '@src/constants';
import Accordion from '@src/modules/DigitalForm/components/selector-component/Accordion';
import { taskActions } from '@src/slice/tasks.slice';
import { useDispatch, useSelector } from '@src/store';
import { isEqual } from 'lodash';
import { Box, Button, Divider, FlatList, Flex, HStack, Spinner, Text, View } from 'native-base';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { InteractionManager, ModalProps, Platform, StyleSheet, TouchableOpacity } from 'react-native';

// TODO: remove props, use redux instead
interface Props {
  onStatus?: (values: string[]) => void;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const GroupFilter = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const [dataSource, setDataSource] = useState<any>([]);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  // redux
  const { filter } = useSelector(state => state.tasks, isEqual);
  const dispatch = useDispatch();

  const [getGroups, { data, refetch, fetchMore, loading }] = Gql.useGetProjectGroupsLazyQuery({
    variables: {
      paging: {
        limit: 10,
        offset: 0
      }
    },
    fetchPolicy: 'cache-and-network'
  });

  const onLoaded = async () => {
    if (!data?.getProjectGroups.pageInfo.hasNextPage) return;
    await fetchMore({
      variables: {
        paging: {
          offset: data?.getProjectGroups.nodes.length,
          limit: 20
        }
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;
        const result = Object.assign({}, prev, {
          getProjectGroups: {
            ...fetchMoreResult.getProjectGroups,
            nodes: [...prev.getProjectGroups.nodes, ...fetchMoreResult.getProjectGroups.nodes]
          }
        });
        return result;
      }
    });
  };
  // const taskGroup = data?.getProjectGroups?.nodes ?? [];

  const handlePress = (item: ProjectGroup) => {
    const selectedGroup = { id: item.id, title: item.title };
    dispatch(taskActions.setFilter({ ...filter, group: selectedGroup }));
    modalRef.current.closeModal();
    // dispatch(
    //   taskActions.setFilter({
    //     ...filter,
    //     group: filter.group?.includes(item.id)
    //       ? filter.group?.filter((groupId: string) => groupId !== item.id)
    //       : [...(filter.group ?? []), item.id]
    //   })
    // );
  };

  return (
    <Modal ref={modalRef} type="bottom" onShow={() => getGroups()}>
      <Flex direction="row" style={styles.container} width="100%">
        <Button
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          bg="transparent"
          onPress={() => modalRef.current?.closeModal()}
        >
          <HStack alignItems={'center'} space={1}>
            <Icon name="chevron-left" fill={COLORS.primary[1]} />
            <Text color={COLORS.primary[1]}>Back</Text>
          </HStack>
        </Button>
        <Button
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          bg="transparent"
          onPress={() => dispatch(taskActions.setFilter({ ...filter, group: [] }))}
        >
          <Text color={COLORS.primary[1]}>Clear</Text>
        </Button>
      </Flex>
      <View bg="#F0F0F0" h="40px" justifyContent="center">
        <Text fontSize="16" fontWeight="600px" pl={5}>
          Filter Group
        </Text>
      </View>

      <FlatList
        keyboardShouldPersistTaps="handled"
        height="50%"
        contentContainerStyle={{ paddingHorizontal: 20, paddingTop: 20 }}
        data={taskGroup}
        onEndReached={onLoaded}
        ListFooterComponent={data?.getProjectGroups.pageInfo.hasNextPage ? <Spinner size={'lg'} /> : null}
        keyExtractor={(item: any) => item.id}
        onEndReachedThreshold={0.1}
        renderItem={({ item }: any) => {
          return (
            <>
              <Accordion
                item={item}
                title={item?.title ?? ''}
                isParent={!item?.projectGroupId}
                onPressChild={handlePress}
              />
              {/* if the group id is already in the filter group array, show the tick icon
    else show nothing  */}
              {/* {filter.group?.includes(item.id) && <Icon name="tick" />} */}
            </>
          );
        }}
      />
    </Modal>
  );
});

const styles = StyleSheet.create({
  box: {
    backgroundColor: '#FFFFFF',
    padding: 10,
    paddingLeft: 0,
    borderRadius: 12,
    overflow: 'hidden'
  },
  addButton: {
    position: 'absolute',
    bottom: 10,
    alignSelf: 'flex-end',
    right: 30
  },
  tabContainer: {
    backgroundColor: '#FFFFFF'
  },
  container: {
    backgroundColor: '#FFFFFF',
    height: 48,
    justifyContent: 'space-between',
    padding: 6,
    alignItems: 'center'
  }
});

GroupFilter.displayName = 'GroupFilter';
export default GroupFilter;
