import { Content, Modal } from '@commons';
import { Gql } from '@src/api';
import { COLORS } from '@src/constants';
import { taskActions } from '@src/slice/tasks.slice';
import { useDispatch, useSelector } from '@src/store';
import _, { debounce, isEqual } from 'lodash';
import { Avatar, Box, Button, Divider, FlatList, Flex, HStack, Input, Text, View, VStack } from 'native-base';
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps, StyleSheet, TouchableOpacity } from 'react-native';

interface Props {
  onCc?: (values: string[]) => void;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const role: any = {
  CanView: 'Viewer',
  CanEdit: 'Editor',
  ProjectOwner: 'Project Owner',
  CloudCoordinator: 'Cloud Coordinator'
};

const CcFilter = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const [filteredValue, setFilteredValue] = useState<string | null>(null);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);

  const dispatch = useDispatch();
  const { filter } = useSelector(state => state.tasks, isEqual);

  const project = useSelector(state => state.project);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const fetchProjectUser = async () => {
    await getProjectUser();
    return await getAddedProjectUser();
  };

  const [getProjectUser, { data: projectUserData }] = Gql.useProjectUsersLazyQuery({
    variables: {
      filter: {
        projectId: { eq: project.projectId ?? '' }
      }
    },
    fetchPolicy: 'cache-and-network'
  });

  const [getAddedProjectUser, { data: addedUserData }] = Gql.useProjectUsersLazyQuery({
    variables: {
      filter: {
        // user: {
        //   id: { in: filter?.cc && filter.cc?.length > 0 ? filter.cc : null }
        // }
        id: { in: filter?.cc && filter.cc?.length > 0 ? filter.cc : null }
      }
    },
    fetchPolicy: 'cache-and-network'
  });

  const inviteProjectUser = projectUserData?.projectUsers?.nodes.map((u: any) => {
    return {
      name: u.user.name,
      email: u.user.email,
      id: u.id,
      value: u.id,
      avatar: u.user.avatar,
      role: u.role
    };
  });

  const addedUser = addedUserData?.projectUsers?.nodes?.map((u: any) => {
    return {
      name: u.user.name,
      email: u.user.email,
      id: u.id,
      avatar: u.user.avatar,
      role: u.role
    };
  });

  useEffect(() => {
    if (filteredValue === null) return;
    debounceRequest(filteredValue);
  }, [filteredValue]);

  const debounceSearchDeals = debounce(keyword => {
    getProjectUser({
      variables: {
        filter: {
          role: { neq: Gql.ProjectUserRoleType.CanView },
          user: {
            name: { like: `%${keyword}%` },
            id: { notIn: filter?.cc }
          }
        }
      }
    });
  }, 1000);

  const debounceRequest = useCallback((value: any) => debounceSearchDeals(value), []);

  return (
    <Modal ref={modalRef} type="bottom" avoidKeyboard={false} onShow={() => fetchProjectUser()}>
      <View style={{ height: '90%' }}>
        <Flex direction="row" style={style.container} width="100%" justifyContent="space-between">
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            bg="transparent"
            onPress={() => {
              // props.onCc(selectedUsers);
              modalRef.current.closeModal();
            }}
          >
            <Text color="#0695D7">Back</Text>
          </Button>

          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            bg="transparent"
            onPress={() => {
              dispatch(
                taskActions.setFilter({
                  ...filter,
                  cc: []
                })
              );
              setFilteredValue('');
              modalRef.current.closeModal();
            }}
          >
            <Text color="#0695D7">Clear</Text>
          </Button>
        </Flex>
        <View bg="#F0F0F0" h="40px" justifyContent="center">
          <Text fontSize="16" fontWeight="600px" pl={5}>
            Filter
          </Text>
        </View>

        <Box p={5}>
          <Text mb={4} color={COLORS.neutrals.gray90}>
            Add Cc
          </Text>

          <Divider mb={4} />

          <Input
            placeholder="Enter a member name or email"
            width="full"
            borderRadius="4"
            fontSize="14"
            onChangeText={_.debounce(value => {
              setFilteredValue(value);
            }, 300)}
          />

          {!!filteredValue ? (
            <>
              <FlatList
                keyboardShouldPersistTaps="handled"
                data={inviteProjectUser}
                keyExtractor={item => item.id}
                renderItem={({ item }) => (
                  <Box style={style.flatList}>
                    <TouchableOpacity
                      onPress={() => {
                        dispatch(
                          taskActions.setFilter({
                            ...filter,
                            cc: filter.cc?.includes(item.id)
                              ? filter.cc?.filter((ccId: string) => ccId !== item.id)
                              : [...(filter.cc ?? []), item.id]
                          })
                        );
                      }}
                    >
                      <HStack width="85%">
                        <Text style={{ fontWeight: '600', fontSize: 14 }} flex={1}>
                          {item.name}
                        </Text>
                        <Text style={{ fontWeight: '400', fontSize: 14, color: '#969696' }} flex={2}>
                          {item.email}
                        </Text>
                      </HStack>
                    </TouchableOpacity>
                  </Box>
                )}
              />
            </>
          ) : null}

          {addedUser ? (
            <>
              {/* Current add cc */}
              <Box mt={4}>
                <Divider mb={4} />
                <FlatList
                  keyboardShouldPersistTaps="handled"
                  data={addedUser}
                  keyExtractor={item => item.id}
                  renderItem={({ item }) => (
                    <Box>
                      <TouchableOpacity
                        onPress={() => {
                          dispatch(
                            taskActions.setFilter({
                              ...filter,
                              cc: filter.cc?.includes(item.id)
                                ? filter.cc?.filter((ccId: string) => ccId !== item.id)
                                : [...(filter.cc ?? []), item.id]
                            })
                          );
                        }}
                      >
                        <HStack alignItems="center" mt={4} justifyContent="space-between">
                          <HStack space={3}>
                            <Avatar
                              size="32px"
                              source={{
                                uri: item.avatar
                              }}
                            />

                            <VStack>
                              <Text style={{ fontWeight: '600', fontSize: 14 }}>{item.name}</Text>
                              <Text style={{ fontWeight: '400', color: COLORS.neutrals.gray90 }}>{item.email}</Text>
                            </VStack>
                          </HStack>
                          <Text style={{ fontWeight: '400', color: COLORS.neutrals.gray90 }}>{role[item.role]}</Text>
                        </HStack>
                      </TouchableOpacity>
                    </Box>
                  )}
                />
              </Box>
            </>
          ) : (
            <>
              {/* Recently Join */}
              <Box>
                <Text mb={4} mt={7} color={COLORS.neutrals.gray90}>
                  Recently joined
                </Text>

                <Divider mb={4} />
                <FlatList
                  keyboardShouldPersistTaps="handled"
                  data={inviteProjectUser}
                  keyExtractor={item => item.id}
                  renderItem={({ item }) => (
                    <Box>
                      <TouchableOpacity
                        onPress={() => {
                          dispatch(
                            taskActions.setFilter({
                              ...filter,
                              cc: filter.cc?.includes(item.id)
                                ? filter.cc?.filter((ccId: string) => ccId !== item.id)
                                : [...(filter.cc ?? []), item.id]
                            })
                          );
                        }}
                      >
                        <HStack alignItems="center" mt={4} justifyContent="space-between">
                          <HStack space={3}>
                            <Avatar
                              size="32px"
                              source={{
                                uri: item.avatar
                              }}
                            />

                            <VStack>
                              <Text style={{ fontWeight: '600', fontSize: 14 }}>{item.name}</Text>
                              <Text style={{ fontWeight: '400', color: COLORS.neutrals.gray90 }}>{item.email}</Text>
                            </VStack>
                          </HStack>
                          <Text style={{ fontWeight: '400', color: COLORS.neutrals.gray90 }}>{role[item.role]}</Text>
                        </HStack>
                      </TouchableOpacity>
                    </Box>
                  )}
                />
              </Box>
            </>
          )}
        </Box>
      </View>
    </Modal>
  );
});

const style = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    height: 48,
    justifyContent: 'space-between',
    padding: 6,
    alignItems: 'center'
  },
  filterButton: {
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20
  },
  flatList: {
    borderWidth: 1,
    borderColor: COLORS.neutrals.gray40,
    padding: 12
  }
});

CcFilter.displayName = 'CcFilter';
export default CcFilter;
