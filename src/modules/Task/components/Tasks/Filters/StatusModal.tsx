import { Icon, Modal } from '@commons';
import { TaskStatusType } from '@src/api/graphql';
import { COLORS } from '@src/constants';
import { taskActions } from '@src/slice/tasks.slice';
import { useDispatch, useSelector } from '@src/store';
import { Box, Button, Divider, Flex, HStack, Text, View } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps, Platform, StyleSheet, TouchableOpacity } from 'react-native';

// TODO: remove props, use redux instead
interface Props {
  onStatus?: (values: string[]) => void;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const StatusModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const [selectedStatus, setSelectedStatus] = useState<[]>([]);

  //redux
  const { filter } = useSelector(state => state.tasks);
  const dispatch = useDispatch();

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  return (
    <Modal ref={modalRef} type="bottom">
      <View>
        <HStack alignItems="center" w="100%" justifyContent="center" position="relative">
          <TouchableOpacity
            style={{ position: 'absolute', left: 16 }}
            onPress={() => {
              modalRef.current.closeModal();
            }}
          >
            <Icon name="chevron-left" />
          </TouchableOpacity>
          <Text fontSize="16" fontWeight="500">
            Filter Status
          </Text>
        </HStack>
        <Flex direction="column" p={5} mb={5}>
          <TouchableOpacity
            onPress={() =>
              dispatch(
                taskActions.setFilter({
                  ...filter,
                  status: filter.status?.includes(TaskStatusType.Open)
                    ? filter.status.filter(s => s !== TaskStatusType.Open)
                    : [...[], TaskStatusType.Open]
                  // : [...(filter.status ?? []), TaskStatusType.Open]
                })
              )
            }
          >
            <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
              <HStack space={2}>
                <View style={[style.dot, { backgroundColor: '#F40000', alignSelf: 'center' }]} />
                <Text fontSize="16px" color="#8F8989">
                  Open
                </Text>
              </HStack>
              {filter.status?.includes(TaskStatusType.Open) ? <Icon name="tick" /> : <></>}
            </Box>
          </TouchableOpacity>

          <Divider mt={4} />
          <TouchableOpacity
            onPress={() =>
              dispatch(
                taskActions.setFilter({
                  ...filter,
                  status: filter.status?.includes(TaskStatusType.InProgress)
                    ? filter.status.filter(s => s !== TaskStatusType.InProgress)
                    : [...[], TaskStatusType.InProgress]
                  // : [...(filter.status ?? []), TaskStatusType.InProgress]
                })
              )
            }
          >
            <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
              <HStack space={2}>
                <View style={[style.dot, { backgroundColor: '#3E78CF', alignSelf: 'center' }]} />
                <Text fontSize="16px" color="#8F8989">
                  In Progress
                </Text>
              </HStack>
              {filter.status?.includes(TaskStatusType.InProgress) ? <Icon name="tick" /> : <></>}
            </Box>
          </TouchableOpacity>

          <Divider mt={4} />
          <TouchableOpacity
            onPress={() =>
              dispatch(
                taskActions.setFilter({
                  ...filter,
                  status: filter.status?.includes(TaskStatusType.Hold)
                    ? filter.status.filter(s => s !== TaskStatusType.Hold)
                    : [...[], TaskStatusType.Hold]
                  // : [...(filter.status ?? []), TaskStatusType.Hold]
                })
              )
            }
          >
            <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
              <HStack space={2}>
                <View style={[style.dot, { backgroundColor: '#F29100', alignSelf: 'center' }]} />
                <Text fontSize="16px" color="#8F8989">
                  Hold
                </Text>
              </HStack>
              {filter.status?.includes(TaskStatusType.Hold) ? <Icon name="tick" /> : <></>}
            </Box>
          </TouchableOpacity>

          <Divider mt={4} />
          <TouchableOpacity
            onPress={() =>
              dispatch(
                taskActions.setFilter({
                  ...filter,
                  status: filter.status?.includes(TaskStatusType.Completed)
                    ? filter.status.filter(s => s !== TaskStatusType.Completed)
                    : [...[], TaskStatusType.Completed]
                  // : [...(filter.status ?? []), TaskStatusType.Completed]
                })
              )
            }
          >
            <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
              <HStack space={2}>
                <View style={[style.dot, { backgroundColor: '#18A601', alignSelf: 'center' }]} />
                <Text fontSize="16px" color="#8F8989">
                  Closed
                </Text>
              </HStack>
              {filter.status?.includes(TaskStatusType.Completed) ? <Icon name="tick" /> : <></>}
            </Box>
          </TouchableOpacity>

          {/* <Divider mt={4} /> */}
        </Flex>
        <Flex direction="row" width="100%" justifyContent="space-between" p={4} pt={0} bg="#ffffff">
          <Button
            flex={1}
            variant="outline"
            marginRight={2}
            _pressed={{ bg: '#E0E0E0', opacity: 0.8 }}
            bg="#ffffff"
            onPress={() => {
              dispatch(taskActions.setFilter({ ...filter, status: [] }));
              modalRef.current.closeModal();
            }}
          >
            <Text color="#0695D7">Clear all</Text>
          </Button>
          <Button
            flex={1}
            _pressed={{ bg: '#007ACC', opacity: 0.8 }}
            bg="#0695D7"
            onPress={() => {
              modalRef.current.closeModal();
            }}
          >
            <Text color="#ffffff">Apply</Text>
          </Button>
        </Flex>
      </View>
    </Modal>
  );
});

const style = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    height: 48,
    justifyContent: 'space-between',
    padding: 6,
    alignItems: 'center'
  },
  filterButton: {
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20
  },
  dot: {
    width: 10,
    height: 10,
    borderRadius: 5
  }
});

StatusModal.displayName = 'StatusModal';
export default StatusModal;
