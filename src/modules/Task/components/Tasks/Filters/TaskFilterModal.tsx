import { Icon, Modal } from '@commons';
import { COLORS } from '@src/constants';
import { taskActions } from '@src/slice/tasks.slice';
import { useDispatch, useSelector } from '@src/store';
import { Box, Button, Divider, Flex, HStack, Switch, Text, View } from 'native-base';
import React, { forwardRef, useRef } from 'react';
import { ModalProps, StyleSheet, TouchableOpacity } from 'react-native';
import AssigneeFilter from './AssigneeFilter';
import CcFilter from './CcFilter';
import QuickFilter from './QuickFilter';
import StatusModal from './StatusModal';
import GroupFilter from './GroupFilter';
import { TaskStatusType } from '@src/api/graphql';
import DateFilter from './DateFilter';
import IDFilter from './IDFilter';

export type TaskFilterType = {
  isAssignToMe: boolean;
  isHideCompletedTask: boolean;
};
interface Props {}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const status: any = {
  Open: 'Open',
  InProgress: 'In progress',
  Completed: 'Closed',
  Hold: 'On Hold'
};

const TaskFilterModal = forwardRef<ModalRef, Props>((props, ref) => {
  // Children filter modal
  const quickFilterModalRef = useRef<any>(null);
  const dateFilterModalRef = useRef<any>(null);
  const statusModalRef = useRef<any>(null);
  const assigneeFilterModalRef = useRef<any>(null);
  const ccFilterModalRef = useRef<any>(null);
  const groupFilterModalRef = useRef<any>(null);
  const IdFilterModalRef = useRef<any>(null);
  const calendarPickerRef = useRef<any>(null);

  const dispatch = useDispatch();
  const states = useSelector(state => state.tasks);
  const { filter } = useSelector(state => state.tasks);

  const quickFilter =
    filter.assignToMe && filter.hideCompleted
      ? 'All'
      : filter.assignToMe
        ? 'Assigned to me'
        : filter.hideCompleted
          ? 'Hide completed task'
          : 'None';

  const idFilter = filter.id ? filter.id : 'None';

  const statusFilter =
    (filter.status || []).length === 4
      ? 'All'
      : (filter.status || []).length > 0
        ? (filter.status || []).map(item => status[item]).join(', ')
        : 'None';

  const assigneeFilter =
    (filter.assignee || []).length === 1
      ? 'Anyone'
      : (filter.assignee || []).length > 1
        ? 'Multiple assignees'
        : 'None';

  const ccFilter = (filter.cc || []).length === 1 ? 'Anyone' : (filter.cc || []).length > 1 ? 'Multiple Cc' : 'None';

  const dateFilter = filter.date != undefined ? filter.date : 'None';

  const groupFilter = filter.group?.id ? filter.group?.title : 'None';

  return (
    <Modal isVisible={states.isTaskFilterModalOpen} onClose={() => dispatch(taskActions.closeAll())}>
      <View>
        <Flex direction="row" style={style.container} width="100%" height={'full'} mb={2}>
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            pb={1}
            bg="transparent"
            onPress={() => {
              dispatch(taskActions.clearFilter());
              dispatch(taskActions.closeAll());
            }}
          >
            <Text fontSize="16" color={COLORS.primary[1]}>
              Clear Filters
            </Text>
          </Button>
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            pb={1}
            bg="transparent"
            onPress={() => {
              dispatch(taskActions.setFilterItems(states.filter));
              dispatch(taskActions.closeAll());
            }}
          >
            <Text fontSize="16" color={COLORS.primary[1]}>
              Apply Filters
            </Text>
          </Button>
        </Flex>

        <View bg="#F0F0F0" h="40px" justifyContent="center">
          <Text fontSize="16" fontWeight="bold" pl={5} color={COLORS.neutrals.black}>
            Filter Tasks
          </Text>
        </View>

        <Flex direction="column" p={5}>
          <TouchableOpacity
            onPress={() => {
              IdFilterModalRef.current.pushModal();
            }}
          >
            <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
              <HStack space={4} alignItems={'center'}>
                <Icon name="id" />
                <Text fontSize="16px">ID</Text>
              </HStack>
              <HStack space={4} alignItems={'center'}>
                <Text color="#8F8989">{idFilter}</Text>
                <Icon name="chevron-right-grey" />
              </HStack>
              {/* {selectedStatus.includes('Submitted') ? <Icon name="tick" /> : <></>} */}
            </Box>
          </TouchableOpacity>

          {/* <Divider mt={4} />
          <TouchableOpacity
            onPress={() => {
              assigneeFilterModalRef.current.pushModal();
            }}
          >
            <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
              <HStack space={4} alignItems={'center'}>
                <Icon name="assignee" />
                <Text fontSize="16px">Assigned to</Text>
              </HStack>
              <HStack space={4} alignItems={'center'}>
                <Text color="#8F8989">{assigneeFilter}</Text>
                <Icon name="chevron-right-grey" />
              </HStack>
            </Box>
          </TouchableOpacity> */}

          <Divider mt={4} />
          <TouchableOpacity
            onPress={() => {
              quickFilterModalRef.current.pushModal();
            }}
          >
            <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
              <HStack space={4} alignItems={'center'}>
                <Icon name="filtering-grey" />
                <Text fontSize="16px">Quick Filters</Text>
              </HStack>
              <HStack space={4} alignItems={'center'}>
                <Text color="#8F8989">{quickFilter}</Text>
                <Icon name="chevron-right-grey" />
              </HStack>
              {/* {selectedStatus.includes('Draft') ? <Icon name="tick" /> : <></>} */}
            </Box>
          </TouchableOpacity>

          <Divider mt={4} />
          <TouchableOpacity
            onPress={() => {
              statusModalRef.current.pushModal();
            }}
          >
            <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
              <HStack space={4} alignItems={'center'}>
                <Icon name="check-square" />
                <Text fontSize="16px">Status</Text>
              </HStack>
              <HStack space={4} alignItems={'center'}>
                <Text color="#8F8989">{statusFilter}</Text>
                <Icon name="chevron-right-grey" />
              </HStack>
              {/* {selectedStatus.includes('Submitted') ? <Icon name="tick" /> : <></>} */}
            </Box>
          </TouchableOpacity>

          <Divider mt={4} />
          <TouchableOpacity
            onPress={() => {
              assigneeFilterModalRef.current.pushModal();
            }}
          >
            <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
              <HStack space={4} alignItems={'center'}>
                <Icon name="assignee" />
                <Text fontSize="16px">Assigned to</Text>
              </HStack>
              <HStack space={4} alignItems={'center'}>
                <Text color="#8F8989">{assigneeFilter}</Text>
                <Icon name="chevron-right-grey" />
              </HStack>
              {/* {selectedStatus.includes('InReview') ? <Icon name="tick" /> : <></>} */}
            </Box>
          </TouchableOpacity>

          <Divider mt={4} />
          <TouchableOpacity
            onPress={() => {
              ccFilterModalRef.current.pushModal();
            }}
          >
            <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
              <HStack space={4} alignItems={'center'}>
                <Icon name="assignees" />
                <Text fontSize="16px">Cc</Text>
              </HStack>
              <HStack space={4} alignItems={'center'}>
                <Text color="#8F8989">{ccFilter}</Text>
                <Icon name="chevron-right-grey" />
              </HStack>
              {/* {selectedStatus.includes('InApproved') ? <Icon name="tick" /> : <></>} */}
            </Box>
          </TouchableOpacity>

          <Divider mt={4} />

          <TouchableOpacity onPress={() => groupFilterModalRef?.current.pushModal()}>
            <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
              <HStack space={4} alignItems={'center'}>
                <Icon name="group" />
                <Text fontSize="16px">Group</Text>
              </HStack>
              <HStack space={4} alignItems={'center'}>
                <Text color="#8F8989">{groupFilter}</Text>
                <Icon name="chevron-right-grey" />
              </HStack>
              {/* {selectedStatus.includes('InApproved') ? <Icon name="tick" /> : <></>} */}
            </Box>
          </TouchableOpacity>

          <Divider mt={4} />

          <TouchableOpacity onPress={() => dateFilterModalRef?.current.pushModal()}>
            <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
              <HStack space={4} alignItems={'center'}>
                <Icon name="calendar-grey" />
                <Text fontSize="16px">Date</Text>
              </HStack>
              <HStack space={4} alignItems={'center'}>
                <Text color="#8F8989">{dateFilter}</Text>
                <Icon name="chevron-right-grey" />
              </HStack>
              {/* {selectedStatus.includes('InApproved') ? <Icon name="tick" /> : <></>} */}
            </Box>
          </TouchableOpacity>

          <Divider mt={4} />
        </Flex>
      </View>
      <IDFilter ref={IdFilterModalRef} />
      <QuickFilter ref={quickFilterModalRef} />
      <DateFilter ref={dateFilterModalRef} />
      <StatusModal ref={statusModalRef} />
      <AssigneeFilter ref={assigneeFilterModalRef} />
      <CcFilter ref={ccFilterModalRef} />
      <GroupFilter ref={groupFilterModalRef} />
    </Modal>
  );
});

const style = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    height: 48,
    justifyContent: 'space-between',
    padding: 6,
    alignItems: 'center'
  },
  filterButton: {
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20
  }
});

export default TaskFilterModal;
