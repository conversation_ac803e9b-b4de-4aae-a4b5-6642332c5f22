import React, { memo, useCallback, useMemo, useState } from 'react';
import { RefreshControl, StyleSheet } from 'react-native';
import { FlashList, ListRenderItem } from '@shopify/flash-list';
import { useTasks } from '@src/queries/tasks/useTasks';
import TaskList from './TaskList';
import { useSelector } from '@src/store';
import Skeleton from '@src/commons/Skeleton';
import { Spinner, Text, View } from 'native-base';
import NotFound from '@src/commons/NotFound';
import useSyncWithDownload from '@src/hooks/useSyncWithDownload';
import TaskModel from '@src/database/model/task.model';

type Props = {
  filteredValue: string | null;
  auth: any;
  role: any;
  navigateToTaskDetail: (taskId: string) => void;
  handleLongPress: (title: string) => void;
};

const styles = StyleSheet.create({
  container: { paddingHorizontal: 20, paddingTop: 20 },
  errorText: { padding: 20 }
});

const TaskListsComponent: React.FC<Props> = ({ filteredValue, auth, handleLongPress, navigateToTaskDetail, role }) => {
  const [refreshing, setRefreshing] = useState(false);

  const { data, fetchNextPage, hasNextPage, isLoading, isFetchingNextPage, refetch, isError } = useTasks({
    filteredValue: filteredValue ?? '',
    filterItems: useSelector(state => state.tasks.filterItems) || []
  });

  const { syncAndDownload } = useSyncWithDownload();

  const tasks = useMemo(() => {
    if (!data?.pages) return [];
    return data.pages.flatMap(page => page?.tasks || []).filter(task => task !== null && task !== undefined);
  }, [data]);

  const handleEndReached = useCallback(() => {
    if (hasNextPage && !isLoading) fetchNextPage();
  }, [hasNextPage, isLoading, fetchNextPage]);

  const renderItem: ListRenderItem<TaskModel> = useCallback(
    ({ item }) => (
      <TaskList
        item={item}
        auth={auth}
        role={role}
        navigateToTaskDetail={navigateToTaskDetail}
        handleLongPress={handleLongPress}
      />
    ),
    [auth, role, navigateToTaskDetail, handleLongPress]
  );

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await syncAndDownload({
        syncMutateOptions: { dispatchStatus: true },
        offlineDownloadOptions: { id: '', dispatchStatus: true },
        showSyncModal: true
      });
      await refetch();
    } catch (error) {
    } finally {
      setRefreshing(false);
    }
  }, [syncAndDownload, refetch]);

  if (isLoading) return <Skeleton />;
  if (isError)
    return (
      <View style={styles.errorText}>
        <Text color="red.500">Failed to load tasks. Please try again.</Text>
      </View>
    );
  if (!tasks.length) return <NotFound />;

  return (
    <FlashList<TaskModel>
      contentContainerStyle={styles.container}
      data={tasks as any}
      keyExtractor={item => item.id}
      onEndReached={handleEndReached}
      onEndReachedThreshold={0.1}
      estimatedItemSize={100}
      renderItem={renderItem}
      ListFooterComponent={isFetchingNextPage ? <Spinner size="lg" color="cyan.500" /> : null}
      refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
    />
  );
};

export default memo(TaskListsComponent);
