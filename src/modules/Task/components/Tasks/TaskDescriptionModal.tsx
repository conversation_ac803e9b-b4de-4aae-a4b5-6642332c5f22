import { Modal } from '@commons';
import _ from 'lodash';
import { Box, Button, Input, Text, VStack } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps } from 'react-native';

interface Props {
  onChange: (value: any) => void;
  data?: any;
  action?: string;
  value?: string;
  textType?: any;
  role?: boolean;
  isTaskOwner?: boolean;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const TaskDescriptionModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const [value, setValue] = useState(props?.value);
  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  return (
    <Modal avoidKeyboard={true} ref={modalRef} style={{ marginTop: 40 }} type="middle">
      <Box p={6}>
        <Text textAlign="center" mb={4} fontSize={16}>
          {props?.textType}
        </Text>

        <VStack>
          <Text>Enter {props?.textType}</Text>
          <Input
            onChangeText={value => {
              setValue(value);
            }}
            isRequired={true}
            defaultValue={props?.value}
            multiline={true}
            numberOfLines={5}
            height={'150px'}
            autoFocus
            isReadOnly={!(props.role || props.isTaskOwner)}
          />
        </VStack>

        <Box flexDirection="row" justifyContent="flex-end" mt={5}>
          <Button
            bg="transparent"
            _pressed={{ bg: 'transparent' }}
            onPress={() => {
              modalRef.current.closeModal();
            }}
          >
            <Text color="#0695D7">Cancel</Text>
          </Button>
          <Button
            bg="transparent"
            _pressed={{ bg: 'transparent' }}
            onPress={() => {
              props.onChange(value);
              modalRef.current.closeModal();
            }}
            isDisabled={!(props.role || props.isTaskOwner)}
            _disabled={{ bg: 'transparent' }}
          >
            <Text color={!(props.role || props.isTaskOwner) ? 'gray.300' : 'black'}>Update</Text>
          </Button>
        </Box>
      </Box>
    </Modal>
  );
});

TaskDescriptionModal.displayName = 'TaskDescriptionModal';
export default TaskDescriptionModal;
