import { Modal } from '@commons';
import { Gql } from '@src/api';
import { pushError, pushSuccess } from '@src/configs';
import database from '@src/database/index.native';
import TaskModel from '@src/database/model/task.model';
import useDeleteTask from '@src/mutation/task/useDeleteTask';
import useUpdateTask from '@src/mutation/task/useUpdateTask';
import { useGetTask } from '@src/queries/tasks/useGetTask';
import { taskActions } from '@src/slice/tasks.slice';
import { useDispatch, useSelector } from '@src/store';
import { Box, Button, Text } from 'native-base';
import React, { FC } from 'react';
import { Linking, StyleSheet, Alert } from 'react-native';

interface Props {
  navigation?: any;
  onChange?: (value: string) => void;
  onDelete?: (value: number) => void;
  refetch: () => void;
}

const TaskOptionModal: FC<Props> = props => {
  const dispatch = useDispatch();
  const states = useSelector(state => state.tasks);
  const projectState = useSelector(state => state.project);
  const userState = useSelector(state => state.auth);

  const { data, isLoading } = useGetTask(states.taskId as string);

  const { mutateAsync: deleteTask } = useDeleteTask();
  const { mutateAsync: updateTask } = useUpdateTask();

  const canGenerateMemo =
    projectState?.projectUserRole === Gql.ProjectUserRoleType.CloudCoordinator ||
    projectState?.projectUserRole === Gql.ProjectUserRoleType.ProjectOwner ||
    (projectState?.projectUserRole === Gql.ProjectUserRoleType.CanEdit && states?.taskOwnerId === userState?.user?.id);

  const isUrgent = data?.isUrgent;

  const markAsUrgent = async () => {
    try {
      await updateTask({
        id: states.taskId as string,
        newTask: {
          isUrgent: !isUrgent
        }
      });

      pushSuccess('Successfully set as urgent');
      dispatch(taskActions.closeAll());
    } catch (e) {
      pushError(e);
    }
  };

  const onDelete = (id: string) => {
    Alert.alert('Delete Task', 'Are you sure you want to delete this task?', [
      {
        text: 'Cancel',
        style: 'cancel'
      },
      {
        text: 'Delete',
        onPress: async () => {
          return await deleteTask(id)
            .then(() => {
              pushSuccess('Task deleted successfully');
            })
            .catch(e => {
              pushError(e);
            });
        }
      }
    ]);
    dispatch(taskActions.closeAll());
  };

  return (
    <>
      <Modal
        isVisible={states.isTaskOptionModalOpen}
        onClose={() => {
          dispatch(taskActions.closeAll());
        }}
        style={{ marginTop: 40 }}
        type="bottom"
      >
        <Box justifyContent={'center'} alignItems={'center'}>
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            mb={4}
            bg="transparent"
            onPress={() => {
              onDelete?.(states.taskId ?? '');
            }}
          >
            <Text color="semantics.danger">Delete</Text>
          </Button>

          {canGenerateMemo ? (
            <Button
              _pressed={{
                bg: 'transparent',
                opacity: 0.8
              }}
              mb={4}
              bg="transparent"
              onPress={async () => {
                if (props?.navigation) {
                  await props.navigation.navigate('TasksNav', { screen: 'CreateMemo', params: { id: states.taskId } });
                }
                dispatch(taskActions.closeAll());
              }}
            >
              <Text>Generate Memo</Text>
            </Button>
          ) : null}

          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            mb={4}
            bg="transparent"
            onPress={markAsUrgent}
          >
            <Text>Set as {isUrgent ? 'non urgent' : 'urgent'}</Text>
          </Button>
        </Box>
      </Modal>
    </>
  );
};

TaskOptionModal.displayName = 'TaskOptionModal';
export default TaskOptionModal;
