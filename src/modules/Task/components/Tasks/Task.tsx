import { Icon } from '@commons';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { COLORS } from '@src/constants';
import { taskActions } from '@src/slice/tasks.slice';
import { useDispatch, useSelector } from '@src/store';
import { RootNavigatorParams } from '@src/types';
import _ from 'lodash';
import { Box, Circle, HStack } from 'native-base';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { InteractionManager, StyleSheet, TouchableOpacity } from 'react-native';
import TaskFilterModal from './Filters/TaskFilterModal';
import TaskOptionModal from './TaskOptionModal';
import { task } from '@src/lib/authority';
import { showFileName } from '@src/configs';
import SearchInput from '@src/commons/SearchInput';
import TaskListsComponent from './TaskListsComponent';

interface Props {
  tabLabel: string;
}
const Tasks: React.FC<Props> = ({}) => {
  const taskFilterModalRef = useRef<any>(null);
  const auth = useSelector(state => state.auth);
  const project = useSelector(state => state.project);
  const dispatch = useDispatch();
  const { filterItems } = useSelector(state => state.tasks);
  const [filteredValue, setFilteredValue] = useState<string | null>(null);
  const [filteredFocused, setFilteredFocused] = useState(false);

  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const viewOnly = project.projectUserRole === 'CanView';
  const role = task(project.projectUserRole);

  useFocusEffect(
    useCallback(() => {
      InteractionManager.runAfterInteractions(() => {
        dispatch(taskActions?.closeAll());
        dispatch(taskActions?.setInitialState(true));
      });
    }, [])
  );

  const [debouncedValue, setDebouncedValue] = useState(filteredValue);

  const debounceFilter = useCallback(
    _.debounce((value: string | null) => {
      setDebouncedValue(value);
    }, 300),
    []
  );

  const handleSearchChange = (value: string | null) => {
    setFilteredValue(value);
    debounceFilter(value);
  };

  const handleLongPress = (title: string) => {
    return showFileName('Task', title);
  };

  const navigateToTaskDetail = (id: string) => {
    dispatch(taskActions?.setInitialState(true));
    navigation?.navigate('TasksNav', { screen: 'EditTask', params: { id: id } });
  };

  const isShowFilterBadge = useMemo(() => {
    return (
      filterItems.assignToMe === true ||
      Object.values(filterItems).some((value, index, array) => {
        if (array[index] !== 'assignToMe') {
          return Boolean(value);
        }
        return false;
      })
    );
  }, [filterItems]);

  return (
    <>
      <Box flex={1} bg={COLORS.neutrals.white}>
        {!viewOnly && (
          <TouchableOpacity
            style={[style.addButton, { zIndex: 1000 }]}
            onPress={() => navigation.navigate('TasksNav', { screen: 'AddTask', params: { refetch: () => {} } })}
          >
            <Circle size="65px" bg="#0695D7">
              <Icon name="plus" />
            </Circle>
          </TouchableOpacity>
        )}
        <HStack width="full" height="48px" bg="white" alignItems="center" px={2} mt={1} space={2}>
          <SearchInput
            placeholder="Search for task title."
            filteredValue={filteredValue}
            setFilteredValue={handleSearchChange}
            onFocused={value => setFilteredFocused(value)}
            onBlurred={value => setFilteredFocused(value)}
            style={{ flex: 1 }}
          />
          {!filteredFocused ? (
            <TouchableOpacity style={{ marginTop: 10 }} onPress={() => dispatch(taskActions.OpenTaskFilterModal())}>
              {isShowFilterBadge ? (
                <Box
                  zIndex={1}
                  width={3}
                  height={3}
                  borderRadius={'full'}
                  position="absolute"
                  top={0.5}
                  right={0.1}
                  bg={'red.500'}
                />
              ) : null}
              <Icon name="filtering-grey" width={30} height={40} />
            </TouchableOpacity>
          ) : null}
        </HStack>
        <TaskListsComponent
          filteredValue={debouncedValue}
          role={role}
          navigateToTaskDetail={navigateToTaskDetail}
          handleLongPress={handleLongPress}
          auth={undefined}
        />
        <TaskOptionModal refetch={() => {}} navigation={navigation} />
        <TaskFilterModal ref={taskFilterModalRef} />
      </Box>
    </>
  );
};

const style = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    height: 48,
    justifyContent: 'space-between',
    padding: 6,
    alignItems: 'center'
  },
  hStack: {
    justifyContent: 'center',
    alignItems: 'center'
  },
  box: {
    backgroundColor: '#FFFFFF',
    paddingLeft: 0,
    borderRadius: 12,
    overflow: 'hidden'
  },
  addButton: {
    position: 'absolute',
    bottom: 10,
    alignSelf: 'flex-end',
    right: '7%',
    shadowColor: 'rgba(0, 0, 0, 0.5)',
    shadowOpacity: 1,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 6 },
    backgroundColor: '#FFFFFF',
    borderRadius: 9999
  },
  descriptionText: {
    fontWeight: '400',
    fontSize: 14
  }
});

export default Tasks;
