import { Icon, Modal } from '@commons';
import { generateRNFile } from '@src/configs';
import { useDispatch, useSelector } from '@src/store';
import { ReactNativeFile } from 'apollo-upload-client';
import { isEqual } from 'lodash';
import { Button, HStack, Text, Box } from 'native-base';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Alert, ModalProps, Platform, StyleSheet } from 'react-native';
import DocumentPicker from 'react-native-document-picker';
import ImagePicker from 'react-native-image-crop-picker';
import TaskGroupAddRename from '../Overview/TaskGroupAddRename';
import prompt from 'react-native-prompt-android';

interface Props {
  onChange?: (file: ReactNativeFile[]) => void;
  takePhotoAttachment?: (capture: any) => void;
  chosedAttachment: (photo: any) => any;
  state?: string;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const TaskPhotosModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const addModalRef = useRef<any>(null);
  const states = useSelector(state => state.tasks, isEqual);
  const [totalSize, setTotalSize] = useState(0);
  const [file, setFile] = useState<any>(null);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  useEffect(() => {
    if (totalSize > 104857600) {
      Alert.alert('Reminder', 'Video size must be less than 100MB per upload');
      return;
    } else {
      const newFile = generateRNFile({
        uri: file?.path,
        name:
          Platform.OS === 'ios'
            ? (file?.filename as string)
            : file?.path.replace('_cloud.bina.android', '').substring(file?.path.lastIndexOf('/') + 1),
        type: file?.mime as string
      });

      if (newFile) {
        props.chosedAttachment([newFile]);
        setFile(null);
      }
    }
  }, [totalSize, file]);

  //Choose Photos
  const openPhotoLibrary = async () => {
    try {
      ImagePicker.openPicker({
        width: 500,
        height: 500,
        cropping: true,
        multiple: true,
        forceJpg: true
      }).then((images: any) => {
        let files: any = [];
        images.forEach((image: any) => {
          const newFile = generateRNFile({
            uri: image.path,
            name:
              Platform.OS === 'ios'
                ? (image.filename as string)
                : image.path.replace('_cloud.bina.android', '').substring(image.path.lastIndexOf('/') + 1),
            type: image.mime as string
          });
          if (newFile) files.push(newFile);
        });
        props.chosedAttachment(files);
        modalRef.current?.closeModal();
      });
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
      } else {
        throw err;
      }
    }
  };

  const openVideoLibrary = async () => {
    try {
      await ImagePicker.openPicker({
        mediaType: 'video'
      }).then((image: any) => {
        const newTotalSize = +image.size + +totalSize;
        setTotalSize(newTotalSize);
        setFile(image);
        modalRef.current?.closeModal();
      });
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
        // Handle cancelation
      } else {
        throw err;
      }
    }
  };

  // Take Photo
  const openCamera = () => {
    ImagePicker.openCamera({
      width: 500,
      height: 500,
      cropping: true,
      forceJpg: true,
      includeBase64: true
    }).then(image => {
      if (Platform.OS === 'ios') {
        Alert.prompt('Rename Photo', `Enter new name photo`, [
          {
            text: 'Cancel',
            style: 'cancel'
          },
          {
            text: 'OK',
            onPress: (name: any) => {
              if (!name.endsWith('.jpeg')) {
                name += '.jpeg';
              }
              addPhotoName(name, image.path);
            }
          }
        ]);
      } else {
        setTimeout(() => {
          prompt('Rename Photo', `Enter new name photo`, [
            {
              text: 'Cancel',
              style: 'cancel'
            },
            {
              text: 'OK',
              onPress: (name: any) => {
                if (!name.endsWith('.jpeg')) {
                  name += '.jpeg';
                }
                addPhotoName(name, image.path);
              }
            }
          ]);
        }, 1000);
      }
    });
  };

  const addPhotoName = async (name: any, imagePath: any) => {
    let files: any = [];
    const image: any = generateRNFile({ uri: imagePath, name: name, type: 'image/jpeg' });
    files.push(image);
    props.chosedAttachment(files);
    modalRef.current?.closeModal();
  };

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="bottom">
      <HStack justifyContent={'space-evenly'} px={0}>
        <Button
          width={'50%'}
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          my={6}
          bg="transparent"
          onPress={() => {
            Alert.alert('Choose Media', 'Which file would you want to upload?', [
              {
                text: 'Video',
                onPress: () => {
                  if (props.state === 'addTask') {
                    Alert.alert('Reminder', 'Video can be uploaded after creating a task');
                  } else if (props.state === 'editTask') {
                    openVideoLibrary();
                  }
                },
                style: 'default'
              },
              {
                text: 'Photos',
                onPress: () => {
                  openPhotoLibrary();
                },
                style: 'default'
              }
            ]);
          }}
          justifyContent={'center'}
        >
          <Box
            alignItems={'center'}
            justifyContent={'center'}
            width={16}
            height={16}
            alignSelf={'center'}
            borderRadius={32}
            borderWidth={2}
            borderColor={'neutrals.gray6'}
          >
            <Icon name="add-contact" />
          </Box>
          <Text color="#6F6D6D">Browse Gallery</Text>
        </Button>

        <Button
          width={'50%'}
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          my={6}
          bg="transparent"
          onPress={() => {
            openCamera();
          }}
          justifyContent={'center'}
        >
          <Box
            alignItems={'center'}
            justifyContent={'center'}
            width={16}
            height={16}
            alignSelf={'center'}
            borderRadius={32}
            borderWidth={2}
            borderColor={'neutrals.gray6'}
          >
            <Icon name="take-photo" />
          </Box>
          <Text color="#6F6D6D">Take Photo</Text>
        </Button>
      </HStack>

      <TaskGroupAddRename ref={addModalRef} refetch={() => {}} action={'photo'} />
    </Modal>
  );
});

const styles = StyleSheet.create({
  folder: {
    borderWidth: 2,
    borderColor: '#756D6D',
    borderRadius: 100,
    backgroundColor: 'transparent',
    height: 52,
    width: 52,
    justifyContent: 'center',
    alignItems: 'center'
  }
});

TaskPhotosModal.displayName = 'TaskPhotosModal';
export default TaskPhotosModal;
