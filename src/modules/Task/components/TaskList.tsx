import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Gql } from '@src/api';
import { Icon } from '@src/commons';
import { RootNavigatorParams } from '@src/types';
import moment from 'moment';
import { FlatList, Flex, HStack, Spacer, Text, VStack } from 'native-base';
import React, { useEffect } from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import Avatar from '@src/commons/Avatar';

interface Props {
  data: any[];
  onSaved?: () => void;
}
const TaskList: React.FC<Props> = props => {
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();

  useEffect(() => {
    props.onSaved?.();
  }, []);

  const getTaskData = props.data.map((task: any) => {
    return {
      id: task.id,
      title: task.title,
      dueDate: task.dueDate,
      taskCode: task.taskCode,
      avatarUrl: task?.owner?.avatar ?? '',
      name: task?.owner?.name ?? '',
      status: task.status
    };
  });

  return (
    <FlatList
      contentContainerStyle={{ paddingHorizontal: 20, paddingTop: 20 }}
      data={getTaskData}
      keyExtractor={item => item.id}
      renderItem={({ item }: any) => (
        <HStack style={style.box}>
          <TouchableOpacity
            style={{ width: '100%' }}
            onPress={() => {
              navigation.push('TasksNav', {
                screen: 'EditTask',
                params: {
                  id: item.id ?? '',
                  refetch: () => {
                    props.onSaved?.();
                  }
                }
              });
            }}
          >
            <VStack space={1}>
              <Flex direction="row" justifyItems="center" alignItems="center" width="80%" height={6}>
                {item.status === Gql.TaskStatusType.InProgress || item.status === Gql.TaskStatusType.Overdue ? (
                  <Icon name="task-checkmark-undone" />
                ) : (
                  <Icon name="task-checkmark-done" />
                )}

                <Text style={{ marginLeft: 4 }} numberOfLines={1} ellipsizeMode="tail">
                  {item.title}
                </Text>
              </Flex>
              <Flex direction="row" justifyItems="center" alignItems="center" width="100%" height={6}>
                <Text color="neutrals.gray90">Due:</Text>

                <Text style={{ marginLeft: 4, color: moment(item.dueDate).isBefore(moment()) ? '#FF2020' : '#585757' }}>
                  {moment(item.dueDate).format('D MMM')}
                </Text>
              </Flex>

              <Spacer />
              <HStack style={{ justifyContent: 'space-between' }}>
                <Flex direction="row" justifyItems="center">
                  <Avatar uri={item.avatarUrl as string} />
                  <Text color="neutrals.gray90" style={{ marginLeft: 4 }}>
                    {item.name}
                  </Text>
                </Flex>

                <Text color="neutrals.gray70">CODE:{item.taskCode}</Text>
              </HStack>
            </VStack>
          </TouchableOpacity>
        </HStack>
      )}
    />
  );
};

const style = StyleSheet.create({
  box: {
    backgroundColor: '#FFFFFF',
    marginBottom: 30,
    padding: 14,
    borderRadius: 12
  }
});

export default TaskList;
