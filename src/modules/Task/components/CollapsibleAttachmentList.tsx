import React, { useState } from 'react';
import { Box, Pressable, HStack, Text, Spinner, FlatList, View } from 'native-base'; // Replace 'your-ui-library' with the actual library you're using

import { COLORS } from '@src/constants';
import { StyleSheet, TouchableOpacity, RefreshControl } from 'react-native';
import { Checkbox, Divider } from 'native-base';
import { Gql } from '@src/api';
import { getFileIcon } from '@src/commons/FileIcon';
import _ from 'lodash';
import Collapsible from 'react-native-collapsible';
import { Icon } from '@src/commons';

type Props = {
  data: any;
  loading: boolean;
  refetch: () => void;
  headerTitle?: string;
  itemIds: string[];
  setItemIds: (id: string[]) => void;
};

const CollapsibleAttachmentList: React.FC<Props> = ({ headerTitle, data, loading, setItemIds, itemIds, refetch }) => {
  const [isCollapsed, setIsCollapsed] = useState(true);

  return (
    <Box mx={5} my={4} w="full">
      <Pressable
        onPress={() => {
          setIsCollapsed(!isCollapsed);
        }}
      >
        <HStack>
          {isCollapsed ? <Icon name="chevron-right-primary-light" /> : <Icon name="chevron-down-primary-light" />}
          <View>
            <Text fontSize={'md'}>{headerTitle}</Text>
          </View>
        </HStack>
      </Pressable>
      <Collapsible collapsed={isCollapsed}>
        {loading ? (
          <Spinner size={'lg'} />
        ) : (
          <FlatList
            keyboardShouldPersistTaps="handled"
            data={data}
            keyExtractor={item => item.id}
            style={styles.flatList} // You may want to define the styles
            refreshControl={<RefreshControl refreshing={loading} onRefresh={refetch} />}
            renderItem={({ item }) => {
              return <RenderItem item={item} setItemIds={setItemIds} itemIds={itemIds} />;
            }}
            contentContainerStyle={{ paddingBottom: 65 }}
          />
        )}
      </Collapsible>
    </Box>
  );
};

type PropsRenderItem = {
  item: Gql.Task;
  setItemIds: (id: string[]) => void;
  itemIds: string[];
};

const RenderItem: React.FC<PropsRenderItem> = ({ item, setItemIds, itemIds }: any) => {
  const hasId = _.includes(itemIds, item.id);

  const onPress = () => {
    setItemIds((prev: string) => {
      if (hasId) {
        // If item.id is already in the array, remove it
        return prev.filter((id: string) => id !== item.id);
      } else {
        // If item.id is not in the array, add it
        return [...prev, item.id];
      }
    });
  };

  return (
    <Box key={item.id} pr={10}>
      <Divider my={4} w="full" />
      <TouchableOpacity
        onPress={() => {
          // props.navigation.navigate('TasksNav', {
          //   screen: 'EditTask',
          //   params: { id: item?.id }
          // });
        }}
      >
        <Box>
          <Pressable onPress={onPress}>
            <HStack>
              <Checkbox
                value=""
                color={'green'}
                isChecked={hasId}
                accessibilityLabel="Checkbox Label "
                onChange={onPress}
              />
              <Box pl={2}>{getFileIcon(item?.type)}</Box>
              <Text ml={2} fontSize={14} numberOfLines={1} ellipsizeMode="tail">
                {item.name}
              </Text>
            </HStack>
          </Pressable>
        </Box>
      </TouchableOpacity>
    </Box>
  );
};

const styles = StyleSheet.create({
  memberBox: {
    borderWidth: 1,
    borderColor: COLORS.neutrals.gray40,
    borderRadius: 8,
    paddingLeft: 10,
    alignItem: 'center'
  },
  flatList: {
    borderColor: COLORS.neutrals.gray40,
    padding: 8

    // backgroundColor: '#E6F1FF'
  },
  container: {
    flex: 1,
    justifyContent: 'center'
  },
  horizontal: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 10
  }
});

export default CollapsibleAttachmentList;
