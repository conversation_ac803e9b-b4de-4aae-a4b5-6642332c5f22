import { Gql } from '@src/api';
import { Icon } from '@src/commons';
import { COLORS } from '@src/constants/colors';
import apolloClient from '@src/lib/apollo';
import { useSelector } from '@src/store';
import _ from 'lodash';
import { Box, Center, Divider, FlatList, HStack, VStack } from 'native-base';
import React, { useEffect, useState } from 'react';
import { InteractionManager, StyleSheet, Text, TouchableOpacity } from 'react-native';

type Props = {
  allUsersTab?: boolean;
  assignedTab?: boolean;
  selectedTaskId?: string;
  tabLabel: string;
  onSelectedAssignees: (values: string[]) => void;
};

const Assignee: React.FC<Props> = ({ allUsersTab, assignedTab, selectedTaskId, onSelectedAssignees }) => {
  const project = useSelector(state => state.project);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [data, setData] = useState<any[]>([]);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getUserData();
    });
  }, [allUsersTab, assignedTab]);

  useEffect(() => {
    onSelectedAssignees(selectedUsers);
  }, [selectedUsers]);

  const getUserData = async () => {
    if (allUsersTab) {
      const res = await apolloClient.query<Gql.ProjectUsersQuery, Gql.ProjectUsersQueryVariables>({
        query: Gql.ProjectUsersDocument,
        variables: {
          filter: { projectId: { eq: project.projectId ?? '' } }
        }
      });
      const projectUsers = res?.data?.projectUsers.nodes.map((user: any) => {
        return {
          id: user.id,
          name: user.user.name,
          company: user.user.company.name,
          email: user.user.email,
          phoneNo: user.user.phoneNo
        };
      });
      setData(projectUsers ?? []);
    }

    if (assignedTab) {
      const res = await apolloClient.query<Gql.GetTaskQuery, Gql.GetTaskQueryVariables>({
        query: Gql.GetTaskDocument,
        variables: {
          id: selectedTaskId ?? ''
        }
      });
      const selectedProjectUser = res.data.task?.assignees?.nodes.map((assignee: any) => {
        return {
          id: assignee.id,
          name: assignee.user.name,
          company: assignee.user.company.name,
          email: assignee.user.email,
          phoneNo: assignee.user.phoneNo
        };
      });
      setData(selectedProjectUser ?? []);
    }
  };

  return (
    <>
      <FlatList
        keyboardShouldPersistTaps="handled"
        contentContainerStyle={{ paddingHorizontal: 20, paddingTop: 20 }}
        data={data}
        keyExtractor={item => item.id}
        renderItem={({ item }: any) => (
          <Center>
            <TouchableOpacity
              onPress={() => {
                if (!selectedUsers.includes(item.id)) {
                  setSelectedUsers(prev => _.uniq([...prev, item.id]));
                } else {
                  setSelectedUsers(prev => prev.filter(id => id !== item.id));
                }
              }}
            >
              <Box style={styles.box}>
                <VStack space={2}>
                  <Text>{item.name}</Text>
                  <HStack space={250}>
                    <Text style={styles.allUserText}>{item.company}</Text>

                    {allUsersTab === true ? (
                      selectedUsers.includes(item.id) ? (
                        <Icon name="tick" />
                      ) : (
                        ''
                      )
                    ) : (
                      <Icon name="delete" />
                    )}
                  </HStack>
                  <HStack>
                    <Text style={styles.allUserText}>{item.email}</Text>
                    <Divider orientation="vertical" mx={3} />
                    <Text style={styles.allUserText}>{item.phoneNo}</Text>
                  </HStack>
                </VStack>
              </Box>
            </TouchableOpacity>
          </Center>
        )}
      />
    </>
  );
};

const styles = StyleSheet.create({
  box: {
    backgroundColor: '#FFFFFF',
    marginBottom: 20,
    padding: 14,
    borderRadius: 12
  },
  allUserText: {
    fontWeight: '400',
    color: COLORS.neutrals.gray90
  }
});

export default Assignee;
