import { Footer, Icon, Modal } from '@commons';
import { Gql } from '@src/api';
import { getFileIcon } from '@src/commons/FileIcon';
import { pushError } from '@src/configs';
import { useSelector } from '@src/store';
import _ from 'lodash';
import { Box, Button, Center, Divider, FlatList, Flex, HStack, Input, Pressable, ScrollView, Text } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps, TouchableOpacity } from 'react-native';

type SelectedDocumentType = {
  id: string;
  name: string;
};

interface Props {
  onConfirm: (values: SelectedDocumentType[]) => void;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const TaskLinkedToModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const project = useSelector(state => state.project);
  const [selectedDocuments, setSelectedDocuments] = useState<SelectedDocumentType[]>([]);
  const [filteredValue, setFilteredValue] = useState<string>();
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const { data } = Gql.useProjectDocumentsQuery({
    variables: {
      filter: {
        projectId: { eq: project.projectId ?? '' },
        fileSystemType: { eq: Gql.FileSystemType.Document }
      },
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.ProjectDocumentSortFields.CreatedAt
        }
      ],
      paging: { limit: 4, offset: 0 }
    }
  });

  const { data: resultData } = Gql.useProjectDocumentsQuery({
    variables: {
      filter: {
        projectId: { eq: project.projectId ?? '' },
        fileSystemType: { eq: Gql.FileSystemType.Document },
        ...{ name: { like: `%${filteredValue}%` } }
      },
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.ProjectDocumentSortFields.CreatedAt
        }
      ]
    }
  });

  const recentData = data?.projectDocuments?.nodes.map((document: any) => {
    return { id: document.id, name: document.name, type: document.type };
  });

  const allResultData = resultData?.projectDocuments?.nodes.map((document: any) => {
    return { id: document.id, name: document.name, type: document.type };
  });

  const onSearch = (values: string) => {
    if (values) {
      setFilteredValue(values);
    } else {
      setFilteredValue('');
    }
  };

  const onConfirm = async () => {
    setIsSubmitting(true);
    try {
      props.onConfirm(selectedDocuments);
      modalRef?.current?.closeModal();
    } catch (e) {
      pushError(e);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="bottom">
      <ScrollView>
        <Flex direction="row" alignItems="center">
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            bg="transparent"
            onPress={() => {
              modalRef?.current?.closeModal();
            }}
          >
            <Icon name="cancel" />
          </Button>
          <Text ml={39}>Link a document to this tasks</Text>
        </Flex>
        <Divider />

        <Box bg="#F5F7FB">
          <Center>
            <Input
              placeholder="Search for a file name"
              mt="2"
              width="335px"
              borderRadius="4"
              fontSize="14"
              InputLeftElement={
                <Box px="6" py="6">
                  <Icon name="search" />
                </Box>
              }
              InputRightElement={
                <Pressable
                  pr="2"
                  py="6"
                  onPress={() => {
                    setFilteredValue('');
                  }}
                >
                  <Icon name="cancel" />
                </Pressable>
              }
              value={filteredValue}
              onChangeText={value => {
                onSearch(value);
              }}
            />
          </Center>
          <Box>
            {_.isEmpty(filteredValue) ? (
              <Text style={{ marginLeft: 30, marginTop: 10 }} color="neutrals.gray90">
                Recent
              </Text>
            ) : (
              <Text style={{ marginLeft: 30, marginTop: 10 }} color="neutrals.gray90">
                Results
              </Text>
            )}

            <Box>
              <FlatList
                keyboardShouldPersistTaps="handled"
                data={_.isEmpty(filteredValue) ? recentData : allResultData}
                keyExtractor={item => item.id}
                renderItem={({ item }: any) => (
                  <Box>
                    <TouchableOpacity
                      onPress={() => {
                        if (!selectedDocuments.find(x => x.id === item.id)) {
                          setSelectedDocuments(prev => _.uniqBy([...prev, item], e => e.id));
                        } else {
                          setSelectedDocuments(prev => prev.filter(d => d.id !== item.id));
                        }
                      }}
                    >
                      <Box ml={7} mb={4}>
                        <HStack space={2} alignItems="center" width="80%" mt={2}>
                          {getFileIcon(item.type)}
                          <Text color="neutrals.gray100" numberOfLines={1} ellipsizeMode="tail">
                            {item.name}
                          </Text>
                          {selectedDocuments.find(d => d.id === item.id) ? <Icon name="tick" /> : ''}
                        </HStack>
                      </Box>
                    </TouchableOpacity>
                  </Box>
                )}
              />
            </Box>
          </Box>
          <Footer>
            <Button
              variant="primary"
              isLoading={isSubmitting}
              onPress={() => {
                onConfirm();
              }}
            >
              Confirm
            </Button>
          </Footer>
        </Box>
      </ScrollView>
    </Modal>
  );
});

TaskLinkedToModal.displayName = 'TaskAddLinkedToModal';
export default TaskLinkedToModal;
