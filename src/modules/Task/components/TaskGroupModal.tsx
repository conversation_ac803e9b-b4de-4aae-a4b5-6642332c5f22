import { Modal } from '@commons';
import { Q } from '@nozbe/watermelondb';
import { FlashList } from '@shopify/flash-list';
import NotFound from '@src/commons/NotFound';
import database from '@src/database/index.native';
import ProjectGroupModel from '@src/database/model/project-group.model';
import Accordion from '@src/modules/DigitalForm/components/selector-component/Accordion';
import { taskActions } from '@src/slice/tasks.slice';
import { useDispatch, useSelector } from '@src/store';
import { ScrollView, Text, View } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps } from 'react-native';

interface Props {
  setGroup?: any;
  action: string;
  groups?: any;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const TaskGroupModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const dispatch = useDispatch();
  const states = useSelector(state => state.tasks);

  const project = useSelector(state => state.project);
  const [groups, setGroups] = useState<any>([]);

  const fetchGroups = async () => {
    const projectGroupsCollection = database.collections.get<ProjectGroupModel>('project_groups');
    const query = projectGroupsCollection.query(
      Q.where('projectId', project.projectId !== undefined ? parseInt(project.projectId) : null),
      Q.sortBy('title', Q.asc)
    );
    const result = await query.fetch();

    const groupMap = new Map(result.map(group => [group.remoteId, { ...group, children: [] }]));

    result.forEach(group => {
      if (group.projectGroupId) {
        const parentGroup: any = groupMap.get(parseInt(group.projectGroupId));
        if (parentGroup) {
          parentGroup.children.push(group);
        }
      }
    });

    const topLevelGroups = Array.from(groupMap.values()).filter(group => group?._raw?.projectGroupId === null);
    const ungroupTasksIndex = topLevelGroups.findIndex(group => group?._raw?.title === 'Ungroup Tasks');
    if (ungroupTasksIndex !== -1) {
      const [ungroupTasksItem] = topLevelGroups.splice(ungroupTasksIndex, 1);
      topLevelGroups.unshift(ungroupTasksItem);
    }
    setGroups(topLevelGroups);
  };

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const keyExtractor = (item: any, index: number) => index?.toString();

  const handlePress = (item: any) => {
    const selectedGroup = { id: item?._raw?.id, title: item?._raw?.title, remoteId: item?._raw?.remoteId };

    dispatch(taskActions.initialState({ ...states.task, group: selectedGroup } as any));
    props.setGroup?.({ id: item?._raw?.remoteId, title: item?._raw?.title } as any);
    modalRef?.current?.closeModal();
  };

  return (
    <Modal ref={modalRef} type="bottom" onShow={async () => await fetchGroups()}>
      <ScrollView style={{ height: '60%' }} keyboardShouldPersistTaps="handled" contentContainerStyle={{ flexGrow: 1 }}>
        <View bg="#F0F0F0" h="40px" justifyContent="center">
          <Text fontSize="16" fontWeight="600px" pl={5}>
            Choose Group
          </Text>
        </View>

        <FlashList
          data={groups}
          ListEmptyComponent={<NotFound />}
          keyExtractor={keyExtractor}
          onEndReachedThreshold={0.1}
          estimatedItemSize={50}
          renderItem={({ item }: any) => (
            <Accordion
              item={item}
              title={item?._raw.title ?? ''}
              isParent={!item?.projectGroupId}
              onPressChild={handlePress}
            />
          )}
        />
      </ScrollView>
    </Modal>
  );
});

TaskGroupModal.displayName = 'TaskGroupModal';
export default TaskGroupModal;
