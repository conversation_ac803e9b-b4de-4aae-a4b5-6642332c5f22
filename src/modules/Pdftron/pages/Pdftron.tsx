import { Config, DocumentView, RNPdftron } from 'react-native-pdftron';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps, useNavigation } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackNavigationProp } from '@react-navigation/stack';
import { Gql } from '@src/api';
import { AppBar, Footer } from '@src/commons';
import { generateRNFile, pushError, pushMessaage, pushSuccess } from '@src/configs';
import { COLORS } from '@src/constants';
import apolloClient from '@src/lib/apollo';
import { PdftronNavigatorParams, RootNavigatorParams } from '@src/types';
import _, { set } from 'lodash';
import { Box, Button, HStack, Text, message } from 'native-base';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, InteractionManager, Platform } from 'react-native';
import prompt from 'react-native-prompt-android';
import { useDispatch, useSelector } from '@src/store';
import { AuthActions } from '@src/slice/auth.slice';
import { useReceiveMemoMutation, useUpdateOneTaskMutation } from '@src/api/graphql';
import useCreateWorkspaceTemplate from '@src/mutation/workspace/useCreateWorkspaceTemplate';
import useUpdateWorkspaceDocument from '@src/mutation/workspace/useUpdateWorkspaceDocument';
import { ReactNativeFile } from 'apollo-upload-client';
import RNFS from 'react-native-fs';
import { useGetDrawingRevision } from '@src/queries/drawing-revision/useGetDrawingRevision';
import DrawingLinks from '@src/modules/Pdftron/components/DrawingLinks';
import ProjectDocumentModel from '@src/database/model/project-document';
import { useGetUnsyncAttachmentMedia } from '@src/queries/workspace/useGetUnsycAttachmentMedia';
import { isMobileLocalPath } from '@src/utils/filePathUtils';

type Props = CompositeScreenProps<
  BottomTabScreenProps<PdftronNavigatorParams, 'Pdftron'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const PdfTron: React.FC<Props> = ({ route, navigation }) => {
  // const { data: userData } = Gql.useGetUserMeQuery({});
  const userData = useSelector(state => state.auth.user);
  const currentUserId = parseInt(userData?.id ?? '0');
  const { signUrl, stampUrl } = useSelector(state => state.auth.user as any);
  const viewerRef = useRef<any>();
  const drawings = useSelector((state: any) => state.drawings);
  const id = route?.params.id;
  const data = route?.params.data;
  const role = route?.params.role;
  const assigneeIds = route?.params.assigneeIds;
  const addedBy = route?.params.addedBy;
  const driveType = route?.params.driveType;
  const status = route?.params.status;
  const currentUserStatus = route?.params.currentUserStatus;
  const modules = route?.params.modules;
  const routePath = route?.params?.path;
  const routeScreen = route?.params?.screen;
  const projectDocumentId = route?.params?.projectDocumentId;
  const isCurrentAssignee = route?.params?.isCurrentAssignee;
  const workflow = route?.params?.workflow;
  const filteredValue = drawings?.filteredValue;
  const [logArray, setLogArray] = useState<any[]>([]);
  const authState = useSelector(state => state.auth);
  const project = useSelector(state => state.project);
  const isPreview = route?.params?.isPreview ?? false;
  const savable =
    modules === 'Templates' || modules === 'Drawings' || modules === Gql.CategoryType.AllForm || modules === 'Revision';
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [hasNextDoc, setHasNextDoc] = useState<boolean>(true);
  const [hasPrevDoc, setHasPrevDoc] = useState<boolean>(true);
  let view = [] as any;
  const navigationRoute = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const dispatch = useDispatch();
  const highestVersion = route?.params?.isHigherVersion;
  const fileUrl = route?.params?.fileUrl;
  const drawingLinksModalRef = useRef<any>(null);
  const [annotationId, setAnnotationId] = useState<any>('');
  const localRemoteId = route?.params?.localRemoteId;
  const localId = route?.params?.localId;

  const { mutateAsync: updateProjectDocument } = useUpdateWorkspaceDocument();
  const { mutateAsync: createWorkspaceTemplate, isPending } = useCreateWorkspaceTemplate();

  const { data: unsyncedAttachmentMedia } =
    modules === Gql.CategoryType.AllForm ? useGetUnsyncAttachmentMedia(localId || '') : { data: null };

  const { data: drawingRevision } = useGetDrawingRevision();

  RNPdftron.initialize(process.env.PDFTRON_LICENSE_KEY as string);
  RNPdftron.enableJavaScript(true);

  const disabledView = ['TaskAttachment', 'TaskMedia', 'WorkspaceAttachment', 'WorkspaceMedia', 'CloudDocs', 'Photos'];
  const removeAddPages = [
    Config.Buttons.InsertBlankPage,
    Config.Buttons.InsertFromDocument,
    Config.Buttons.InsertFromImage,
    Config.Buttons.InsertFromPhoto,
    Config.Buttons.InsertFromScanner
  ];

  const FillAndSign = {
    [Config.CustomToolbarKey.Id]: 'FillAndSign',
    [Config.CustomToolbarKey.Name]: 'Fill And Sign',
    [Config.CustomToolbarKey.Icon]: Config.ToolbarIcons.FillAndSign,
    [Config.CustomToolbarKey.Items]: [
      Config.Tools.annotationCreateSignature,
      Config.Tools.annotationCreateFreeText,
      Config.Tools.annotationCreateFreeTextDate,
      Config.Tools.annotationCreateCheckMarkStamp,
      Config.Buttons.undo
    ]
  };

  const AnnotateToolbar = {
    [Config.CustomToolbarKey.Id]: 'Annotate',
    [Config.CustomToolbarKey.Name]: 'Annotate',
    [Config.CustomToolbarKey.Icon]: Config.ToolbarIcons.Annotate,
    [Config.CustomToolbarKey.Items]: [
      Config.Tools.annotationCreateFreeText,
      Config.Tools.annotationCreateRectangle,
      Config.Tools.annotationCreateFreeHand,
      Config.Tools.annotationCreateFreeHighlighter,
      Config.Tools.annotationCreatePolygonCloud,
      Config.Tools.annotationCreateArrow,
      Config.Buttons.undo
    ]
  };

  const MeasureToolbar = {
    [Config.CustomToolbarKey.Id]: 'Measure',
    [Config.CustomToolbarKey.Name]: 'Measure',
    [Config.CustomToolbarKey.Icon]: Config.ToolbarIcons.Measure,
    [Config.CustomToolbarKey.Items]: [
      Config.Tools.annotationCreateDistanceMeasurement,
      Config.Tools.annotationCreatePerimeterMeasurement,
      Config.Tools.annotationCreateAreaMeasurement,
      Config.Tools.annotationCountTool
    ]
  };

  const [createAuditLog] = Gql.useCreateManyAuditLogsMutation({
    onCompleted: () => {}
  });

  // const [updateFile, { loading: updateLoading }] = Gql.useUpdateOneProjectDocumentMutation({
  //   onCompleted: async () => {
  //     pushSuccess('File saved successfully');
  //     setTimeout(() => {
  //       navigation.goBack();
  //       dispatch(AuthActions.setRequestForLoggedOut(false));
  //     }, 500);
  //     dispatch(AuthActions.setRequestForLoggedOut(false));
  //   },
  //   onError: (err: any) => {
  //     //? close the loading overlay
  //     dispatch(AuthActions.setRequestForLoggedOut(false));
  //     pushError(err.message);
  //   }
  // });
  const [receiveMemo, { loading: updating }] = useReceiveMemoMutation({
    onError: e => {
      pushError(e);
    },
    onCompleted: () => {
      pushSuccess('Receive memo successfully');
      navigation.goBack();
    }
  });

  const [updateTask, { loading: updatingTask }] = useUpdateOneTaskMutation({
    onCompleted: () => {
      pushSuccess('Memo successfully generated');
      navigation.goBack();
    }
  });

  const onUpdate = useCallback(
    async (files: ReactNativeFile) => {
      if (status !== Gql.ProjectDocumentStatus.Draft) {
        // if there's unsynced attachment, media, or document, prevent the update
        if (unsyncedAttachmentMedia) {
          pushError('Please sync your unsynced attachment, media, or document before updating the file.');
          return;
        }
      }

      await updateProjectDocument({
        id: localId as string,
        fileUrl: files.uri as string,
        fileKey: null as any,
        localFileUrl: null as any,
        isQueued: false
      });
    },
    [status]
  );

  const handleDocumentNavigation = async (direction: 'next' | 'previous') => {
    try {
      if (!project?.projectId) {
        pushError('Project ID is missing');
        return;
      }

      setIsSubmitting(true);
      const adjacentDoc = await ProjectDocumentModel.getAdjacentDocument(
        route?.params.localId ?? '',
        direction,
        parseInt(project.projectId),
        'TwoDDrawings',
        projectDocumentId as any
      );

      if (!adjacentDoc) {
        Alert.alert('Navigation Error', `No ${direction} document available`);
        if (direction === 'next') {
          setHasNextDoc(false);
        } else {
          setHasPrevDoc(false);
        }
        return;
      }

      // Reset navigation flags when document exists
      setHasNextDoc(true);
      setHasPrevDoc(true);

      const params: PdftronNavigatorParams['Pdftron'] = {
        id: String(adjacentDoc.remoteId ?? adjacentDoc.id),
        modules: modules,
        role: project.projectUserRole as string,
        localId: adjacentDoc.id,
        projectDocumentId: adjacentDoc.projectDocumentId
          ? adjacentDoc.projectDocumentId
          : adjacentDoc.localProjectDocumentId,
        ...(adjacentDoc.isUnsync
          ? { fileUrl: adjacentDoc.fileUrl }
          : adjacentDoc.localFileUrl
            ? { fileUrl: adjacentDoc.localFileUrl }
            : { id: adjacentDoc.remoteId }),
        xfdf: adjacentDoc.xfdf || '',
        isHigherVersion: false,
        revisionData: [],
        version: 0
      };

      return navigation.replace('PdftronNav', {
        screen: 'Pdftron',
        params
      });
    } catch (error) {
      pushError(`Failed to navigate to ${direction} document`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const dynamicBackHandler = () => {
    navigationRoute?.replace('Tab' as any, {
      screen: routeScreen,
      params: {
        pageIndex: 0
      }
    });
  };

  // const [updateUserSignature] = Gql.useUpdateUserMeMutation({
  //   onCompleted: () => {
  //     pushSuccess('Signature saved successfully');
  //   }
  // });

  // const onUpdateSignature = (files: any) => {
  //   updateUserSignature({
  //     variables: {
  //       input: {
  //         signUrl: files as any
  //       }
  //     }
  //   });
  // };

  const onUploadTemplate = async (fileUrl: any, xfdf: string) => {
    // uploadFile({
    //   variables: {
    //     input: {
    //       projectDocument: {
    //         name: fileurl.name,
    //         category: Gql.CategoryType.StandardForm,
    //         fileSystemType: Gql.FileSystemType.Document,
    //         projectDocumentId: projectDocumentId,
    //         fileUrl: fileurl,
    //         xfdf
    //       }
    //     }
    //   }
    // });

    // create a new file and save it to watermelon db

    if (!project?.projectId) return;
    if (!fileUrl.uri) throw new Error('File URL is missing');

    // file:///data/user/0/cloud.bina.android/cache/uri_cache/Lukisan%20Rekabentuk%20Jalan%20Road.pdf
    //

    const newDocs: CreateProjectDocumentInput[] = [
      {
        name: fileUrl.name,
        category: Gql.CategoryType.StandardForm,
        fileSystemType: Gql.FileSystemType.Document,
        projectDocumentId: projectDocumentId as any,
        localRemoteId,
        fileUrl: fileUrl.uri,
        xfdf,
        status: 'Draft',
        type: 'pdf',
        projectId: parseInt(project?.projectId),
        addedBy: parseInt(userData?.id ?? '0')
      }
    ];

    return await createWorkspaceTemplate(newDocs).then(() => {
      pushSuccess('Your document is now available in the drafts section', 'Document Generated');
      setTimeout(() => {
        dispatch(AuthActions.setRequestForLoggedOut(false));
        navigation.goBack();
      }, 500);
    });
  };

  const onUploadDocument = async (fileUrl: ReactNativeFile) => {
    if (!project?.projectId) return;

    const newDocs: CreateProjectDocumentInput[] = [
      {
        name: fileUrl.name ?? '',
        category: Gql.CategoryType.AllForm,
        fileSystemType: Gql.FileSystemType.Document,
        localRemoteId,
        driveType: undefined,
        fileUrl: fileUrl.uri,
        type: 'pdf',
        projectId: parseInt(project?.projectId ?? '0'),
        status: Gql.ProjectDocumentStatus.Draft,
        addedBy: parseInt(userData?.id ?? '0')
      }
    ];

    await createWorkspaceTemplate(newDocs).then(() => {
      pushSuccess('Your document is now available in the drafts section', 'Document Generated');
      setTimeout(() => {
        dispatch(AuthActions.setRequestForLoggedOut(false));
        navigation.goBack();
      }, 500);
    });
  };

  if (modules === Gql.CategoryType.AllForm) {
    if (
      (assigneeIds === '' ||
        isCurrentAssignee ||
        (assigneeIds?.includes(currentUserId) && workflow === 'Linear') ||
        (addedBy === currentUserId && status === 'Draft')) &&
      role !== 'CanView' &&
      status !== 'Approved' &&
      status !== 'Pending' &&
      status !== 'Submitted' &&
      status !== 'Rejected' &&
      currentUserStatus != 'Approved'
    ) {
      view = [Config.DefaultToolbars.View, FillAndSign, AnnotateToolbar];
    } else {
      view = [Config.DefaultToolbars.View];
    }
  } else if (modules === 'CloudDocs') {
    view = [Config.DefaultToolbars.View];
  } else if (modules === 'Templates' && role !== 'CanView') {
    view = [Config.DefaultToolbars.View, FillAndSign, AnnotateToolbar];
  } else if ((modules === 'Drawings' || modules === 'Revision') && role !== 'CanView') {
    view = [Config.DefaultToolbars.View, AnnotateToolbar, MeasureToolbar]; //Disabled
  } else {
    view = [Config.DefaultToolbars.View];
  }

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      // if (fileUrl !== undefined) return;
      if (modules === 'TaskAttachment') getTaskAttach();
      else if (modules === 'TaskMedia') getTaskMedia();
      else if (modules === 'WorkspaceAttachment') getWorkspaceAttach();
      else if (modules == 'WorkspaceMedia') getWorkspaceMedia();
      else if (modules === 'Revision') getDrawingRevision();
      else if (modules === 'Task') getTask();
      else if (modules === 'DrawingLink') getDrawingLinksAttachment();
      else getDoc();
    });
  }, [fileUrl]);

  useEffect(() => {
    if (!routePath || !routeScreen) return;
    //? override back button
    BackHandler.addEventListener('hardwareBackPress', () => {
      dynamicBackHandler();
      return true;
    });

    return () => {
      BackHandler.removeEventListener('hardwareBackPress', () => {
        dynamicBackHandler();
        return true;
      });
    };
  }, [dynamicBackHandler, routePath, routeScreen]);

  const [getDoc, { data: docsData }] = Gql.useProjectDocumentLazyQuery({ variables: { id: id } });
  const [getTaskAttach, { data: taskAttachData }] = Gql.useGetTasksAttachmentLazyQuery({ variables: { id: id } });
  const [getTaskMedia, { data: taskMediaData }] = Gql.useGetTasksMediaLazyQuery({ variables: { id: id } });
  const [getWorkspaceAttach, { data: workspaceAttachData }] = Gql.useWorkspaceAttachmentLazyQuery({
    variables: { id: id }
  });

  const [getWorkspaceMedia, { data: workspaceMediaData }] = Gql.useWorkspacePhotoLazyQuery({
    variables: { id: id }
  });
  const [getDrawingRevision, { data: drawingRevisionData }] = Gql.useGetDrawingRevisionLazyQuery({
    variables: { id: id }
  });
  const [getTask, { data: taskData }] = Gql.useGetTaskLazyQuery({ variables: { id: id } });
  const [getDrawingLinksAttachment, { data: drawingLinksAttachmentData }] = Gql.useGetDrawingLinkAttachmentLazyQuery({
    variables: { id: id }
  });

  const path =
    (fileUrl && isMobileLocalPath(fileUrl)
      ? fileUrl
      : // pdfData?.fileUrl ||
        docsData?.projectDocument?.fileUrl ||
        taskAttachData?.tasksAttachment?.fileUrl ||
        taskMediaData?.tasksMedia?.fileUrl ||
        workspaceAttachData?.workspaceAttachment?.fileUrl ||
        workspaceMediaData?.workspacePhoto?.fileUrl ||
        (isPreview ? taskData?.task?.previewMemoUrl : taskData?.task?.memoUrl) ||
        drawingRevisionData?.drawingRevision?.fileUrl ||
        drawingLinksAttachmentData?.drawingLinkAttachment?.fileUrl) ?? '';

  const dataId =
    (docsData?.projectDocument?.id ||
      taskAttachData?.tasksAttachment?.id ||
      taskMediaData?.tasksMedia?.id ||
      workspaceAttachData?.workspaceAttachment?.id ||
      workspaceMediaData?.workspacePhoto?.id ||
      taskData?.task?.id ||
      drawingRevisionData?.drawingRevision?.id) ??
    '';
  const name =
    (docsData?.projectDocument?.name ||
      taskAttachData?.tasksAttachment?.name ||
      taskMediaData?.tasksMedia?.name ||
      workspaceAttachData?.workspaceAttachment?.name ||
      workspaceMediaData?.workspacePhoto?.name ||
      taskData?.task?.title ||
      drawingRevisionData?.drawingRevision?.fileName ||
      drawingLinksAttachmentData?.drawingLinkAttachment?.fileName) ??
    '';
  // const xfdf = fileUrl ? route?.params?.xfdf : (docsData?.projectDocument?.xfdf || drawingRevisionData?.drawingRevision?.xfdf);
  const xfdf = useMemo(() => {
    if (route?.params?.xfdf) {
      return route?.params?.xfdf;
    } else {
      return docsData?.projectDocument?.xfdf || drawingRevisionData?.drawingRevision?.xfdf;
    }
  }, []);

  const isMemoReceived = taskData?.task?.isMemoReceive ?? false;

  const onLeadingNavButtonPressed = () => {
    if (Platform.OS === 'ios') {
      Alert.alert('App', 'onLeadingNavButtonPressed', [{ text: 'OK', onPress: () => {} }], {
        cancelable: true
      });
    } else {
      if (routePath && routeScreen) {
        return dynamicBackHandler();
      }
      navigation.goBack();
      route?.params?.onPressBack?.();
    }
  };

  const flattenAnnotation = async () => {
    try {
      await viewerRef.current.flattenAnnotations(false);
      const files = await viewerRef.current.saveDocument();

      const newFile = await Promise.all([
        Platform.OS === 'ios'
          ? generateRNFile({
              uri: files,
              name: name as string,
              type: 'application/pdf',
              extension: 'pdf'
            })
          : generateRNFile({
              uri: `file://${encodeURI(files)}`,
              name: name as string,
              type: 'application/pdf',
              extension: 'pdf'
            })
      ]).then(([file]) => file);

      await onUpdate(newFile as ReactNativeFile);
      pushSuccess('Saved successfully');
      navigation.goBack();
    } catch (e) {
      pushError(e);
      dispatch(AuthActions.setRequestForLoggedOut(false));
    }
  };

  const onSavedXfdf = useCallback(
    async (xfdf: string) => {
      if (status !== Gql.ProjectDocumentStatus.Draft && modules === Gql.CategoryType.AllForm) {
        // if there's unsynced attachment, media, or document, prevent the update
        if (unsyncedAttachmentMedia) {
          pushError('Please sync your unsynced attachment, media, or document before updating the file.');
          return;
        }
      }

      if (modules === 'Drawings' || modules === 'Templates') {
        setIsSubmitting(true);
        try {
          await updateProjectDocument({
            id: localId as string,
            xfdf
          });
        } catch (e) {
          pushError(e);
          dispatch(AuthActions.setRequestForLoggedOut(false));
        } finally {
          if (modules === 'Templates') {
            await saveActivityLog('');
          }
          pushSuccess('Saved successfully');
          setIsSubmitting(false);
          dispatch(AuthActions.setRequestForLoggedOut(false));
          setTimeout(() => {
            navigation.goBack();
          }, 500);
        }
      } else if (modules === Gql.CategoryType.AllForm) {
        setIsSubmitting(true);
        try {
          await updateProjectDocument({
            id: localId as string,
            xfdf
          });
        } catch (e) {
          pushError(e);
          dispatch(AuthActions.setRequestForLoggedOut(false));
        } finally {
          pushSuccess('Saved successfully');
          setIsSubmitting(false);
          dispatch(AuthActions.setRequestForLoggedOut(false));
          setTimeout(() => {
            navigation.goBack();
          }, 500);
        }
      } else if (modules === 'Revision') {
        setIsSubmitting(true);
        try {
          await apolloClient.mutate<Gql.UpdateOneDrawingRevisionMutation>({
            mutation: Gql.UpdateOneDrawingRevisionDocument,
            variables: {
              input: {
                id: _.toString(dataId),
                update: {
                  xfdf
                }
              }
            }
          });
        } catch (e) {
          pushError(e);
          dispatch(AuthActions.setRequestForLoggedOut(false));
        } finally {
          pushSuccess('Saved successfully');
          setIsSubmitting(false);
          dispatch(AuthActions.setRequestForLoggedOut(false));
          setTimeout(() => {
            navigation.goBack();
          }, 500);
        }
      }
    },
    [status]
  );

  const encodeFilePath = (filePath: string) => {
    const parts = filePath.split('/');
    const filename = parts.pop();

    if (!filename) {
      throw new Error('Invalid file path');
    }

    const modifiedFilename = filename.replace(/%/g, ' ');
    return `${parts.join('/')}/${modifiedFilename}`;
  };

  const signatureArray = [signUrl, stampUrl].filter(url => url !== null);

  if (!path) return null;

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <AppBar
        goBack={Platform.OS === 'ios' ? true : undefined}
        barStyle={'dark-content'}
        title={name}
        noRight={Platform.OS === 'ios' ? true : undefined}
        onPressTitle={() => {
          Alert.alert(name);
        }}
        onGoBack={async () => {
          if (modules === 'Templates') {
            await RNFS.unlink(path);
          }
          if (routePath && routeScreen) {
            return dynamicBackHandler();
          }
          if (route.params?.onPressBack) {
            navigation.goBack();
            return route.params?.onPressBack();
          }
          navigation.goBack();
        }}
      />
      <DocumentView
        disabledElements={modules === 'CloudDocs' ? removeAddPages : []}
        ref={viewerRef}
        document={path}
        fontSize={32}
        //? Todo: Add signature
        // maxSignatureCount={signatureArrayUrl.length}
        {...(signatureArray.length ? { signatureArrayUrl: signatureArray } : {})}
        annotationToolbars={view}
        useStylusAsPen={false}
        onAnnotationsSelected={({ annotations }: any) => {
          annotations.forEach((annotation: any) => {
            if ((modules === 'Drawings' || modules === 'Revision') && annotation.type === 'AnnotationCreateStamp') {
              setAnnotationId(annotation.id);
              drawingLinksModalRef?.current?.pushModal();
            }
          });
        }}
        onAnnotationChanged={(annotations: any) => {
          viewerRef.current
            .getPropertiesForAnnotation(annotations.annotations[0].id, annotations.annotations[0].pageNumber)
            .then((res: any) => {
              //Return if only want add
              if (annotations.action === 'modify' && annotations.annotations[0]?.type === 'AnnotationCreateStamp') {
                return;
              }
              const [annotationMessage] = annotMessage(annotations.annotations);
              const actionMessage = actMessage(annotations.action);
              let actionLog;
              if (annotations.action === 'delete') actionLog = Gql.AuditLogActionType.Delete;
              else if (annotations.action === 'modify') actionLog = Gql.AuditLogActionType.Update;
              else if (annotations.action === 'add') actionLog = Gql.AuditLogActionType.Add;
              else return;

              const newLogObject = {
                action: actionLog,
                module: Gql.AuditLogModuleType.DocumentEditor,
                resourceId: id,
                userId: authState?.user?.id,
                projectId: project?.projectId,
                content: `${
                  authState?.user?.name
                } ${actionMessage} ${annotationMessage?.toUpperCase()} at ${name} | Page: ${
                  annotations.annotations[0].pageNumber
                }`,
                text: res.contents ? res.contents : null
              };

              setLogArray(prevLogArray => [...prevLogArray, newLogObject]);
            });
        }}
        onLeadingNavButtonPressed={onLeadingNavButtonPressed}
        onDocumentLoaded={() => {
          if (xfdf) {
            viewerRef?.current?.importAnnotations(xfdf).then((importedAnnotations: any) => {
              importedAnnotations.forEach((data: any) => {
                viewerRef?.current.getPropertiesForAnnotation(data.id, data.pageNumber).then((annotation: any) => {
                  if (annotation.subject === 'Rectangle') {
                    viewerRef.current.deleteAnnotations([{ id: data.id, pageNumber: data.pageNumber }]);
                  }
                });
              });
            });
          }
        }}
      />
      <Footer>
        {modules === 'Revision' &&
          (highestVersion ? null : (
            <Button colorScheme={'red'} disabled variant="ghost">
              OBSOLETE
            </Button>
          ))}
        <HStack justifyContent={'flex-end'}>
          {modules === 'Task' && !isMemoReceived && !isPreview ? (
            <Button
              variant="primary"
              isLoading={isSubmitting}
              isDisabled={disabledView.includes(modules)}
              mx={1}
              width={100}
              onPress={async () => {
                Alert.alert('Receive Memo', 'Are you sure you want to receive this memo?', [
                  {
                    text: 'Cancel',
                    style: 'cancel',
                    onPress: () => {}
                  },
                  {
                    text: 'OK',
                    onPress: () => {
                      receiveMemo({
                        variables: {
                          input: {
                            taskId: Number(id)
                          }
                        }
                      });
                    }
                  }
                ]);
              }}
            >
              Receive
            </Button>
          ) : null}

          {modules === 'Task' && isPreview ? (
            <Button
              variant="primary"
              isLoading={isSubmitting || updatingTask}
              isDisabled={disabledView.includes(modules)}
              mx={1}
              width={100}
              onPress={async () => {
                Alert.alert('Generate Memo', 'Are you sure you want to generate this memo?', [
                  {
                    text: 'Cancel',
                    style: 'cancel',
                    onPress: () => {}
                  },
                  {
                    text: 'OK',
                    onPress: () => {
                      updateTask({
                        variables: {
                          id: id,
                          input: {
                            memoUrl: taskData?.task?.previewMemoUrl
                          }
                        }
                      });
                    }
                  }
                ]);
              }}
            >
              Generate
            </Button>
          ) : null}
          {modules === Gql.CategoryType.AllForm && workflow === 'Dynamic' ? (
            <Button
              variant="primary"
              isLoading={isSubmitting}
              isDisabled={disabledView.includes(modules)}
              mx={1}
              width={100}
              onPress={async () => {
                return await viewerRef?.current.exportAnnotations().then((data: any) => onSavedXfdf(data));
              }}
            >
              Draft
            </Button>
          ) : null}

          {/* {(modules === 'Revision' || modules === 'Drawings') && role !== 'CanView' ? (
            <Box maxW="250">
              <HStack space={1}>
                <Button
                  variant="primary"
                  isLoading={isSubmitting}
                  onPress={() => handleDocumentNavigation('previous')}
                  disabled={isSubmitting || !hasPrevDoc}
                >
                  <Text color="white" fontSize="lg">
                    {'<'}
                  </Text>
                </Button>
                <Button
                  variant="primary"
                  isLoading={isSubmitting}
                  onPress={() => handleDocumentNavigation('next')}
                  disabled={isSubmitting || !hasNextDoc}
                >
                  <Text color="white" fontSize="lg">
                    {'>'}
                  </Text>
                </Button>
                <Select
                  minWidth="135"
                  accessibilityLabel="Choose Service"
                  placeholderTextColor={COLORS.primary[1]}
                  placeholder={
                    drawingRevisionData?.drawingRevision?.versionName ??
                    docsData?.projectDocument?.versionName ??
                    'Version No'
                  }
                  _selectedItem={{
                    color: COLORS.primary[1],
                    bg: 'teal.600',
                    endIcon: <CheckIcon size="5" />
                  }}
                  mt={1}
                  onValueChange={(selectedRemoteId: string) => {
                    const params: PdftronNavigatorParams['Pdftron'] = {
                      id: parseInt(selectedRemoteId),
                      data: data,
                      revisionData: revisionData,
                      modules: 'Revision',
                      projectDocumentId
                      // version: selectedDrawing.version,
                    };

                    navigation.goBack();
                    navigation.navigate('PdftronNav', {
                      screen: 'Pdftron',
                      params
                    });
                  }}
                >
                  {revisionData?.map((item: DrawingRevisionModel, index: any) => (
                    <Select.Item label={item?.versionName ?? ''} value={String(item?.remoteId ?? '')} key={index} />
                  ))}
                </Select>
              </HStack>
            </Box>
          ) : null} */}
          {savable && status === Gql.ProjectDocumentStatus.Approved ? (
            <Button
              variant="primary"
              isLoading={isSubmitting}
              isDisabled={disabledView.includes(modules)}
              mx={1}
              width={'auto'}
              onPress={async () => {
                dispatch(AuthActions.setRequestForLoggedOut(true));
                if (modules === 'Drawings' || modules === 'Revision' || modules === Gql.CategoryType.AllForm) {
                  viewerRef?.current.exportAnnotations().then(onSavedXfdf);
                  return;
                } else if (modules === 'Templates') {
                  viewerRef.current.saveDocument().then((files: any) => {
                    viewerRef?.current.exportAnnotations().then((xfdf: any) => {
                      if (Platform.OS === 'ios') {
                        Alert.prompt('Save as new template', 'Enter a name for your template', [
                          {
                            text: 'Cancel',
                            style: 'cancel',
                            onPress: () => {
                              dispatch(AuthActions.setRequestForLoggedOut(false));
                            }
                          },
                          {
                            text: 'OK',
                            onPress: (name: any) => {
                              if (!name || name === '') {
                                pushMessaage('Error on name', 'error', 'Name is required!');
                                dispatch(AuthActions.setRequestForLoggedOut(false));
                                return;
                              } else if (!_.endsWith(name, '.pdf')) {
                                name = name + '.pdf';
                              }
                              const newFile = generateRNFile({
                                uri: files,
                                name: name as string,
                                type: 'application/pdf',
                                extension: 'pdf'
                              });
                              onUploadTemplate(newFile, xfdf);
                            }
                          }
                        ]);
                      } else {
                        prompt('Save as new template', 'Enter a name for your template', [
                          {
                            text: 'Cancel',
                            style: 'cancel',
                            onPress: () => {
                              dispatch(AuthActions.setRequestForLoggedOut(false));
                            }
                          },
                          {
                            text: 'OK',
                            onPress: async (name: any) => {
                              if (!name || name === '') {
                                pushMessaage('Error on name', 'error', 'Name is required!');
                                return;
                              } else if (!_.endsWith(name, '.pdf')) {
                                name = name + '.pdf';
                              }
                              const filePath = encodeFilePath(files);
                              const newFile = generateRNFile({
                                uri: filePath,
                                name: name,
                                type: 'application/pdf',
                                extension: 'pdf'
                              });
                              onUploadTemplate(newFile, xfdf);
                            }
                          }
                        ]);
                      }
                    });
                  });
                } else if (modules === Gql.CategoryType.AllForm) {
                  await flattenAnnotation();
                }
              }}
            >
              <Text color={'white'} numberOfLines={1} fontWeight={'semibold'}>
                {modules === 'Templates' ? 'Save as Template' : '   Save   '}
              </Text>
            </Button>
          ) : null}
          {modules === 'Templates' ? (
            <Button
              variant="primary"
              isLoading={isSubmitting}
              isDisabled={disabledView.includes(modules)}
              mx={1}
              width={100}
              onPress={async () => {
                if (modules === 'Templates') {
                  viewerRef?.current.flattenAnnotations(false).then(() => {
                    viewerRef.current.saveDocument().then((files: any) => {
                      const filePath = encodeFilePath(files);
                      if (Platform.OS === 'ios') {
                        Alert.prompt('Rename', 'Input your new generated file name', [
                          {
                            text: 'Cancel',
                            style: 'cancel',
                            onPress: () => {
                              dispatch(AuthActions.setRequestForLoggedOut(false));
                            }
                          },
                          {
                            text: 'OK',
                            onPress: (name: any) => {
                              if (!name || name === '') {
                                pushMessaage('Error on name', 'error', 'Name is required!');
                                dispatch(AuthActions.setRequestForLoggedOut(false));
                                return;
                              } else if (!_.endsWith(name, '.pdf')) {
                                name = name + '.pdf';
                              }

                              const newFile = generateRNFile({
                                uri: filePath,
                                name: name as string,
                                type: 'application/pdf',
                                extension: 'pdf'
                              });
                              onUploadDocument(newFile as ReactNativeFile);
                            }
                          }
                        ]);
                      } else {
                        prompt('Rename', 'Input your new generated file name', [
                          {
                            text: 'Cancel',
                            style: 'cancel',
                            onPress: () => {
                              dispatch(AuthActions.setRequestForLoggedOut(false));
                            }
                          },
                          {
                            text: 'OK',
                            onPress: async (name: any) => {
                              if (!name || name === '') {
                                pushMessaage('Error on name', 'error', 'Name is required!');
                                return;
                              } else if (!_.endsWith(name, '.pdf')) {
                                name = name + '.pdf';
                              }
                              const encodedURI = encodeURI(files);
                              const newFile = generateRNFile({
                                uri: `file://${encodedURI}`,
                                name: name as string,
                                type: 'application/pdf',
                                extension: 'pdf'
                              });

                              onUploadDocument(newFile as ReactNativeFile);
                            }
                          }
                        ]);
                      }
                    });
                  });
                }
              }}
            >
              Generate
            </Button>
          ) : null}
          {(modules === 'Drawings' || modules === 'Revision') && role !== 'CanView' ? (
            <>
              <Button
                bg={'white'}
                variant="outline"
                borderWidth={1}
                borderColor={'#d9d9d9'}
                mx={1}
                width={100}
                onPress={() => {
                  Alert.alert('Annotations will not be saved on cancel', 'Do you want to continue?', [
                    {
                      text: 'No',
                      style: 'cancel'
                    },
                    {
                      text: 'Yes',
                      onPress: () => navigation.goBack()
                    }
                  ]);
                }}
              >
                <Text color={'primary.600'} numberOfLines={1} fontWeight={'semibold'}>
                  Cancel
                </Text>
              </Button>
              <Button variant="primary" mx={1} width={100} onPress={flattenAnnotation}>
                Publish
              </Button>
            </>
          ) : null}
        </HStack>
      </Footer>
      <DrawingLinks ref={drawingLinksModalRef} annotId={annotationId} />
    </Box>
  );
  function annotMessage(annotation: any) {
    let annotationLog = annotation[0]?.type;
    let annotationMessage;
    if (annotationLog === 'AnnotationCreateStamp') annotationMessage = 'Stamp/Sign';
    else if (annotationLog === 'Accepted') annotationMessage = 'Tick';
    else if (annotationLog === 'AnnotationCreateFreeText') {
      annotationMessage = 'Free Text';
    } else annotationMessage = annotationLog;

    return [annotationMessage];
  }
  function actMessage(action: any) {
    let actionMessage;
    if (action === 'add') actionMessage = 'added';
    else if (action === 'delete') actionMessage = 'deleted';
    else if (action === 'modify') actionMessage = 'modified';
    return actionMessage;
  }
  async function saveActivityLog(id: string) {
    let stampAndSignatureData;
    const updateFreeTextData = logArray.filter(log => log.content?.includes('FREE TEXT'));

    if (id !== '') {
      stampAndSignatureData = logArray
        .filter(log => !log.content?.includes('FREE TEXT'))
        .map(log => ({ ...log, resourceId: id }));
    } else {
      stampAndSignatureData = logArray.filter(log => !log.content?.includes('FREE TEXT'));
    }

    // Combine 'text' values into a comma-separated string
    let combinedText = updateFreeTextData.map(update => update.text).join('~');

    let updateResult = null;
    if (updateFreeTextData.length > 0) {
      // Create the desired object
      updateResult = {
        action: updateFreeTextData[0].action,
        content: updateFreeTextData[0].content,
        module: updateFreeTextData[0].module,
        projectId: updateFreeTextData[0].projectId,
        resourceId: id !== '' ? id : updateFreeTextData[0].resourceId,
        text: combinedText,
        userId: updateFreeTextData[0].userId
      };
    }

    const toSend = [...stampAndSignatureData, ...(updateResult ? [updateResult] : [])];

    if (logArray.length > 0) {
      await createAuditLog({ variables: { input: { auditLogs: toSend } } });
    }
  }
};

export default PdfTron;
