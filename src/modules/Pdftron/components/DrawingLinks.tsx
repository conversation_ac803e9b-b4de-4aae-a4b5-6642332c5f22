import { AppBar, Content, Footer, Icon, Modal } from '@commons';
import { Gql } from '@src/api';
import CommentComponent from './CommentComponent';
import _ from 'lodash';
import { Box, Button, HStack, Input, ScrollView, Spinner, Text, VStack } from 'native-base';
import React, { forwardRef, memo, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps, TouchableOpacity } from 'react-native';
import DocumentPicker from 'react-native-document-picker';
import { ReactNativeFile } from 'apollo-upload-client';
import { generateRNFile, pushError, pushMessaage, pushSuccess } from '@src/configs';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootNavigatorParams } from '@src/types';
import moment from 'moment';
import LinkedToModal from '@src/commons/LinkedToModal';
import { TextInput } from '@src/inputs';
import { Field, Formik, FormikProps } from 'formik';
import Loading from '@src/modules/App/components/Loading';

interface Props {
  annotId: string;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const DrawingLinksModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const linkedToRef = useRef<any>(null);
  const formRef = useRef<FormikProps<FormValues>>(null);
  const [initialValues, setInitialValues] = useState<any>({
    name: '',
    description: ''
  });

  //Get drawing links query by annotId
  const [getDrawingLink, { data, refetch, loading: fetching }] = Gql.useGetDrawingLinksByAnnotIdLazyQuery({
    variables: { input: { annotationId: props?.annotId } },
    // skip: !props?.annotId,
    onCompleted: async data => {
      await setInitialValues({
        ...data?.getDrawingLinksByAnnotId
      });
    },
    onError: err => {
      pushError(err);
    }
  });

  //Upload function for attachments
  const value = data?.getDrawingLinksByAnnotId;
  const existingAttachments = value?.drawingLinkAttachments?.nodes ?? [];
  const [uploadManyAttachment, { loading: creatingDrawingLinkAttachment }] =
    Gql.useCreateManyDrawingLinkAttachmentsMutation({
      onCompleted: () => {
        pushSuccess('Upload attachment successfully');
        refetch();
      }
    });

  //Delete one drawing link attachment
  const [deleteDrawingLinkAttachment] = Gql.useDeleteOneDrawingLinkAttachmentMutation({
    onCompleted: () => {
      pushSuccess('Delete attachment successfully');
      refetch();
    },
    onError: (err: any) => {
      pushError(err);
    }
  });

  const [updateDrawingLink, { loading: updating }] = Gql.useUpdateDrawingLinkMutation({
    onCompleted: () => {
      refetch();
      modalRef.current?.closeModal();
      pushSuccess('Update drawing link successfully');
    },
    onError: (err: any) => {
      pushError(err);
    }
  });

  const uploadDrawingLinksAttachments = async (attachments: ReactNativeFile[]) => {
    try {
      await uploadManyAttachment({
        variables: {
          input: {
            drawingLinkAttachments: attachments.map(attachment => ({
              fileUrl: attachment,
              fileName: attachment.name ?? '',
              drawingLinkId: parseInt(value?.id ?? '0')
            }))
          }
        }
      });
    } catch (error) {}
  };

  //Choose document function
  const chooseDocument = async () => {
    try {
      const res = await DocumentPicker.pick({
        type: [DocumentPicker.types.pdf],
        allowMultiSelection: true
      });
      let files: ReactNativeFile[] = [];
      res.forEach(element => {
        const newFile = generateRNFile({
          uri: element.uri,
          name: element.name as string,
          type: element.type as string
        });
        if (newFile) files.push(newFile);
      });

      await uploadDrawingLinksAttachments(files);
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
      } else {
        throw err;
      }
    }
  };

  useImperativeHandle(ref, () => ({
    pushModal: async () => {
      await getDrawingLink();
      modalRef?.current?.pushModal();
    }
  }));

  const getDrawingLinkLinkedDocuments = data?.getDrawingLinksByAnnotId?.drawingLinkDocuments?.map(
    (node: any) => node.id
  );
  const docsLink = data?.getDrawingLinksByAnnotId?.drawingLinkDocuments;

  const onSubmit = async (variables: any) => {
    const { name, description } = variables;

    await updateDrawingLink({
      variables: { input: { id: initialValues?.id as string, update: { name, description } } }
    });
  };

  // for autosave
  const SubmitListener = props => {
    const [lastValues, updateState] = React.useState(props.formik.values);
    const inputRef = React.useRef(null);

    const debouncedSubmit = React.useCallback(
      _.debounce(() => {
        if (inputRef.current) {
          // inputRef.current.focus();
        }
        props.formik.submitForm();
      }, 1000),
      [props.formik]
    );

    React.useEffect(() => {
      const valuesEqualLastValues = _.isEqual(lastValues, props.formik.values);
      const valuesEqualInitialValues = props.formik.values === props.formik.initialValues;

      if (!valuesEqualLastValues) {
        updateState(props.formik.values);
      }

      if (!valuesEqualLastValues && !valuesEqualInitialValues) {
        debouncedSubmit();
      }
    }, [
      // lastValues,
      // props.formik.values,
      // props.formik.initialValues,
      // props.onBlur,
      debouncedSubmit
    ]);

    return <Field name="name" label="Title" placeholder="" component={TextInput} innerRef={inputRef} />;
  };

  const loading = creatingDrawingLinkAttachment || fetching || updating;

  return (
    <Modal ref={modalRef} avoidKeyboard={true}>
      <Box mt={3} justifyContent="center" alignItems="center">
        <Text fontSize="md">Drawing Links</Text>
      </Box>

      <ScrollView height={'70%'}>
        {loading && <Loading />}
        <Box p="4">
          <Formik onSubmit={onSubmit} initialValues={initialValues} innerRef={formRef}>
            {() => {
              return (
                <Box flex={1}>
                  <Field name="name" label="Title" component={TextInput} />
                  <Field name="description" label="Description" component={TextInput} />
                </Box>
              );
            }}
          </Formik>

          <Text mt="2" variant="bodyReg">
            Attachments
          </Text>
          {existingAttachments.map((attachment, index) => (
            <HStack mt={2} key={index} style={{ width: '100%', justifyContent: 'space-between' }}>
              <TouchableOpacity
                style={{ flexDirection: 'row', flex: 1, alignItems: 'center' }}
                onPress={e => {
                  e.stopPropagation();
                  modalRef.current.closeModal();
                  navigation.push('PdftronNav', {
                    screen: 'Pdftron',
                    params: {
                      id: attachment.id ?? '',
                      modules: 'DrawingLink'
                    }
                  });
                }}
              >
                <Icon name="filetype-pdf" style={{ marginRight: 6 }} />
                <Text style={{ color: '#50A0D7', flex: 1 }}>{attachment.fileName}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={event => {
                  event.stopPropagation();
                  deleteDrawingLinkAttachment({
                    variables: {
                      id: attachment.id
                    }
                  });
                }}
              >
                <Icon name="delete-bin" width={15} height={15} />
              </TouchableOpacity>
            </HStack>
          ))}

          <TouchableOpacity
            onPress={() => {
              chooseDocument();
            }}
          >
            <Text mt={4} mb={2} color="neutrals.gray90">
              {' '}
              + Add attachment
            </Text>
          </TouchableOpacity>

          <VStack mt={4} mb={2}>
            <Box flex={1} flexDirection="row" alignItems="center" mb={2}>
              <Text variant="bodyReg">Linked to</Text>
            </Box>
            <Box flex={2}>
              <VStack>
                <VStack ml={1}>
                  {docsLink &&
                    docsLink?.map((files: any, index: number) => {
                      return (
                        <HStack alignItems={'center'} justifyContent={'space-between'} key={index}>
                          <TouchableOpacity
                            onPress={() => {
                              navigation.navigate('PdftronNav', {
                                screen: 'Pdftron',
                                params: {
                                  id: files.id ?? '',
                                  modules: 'ProjectDocument'
                                }
                              });
                            }}
                            // disabled={allLoading}
                          >
                            <Text
                              key={files.id ? files.id : moment().toLocaleString() + files.name}
                              numberOfLines={1}
                              ellipsizeMode="tail"
                              maxWidth={'90%'}
                              mt={2}
                              color={'#50A0D7'}
                            >
                              {files?.name}
                            </Text>
                          </TouchableOpacity>
                        </HStack>
                      );
                    })}
                </VStack>
                {docsLink && docsLink?.length < 5 && (
                  <TouchableOpacity
                    onPress={() => {
                      linkedToRef?.current?.pushModal();
                    }}
                    // disabled={!isTaskOwner && !role.tasks.canEdit && !isAssigned}
                  >
                    <Text mt={2}> + Add Linked document</Text>
                  </TouchableOpacity>
                )}
              </VStack>
            </Box>
          </VStack>

          <Text mt={2} variant="bodyReg">
            Comments
          </Text>
          <CommentComponent drawingLinkId={parseInt(value?.id ?? '0')} />
        </Box>

        <LinkedToModal
          ref={linkedToRef}
          setLinkedDocument={file => {
            let newLinkedDocument: any[] = [];
            if (getDrawingLinkLinkedDocuments) {
              newLinkedDocument = [...getDrawingLinkLinkedDocuments, file?.id];
            } else {
              newLinkedDocument = [file?.id];
            }

            if (newLinkedDocument?.length > 0) {
              updateDrawingLink({
                variables: {
                  input: {
                    id: value?.id as string,
                    update: {
                      drawingLinkDocuments: _.map(_.uniq(newLinkedDocument), obj => {
                        return { id: Number(obj) };
                      })
                    }
                  }
                }
              });
            }
          }}
        />
      </ScrollView>
      <Footer>
        <HStack space={2}>
          <Button
            variant="light"
            w={'1/2'}
            onPress={() => {
              modalRef.current?.closeModal();
            }}
            isLoading={loading}
            mt={4}
          >
            Cancel
          </Button>
          <Button
            w={'1/2'}
            variant="primary"
            onPress={() => {
              formRef.current?.submitForm();
            }}
            isLoading={loading}
            mt={4}
          >
            Save
          </Button>
        </HStack>
      </Footer>
    </Modal>
  );
});

interface FormValues {
  name: string;
  description: string;
}

export default memo(DrawingLinksModal);
