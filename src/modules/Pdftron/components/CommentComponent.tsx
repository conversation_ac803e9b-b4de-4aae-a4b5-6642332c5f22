import { Gql } from '@src/api';
import { Icon } from '@src/commons';
import { pushError } from '@src/configs';
import CommentModal from '@src/modules/Task/components/Tasks/CommentModal';
import { useSelector } from '@src/store';
// import _ from 'lodash';
import moment from 'moment';
import { Avatar, Box, FlatList, HStack, Pressable, Text, VStack } from 'native-base';
import React, { useEffect, useRef } from 'react';
import { Alert, StyleSheet, TouchableOpacity } from 'react-native';
import ParsedText from 'react-native-parsed-text';

type Props = {
  drawingLinkId: number;
};

export const CommentComponent: React.FC<Props> = props => {
  const commentRef = useRef<any>(null);
  let regexArr: any[] = [];
  const { user } = useSelector(state => state.auth);
  const loggedInUser = user?.avatar;
  const project = useSelector(state => state.project);
  const role = project.projectUserRole;
  const canDelete = role === Gql.ProjectUserRoleType.CloudCoordinator || role === Gql.ProjectUserRoleType.ProjectOwner;

  const [getComments, { data, refetch }] = Gql.useGetDrawingLinkCommentsLazyQuery({
    variables: {
      filter: { drawingLinkId: { eq: props.drawingLinkId } },
      sorting: [
        {
          direction: Gql.SortDirection.Asc,
          field: Gql.DrawingLinkCommentSortFields.CreatedAt
        }
      ]
    }
  });

  useEffect(() => {
    if (!props.drawingLinkId) return;
    getComments();
  }, [props.drawingLinkId]);

  const comments = data?.drawingLinkComments?.nodes ?? [];
  const allComments = comments.map((comment: any) => {
    return {
      id: comment.id,
      message: comment.message,
      createdAt: comment.createdAt,
      avatarUrl: comment.user?.avatar,
      userId: comment.user?.id,
      fullName: comment.user?.name
    };
  });
  const { data: me } = Gql.useGetUserMeQuery({ onError: pushError });

  // deleteProjectComment mutation
  const [deleteDrawingLinkCommentMutation] = Gql.useDeleteDrawingLinkCommentMutation({
    onError: pushError as any,
    onCompleted: () => {
      refetch();
    }
  });

  return (
    <Box>
      <Box>
        <FlatList
          keyboardShouldPersistTaps="handled"
          data={allComments}
          renderItem={({ item }) => {
            var matches = item.message.match(/\[(.*?)\]/g);
            matches?.map((itemMatch: string) => {
              let replaceName1 = itemMatch.replace('[', '');
              let replaceName2 = replaceName1.replace(']', '');
              item.message =
                item?.message != undefined ? item?.message?.replace(itemMatch, replaceName2) : item?.message;
              let reg = new RegExp('@' + replaceName2);
              let pattern1 = {
                pattern: reg,
                style: {
                  color: '#2a64f9'
                }
              };
              regexArr.push(pattern1);
            });
            // check more than 5 minute or not
            const isMoreThan5Minute = moment().diff(moment(item.createdAt), 'minutes') > 5;

            // check is user or not
            const isUser = me?.getUserMe?.id === item.userId;

            return (
              <Box pl={['0', '4']} pr={['0', '5']} py="3">
                <HStack space={[2, 3]} justifyContent="space-between">
                  <Avatar
                    size="32px"
                    source={{
                      uri: item.avatarUrl
                    }}
                  />
                  <VStack flex={1}>
                    <HStack space={2}>
                      <Text
                        _dark={{
                          color: 'warmGray.50'
                        }}
                        color="coolGray.800"
                        bold
                        ellipsizeMode="tail"
                        numberOfLines={1}
                        maxWidth={'50%'}
                      >
                        {item.fullName}
                      </Text>
                      <Text
                        fontSize="xs"
                        _dark={{
                          color: 'warmGray.50'
                        }}
                        color="coolGray.800"
                        alignSelf="flex-start"
                      >
                        {moment(item.createdAt).format('D MMM YYYY, h:mma')}
                      </Text>
                    </HStack>

                    <ParsedText
                      style={{ color: 'black', fontSize: 15, maxWidth: '90%' }}
                      parse={regexArr}
                      childrenProps={{ allowFontScaling: false }}
                    >
                      {item?.message.replace(/ *\([^)]*\)*/g, '')}
                    </ParsedText>
                  </VStack>
                  {(!isMoreThan5Minute && isUser) || canDelete ? (
                    <Pressable
                      onPress={async () => {
                        Alert.alert('Delete Comment', 'Are you sure you want to delete this comment?', [
                          {
                            text: 'Cancel',
                            style: 'cancel'
                          },
                          {
                            text: 'Delete',
                            style: 'destructive',
                            onPress: async () => {
                              await deleteDrawingLinkCommentMutation({ variables: { input: { id: item.id } } });
                            }
                          }
                        ]);
                      }}
                    >
                      <Box flex={1} justifyContent={'center'}>
                        <Icon name="delete-bin" width={15} height={15} />
                      </Box>
                    </Pressable>
                  ) : null}
                </HStack>
              </Box>
            );
          }}
          keyExtractor={item => item.id}
        />
      </Box>
      <Box flexDirection="row" mt={4}>
        <Box flex={0.7}>
          <Avatar
            size="32px"
            source={{
              uri: loggedInUser ?? ''
            }}
          />
        </Box>
        <Box flex={6}>
          <TouchableOpacity onPressIn={() => commentRef.current?.pushModal()} style={styles.mentionInput} />
        </Box>
      </Box>
      <CommentModal
        // refetchComment={() => {
        //   refetch();
        // }}
        ref={commentRef}
        module="drawingLink"
        drawingLinkId={props.drawingLinkId}
      />
    </Box>
  );
};

const styles = StyleSheet.create({
  mentionInput: {
    borderWidth: 1,
    borderColor: '#E6E6E6',
    borderRadius: 5,
    padding: 5,
    textAlignVertical: 'top',
    height: 70
  }
});

export default CommentComponent;
