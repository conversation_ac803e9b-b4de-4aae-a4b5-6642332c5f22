import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { PdftronNavigatorParams } from '@src/types';
import React from 'react';
import Pdftron from './pages/Pdftron';

const PdftronStack = createNativeStackNavigator<PdftronNavigatorParams>();

const PdftronNavigator: React.FC<any> = () => {
  return (
    <PdftronStack.Navigator initialRouteName="Pdftron" screenOptions={{ headerShown: false }}>
      <PdftronStack.Screen name="Pdftron" component={Pdftron} />
    </PdftronStack.Navigator>
  );
};

export default PdftronNavigator;
