import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { AppBar } from '@src/commons';
import { getLinkId } from '@src/database/utils/numeric';
import { showFileName } from '@src/configs';
import useDebouncedSearch from '@src/hooks/useDebounceSearch';
import { manageFiles } from '@src/lib/authority';
import { useSelector } from '@src/store';
import { CloudDocNavigatorParams, RootNavigatorParams } from '@src/types';
import _, { get } from 'lodash';
import { Box, Skeleton, Stack, VStack } from 'native-base';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import CloudDocAddModal from '../components/CloudDocAddModal';
import ThreeDotsOptionModal from '../components/ThreeDotsOptionModal';
// import RNFS from 'react-native-fs';
import { useGetProjectDocuments } from '@src/queries/cloud-docs/useGetCloudDocs';
import ProjectDocumentModel from '@src/database/model/project-document.model';
import AddButton from '@src/commons/AddButton';
import SearchInput from '@src/commons/SearchInput';
import DocumentList from '@src/commons/list/DocumentList';
import { openDocsViewer } from '@src/commons/docs-viewer/DocsViewer';

type Props = CompositeScreenProps<
  BottomTabScreenProps<CloudDocNavigatorParams, 'CloudDocSubFolder'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const CloudDocSubFolder: React.FC<Props> = ({ navigation, route }) => {
  const id = route.params.id;
  const folderName = route.params.name;
  const category = route.params.category as Gql.CategoryType;
  const role = route.params.role;
  const driveType = route.params.driveType as Gql.ProjectDocumentDriveType;
  const cloudDocAddModalRef = useRef<any>(null);
  const pageIndex = Number(_.get(route, 'params.pageIndex', '0'));
  const linkId = getLinkId(id);
  const [onAction, setOnAction] = useState(false);
  const [filteredValue, setFilteredValue] = useState<string | null>(null);
  // const [search, setSearch] = useState<any>(false);
  const [loading, setLoading] = useState(false);
  const cloudDocThreeOptionModal = useRef<any>(null);
  const [selectedDocument, setSelectedDocument] = useState<any>({});
  const project = {
    ...useSelector(state => state.project),
    companyName: useSelector(state => state.auth).company?.name
  };
  const { canCreate, canDelete, canEdit } = manageFiles(project.projectUserRole ?? '', id ?? '', driveType ?? '');
  const { OFFLINE_MODE } = useSelector(state => state.app);
  const { debouncedValue, setInputValue } = useDebouncedSearch();

  const [downloadedMap, setDownloadedMap] = useState(new Map<string, boolean>());

  const {
    data: getProjectDocuments,
    fetchNextPage,
    hasNextPage,
    isLoading,
    isRefetching
  } = useGetProjectDocuments(debouncedValue ?? '', {
    category: category,
    driveType: driveType,
    projectId: project.projectId,
    limit: 10,
    pageIndex: pageIndex,
    ...linkId
  });

  const projectDocuments = useMemo(() => {
    if (!getProjectDocuments) return [];
    return getProjectDocuments.pages.map(page => page.results).flat();
  }, [getProjectDocuments]);

  const onSearchInputChange = (newSearchTerm: any) => {
    setFilteredValue(newSearchTerm);
    setInputValue(newSearchTerm); // This should only update the search term
  };

  const loadMoreDocuments = useCallback(() => {
    if (hasNextPage && !isLoading) {
      fetchNextPage();
    }
  }, [hasNextPage, isLoading, fetchNextPage]);

  const onItemPress = (item: ProjectDocumentModel) => {
    if (item.type === 'folder') {
      navigation.push('CloudDocsNav', {
        screen: 'CloudDocSubFolder',
        params: {
          id: item.remoteId ? (item.remoteId as any) : item.id,
          name: item.name ?? '',
          category: item.category ?? '',
          refetch: () => {},
          role: role ?? '',
          driveType: driveType
        }
      });
    } else if (item.type === 'pdf') {
      navigation.navigate('PdftronNav', {
        screen: 'Pdftron',
        params: {
          // id: item.remoteId ? (item.remoteId as any) : item.id,
          // modules: 'CloudDocs',
          // role: project.projectUserRole as string,
          // assigneeIds: null,
          // fileUrl: item?.localFileUrl ? `${item.localFileUrl}` : undefined
          id: item.remoteId as any,
          role: role ?? '',
          //TODO: do it later
          // revisionData: item.drawingRevisions,
          modules: 'CloudDocs',
          ...(item.localFileUrl && { fileUrl: item?.localFileUrl as any }),
          ...(!item.remoteId && { fileUrl: item?.fileUrl as any }),
          addedBy: item.addedBy as number
        }
      });
    } else if (item.type === 'docx' || item.type === 'pptx' || item.type === 'xlsx') {
      openDocsViewer(item as any);
    } else if (['pdf', 'jpg', 'jpeg', 'png', 'svg', 'dwg'].includes(item?.type?.toLowerCase?.()) === false) {
      onDownload(item.id);
    }
  };

  const onOptionPress = (item: ProjectDocumentModel) => {
    cloudDocThreeOptionModal?.current?.pushModal();
    setSelectedDocument({ document: item });
  };

  // useEffect(() => {
  //   refetchDocuments(debouncedValue);
  // }, [debouncedValue, driveType]);

  // Download file except pdf & dwg
  const onDownload = async (id: string) => {
    //   const file = projectDocuments.find((node: any) => node.id === id);
    //   if (!file) return;
    //   try {
    //     const dir = `${RNFetchBlob.fs.dirs.DCIMDir}/${file?.name}`;
    //     await RNFetchBlob.config({
    //       fileCache: true,
    //       addAndroidDownloads: {
    //         path: dir,
    //         mediaScannable: true,
    //         useDownloadManager: true,
    //         notification: true,
    //         title: file?.name,
    //         mime: file.type ?? ''
    //       }
    //     }).fetch('GET', file.fileUrl ?? '', {});
    //   } catch (e) {
    //     pushError(e);
    //   }
  };

  const keyExtractor = (item: any, index: number) => String(`${item.id}-${index}`);

  const handleLongPress = (title: string, type: 'folder' | 'pdf') => {
    return showFileName(type === 'folder' ? 'Folder' : 'File', title);
  };

  return (
    <Box height="full" bg="white" flex={1}>
      <AppBar goBack barStyle={'dark-content'} title={folderName} noRight onPressTitle={() => null} />

      {/* Add button */}
      <AddButton isAllowed={true} onPress={() => cloudDocAddModalRef?.current?.pushModal()} />

      {/* Search Input */}
      <SearchInput
        filteredValue={filteredValue}
        setFilteredValue={onSearchInputChange}
        placeholder="Search for documents"
        style={{ marginBottom: 10 }}
      />

      {isLoading ? (
        <SkeletonComponent />
      ) : (
        <DocumentList
          documents={projectDocuments}
          isLoading={isLoading}
          loadMoreDocuments={loadMoreDocuments}
          refetch={() => {}}
          onItemPress={onItemPress}
          onOptionPress={onOptionPress}
          onAction={onAction}
          setOnAction={setOnAction}
        />
      )}

      <CloudDocAddModal
        ref={cloudDocAddModalRef}
        category={category}
        projectDocumentId={id} // id from root folder
        refetch={() => {
          // refetch logic here
        }}
        driveType={driveType}
        root={false}
      />
      {/* <SearchCloudDocModal ref={searchCloudDocModalRef} category={category} /> */}
      <ThreeDotsOptionModal
        ref={cloudDocThreeOptionModal}
        selectedDocument={selectedDocument}
        refetch={() => null}
        onChange={() => null}
        canDelete={canDelete}
        canEdit={canEdit}
      />
    </Box>
  );
};
const SkeletonComponent: React.FC<any> = () => {
  const SkeletonItem = () => {
    return (
      <VStack mb={5}>
        <Skeleton style={{ height: 70, padding: 14, borderRadius: 12 }} />
        <VStack space={1}></VStack>
      </VStack>
    );
  };
  return (
    <Stack p={5}>
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
    </Stack>
  );
};

export default CloudDocSubFolder;
