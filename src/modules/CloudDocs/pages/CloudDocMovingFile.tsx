import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ScrollableTabBar } from '@src/commons';
import CustomAppBar from '@src/commons/CustomAppBar';
import { COLORS } from '@src/constants';
import { CloudDocNavigatorParams, RootNavigatorParams } from '@src/types';
import _ from 'lodash';
import { Box } from 'native-base';
import React, { useRef } from 'react';
import { StyleSheet } from 'react-native';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import Correspondence from '../components/MovingFile/CorrespondenceComponent';
import ProjectDocuments from '../components/MovingFile/ProjectDocumentsComponent';
import WorkProgramme from '../components/MovingFile/WorkProgrammeComponent';

type Props = CompositeScreenProps<
  BottomTabScreenProps<CloudDocNavigatorParams, 'CloudDocMovingFile'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const CloudDocMovingFile: React.FC<Props> = ({ navigation, route }) => {
  const tabBarRef = useRef<any>(null);
  const pageIndex = Number(_.get(route, 'params.pageIndex', '0'));
  const selectedDocumentId = route?.params?.selectedDocumentId;

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <CustomAppBar header barStyle={'light-content'} />

      <ScrollableTabView
        key={pageIndex}
        initialPage={pageIndex}
        renderTabBar={() => (
          <ScrollableTabBar
            activeTextColor="#0695D7"
            inactiveTextColor={COLORS.neutrals.gray70}
            containerStyle={styles.tabContainer}
            ref={tabBarRef}
          />
        )}
      >
        <ProjectDocuments tabLabel="Project Document" selectedDocumentId={selectedDocumentId} />
        <WorkProgramme tabLabel="Work Programme" selectedDocumentId={selectedDocumentId} />
        <Correspondence tabLabel="Correspondence" selectedDocumentId={selectedDocumentId} />
      </ScrollableTabView>
    </Box>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    backgroundColor: '#FFFFFF'
  }
});

export default CloudDocMovingFile;
