import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
// import { ScrollableTabBar } from '@src/commons';
import CustomAppBar from '@src/commons/CustomAppBar';
import { COLORS } from '@src/constants';
import { useSelector } from '@src/store';
import { CloudDocNavigatorParams, RootNavigatorParams } from '@src/types';
import _ from 'lodash';
import { Box } from 'native-base';
import React, { useRef } from 'react';
import { Dimensions, StyleSheet } from 'react-native';
import ScrollableTabView, { DefaultTabBar, ScrollableTabBar } from 'react-native-scrollable-tab-view';
import Correspondence from '../components/Correspondence';
import ProjectDocuments from '../components/ProjectDocuments';
import WorkProgramme from '../components/WorkProgramme';

type Props = CompositeScreenProps<
  BottomTabScreenProps<CloudDocNavigatorParams, 'CloudDoc'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const CloudDoc: React.FC<Props> = ({ navigation, route }) => {
  const tabBarRef = useRef<any>(null);
  const pageIndex = Number(_.get(route, 'params.pageIndex', '0'));
  const WINDOW_WIDTH = Dimensions.get('window').width;
  const { OFFLINE_MODE } = useSelector(state => state.app);
  // get the device width

  const companySubscriptions = Gql.useCompanySubscriptionsQuery({
    variables: {
      sorting: {
        direction: Gql.SortDirection.Desc,
        field: Gql.CompanySubscriptionSortFields.SubscriptionEndDate
      },
      paging: {
        limit: 1
      }
    },
    fetchPolicy: 'cache-and-network'
  });

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <CustomAppBar header barStyle={'light-content'} />
      {/* //! store company subscription */}
      {/* {companySubscriptions && !OFFLINE_MODE &&
        <ScrollableTabView
          key={pageIndex}
          initialPage={0}
          style={{ backgroundColor: '#FFFFFF' }}
          tabBarInactiveTextColor="#000"
          tabBarActiveTextColor="#0695D7"
          tabBarUnderlineStyle={{ backgroundColor: '#0695D7', height: 2 }}
          scrollWithoutAnimation={true}
          locked={true}
          renderTabBar={() => <DefaultTabBar style={{ height: 40, marginTop: 10 }} tabStyle={{ height: 40 }} />}
        >
          {isAllowed(companySubscriptions, ProjectAccess.PROJECT_DOCUMENT) && <ProjectDocuments tabLabel="Project Document" />}
          {isAllowed(companySubscriptions, ProjectAccess.WORK_PROGRAMME) && <WorkProgramme tabLabel="Work Programme" />}
          {isAllowed(companySubscriptions, ProjectAccess.CORRESPONDENCE) && <Correspondence tabLabel="Correspondence" />}
        </ScrollableTabView>
      } */}

      <ScrollableTabView
        key={pageIndex}
        initialPage={0}
        style={{ backgroundColor: '#FFFFFF' }}
        tabBarInactiveTextColor="#000"
        tabBarActiveTextColor="#0695D7"
        tabBarUnderlineStyle={{ backgroundColor: '#0695D7', height: 2 }}
        scrollWithoutAnimation={true}
        locked={true}
        renderTabBar={() => (
          <ScrollableTabBar style={{ height: 45 }} tabStyle={{ width: WINDOW_WIDTH / 2, height: 40 }} />
        )}
      >
        <ProjectDocuments tabLabel="Project Document" />
        <WorkProgramme tabLabel="Work Programme" />
        <Correspondence tabLabel="Correspondence" />
      </ScrollableTabView>
    </Box>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    backgroundColor: '#FFFFFF'
  }
});

export default CloudDoc;
