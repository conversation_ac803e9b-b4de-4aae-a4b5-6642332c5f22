import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { AppBar, Icon, Image } from '@src/commons';
import { COLORS } from '@src/constants';
import { CloudDocNavigatorParams, RootNavigatorParams } from '@src/types';
import { Box, Button, View } from 'native-base';
import React, { useEffect, useRef } from 'react';
import { InteractionManager, StyleSheet } from 'react-native';
import ImageThreeDotsModal from '../components/ImageThreeDotsModal';

type Props = CompositeScreenProps<
  BottomTabScreenProps<CloudDocNavigatorParams, 'CloudDocImageViewer'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const CloudDocImageViewer: React.FC<Props> = ({ route }) => {
  const id = route.params.id;
  const imageThreeDotsModalRef = useRef<any>(null);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getDoc();
    });
  }, []);

  const [getDoc, { data }] = Gql.useProjectDocumentLazyQuery({ variables: { id: id } });
  const uri = data?.projectDocument?.fileUrl ?? '';
  const fileName = data?.projectDocument?.name;

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <AppBar
        goBack
        barStyle={'dark-content'}
        title={fileName}
        rightComponent={
          <>
            <Button
              _pressed={{
                bg: 'transparent',
                opacity: 0.8
              }}
              bg="transparent"
              p={0}
              justifyContent="flex-start"
              onPress={() => imageThreeDotsModalRef?.current.pushModal()}
            >
              <Icon name="three-dots" />
            </Button>
          </>
        }
      />
      <View style={styles.container}>
        <Image width="100%" height={500} uri={uri} mode="contain" />
      </View>
      <ImageThreeDotsModal ref={imageThreeDotsModalRef} id={id} />
    </Box>
  );
};
const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center'
  }
});
export default CloudDocImageViewer;
