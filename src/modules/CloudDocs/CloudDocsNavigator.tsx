import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { CloudDocNavigatorParams } from '@src/types';
import React from 'react';
import CloudDocMovingFile from './pages/CloudDocMovingFile';
import CloudDoc from './pages/CloudDoc';
import CloudDocFolder from './pages/CloudDocFolder';
import CloudDocImageViewer from './pages/CloudDocImageViewer';
import CloudDocSubFolder from './pages/CloudDocSubFolder';
// import CloudDocWebView from './pages/CloudDocWebView';
// import Pdftron from '../Pdftron/pages/Pdftron';

const CloudDocStack = createNativeStackNavigator<CloudDocNavigatorParams>();

const CloudDocNavigator: React.FC<any> = () => (
  <CloudDocStack.Navigator initialRouteName="CloudDoc" screenOptions={{ headerShown: false }}>
    <CloudDocStack.Screen name="CloudDoc" component={CloudDoc} />
    <CloudDocStack.Screen name="CloudDocSubFolder" component={CloudDocSubFolder} />
    <CloudDocStack.Screen name="CloudDocFolder" component={CloudDocFolder} />
    <CloudDocStack.Screen name="CloudDocImageViewer" component={CloudDocImageViewer} />
    {/* <CloudDocStack.Screen name="CloudDocWebView" component={CloudDocWebView} /> */}
    <CloudDocStack.Screen name="CloudDocMovingFile" component={CloudDocMovingFile} />
  </CloudDocStack.Navigator>
);

export default CloudDocNavigator;
