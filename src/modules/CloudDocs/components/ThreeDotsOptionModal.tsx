import { Modal } from '@commons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootNavigatorParams } from '@src/types';
import { Button, Center, Text, VStack } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { Alert, ModalProps, StyleSheet } from 'react-native';
import RenameFolderFile from './RenameFolderFile';
import { Gql } from '@src/api';
import { getDeleteConfirmationMessage, getDeleteConfirmationTitle, pushError, pushSuccess } from '@src/configs';
import { useSelector } from '@src/store';
import useDeleteCloudDocs from '@src/mutation/cloud-docs/useDeleteCloudDocs';

interface Props {
  category?: string;
  refetch?: () => void;
  selectedDocument?: any;
  onChange?: (values: string) => void;
  canDelete?: boolean;
  canEdit?: boolean;
  canShare?: boolean;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const ThreeDotsOptionModal = forwardRef<ModalRef, Props>((props, ref) => {
  const { OFFLINE_MODE } = useSelector(state => state.app);
  const modalRef = useRef<any>(null);
  const renameModalRef = useRef<any>(null);
  const document = props?.selectedDocument?.document;
  const { mutateAsync: deleteCloudDocs } = useDeleteCloudDocs();
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const [deleteFolder] = Gql.useDeleteProjectDocumentsMutation({
    onCompleted: () => {
      props.refetch?.();
      pushSuccess('Folder/File deleted successfully');
    },
    onError: (err: any) => {
      pushError(err);
    }
  });

  const showAlert = () =>
    Alert.alert(
      'Share From Web',
      'For the best experience and accuracy of sharing with members, please share from the web platform.',
      [
        {
          text: 'Dismiss',
          // onPress: () => Alert.alert('Cancel Pressed'),
          style: 'cancel'
        }
      ],
      {
        cancelable: true
        // onDismiss: () =>
        //   Alert.alert(
        //     'This alert was dismissed by tapping outside of the alert dialog.',
        //   ),
      }
    );

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="bottom">
      <VStack>
        <Center>
          {/* <Button
          width={"100%"}
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            my={2}
            bg="transparent"
            onPress={() => {
              props?.onChange?.(props.selectedDocumentId ?? '');
            }}
          >
            <Text>Download </Text>
          </Button> */}
          {(document?.category == Gql.CategoryType.ProjectDocument ||
            document?.category == Gql.CategoryType.Correspondence) &&
            props?.canShare &&
            !OFFLINE_MODE && (
              <Button
                _pressed={{
                  bg: 'transparent',
                  opacity: 0.8
                }}
                my={2}
                // mb={4}
                bg="transparent"
                onPress={() => {
                  showAlert();
                }}
              >
                <Text>Share </Text>
              </Button>
            )}

          {/* <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            my={2}
            // mb={4}
            bg="transparent"
            onPress={() => {
              navigation.navigate('CloudDocsNav', {
                screen: 'CloudDocMovingFile',
                params: {
                  selectedDocumentId: props?.selectedDocumentId ?? ''
                }
              });
            }}
          >
            <Text>Move </Text>
          </Button> */}
          {props?.canEdit && !OFFLINE_MODE && (
            <Button
              width={'100%'}
              _pressed={{
                bg: 'transparent',
                opacity: 0.8
              }}
              my={2}
              // mb={4}
              bg="transparent"
              onPress={() => {
                renameModalRef.current.pushModal();
              }}
            >
              <Text>Rename </Text>
            </Button>
          )}

          {(props?.canDelete || OFFLINE_MODE) && (
            <Button
              width={'100%'}
              _pressed={{
                bg: 'transparent',
                opacity: 0.8
              }}
              my={2}
              bg="transparent"
              onPress={async () => {
                Alert.alert(
                  // 'Delete Folder/File',
                  getDeleteConfirmationTitle(OFFLINE_MODE),
                  // 'Are you sure you want to delete this?',
                  getDeleteConfirmationMessage(OFFLINE_MODE),
                  [
                    {
                      text: 'Cancel',
                      onPress: () => {},
                      style: 'cancel'
                    },
                    {
                      text: 'OK',
                      onPress: async () => {
                        if (!document) {
                          return pushError('No document selected');
                        }

                        await deleteCloudDocs(document.id).then(() => {
                          pushSuccess('Folder/File deleted successfully');
                        });
                        // if (OFFLINE_MODE) {
                        //   await deleteOfflineDocument({ id: props?.selectedDocumentId });
                        // } else {
                        //   await deleteFolder({
                        //     variables: {
                        //       input: {
                        //         ids: [parseInt(props?.selectedDocumentId)]
                        //       }
                        //     }
                        //   });
                        // }

                        modalRef.current?.closeModal();
                      }
                    }
                  ],
                  { cancelable: false }
                );
              }}
            >
              <Text color="semantics.danger">Delete </Text>
            </Button>
          )}
        </Center>
      </VStack>
      <RenameFolderFile
        ref={renameModalRef}
        refetch={() => {
          props?.refetch?.();
          modalRef.current.closeModal();
        }}
        id={document?.id}
        name={document?.name}
      />
    </Modal>
  );
});

ThreeDotsOptionModal.displayName = 'ThreeDotsOptionModal';
export default ThreeDotsOptionModal;
