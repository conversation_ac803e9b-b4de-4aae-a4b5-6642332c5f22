import { Modal } from '@commons';
import { useNavigation } from '@react-navigation/native';
import { COLORS } from '@src/constants';
import { Button, Text } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { ModalProps, StyleSheet } from 'react-native';

interface Props {
  id: string;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const ImageThreeDotsModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const navigation = useNavigation();

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  return (
    <Modal ref={modalRef}>
      <Button
        _pressed={{
          bg: 'transparent',
          opacity: 0.8
        }}
        bg="transparent"
        onPress={() => {}}
        mb={4}
      >
        <Text>Download</Text>
      </Button>

      <Button
        _pressed={{
          bg: 'transparent',
          opacity: 0.8
        }}
        bg="transparent"
        mb={4}
        onPress={() => {}}
      >
        <Text>Share this file</Text>
      </Button>

      <Button
        _pressed={{
          bg: 'transparent',
          opacity: 0.8
        }}
        mb={4}
        bg="transparent"
        onPress={() => {}}
      >
        <Text color={COLORS.semantics.danger}>Delete forever</Text>
      </Button>
    </Modal>
  );
});

const styles = StyleSheet.create({});

export default ImageThreeDotsModal;
