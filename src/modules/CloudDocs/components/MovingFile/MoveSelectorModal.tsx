import { Modal } from '@commons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Gql } from '@src/api';
import { pushError, pushSuccess } from '@src/configs';
import apolloClient from '@src/lib/apollo';
import { RootNavigatorParams } from '@src/types';
import { Box, Button, Center, Text } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { ModalProps } from 'react-native';

interface Props {
  projectDocument: string;
  selectedDocumentId: string;
  refetch: () => void;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const MoveSelectorModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);

  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  // Move file function
  const onMove = async () => {
    try {
      await apolloClient.mutate<Gql.UpdateOneProjectDocumentMutation, Gql.UpdateOneProjectDocumentMutationVariables>({
        mutation: Gql.UpdateOneProjectDocumentDocument,
        variables: {
          input: {
            id: props?.selectedDocumentId ?? '',
            update: {
              projectDocumentId: props?.projectDocument
            }
          }
        }
      });
      props?.refetch?.();
      pushSuccess('Moved file successfully');
      navigation.goBack();
    } catch (e: any) {
      pushError(e.message);
    }
  };

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="bottom">
      <Center mb={10}>
        <Box flexDirection="row">
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            mt={2}
            mb={4}
            mr={20}
            bg="transparent"
            onPress={() => {
              modalRef?.current?.closeModal();
            }}
          >
            <Text>Cancel</Text>
          </Button>

          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            mt={2}
            mb={4}
            bg="transparent"
            onPress={() => {
              onMove();
            }}
          >
            <Text>Move</Text>
          </Button>
        </Box>
      </Center>
    </Modal>
  );
});

MoveSelectorModal.displayName = 'MoveSelectorModal';
export default MoveSelectorModal;
