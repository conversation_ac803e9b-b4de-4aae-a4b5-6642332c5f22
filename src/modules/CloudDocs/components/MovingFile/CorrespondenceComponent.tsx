import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Gql } from '@src/api';
import { Icon } from '@src/commons';
import { getFileIcon } from '@src/commons/FileIcon';
import UploadFileModal from '@src/commons/UploadFileModal';
import { pushError } from '@src/configs';
import { COLORS } from '@src/constants';
import { useSelector } from '@src/store';
import { RootNavigatorParams } from '@src/types';
import _ from 'lodash';
import moment from 'moment';
import { Box, Button, Center, Divider, FlatList, Flex, HStack, Input, Pressable, Text, VStack } from 'native-base';
import React, { useEffect, useRef, useState } from 'react';
import { InteractionManager, StyleSheet, TouchableOpacity } from 'react-native';
import RNFetchBlob from 'rn-fetch-blob';
import CloudDocAddModal from '../CloudDocAddModal';
import ThreeDotsOptionModal from '../ThreeDotsOptionModal';

const Correspondence: React.FC<any> = props => {
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const uploadFileModalRef = useRef<any>(null);
  const project = useSelector(state => state.project);
  const threeOptionModalRef = useRef<any>(null);
  const addModalRef = useRef<any>(null);
  const [filteredValue, setFilteredValue] = useState('');

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getProjectDocs();
    });
    getProjectDocs();
  }, []);

  const [getProjectDocs, { data, refetch, loading, fetchMore }] = Gql.useGetProjectDocumentsLazyQuery({
    variables: {
      filter: {
        projectId: { eq: project.projectId ?? '' },
        category: { eq: Gql.CategoryType.Correspondence },
        projectDocumentId: { eq: null },
        fileSystemType: { eq: Gql.FileSystemType.Folder },
        name: { like: `%${filteredValue}%` }
      },
      paging: {
        limit: 20,
        offset: 0
      }
    },
    fetchPolicy: 'cache-and-network'
  });

  const projectDocs = data?.getProjectDocuments?.nodes.map((doc: any) => {
    return {
      id: doc.id,
      name: doc.name,
      lastUpdate: moment(doc.updatedAt).format('DD MMMM YYYY'),
      fileSize: doc.fileSize,
      fileType: doc.type,
      category: doc.category
    };
  });
  if (!projectDocs) return null;

  const onLoadedMore = () => {
    if (!data?.getProjectDocuments.pageInfo.hasNextPage) return;
    fetchMore({
      variables: {
        paging: {
          offset: data?.getProjectDocuments.nodes.length,
          limit: 20
        }
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;
        const result = Object.assign({}, prev, {
          tasks: {
            ...fetchMoreResult.getProjectDocuments,
            nodes: [...prev.getProjectDocuments.nodes, ...fetchMoreResult.getProjectDocuments.nodes]
          }
        });
        return result;
      }
    });
  };

  // Download file except pdf & dwg
  const onDownload = async (id: string) => {
    const file = _.find(data?.getProjectDocuments.nodes, data => data.id === id);
    if (!file) return;
    try {
      const dir = `${RNFetchBlob.fs.dirs.DCIMDir}/${file?.name}`;
      await RNFetchBlob.config({
        fileCache: true,
        addAndroidDownloads: {
          path: dir,
          mediaScannable: true,
          useDownloadManager: true,
          notification: true,
          title: file?.name,
          mime: file.type ?? ''
        }
      }).fetch('GET', file.fileUrl ?? '', {});
    } catch (e) {
      pushError(e);
    }
  };

  return (
    <Box flex={1} bg="white" height="full">
      <Divider />

      <Box>
        {/* Search Input */}
        <Center>
          <Input
            bg="#F5F7FB"
            mt="4"
            width="335px"
            borderRadius="4"
            fontSize="14"
            InputLeftElement={
              <Box pl="6" py="6">
                <Icon name="search" />
              </Box>
            }
            InputRightElement={
              <Pressable pr="2" py="6" onPress={() => setFilteredValue('')}>
                <Icon name="cancel" />
              </Pressable>
            }
            value={filteredValue}
            onChangeText={value => {
              setFilteredValue(value);
            }}
          />
        </Center>

        {/* Selection */}
        <Flex
          direction="row"
          width="full"
          height="48px"
          bg="white"
          alignItems="center"
          justifyContent="flex-end"
          px={4}
        >
          <HStack space={3}>
            <Button
              variant="primary"
              height={36}
              onPress={() => {
                addModalRef?.current?.pushModal();
              }}
            >
              <HStack space={2}>
                <Text fontSize={14} fontWeight={600} color="white">
                  Add
                </Text>
                <Icon name="plus" />
              </HStack>
            </Button>
          </HStack>
        </Flex>
        <Divider mt={2} />
      </Box>

      <FlatList
        keyboardShouldPersistTaps="handled"
        contentContainerStyle={{ paddingHorizontal: 20, paddingTop: 20 }}
        data={projectDocs}
        keyExtractor={item => item.id}
        onEndReachedThreshold={0.3}
        onEndReached={onLoadedMore}
        renderItem={({ item }: any) => (
          <Box flexDirection="row" alignItems="center">
            <TouchableOpacity>
              <Box style={styles.box}>
                <HStack alignItems="center" mb={2}>
                  <Box width="15%">{getFileIcon(item.fileType)}</Box>
                  <VStack width="85%">
                    <Text fontWeight={600} numberOfLines={1} ellipsizeMode="tail">
                      {item.name}
                    </Text>

                    <HStack space={2} alignItems="center">
                      <Text color={COLORS.neutrals.gray90}>{item.lastUpdate}</Text>
                      <Text fontSize="2xs" color={COLORS.neutrals.gray90}>
                        {'\u2022'}
                      </Text>
                      <Text color={COLORS.neutrals.gray90}>{item.fileSize} MB</Text>
                    </HStack>
                  </VStack>
                </HStack>
                <Divider mt={2} mx={10} />
              </Box>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => {
                threeOptionModalRef?.current?.pushModal();
              }}
            >
              <Box width={50}>
                <Icon name="option-dot" />
              </Box>
            </TouchableOpacity>
          </Box>
        )}
      />

      <UploadFileModal
        ref={uploadFileModalRef}
        category={Gql.CategoryType.ProjectDocument}
        onSaved={() => {
          refetch();
        }}
      />

      <CloudDocAddModal ref={addModalRef} category={Gql.CategoryType.ProjectDocument} refetch={() => refetch()} />
      <ThreeDotsOptionModal ref={threeOptionModalRef} selectedDocumentId={''} />
    </Box>
  );
};
const styles = StyleSheet.create({
  box: {
    backgroundColor: '#FFFFFF',
    padding: 14,
    borderRadius: 12,
    overflow: 'hidden'
  }
});

export default Correspondence;
