import { Modal } from '@commons';
import { pushError, pushSuccess } from '@src/configs';
import { useUpdateCloudDocs } from '@src/mutation/cloud-docs/useUpdateCloudDocs';
import _ from 'lodash';
import { Box, Button, Input, Text, VStack } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps } from 'react-native';

interface Props {
  refetch?: () => void;
  name: any;
  id: any;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const RenameFolderFile = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const [value, setValue] = useState(props?.name ?? '');
  const { mutate: editName, isPending } = useUpdateCloudDocs();

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const updateFolderFileName = async (values: any) => {
    if (values === '') {
      pushError('Please enter name');
      return;
    }

    try {
      editName({
        id: props?.id,
        name: values
      });
      pushSuccess('Folder/file name updated successfully');
      props?.refetch?.();
      modalRef.current.closeModal();
    } catch (error) {
      pushError(error);
    }
  };

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="middle">
      <Box p={6}>
        <Text textAlign="center" mb={4} fontSize={16}>
          Update new name
        </Text>

        <VStack>
          <Text>Enter name</Text>
          <Input
            onChangeText={value => {
              setValue(value);
            }}
            isDisabled={isPending}
            isRequired={true}
            defaultValue={props?.name}
          />
        </VStack>

        <Box flexDirection="row" justifyContent="flex-end" mt={5}>
          <Button
            bg="transparent"
            _pressed={{ bg: 'transparent' }}
            onPress={() => {
              modalRef.current.closeModal();
            }}
          >
            <Text color="#0695D7">Cancel</Text>
          </Button>
          <Button
            spinnerPlacement="end"
            isLoadingText="Renaming"
            _loading={{
              _text: {
                color: 'black'
              }
            }}
            _spinner={{
              color: 'black'
            }}
            disabled={isPending}
            isLoading={isPending}
            bg="transparent"
            _pressed={{ bg: 'transparent' }}
            onPress={() => {
              updateFolderFileName(value);
            }}
          >
            <Text color="black">Rename</Text>
          </Button>
        </Box>
      </Box>
    </Modal>
  );
});

RenameFolderFile.displayName = 'RenameFolderFile';
export default RenameFolderFile;
