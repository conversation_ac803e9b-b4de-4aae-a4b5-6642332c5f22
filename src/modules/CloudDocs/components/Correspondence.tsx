import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import UploadFileModal from '@src/commons/UploadFileModal';
import { showFileName } from '@src/configs';
import { useSelector } from '@src/store';
import { RootNavigatorParams } from '@src/types';
import { Box, Divider, HStack, Skeleton, Stack, VStack } from 'native-base';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import CloudDocAddModal from './CloudDocAddModal';
import ThreeDotsOptionModal from './ThreeDotsOptionModal';
import { manageFiles, task } from '@src/lib/authority';
import useDebouncedSearch from '@src/hooks/useDebounceSearch';
import { useGetProjectDocuments } from '@src/queries/cloud-docs/useGetCloudDocs';
import ProjectDocumentModel from '@src/database/model/project-document';
import AddButton from '@src/commons/AddButton';
import SearchInput from '@src/commons/SearchInput';
import DocumentList from '@src/commons/list/DocumentList';
import DriveTypeSelect from '@src/commons/DriveTypeSelect';
import { handleDocumentNavigation } from '@src/utils/documentNavigation';

interface Props {
  tabLabel: string;
  selectedDocumentId?: string;
}

const Correspondence: React.FC<Props> = () => {
  const navigation = useNavigation<NativeStackNavigationProp<RootNavigatorParams>>();
  const uploadFileModalRef = useRef<any>(null);
  const project = {
    ...useSelector(state => state.project),
    companyName: useSelector(state => state.auth).company?.name
  };
  const cloudDocAddModalRef = useRef<any>(null);
  const threeOptionModalRef = useRef<any>(null);
  const [filteredValue, setFilteredValue] = useState<string | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<any>({});
  const [driveType, setDriveType] = useState<Gql.ProjectDocumentDriveType>(Gql.ProjectDocumentDriveType.Shared);
  const { canDelete, canEdit, canShare } = manageFiles(project.projectUserRole ?? '', '', driveType);
  const [downloadedMap, setDownloadedMap] = useState(new Map<string, boolean>());
  const role = project?.projectUserRole;
  const projectDocs = task(role ?? '');

  const { debouncedValue, setInputValue } = useDebouncedSearch();

  const {
    data: projectDocument,
    fetchNextPage,
    hasNextPage,
    isLoading
  } = useGetProjectDocuments(debouncedValue ?? '', {
    category: Gql.CategoryType.Correspondence,
    projectId: project.projectId,
    driveType: driveType,
    limit: 10,
    projectDocumentId: null as any,
    pageIndex: 1
  });

  const projectDocuments = useMemo(() => {
    if (!projectDocument) return [];
    return projectDocument.pages.map(page => page.results).flat();
  }, [projectDocument]);

  const onDriveTypeChange = (newDriveType: any) => {
    setDriveType(newDriveType);
  };

  const onSearchInputChange = (newSearchTerm: any) => {
    setFilteredValue(newSearchTerm);
    setInputValue(newSearchTerm);
  };

  const loadMoreDocuments = useCallback(() => {
    if (hasNextPage && !isLoading) {
      fetchNextPage();
    }
  }, [hasNextPage, isLoading, fetchNextPage]);

  const onItemPress = async (item: ProjectDocumentModel) => {
    await handleDocumentNavigation({
      item,
      role,
      navigation,
      modules: 'CloudDocs',
      driveType,
      onRefetch: () => {}
    });
  };

  const onOptionPress = (item: ProjectDocumentModel) => {
    threeOptionModalRef?.current?.pushModal();
    setSelectedDocument({ document: item });
  };

  return (
    <Box flex={1} bg="white" height="full">
      <Divider />
      {/* Add Button */}
      <AddButton
        isAllowed={projectDocs.overview.canCreateRoot}
        onPress={() => cloudDocAddModalRef?.current?.pushModal()}
      />

      <HStack mb={2}>
        {/* Search Input */}
        <SearchInput
          filteredValue={filteredValue}
          setFilteredValue={onSearchInputChange}
          placeholder="Search for documents"
          style={{ width: '65%' }}
        />

        {/* Drive Type */}
        <DriveTypeSelect
          driveType={driveType}
          onDriveTypeChange={onDriveTypeChange}
          projectUserRole={project.projectUserRole ?? ''}
          gotPersonal={false}
          Gql={Gql}
        />
      </HStack>

      {isLoading ? (
        <>
          <SkeletonComponent />
        </>
      ) : (
        <DocumentList
          documents={projectDocuments}
          isLoading={isLoading}
          loadMoreDocuments={loadMoreDocuments}
          refetch={() => {}}
          onItemPress={onItemPress}
          onOptionPress={onOptionPress}
          onAction={false}
          setOnAction={() => {}}
        />
      )}

      <UploadFileModal
        ref={uploadFileModalRef}
        category={Gql.CategoryType.Correspondence}
        onSaved={() => {}}
        driveType={driveType}
        onLoading={() => null}
      />

      <CloudDocAddModal
        ref={cloudDocAddModalRef}
        category={Gql.CategoryType.Correspondence}
        refetch={() => null}
        driveType={driveType}
        root={true}
      />
      <ThreeDotsOptionModal
        ref={threeOptionModalRef}
        selectedDocument={selectedDocument}
        refetch={async () => {
          threeOptionModalRef?.current?.pushModal();
        }}
        onChange={() => {}}
        canDelete={canDelete}
        canEdit={canEdit}
        canShare={canShare}
      />
    </Box>
  );
};

const SkeletonComponent: React.FC<any> = () => {
  const SkeletonItem = () => {
    return (
      <VStack mb={5}>
        <Skeleton style={{ height: 70, padding: 14, borderRadius: 12 }} />
        <VStack space={1}></VStack>
      </VStack>
    );
  };
  return (
    <Stack p={5}>
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
    </Stack>
  );
};

export default Correspondence;
