import { Icon, Modal } from '@commons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Gql } from '@src/api';
import UploadFileModal from '@src/commons/UploadFileModal';
import { RootNavigatorParams } from '@src/types';
import { Box, Button, Center, HStack, Spinner, Text, VStack } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { ActivityIndicator, ModalProps, StyleSheet, TouchableOpacity } from 'react-native';

interface Props {
  projectDocumentId?: string;
  category: Gql.CategoryType;
  refetch: () => void;
  driveType?: Gql.ProjectDocumentDriveType;
  root?: boolean;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const CloudDocAddModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const uploadFileModalRef = useRef<any>(null);
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const driveType = props?.driveType;
  const category = props?.category;
  const dirId = props?.projectDocumentId;
  const [loading, setLoading] = React.useState(false);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
      if (props?.root) navigateToCreateModal();
    }
  }));

  const navigateToCreateModal = () => {
    navigation.navigate('DigitalFormNav', {
      screen: 'CreateFolder',
      params: {
        projectDocumentId: props.projectDocumentId, // use to add sub folder
        category: props.category,
        refetch: () => {
          props?.refetch?.();
        },
        driveType: props?.driveType
      }
    });
    modalRef?.current?.closeModal();
  };

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="bottom">
      <HStack justifyContent={'space-evenly'} px={8}>
        <Button
          width={'50%'}
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          my={6}
          bg="transparent"
          onPress={() => {
            navigateToCreateModal();
          }}
          justifyContent={'center'}
        >
          <Box
            alignItems={'center'}
            justifyContent={'center'}
            width={16}
            height={16}
            alignSelf={'center'}
            borderRadius={32}
            borderWidth={2}
            borderColor={'neutrals.gray6'}
          >
            <Icon name="folder-outline" />
          </Box>
          <Text color="#6F6D6D">Create Folder</Text>
        </Button>

        {loading ? (
          <ActivityIndicator size="large" />
        ) : (
          <Button
            width={'50%'}
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            my={6}
            bg="transparent"
            onPress={() => {
              uploadFileModalRef?.current?.pushModal();
            }}
            justifyContent={'center'}
          >
            <Box
              alignItems={'center'}
              justifyContent={'center'}
              width={16}
              height={16}
              alignSelf={'center'}
              borderRadius={32}
              borderWidth={2}
              borderColor={'neutrals.gray6'}
            >
              <Icon name="add-file" />
            </Box>
            <Text color="#6F6D6D">Upload Files</Text>
          </Button>
        )}
      </HStack>

      <UploadFileModal
        ref={uploadFileModalRef}
        category={category}
        projectDocumentId={dirId}
        onSaved={() => {
          props?.refetch?.();
          modalRef?.current?.closeModal();
        }}
        onLoading={loading => {
          setLoading(loading);
        }}
        driveType={driveType}
      />
    </Modal>
  );
});

const styles = StyleSheet.create({
  folder: {
    borderWidth: 2,
    borderColor: '#756D6D',
    borderRadius: 100,
    backgroundColor: 'transparent',
    height: 52,
    width: 52,
    justifyContent: 'center',
    alignItems: 'center'
  }
});

CloudDocAddModal.displayName = 'CloudDocAddModal';
export default CloudDocAddModal;
