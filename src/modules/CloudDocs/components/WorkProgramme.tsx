import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import UploadFileModal from '@src/commons/UploadFileModal';
import useDebouncedSearch from '@src/hooks/useDebounceSearch';
import { task } from '@src/lib/authority';
import { useSelector } from '@src/store';
import { RootNavigatorParams } from '@src/types';
import { Box, Divider, Skeleton, Stack, VStack } from 'native-base';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import CloudDocAddModal from './CloudDocAddModal';
import ThreeDotsOptionModal from './ThreeDotsOptionModal';
import { useGetProjectDocuments } from '@src/queries/cloud-docs/useGetCloudDocs';
import AddButton from '@src/commons/AddButton';
import SearchInput from '@src/commons/SearchInput';
import DocumentList from '@src/commons/list/DocumentList';
import { handleDocumentNavigation } from '@src/utils/documentNavigation';

interface Props {
  selectedDocumentId?: string;
  tabLabel?: string;
}

type NavigationType = NativeStackNavigationProp<RootNavigatorParams>;

const WorkProgramme: React.FC<Props> = props => {
  const navigation = useNavigation<NavigationType>();
  const uploadFileModalRef = useRef<any>(null);
  const project = {
    ...useSelector(state => state.project),
    companyName: useSelector(state => state.auth).company?.name
  };
  const cloudDocAddModalRef = useRef<any>(null);
  const threeOptionModalRef = useRef<any>(null);
  const [filteredValue, setFilteredValue] = useState<string | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<any>({});
  const role = project?.projectUserRole;
  const projectDocs = task(role ?? '');

  const { debouncedValue, setInputValue } = useDebouncedSearch();

  const {
    data: projectDocument,
    fetchNextPage,
    hasNextPage,
    isLoading
  } = useGetProjectDocuments(debouncedValue ?? '', {
    category: Gql.CategoryType.WorkProgramme,
    projectId: project.projectId,
    limit: 10,
    projectDocumentId: null as any,
    pageIndex: 1
  });

  const projectDocuments = useMemo(() => {
    if (!projectDocument) return [];
    return projectDocument.pages.map((page: any) => page.results).flat();
  }, [projectDocument]);

  const onSearchInputChange = (newSearchTerm: any) => {
    setFilteredValue(newSearchTerm);
    setInputValue(newSearchTerm);
  };

  const loadMoreDocuments = useCallback(() => {
    if (hasNextPage && !isLoading) {
      fetchNextPage();
    }
  }, [hasNextPage, isLoading, fetchNextPage]);

  const onOptionPress = (item: any) => {
    threeOptionModalRef?.current?.pushModal();
    setSelectedDocument({ document: item });
  };

  return (
    <Box flex={1} bg="white" height="full">
      <Divider />
      {/* Add Button */}
      <AddButton
        isAllowed={projectDocs.overview.canCreateRoot}
        onPress={() => cloudDocAddModalRef?.current?.pushModal()}
      />
      {/* Search Input */}
      <SearchInput
        filteredValue={filteredValue}
        setFilteredValue={onSearchInputChange}
        placeholder="Search for documents"
      />

      {/* List */}
      {isLoading ? (
        <>
          <SkeletonComponent />
        </>
      ) : (
        <DocumentList
          documents={projectDocuments}
          isLoading={isLoading}
          loadMoreDocuments={loadMoreDocuments}
          refetch={() => {}}
          onItemPress={async item => {
            await handleDocumentNavigation({
              item,
              role,
              navigation,
              modules: 'CloudDocs',
              onRefetch: () => {}
            });
          }}
          onOptionPress={onOptionPress}
          onAction={false}
          setOnAction={() => {}}
        />
      )}

      <UploadFileModal
        ref={uploadFileModalRef}
        category={Gql.CategoryType.WorkProgramme}
        onSaved={() => {}}
        onLoading={() => null}
      />
      <CloudDocAddModal ref={cloudDocAddModalRef} category={Gql.CategoryType.WorkProgramme} refetch={() => null} />
      <ThreeDotsOptionModal
        ref={threeOptionModalRef}
        selectedDocument={selectedDocument}
        refetch={async () => {
          threeOptionModalRef?.current?.pushModal();
        }}
        onChange={() => {}}
        canDelete={false}
        canEdit={false}
      />
    </Box>
  );
};

const SkeletonComponent: React.FC<any> = () => {
  const SkeletonItem = () => {
    return (
      <VStack mb={5}>
        <Skeleton style={{ height: 70, padding: 14, borderRadius: 12 }} />
        <VStack space={1}></VStack>
      </VStack>
    );
  };
  return (
    <Stack p={5}>
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
    </Stack>
  );
};

export default WorkProgramme;
