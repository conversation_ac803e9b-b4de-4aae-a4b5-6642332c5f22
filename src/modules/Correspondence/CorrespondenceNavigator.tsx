import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { CorrespondenceNavigatorParams } from '@types';
import React from 'react';
import Overview from './pages/Overview';
import EmailContent from './pages/EmailContent';

const CorrespondenceStack = createNativeStackNavigator<CorrespondenceNavigatorParams>();

const CorrespondenceNavigator: React.FC<any> = () => {
  return (
    <CorrespondenceStack.Navigator initialRouteName="Overview" screenOptions={{ headerShown: false }}>
      <CorrespondenceStack.Screen name="Overview" component={Overview} />
      <CorrespondenceStack.Screen name="Content" component={EmailContent} />
    </CorrespondenceStack.Navigator>
  );
};

export default CorrespondenceNavigator;
