import { CustomAppBar, Icon, ModalRef } from '@commons';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { pushError } from '@src/configs';
import { correspondenceActions } from '@src/slice/corrrespondence';
import { useDispatch, useSelector } from '@src/store';
import { CorrespondenceNavigatorParams } from '@src/types';
import he from 'he';
import _ from 'lodash';
import moment from 'moment';
import {
  Avatar,
  Badge,
  Box,
  Circle,
  FlatList,
  HStack,
  Input,
  Pressable,
  Skeleton as SkeletonComponent,
  Text,
  VStack
} from 'native-base';
import React, { useEffect, useState } from 'react';
import { InteractionManager, RefreshControl, StyleSheet, TouchableOpacity } from 'react-native';
import FilteringModal from '../components/FilteringModal';
type Props = NativeStackScreenProps<CorrespondenceNavigatorParams, 'Overview'>;

const Overview: React.FC<Props> = ({ navigation }) => {
  const project = useSelector(state => state.project);
  const user = useSelector(state => state.auth.user);
  const correspondence = useSelector(state => state.correspondence);
  const dispatch = useDispatch();
  const [filteredValue, setFilteredValue] = React.useState<string>('');
  const [filteredFocused, setFilteredFocused] = useState(false);
  const searchBtn = ['Sent by Me', 'Sent to Me'];
  const filteringModalRef = React.useRef<ModalRef>(null);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getEmail();
    });
  }, []);

  // Email Query
  const [getEmail, { data, loading, refetch, fetchMore }] = Gql.useGetEmailsLazyQuery({
    variables: {
      filter: {
        projectId: { eq: project.projectId },
        subject: { like: `%${filteredValue}%` },
        createdBy: correspondence.filter?.sentByMe ? { eq: Number(user?.id) } : {},
        receivers: correspondence.filter?.sentToMe ? { id: { eq: user?.id } } : {},
        deliveryStatus: correspondence.filter?.status
          ? {
              eq:
                correspondence.filter?.status === 'read'
                  ? Gql.EmailDeliveryStatus.Opened || Gql.EmailDeliveryStatus.Clicked
                  : correspondence.filter?.status === 'unread'
                    ? Gql.EmailDeliveryStatus.Delivered || Gql.EmailDeliveryStatus.Accepted
                    : undefined
            }
          : undefined,
        replyAt: correspondence.filter?.date ? { isNot: null } : undefined
      },
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.EmailSortFields.CreatedAt
        }
      ]
    },
    onError: pushError
  });

  const emails = data?.emails?.nodes ?? [];

  const onLoaded = () => {
    if (!data?.emails.pageInfo.hasNextPage) return;
    fetchMore({
      variables: {
        paging: {
          offset: data?.emails.nodes.length,
          limit: 20
        }
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;
        const result = Object.assign({}, prev, {
          emails: {
            ...fetchMoreResult.emails,
            nodes: [...prev.emails.nodes, ...fetchMoreResult.emails.nodes]
          }
        });
        return result;
      }
    });
  };

  return (
    <Box flex={1} bg="white">
      <CustomAppBar header barStyle={'light-content'} />
      <HStack space={2} my={2} alignItems={'center'} justifyItems={'center'} ml={2}>
        <Pressable onPress={() => navigation.getParent()?.navigate('Tab', { screen: 'More' })}>
          <Icon name="chevron-left" />
        </Pressable>
        <Text fontSize={'xl'}>Correspondence</Text>
      </HStack>
      <Input
        m={2}
        borderRadius="8"
        borderColor="#E6E6E6"
        fontSize="14"
        placeholder="Search by subject"
        InputLeftElement={
          <Box pl="2" py="6">
            <Icon name="search" />
          </Box>
        }
        InputRightElement={
          <Pressable px="4" py="6" onPress={() => setFilteredValue('')}>
            {filteredValue !== '' && <Icon name="cancel" />}
          </Pressable>
        }
        value={filteredValue}
        onChangeText={value => {
          setFilteredValue(value);
        }}
        onFocus={() => setFilteredFocused(true)}
        onBlur={() => setFilteredFocused(false)}
      />
      <HStack>
        <Box flex={1}>
          <HStack space={2} ml={2}>
            {_.map(searchBtn, (btn, index) => {
              const isActive =
                (btn === 'Sent by Me' && correspondence.filter?.sentByMe) ||
                (btn === 'Sent to Me' && correspondence.filter?.sentToMe);

              return (
                <Pressable
                  key={index}
                  onPress={() => {
                    if (btn === 'Sent by Me') {
                      dispatch(
                        correspondenceActions.setFilter({
                          ...correspondence?.filter,
                          sentByMe: !correspondence.filter?.sentByMe
                        })
                      );
                    } else {
                      dispatch(
                        correspondenceActions.setFilter({
                          ...correspondence?.filter,
                          sentToMe: !correspondence.filter?.sentToMe
                        })
                      );
                    }
                  }}
                  style={{
                    backgroundColor: isActive ? '#E9F3FB' : 'transparent',
                    paddingVertical: 8,
                    paddingHorizontal: 16,
                    borderRadius: 20,
                    borderWidth: 1,
                    borderColor: isActive ? '#E9F3FB' : '#E5E5E5'
                  }}
                >
                  <Text style={{ color: '#000' }}>{btn}</Text>
                </Pressable>
              );
            })}
          </HStack>
        </Box>
        <Box alignSelf={'flex-end'} mr={2}>
          <TouchableOpacity onPress={() => dispatch(correspondenceActions.setOpenModalFilter(true))}>
            {correspondence?.filter &&
            (correspondence.filter.sentByMe ||
              correspondence.filter.sentToMe ||
              correspondence.filter.status ||
              correspondence.filter.date) ? (
              <Box
                zIndex={1}
                width={3}
                height={3}
                borderRadius={'full'}
                position="absolute"
                top={0.5}
                right={0.1}
                bg={'red.500'}
              />
            ) : null}
            <Icon name="filtering-grey" width={30} height={40} />
          </TouchableOpacity>
        </Box>
      </HStack>

      {loading ? (
        <SkeletonComponent />
      ) : (
        <FlatList
          contentContainerStyle={{ paddingHorizontal: 20, paddingTop: 20 }}
          data={emails}
          onEndReachedThreshold={0.3}
          onEndReached={onLoaded}
          refreshControl={<RefreshControl refreshing={loading} onRefresh={refetch} />}
          renderItem={({ item }) => {
            const { deliveryStatus, replyAt } = item;

            let status;
            let color;

            if (replyAt) {
              status = 'Replied';
              color = '#1EA8E0';
            } else if (
              deliveryStatus === Gql.EmailDeliveryStatus.Opened ||
              deliveryStatus === Gql.EmailDeliveryStatus.Clicked
            ) {
              status = 'Read';
              color = '#0CA85D';
            } else if (deliveryStatus === Gql.EmailDeliveryStatus.Sending) {
              status = 'Sending';
            } else if (
              deliveryStatus === Gql.EmailDeliveryStatus.TemporaryFailures ||
              deliveryStatus === Gql.EmailDeliveryStatus.PermanentFailures
            ) {
              status = 'Failed';
            } else if (
              deliveryStatus === Gql.EmailDeliveryStatus.Delivered ||
              deliveryStatus === Gql.EmailDeliveryStatus.Accepted
            ) {
              status = 'Unread';
              color = '#C94C4F';
            }

            return (
              <Pressable onPress={() => navigation.navigate('Content', { id: item.id })}>
                <Box width="100%" my={2}>
                  <HStack alignItems="center" mb={2}>
                    <Avatar
                      size="50px"
                      source={{
                        uri: item?.sender?.avatar || undefined
                      }}
                    />
                    <VStack ml={3} flex={1}>
                      <HStack alignItems="center" justifyContent="space-between">
                        <Text bold flex={1}>
                          {item?.sender?.name}
                        </Text>
                        <Text color="#535862" textAlign="right" bold>
                          {moment(item?.sentAt || item?.replyAt).format('DD MMM, YYYY')}
                        </Text>
                      </HStack>
                      <Text color="#535862" bold>
                        {item?.subject}
                      </Text>
                      <Text numberOfLines={2} ellipsizeMode="tail" color="#535862">
                        {he.decode(item?.parsedBody.replace(/<[^>]*>/g, ''))}
                      </Text>
                      <Box width="30%">
                        <Badge variant="outline" borderRadius={10} mt={2}>
                          <HStack alignItems="center" space={1}>
                            <Circle size="8px" bg={color} />
                            <Text>{status}</Text>
                          </HStack>
                        </Badge>
                      </Box>
                    </VStack>
                  </HStack>
                </Box>
              </Pressable>
            );
          }}
        />
      )}

      <FilteringModal ref={filteringModalRef} />
    </Box>
  );
};

const styles = StyleSheet.create({
  box: {
    padding: 14,
    backgroundColor: '#FFFFFF',
    marginBottom: 10,
    borderRadius: 12
  },
  text: {
    fontWeight: '600',
    fontSize: 14
  },
  date: {
    fontWeight: '400',
    fontSize: 14,
    color: 'neutrals.gray90'
  },
  createdAt: {
    fontWeight: '400',
    color: 'neutrals.gray70',
    alignSelf: 'flex-start'
  }
});
export default Overview;
