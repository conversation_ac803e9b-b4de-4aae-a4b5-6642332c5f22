import { AppBar, Icon } from '@commons';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { pushError } from '@src/configs';
import { DIMENS } from '@src/constants';
import { CorrespondenceNavigatorParams } from '@src/types';
import moment from 'moment';
import { Avatar, Box, HStack, Skeleton, Stack, Text, VStack } from 'native-base';
import React, { useEffect } from 'react';
import { Alert, InteractionManager, Pressable, StyleSheet } from 'react-native';
import RenderHtml from 'react-native-render-html';

type Props = NativeStackScreenProps<CorrespondenceNavigatorParams, 'Content'>;

const EmailContent: React.FC<Props> = ({ navigation, route }) => {
  const id = route?.params?.id;

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getEmail();
    });
  }, []);

  // Email Query
  const [getEmail, { data, loading, refetch, fetchMore }] = Gql.useGetEmailLazyQuery({
    variables: {
      id: id
    },
    onError: pushError
  });

  const email = data?.email;

  return (
    <Box flex={1} bg="white">
      <AppBar goBack={false} barStyle={'dark-content'} title="" background="#0695D7" noRight noLeft />
      {loading ? (
        <SkeletonComponent />
      ) : (
        <Box px={5}>
          <HStack justifyContent="space-between" width="100%" mt={4} mb={1}>
            <Pressable onPress={() => navigation.goBack()}>
              <Icon name="chevron-left" width={18} height={18} />
            </Pressable>
          </HStack>
          <Text fontSize={'xl'} mt={4}>
            {email?.subject}
          </Text>
          <Box mt={5} borderBottomWidth={1} borderColor="lightgray" pb={2} mb={4}>
            <HStack width="100%">
              <Avatar source={{ uri: email?.sender?.avatar || '' }} size="md" mt={1} mr={4} />
              <VStack flex={1}>
                <Text color="#3E5155">
                  From <Text bold>{email?.sender?.name}</Text>
                </Text>
                <Text color="#3E5155">
                  To{' '}
                  {email?.receivers?.nodes?.map((receiver, index) => (
                    <Text key={index} bold>
                      {receiver.name}
                      {index < email.receivers.nodes.length - 1 && ', '}
                    </Text>
                  ))}
                </Text>
                <Text color="#3E5155">
                  Subject <Text bold>{email?.subject}</Text>
                </Text>
                <Text color="gray.600">
                  Sender Reference <Text bold>{email?.reference}</Text>
                </Text>
              </VStack>
              <Text fontWeight={400} color={'gray.700'}>
                {moment(email?.sentAt || email?.replyAt).format('DD MMM, YYYY')}
              </Text>
            </HStack>
          </Box>
          <RenderHtml
            contentWidth={DIMENS.screenWidth}
            source={{ html: email?.parsedBody || '' }}
            tagsStyles={{
              p: { textAlign: 'justify' },
              div: { textAlign: 'justify' }
            }}
          />
        </Box>
      )}
    </Box>
  );
};

const SkeletonComponent: React.FC<any> = () => {
  const SkeletonItem = () => {
    return (
      <VStack mb={5}>
        <Skeleton style={{ width: DIMENS.screenWidth / 1.5, height: 22 }} mb={2} />
        <Skeleton style={{ height: 90, padding: 14, borderRadius: 12 }} />
        <VStack space={1}></VStack>
      </VStack>
    );
  };
  return (
    <Stack p={5}>
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
    </Stack>
  );
};

const styles = StyleSheet.create({
  box: {
    padding: 14,
    backgroundColor: '#FFFFFF',
    marginBottom: 10,
    borderRadius: 12
  },
  text: {
    fontWeight: '600',
    fontSize: 14
  },
  date: {
    fontWeight: '400',
    fontSize: 14,
    color: 'neutrals.gray90'
  },
  createdAt: {
    fontWeight: '400',
    color: 'neutrals.gray70',
    alignSelf: 'flex-start'
  }
});
export default EmailContent;
