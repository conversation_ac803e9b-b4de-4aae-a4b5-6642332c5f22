import { Icon, Modal } from '@commons';
import { Gql } from '@src/api';
import { correspondenceActions } from '@src/slice/corrrespondence';
import { useDispatch, useSelector } from '@src/store';
import _ from 'lodash';
import { Box, HStack, Text } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps, Pressable, StyleSheet } from 'react-native';
import DatePickerModal from './DatePicker';
import StatusModal from './StatusModal';

export interface CorrespondenceInterface {
  status?: Gql.EmailDeliveryStatus;
  date?: any;
}
interface Props {
  onFilter?: (values: CorrespondenceInterface) => void;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const FilteringModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const statusModalRef = useRef<any>(null);
  const datePickerModalRef = useRef<any>(null);
  const correspondence = useSelector(state => state.correspondence);
  const dispatch = useDispatch();
  const { filter } = correspondence;

  const [getFilter, setFilter] = useState<any>({});

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  return (
    <Modal
      // ref={modalRef}
      isVisible={correspondence.openModal}
      onClose={() => {
        dispatch(correspondenceActions.setOpenModalFilter(false));
      }}
      style={{ margin: 120 }}
      type="bottom"
    >
      <Box py={6} px={2} justifyContent="center">
        <Text variant="headline" mb={3} textAlign="center">
          Filter
        </Text>
        <HStack justifyContent={'space-between'}>
          <Pressable
            onPress={() => {
              dispatch(correspondenceActions.setFilter({ ...filter, status: getFilter.status }));
              dispatch(correspondenceActions.setOpenModalFilter(false));
            }}
          >
            <Text fontSize={17} color={'#0695D7'}>
              Apply
            </Text>
          </Pressable>
          <Pressable
            onPress={() => {
              dispatch(correspondenceActions.clearFilters());
              setFilter({});
              dispatch(correspondenceActions.setOpenModalFilter(false));
            }}
          >
            <Text fontSize={17} color={'#0695D7'} mr={2}>
              Clear
            </Text>
          </Pressable>
        </HStack>
      </Box>
      <Pressable onPress={() => statusModalRef?.current?.pushModal()}>
        <Box flexDirection="row" justifyContent="space-between" mt={4} style={style.filterButton}>
          <Box flexDirection="row" alignItems="center">
            <Icon name="check-square" />
            <Text fontSize="16px"> Status</Text>
          </Box>
          <Box flexDirection="row">
            <Text color="#8B8B8B" fontSize="16px">
              {filter?.status || getFilter?.status
                ? `${_.startCase(filter?.status || getFilter?.status)}`
                : 'Choose Status'}
            </Text>
            <Icon name="chevron-right" />
          </Box>
        </Box>
      </Pressable>

      <Pressable onPress={() => datePickerModalRef?.current?.pushModal()}>
        <Box flexDirection="row" justifyContent="space-between" mt={4} style={style.filterButton}>
          <Box flexDirection="row" alignItems="center">
            <Icon name="check-square" />
            <Text fontSize="16px"> Date</Text>
          </Box>
          <Box flexDirection="row">
            <Text color="#8B8B8B" fontSize="16px">
              {filter?.date || getFilter?.date ? filter?.date || getFilter?.date : 'Choose Date'}
            </Text>
            <Icon name="chevron-right" />
          </Box>
        </Box>
      </Pressable>

      <StatusModal
        ref={statusModalRef}
        onApply={val => {
          setFilter({ ...getFilter, status: val });
        }}
      />
      <DatePickerModal
        ref={datePickerModalRef}
        onApply={val => {
          setFilter({ ...getFilter, date: val });
        }}
      />
    </Modal>
  );
});

const style = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    height: 8,
    justifyContent: 'space-between',
    padding: 6,
    alignItems: 'center'
  },
  filterButton: {
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    margin: 10
  }
});

FilteringModal.displayName = 'FilteringModal';
export default FilteringModal;
