import { Icon, Modal } from '@commons';
import { correspondenceActions } from '@src/slice/corrrespondence';
import { useDispatch, useSelector } from '@src/store';
import { Box, Button, Divider, Flex, HStack, Text, View } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps, StyleSheet, TouchableOpacity } from 'react-native';

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

type Props = {
  onApply?: (status: string) => void;
};

const StatusModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const { filter } = useSelector(state => state.correspondence);
  const correspondence = useSelector(state => state.correspondence);
  const [status, setStatus] = useState<string>(filter?.status || '');
  const dispatch = useDispatch();

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  return (
    <Modal ref={modalRef} type="bottom">
      <HStack alignItems="center" w="100%" justifyContent="center" position="relative">
        <TouchableOpacity
          style={{ position: 'absolute', left: 16 }}
          onPress={() => {
            modalRef.current.closeModal();
          }}
        >
          <Icon name="chevron-left" />
        </TouchableOpacity>
        <Text fontSize="16" fontWeight="500">
          Filter Status
        </Text>
      </HStack>
      <Flex direction="column" p={5} pt={3}>
        <TouchableOpacity
          onPress={() => {
            setStatus('unread');
          }}
        >
          <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
            <HStack space={2}>
              <View style={[style.dot, { backgroundColor: '#C94C4F', alignSelf: 'center' }]} />
              <Text fontSize="16px" color="#8F8989">
                Unread
              </Text>
            </HStack>
            {status === 'unread' ? <Icon name="tick" /> : <></>}
          </Box>
        </TouchableOpacity>

        <Divider mt={4} />
        <TouchableOpacity
          onPress={() => {
            setStatus('read');
          }}
        >
          <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
            <HStack space={2}>
              <View style={[style.dot, { backgroundColor: '#0CA85D', alignSelf: 'center' }]} />
              <Text fontSize="16px" color="#8F8989">
                Read
              </Text>
            </HStack>
            {status === 'read' ? <Icon name="tick" /> : <></>}
          </Box>
        </TouchableOpacity>

        <Divider mt={4} />

        <TouchableOpacity
          onPress={() => {
            setStatus('replied');
          }}
        >
          <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
            <HStack space={2}>
              <View style={[style.dot, { backgroundColor: '#1EA8E0', alignSelf: 'center' }]} />
              <Text fontSize="16px" color="#8F8989">
                Replied
              </Text>
            </HStack>
            {status === 'replied' ? <Icon name="tick" /> : <></>}
          </Box>
        </TouchableOpacity>
      </Flex>
      <Flex direction="row" width="100%" justifyContent="space-between" padding={4} bg="#ffffff">
        <Button
          flex={1}
          variant="outline"
          marginRight={2}
          _pressed={{ bg: '#E0E0E0', opacity: 0.8 }}
          bg="#ffffff"
          onPress={() => {
            dispatch(correspondenceActions.setFilter({ ...correspondence.filter, status: undefined }));
            setStatus('');
            modalRef.current.closeModal();
          }}
        >
          <Text color="#0695D7">Clear all</Text>
        </Button>
        <Button
          flex={1}
          _pressed={{ bg: '#007ACC', opacity: 0.8 }}
          bg="#0695D7"
          onPress={() => {
            modalRef.current.closeModal();
            props.onApply?.(status);
          }}
        >
          <Text color="#ffffff">Apply</Text>
        </Button>
      </Flex>
    </Modal>
  );
});

const style = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    height: 48,
    justifyContent: 'space-between',
    padding: 6,
    alignItems: 'center'
  },
  filterButton: {
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20
  },
  dot: {
    width: 10,
    height: 10,
    borderRadius: 5
  }
});

StatusModal.displayName = 'StatusModal';
export default StatusModal;
