import { Modal } from '@commons';
import { correspondenceActions } from '@src/slice/corrrespondence';
import { useDispatch, useSelector } from '@src/store';
import { Box, Text } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { ModalProps, Pressable, StyleSheet } from 'react-native';
import DatePicker from 'react-native-date-picker';

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

type Props = {
  onApply: (status: string) => void;
};

const DatePickerModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const { filter } = useSelector(state => state.correspondence);
  const dispatch = useDispatch();

  const [time, setTime] = React.useState(new Date());

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  return (
    <Modal ref={modalRef} type="bottom">
      <Box py={6} px={5} justifyContent="center">
        <Text variant="headline" mb={3} textAlign="center">
          Select a time
        </Text>
        <Box justifyContent="center">
          <DatePicker mode="date" date={time} onDateChange={date => setTime(date)} style={{ alignSelf: 'center' }} />
        </Box>
        <Box justifyContent="center" mt={5}>
          <Text variant="headline" textAlign="center">
            {time.toLocaleDateString()}
          </Text>
        </Box>
      </Box>
      <Pressable
        onPress={() => {
          modalRef.current.closeModal();
          props.onApply(time.toLocaleDateString());
          // dispatch(correspondenceActions.setFilter({ ...filter, date: time.toLocaleDateString() }));
        }}
      >
        <Box flexDirection="row" justifyContent="center" mt={4}>
          <Text fontSize="16px" color="#8B8B8B">
            Done
          </Text>
        </Box>
      </Pressable>
    </Modal>
  );
});

const style = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    height: 48,
    justifyContent: 'space-between',
    padding: 6,
    alignItems: 'center'
  },
  filterButton: {
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20
  },
  dot: {
    width: 10,
    height: 10,
    borderRadius: 5
  }
});

DatePickerModal.displayName = 'DatePickerModal';
export default DatePickerModal;
