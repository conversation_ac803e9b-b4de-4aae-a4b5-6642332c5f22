import { Content, Icon } from '@commons';
import { composeValidators, email, pushError, pushMessaage, required } from '@configs';
import { TextInput } from '@inputs';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import type { NativeStackScreenProps } from '@react-navigation/native-stack';
import { AuthActions } from '@src/slice/auth.slice';
import { useDispatch, useSelector } from '@src/store';
import { AuthenticationNavigatorParams } from '@types';
import { Field, Formik, FormikProps } from 'formik';
import _ from 'lodash';
import { Box, Button, Center, HStack, Pressable, Text } from 'native-base';
import React, { useEffect, useRef, useState } from 'react';
import { KeyboardAvoidingView, Platform, StyleSheet } from 'react-native';
import FastImage from 'react-native-fast-image';
import { LoginManager } from 'react-native-fbsdk-next';

import bgAuth from '@assets/bg_auth.png';
import binaLogo from '@assets/bina_logo.png';
import { appleAuth } from '@invertase/react-native-apple-authentication';
import { requestPermissions } from '@src/configs/permission';
import { useGenerateOtpMutation } from '@src/api/graphql';

type Props = NativeStackScreenProps<AuthenticationNavigatorParams, 'Login'>;

interface ISocialLoginResult {
  accessToken: string;
  refreshToken: string;
  accessTokenExpiry: number;
}

const Login: React.FC<Props> = ({ navigation, route }) => {
  const [initialValues] = useState<FormValues>({ email: '', password: '', rememberMe: true });
  const formRef = useRef<FormikProps<FormValues>>(null);

  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [show, setShow] = useState(false);
  const dispatch = useDispatch();
  const auth = useSelector(state => state.auth);

  const onSuccess = _.get(route, 'params.onSuccess', null);

  useEffect(() => {
    const initStoragePermission = async () => {
      await requestPermissions();
    };

    initStoragePermission();
  }, []);

  useEffect(() => {
    if (auth.isAuthenticated) {
      if (onSuccess) onSuccess();
    }
  }, [auth.isAuthenticated]);

  const [generateOtp, { error }] = useGenerateOtpMutation({
    onError: e => {
      pushError(e);
    }
  });

  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true);
    try {
      const loginRes = await dispatch(
        AuthActions.login({ email: values.email, password: values.password, rememberMe: true })
      );

      if (loginRes !== undefined && loginRes !== null) {
        const res = await generateOtp({
          variables: {
            input: {
              email: values.email
            }
          }
        });

        if (res.data?.generateOtp === 'Two Factor Authentication is not enabled') {
          await dispatch(AuthActions.verifyOtp({ email: values.email, tokens: loginRes }));
        } else {
          pushMessaage('OTP sent to your email', 'success');
          navigation.navigate('VerifyOTP', { email: values.email, tokens: loginRes });
        }
      }
    } catch (e: any) {
      if (e?.response?.status === 503) {
        pushMessaage('System is under maintenance', 'warning');
      } else {
        pushError('Invalid email or password');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // reference: https://ibjects.medium.com/google-signin-tutorial-for-react-native-81a57fb67b18
  const onGoogleLogin = async () => {
    let token: ISocialLoginResult | null = null;
    GoogleSignin.configure({
      webClientId: '328771878135-bgv5p9osu52kokv82o07s5go86pkm1l3.apps.googleusercontent.com',
      iosClientId: '328771878135-c91sipt1htcp2ih05586ttjh0k97uhbc.apps.googleusercontent.com'
    });
    try {
      const hasPlayService = await GoogleSignin.hasPlayServices();
      if (hasPlayService) {
        await GoogleSignin.signIn();
        const { accessToken } = await GoogleSignin.getTokens();

        token = await fetch(`${process.env.PROD_API_URL}/api/auth/user/google-signIn`, {
          method: 'POST',
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            googleToken: accessToken
          })
        })
          .then(res => res.json())
          .then(responseData => {
            return responseData;
          });
        if (token)
          dispatch(
            AuthActions.loginSocial({
              accessToken: token.accessToken,
              refreshToken: token.refreshToken,
              accessTokenExpiry: token.accessTokenExpiry
            })
          );
      }
    } catch (e) {
      pushError(e);
    }
  };

  const onFbLogin = async () => {
    try {
      const result = await LoginManager.logInWithPermissions(['public_profile']);
    } catch (e) {
      pushError(e);
    }
  };

  const onAppleLogin = async () => {
    const appleAuthRequestResponse = await appleAuth.performRequest({
      requestedOperation: appleAuth.Operation.LOGIN,
      requestedScopes: [appleAuth.Scope.EMAIL, appleAuth.Scope.FULL_NAME]
    });

    const credentialState = await appleAuth.getCredentialStateForUser(appleAuthRequestResponse.user);

    try {
      if (credentialState === appleAuth.State.AUTHORIZED) {
        const { identityToken } = appleAuthRequestResponse;

        if (identityToken) {
          let token: ISocialLoginResult | null = null;
          token = await fetch(`${process.env.PROD_API_URL}/api/auth/user/apple-signIn`, {
            method: 'POST',
            headers: {
              Accept: 'application/json',
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              appleToken: appleAuthRequestResponse
            })
          })
            .then(res => res.json())
            .then(responseData => {
              return responseData;
            });

          if (token?.accessToken)
            dispatch(
              AuthActions.loginSocial({
                accessToken: token.accessToken,
                refreshToken: token.refreshToken,
                accessTokenExpiry: token.accessTokenExpiry
              })
            );
          else pushError(JSON.parse(JSON.stringify(token))?.message);
        }
      }
    } catch (e) {
      pushError(e);
    }
  };

  return (
    <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : undefined} style={{ flex: 1 }}>
      <Box flex={1} bg="#FFF">
        <FastImage source={bgAuth} style={style.backgroundCover} resizeMode="cover">
          {/* <AppBar goBack /> */}
          <Formik onSubmit={onSubmit} initialValues={initialValues} innerRef={formRef}>
            {() => {
              return (
                <Box flex={1}>
                  <Content pt={1}>
                    <FastImage source={binaLogo} resizeMode={'contain'} style={style.logo} />
                    <Field
                      autoFocus
                      name="email"
                      placeholder="Email address"
                      validate={composeValidators(required, email)}
                      component={TextInput}
                      testID="loginEmailInput"
                    />
                    <Field
                      name="password"
                      placeholder="Password"
                      component={TextInput}
                      secureTextEntry={show ? false : true}
                      InputRightElement={
                        <Pressable onPress={() => setShow(!show)}>
                          <Icon name={show ? 'password-visible' : 'password-hidden'} style={style.passwordIcons} />
                        </Pressable>
                      }
                      testID="loginPasswordInput"
                    />

                    <Button
                      isLoading={isSubmitting}
                      variant="primary"
                      mt={2}
                      onPress={() => {
                        formRef.current?.handleSubmit();
                      }}
                      testID="loginButton"
                    >
                      Sign In
                    </Button>

                    <Text
                      textAlign={'center'}
                      color="#FFF"
                      fontSize="14px"
                      bold
                      mt={10}
                      onPress={() => navigation.navigate('ForgotPassword' as never)}
                    >
                      Forgot password?
                    </Text>
                    <Center>
                      {/* <Text color="neutrals.white" mt={5}>
                        or continue with
                      </Text> */}
                    </Center>
                  </Content>
                </Box>
              );
            }}
          </Formik>
        </FastImage>
      </Box>
    </KeyboardAvoidingView>
  );
};

interface FormValues {
  email: string;
  password: string;
  rememberMe: boolean;
}

const style = StyleSheet.create({
  passwordIcons: {
    marginRight: '4%'
  },
  backgroundCover: {
    width: '100%',
    flex: 1
  },
  logo: {
    width: '100%',
    aspectRatio: 1
  },
  socialMediaButton: {
    borderRadius: 45,
    borderColor: '#E8EFFF',
    backgroundColor: '#FFF'
  },
  socialMediaIcon: {
    width: 90,
    height: 90,
    margin: 0,
    padding: 0
  },
  checkbox: {
    alignSelf: 'center'
  }
});

export default Login;
