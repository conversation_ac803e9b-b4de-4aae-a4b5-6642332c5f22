import { AppBar, Checkbox, Content, Footer, Icon } from '@commons';
import { composeValidators, email, pushError, required } from '@configs';
import { TextInput } from '@inputs';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { UserAuthService } from '@src/api/rest';
import { COLORS } from '@src/constants';
import { AuthActions } from '@src/slice/auth.slice';
import { AuthenticationNavigatorParams } from '@types';
import { Field, Formik, FormikProps } from 'formik';
import { Box, Button, Center, Divider, HStack, Pressable, Text } from 'native-base';
import React, { useRef, useState } from 'react';
import { Platform, StyleSheet } from 'react-native';
import { appleAuth } from '@invertase/react-native-apple-authentication';
import { LoginManager } from 'react-native-fbsdk-next';
import { useDispatch, useSelector } from '@src/store';

type Props = NativeStackScreenProps<AuthenticationNavigatorParams, 'SignUp'>;

interface ISocialLoginResult {
  accessToken: string;
  refreshToken: string;
  accessTokenExpiry: number;
}

const SignUp: React.FC<Props> = ({ navigation }) => {
  const [initialValues] = useState<FormValues>({ email: '', password: '' });
  const formRef = useRef<FormikProps<FormValues>>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [show, setShow] = useState(false);
  const dispatch = useDispatch();

  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true);
    const email = values.email;
    const password = values.password;
    try {
      if (email && password) {
        await UserAuthService.signUp({ body: { email, password } });
      }
    } catch (e) {
      pushError(e);
    } finally {
      setIsSubmitting(false);
    }
  };

  const onGoogleLogin = async () => {
    let token: ISocialLoginResult | null = null;
    GoogleSignin.configure({
      webClientId: '328771878135-bgv5p9osu52kokv82o07s5go86pkm1l3.apps.googleusercontent.com',
      iosClientId: '328771878135-c91sipt1htcp2ih05586ttjh0k97uhbc.apps.googleusercontent.com'
    });
    try {
      const hasPlayService = await GoogleSignin.hasPlayServices();
      if (hasPlayService) {
        await GoogleSignin.signIn();
        const { accessToken } = await GoogleSignin.getTokens();

        token = await fetch(`${process.env.PROD_API_URL}/api/auth/user/google-signIn`, {
          method: 'POST',
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            googleToken: accessToken
          })
        })
          .then(res => res.json())
          .then(responseData => {
            return responseData;
          });
        if (token)
          dispatch(
            AuthActions.loginSocial({
              accessToken: token.accessToken,
              refreshToken: token.refreshToken,
              accessTokenExpiry: token.accessTokenExpiry
            })
          );
      }
    } catch (e) {
      pushError(e);
    }
  };

  const onFbLogin = async () => {
    try {
      const result = await LoginManager.logInWithPermissions(['public_profile']);
    } catch (e) {
      pushError(e);
    }
  };

  const onAppleLogin = async () => {
    const appleAuthRequestResponse = await appleAuth.performRequest({
      requestedOperation: appleAuth.Operation.LOGIN,
      requestedScopes: [appleAuth.Scope.EMAIL, appleAuth.Scope.FULL_NAME]
    });

    const credentialState = await appleAuth.getCredentialStateForUser(appleAuthRequestResponse.user);

    try {
      if (credentialState === appleAuth.State.AUTHORIZED) {
        const { identityToken } = appleAuthRequestResponse;

        if (identityToken) {
          let token: ISocialLoginResult | null = null;
          token = await fetch(`${process.env.PROD_API_URL}/api/auth/user/apple-signIn`, {
            method: 'POST',
            headers: {
              Accept: 'application/json',
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              appleToken: appleAuthRequestResponse
            })
          })
            .then(res => res.json())
            .then(responseData => {
              return responseData;
            });

          if (token?.accessToken)
            dispatch(
              AuthActions.loginSocial({
                accessToken: token.accessToken,
                refreshToken: token.refreshToken,
                accessTokenExpiry: token.accessTokenExpiry
              })
            );
          else pushError(JSON.parse(JSON.stringify(token))?.message);
        }
      }
    } catch (e) {
      pushError(e);
    }
  };

  return (
    <Box flex={1} bg="#FFF">
      <AppBar goBack />
      <Formik onSubmit={onSubmit} initialValues={initialValues} innerRef={formRef}>
        {() => {
          return (
            <Box flex={1}>
              <Content pt={5}>
                <Text variant="h2" mb={5}>
                  Sign Up to BINA
                </Text>
                <Field
                  autoFocus
                  name="email"
                  label="Email address"
                  validate={composeValidators(required, email)}
                  component={TextInput}
                />
                <Field
                  name="password"
                  label="Password"
                  component={TextInput}
                  secureTextEntry={show ? false : true}
                  InputRightElement={
                    <Pressable onPress={() => setShow(!show)}>
                      <Icon name={show ? 'password-visible' : 'password-hidden'} style={style.passwordIcons} />
                    </Pressable>
                  }
                />

                <HStack space={3} mt={2}>
                  <Field component={Checkbox} />
                  <Text color={COLORS.neutrals.gray90}>I agree to the terms.</Text>
                </HStack>

                <Divider mt="5" color="#E8E8E8" />
                <Center>
                  <Text color="neutrals.gray90" mt={5}>
                    or continue with
                  </Text>

                  <HStack flex={1} space={3} mt="4" pb={10}>
                    <Button style={style.socialMediaButton} onPress={() => onGoogleLogin()}>
                      <Icon name="google" style={style.socialMediaIcon}></Icon>
                    </Button>
                    {/* <Button style={style.socialMediaButton} onPress={() => onFbLogin()}>
                      <Icon name="facebook" style={style.socialMediaIcon}></Icon>
                    </Button> */}
                    {Platform.OS === 'ios' && (
                      <Button style={style.socialMediaButton} onPress={() => onAppleLogin()}>
                        <Icon name="apple" style={style.socialMediaIcon}></Icon>
                      </Button>
                    )}
                  </HStack>
                </Center>
              </Content>
              <Footer>
                <Button
                  isLoading={isSubmitting}
                  variant="primary"
                  onPress={() => {
                    formRef.current?.handleSubmit();
                  }}
                >
                  Continue
                </Button>
              </Footer>
            </Box>
          );
        }}
      </Formik>
    </Box>
  );
};

interface FormValues {
  email: string;
  password: string;
}

const style = StyleSheet.create({
  passwordIcons: {
    marginRight: '4%'
  },
  socialMediaButton: {
    borderRadius: 90,
    borderColor: '#E8EFFF',
    height: 50
  },
  socialMediaIcon: {
    width: 90,
    height: 90,
    margin: 0,
    padding: 0
  },
  checkbox: {
    alignSelf: 'center'
  }
});

export default SignUp;
