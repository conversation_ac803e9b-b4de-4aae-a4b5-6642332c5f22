import { DIMENS } from '@src/constants';
import { Center, Spinner, Text, View } from 'native-base';
import React, { useState } from 'react';

const LoginProcessing: React.FC<any> = prop => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const url = prop.route.path;

  const verifyToken = async (token: string) => {
    try {
    } catch (err) {}
  };

  return (
    <View>
      {isLoading && (
        <Center style={{ justifyContent: 'center', alignItems: 'center', marginTop: DIMENS.screenHeight / 2.5 }}>
          <Spinner size="lg" />
          <Text style={{ marginTop: 25 }}>Logging in, please hold on...</Text>
        </Center>
      )}
    </View>
  );
};

export default LoginProcessing;
