import { FocusAwareStatusBar, Footer } from '@commons';
import { StackNavigationProp } from '@react-navigation/stack';
import { AuthenticationNavigatorParams } from '@types';
import { Box, Button, Text } from 'native-base';
import React from 'react';
import { StyleSheet } from 'react-native';
import FastImage from 'react-native-fast-image';

import bgAuth from '@assets/bg_auth.png';
import logo from '@assets/logo.png';

type Props = { navigation: StackNavigationProp<AuthenticationNavigatorParams, 'AuthMenu'> };
const AuthMenu: React.FC<Props> = ({ navigation }) => {
  return (
    <Box flex={1}>
      <FocusAwareStatusBar translucent backgroundColor="transparent" barStyle="light-content" />
      <Box flex={1}>
        <FastImage source={bgAuth} style={styles.backgroundCover} resizeMode="cover" />
        <Box h="100%" position="absolute" w="100%">
          <Box flex={1} safeAreaTop pt={16} alignItems="center">
            <FastImage source={logo} style={styles.logo} />
          </Box>
          <Footer>
            <Text variant="h2" mb={2}>
              Welcome to BINA
            </Text>
            <Text variant="bodyReg" mb={7}>
              Malaysian renown top notch interior designer turns home into living art masterpieces
            </Text>

            <Button variant="primary" onPress={() => navigation.navigate('Login')} mb={4}>
              Login
            </Button>
            <Button variant="light" height={54} onPress={() => navigation.navigate('SignUp')} mb={4}>
              Sign up
            </Button>
          </Footer>
        </Box>
      </Box>
    </Box>
  );
};

const styles = StyleSheet.create({
  backgroundCover: {
    width: '100%',
    flex: 1
  },
  logo: {
    width: 83,
    aspectRatio: 1
  }
});

export default AuthMenu;
