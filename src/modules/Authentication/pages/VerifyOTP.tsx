import React, { useState, useEffect } from 'react';
import { StyleSheet, KeyboardAvoidingView, Platform, Pressable } from 'react-native';
import { Box, Button, Center, Text } from 'native-base';
import { RouteProp } from '@react-navigation/native';
import { AppBar, Content, Icon } from '@commons';
import { StackNavigationProp } from '@react-navigation/stack';
import { AuthenticationNavigatorParams } from '@types';
import { CodeInput } from '@inputs';
// import { requestLogin, requestSMSLogin } from '@store/actions/auth.actions';
import { resetPassword, signUp, verifyOTP } from '@api/auth.api';
import { navigate, pushError, pushSuccess } from '@configs';
import _ from 'lodash';

import ResendCode from '../components/ResendCode';
import { DIMENS } from '@src/constants';
import OTPTextInput from 'react-native-otp-textinput';
import { AuthActions } from '@src/slice/auth.slice';
import { useDispatch, useSelector } from '@src/store';
import { useGenerateOtpMutation } from '@src/api/graphql';
import { Spinner } from 'native-base';

type Props = {
  navigation: StackNavigationProp<AuthenticationNavigatorParams, 'VerifyOTP'>;
  route: RouteProp<
    {
      params: {
        email: string;
        // type: 'SIGN_UP' | 'RESET_PASSWORD';
        tokens: any;
        // password: any;
        // name?: string;
        // email?: string;
        // newPassword?: string;
        // confirmPassword?: string;
      };
    },
    'params'
  >;
};
const VerifyOTP: React.FC<Props> = props => {
  const { route, navigation } = props;
  // const { phone, email, name, password, newPassword, confirmPassword, countryCode, type } = route.params;
  const { email, tokens } = route.params;
  const otpRef = React.useRef(null);
  const [loding, setLoading] = useState(false);

  const dispatch = useDispatch();

  const onVerifyOtp = async (otp: string) => {
    setLoading(true);
    try {
      await dispatch(AuthActions.verifyOtp({ otp: otp, email: email as string, tokens: tokens }));
    } catch (e) {
      pushError(e);
    } finally {
      setLoading(false);
    }
  };

  const [generateOtp, { error }] = useGenerateOtpMutation({
    onError: e => {
      pushError(e);
    },
    onCompleted: () => {
      pushSuccess('OTP has been resent successfully');
    }
  });

  return (
    <KeyboardAvoidingView style={styles.container} behavior={Platform.OS === 'android' ? undefined : 'padding'}>
      <AppBar goBack />
      {loding && <Spinner />}
      <Content mt={DIMENS.screenHeight / 5}>
        <Text fontSize="2xl" fontWeight="bold" textAlign="center" mb={4} pt={5}>
          Verify OTP
        </Text>

        <Text textAlign="center" mb={4} color={'#50555C'}>
          Please enter the 6-character code sent to {email} to continue.
        </Text>

        <OTPTextInput
          inputCount={6}
          ref={otpRef}
          handleTextChange={val => {
            if (val.length === 6) {
              onVerifyOtp(val);
            }
          }}
          textInputStyle={styles.textInputStyle}
        />

        <Box mt={4}>
          <Text textAlign="center">
            Didn’t receive a code?
            <Pressable
              onPress={async () => {
                await generateOtp({
                  variables: {
                    input: {
                      email: email
                    }
                  }
                });
              }}
            >
              <Text color="#058DD8" bold>
                {' '}
                Resend
              </Text>
            </Pressable>
          </Text>
        </Box>
      </Content>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff'
  },
  textInputStyle: {
    borderWidth: 1, // Add border width
    borderColor: '#000', // Border color (black in this case)
    borderRadius: 5, // Optional: Add border radius for rounded corners
    paddingHorizontal: 10, // Adjust padding if needed
    paddingVertical: 5 // Adjust padding if needed
  }
});

export default VerifyOTP;
