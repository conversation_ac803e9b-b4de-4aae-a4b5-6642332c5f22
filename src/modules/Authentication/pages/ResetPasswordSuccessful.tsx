import { AppBar, Content, Footer, Icon } from '@commons';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { AuthenticationNavigatorParams } from '@types';
import { Box, Button, Center, Text } from 'native-base';
import React, { useState } from 'react';
import { StyleSheet } from 'react-native';

type Props = NativeStackScreenProps<AuthenticationNavigatorParams, 'ResetPasswordSuccessful'>;

const ResetPasswordSuccessful: React.FC<Props> = ({ navigation }) => {
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  return (
    <Box flex={1} bg="#FFF">
      <AppBar goBack />
      <Content pt={5}>
        <Center mt={40}>
          <Icon name="password-reset-success" />
          <Text variant="h2" mb={5}>
            Your password is reset!
          </Text>
          <Text mb={5} style={style.text}>
            You can now login with your new password.
          </Text>
        </Center>
      </Content>
      <Footer>
        <Button
          isLoading={isSubmitting}
          variant="primary"
          onPress={() => {
            navigation.navigate('Login');
          }}
        >
          Back to login
        </Button>
      </Footer>
    </Box>
  );
};

const style = StyleSheet.create({
  text: {
    fontSize: 16,
    fontWeight: '400',
    lineHeight: 24,
    color: '#585757'
  }
});

export default ResetPasswordSuccessful;
