import { AppBar, Content, Footer } from '@commons';
import { composeValidators, email, pushError, pushSuccess, required } from '@configs';
import { TextInput } from '@inputs';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { UserAuthService } from '@src/api/rest';
import { AuthenticationNavigatorParams } from '@types';
import { Field, Formik, FormikProps } from 'formik';
import { Box, Button, Text } from 'native-base';
import React, { useRef, useState } from 'react';
import { StyleSheet } from 'react-native';

type Props = NativeStackScreenProps<AuthenticationNavigatorParams, 'ForgotPassword'>;

const ForgotPassword: React.FC<Props> = ({ navigation }) => {
  const [initialValues] = useState<FormValues>({ email: '' });
  const formRef = useRef<FormikProps<FormValues>>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true);
    const email = values.email ?? '';
    try {
      await UserAuthService.forgotPassword({ body: { email } });
      pushSuccess('Reset password link has been sent to your email');
    } catch (e) {
      pushError(e);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box flex={1} bg="#FFF">
      <AppBar goBack />
      <Formik onSubmit={onSubmit} initialValues={initialValues} innerRef={formRef}>
        {() => {
          return (
            <Box flex={1}>
              <Content pt={5}>
                <Text variant="h2" mb={5}>
                  Forgot your password?
                </Text>

                <Text mb={5} style={style.text}>
                  Enter your email address and we'll send you a password reset link.
                </Text>
                <Field
                  autoFocus
                  name="email"
                  label="Email address"
                  validate={composeValidators(required, email)}
                  component={TextInput}
                />
              </Content>
              <Footer>
                <Button
                  // isDisabled={isDisabled || isSubmitting}
                  isLoading={isSubmitting}
                  variant="primary"
                  onPress={() => {
                    formRef.current?.handleSubmit();
                  }}
                >
                  Send password reset link
                </Button>
              </Footer>
            </Box>
          );
        }}
      </Formik>
    </Box>
  );
};

interface FormValues {
  email: string | undefined;
}

const style = StyleSheet.create({
  text: {
    fontSize: 16,
    fontWeight: '400',
    lineHeight: 24,
    color: '#585757'
  }
});

export default ForgotPassword;
