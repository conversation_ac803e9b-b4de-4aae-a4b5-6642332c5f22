import { AppBar, Content, Footer } from '@commons';
import { pushError } from '@configs';
import { AvatarPicker, PhoneInput, TextInput } from '@inputs';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import apolloClient from '@src/lib/apollo';
import { AuthActions } from '@src/slice/auth.slice';
import { useDispatch } from '@src/store';
import { AuthenticationNavigatorParams } from '@types';
import { Field, Formik, FormikProps } from 'formik';
import { Box, Button, Text } from 'native-base';
import React, { useEffect, useRef, useState } from 'react';

type Props = NativeStackScreenProps<AuthenticationNavigatorParams, 'Onboarding'>;

const Onboarding: React.FC<Props> = ({ navigation, route }) => {
  const [initialValues] = useState<FormValues>({
    name: '',
    phoneNo: '',
    companyName: '',
    position: ''
  });
  const formRef = useRef<FormikProps<FormValues>>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const dispatch = useDispatch();

  useEffect(() => {
    if (route.params.token) loginWithToken();
  }, [route.params.token]);

  const loginWithToken = () => {
    const signUpToken = route.params.token;
    try {
      // The authentication will be false first
      dispatch(AuthActions.loginBeforeOnboarding({ signUpToken }));
    } catch (err) {
      pushError(err);
    }
  };

  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true);
    try {
      const { name, phoneNo, companyName, position } = values;
      await apolloClient.mutate({
        mutation: Gql.UpdateUserMeDocument,
        variables: {
          input: {
            name,
            phoneNo,
            companyName,
            position
          }
        }
      });
      // The authentication will be true and logged in after onboarding form is filled
      dispatch(AuthActions.loginAfterOnboarding());
    } catch (e) {
      pushError(e);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box flex={1} bg="#FFF">
      <AppBar goBack />
      <Formik onSubmit={onSubmit} initialValues={initialValues} innerRef={formRef}>
        {() => {
          return (
            <Box flex={1}>
              <Content pt={5}>
                <Text variant="h2" mb={5}>
                  Your 30-day free trial starts today
                </Text>
                <Field autoFocus name="avatar" label="Profile Photo" component={AvatarPicker} />
                <Field autoFocus name="name" label="Full name" component={TextInput} />
                <Field autoFocus name="phoneNo" label="Phone number" component={PhoneInput} />
                <Field autoFocus name="companyName" label="Company Name" component={TextInput} />
                <Field autoFocus name="position" label="Position" component={TextInput} />
              </Content>
              <Footer>
                <Button
                  isLoading={isSubmitting}
                  variant="primary"
                  onPress={() => {
                    formRef.current?.handleSubmit();
                  }}
                >
                  Continue
                </Button>
              </Footer>
            </Box>
          );
        }}
      </Formik>
    </Box>
  );
};

interface FormValues {
  name: string;
  phoneNo: string;
  companyName: string;
  position: string;
}

export default Onboarding;
