import { AppBar, Content, Footer, Icon } from '@commons';
import { pushError } from '@configs';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { UserAuthService } from '@src/api/rest';
import { TextInput } from '@src/inputs';
import { AuthenticationNavigatorParams } from '@types';
import { Field, Formik, FormikProps } from 'formik';
import { Box, Button, Pressable, Text } from 'native-base';
import React, { useRef, useState } from 'react';
import { StyleSheet } from 'react-native';

type Props = NativeStackScreenProps<AuthenticationNavigatorParams, 'ResetPassword'>;

const ResetPassword: React.FC<Props> = ({ navigation, route }) => {
  const [initialValues] = useState<FormValues>({ newPassword: '', confirmPassword: '' });
  const formRef = useRef<FormikProps<FormValues>>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [show, setShow] = useState(false);
  const [isDisabled, setIsDisabled] = useState<boolean>(true);

  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true);
    const newPassword = values.newPassword;
    const resetToken = route.params.token;

    try {
      await UserAuthService.resetPassword({ body: { newPassword, resetToken } });
      navigation.navigate('ResetPasswordSuccessful');
    } catch (e) {
      pushError(e);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box flex={1} bg="#FFF">
      <AppBar goBack />
      <Formik onSubmit={onSubmit} initialValues={initialValues} innerRef={formRef}>
        {() => {
          return (
            <Box flex={1}>
              <Content pt={5}>
                <Text variant="h2" mb={5}>
                  Reset your password
                </Text>

                <Field
                  name="newPassword"
                  label="Enter your new password"
                  component={TextInput}
                  secureTextEntry={show ? false : true}
                  InputRightElement={
                    <Pressable onPress={() => setShow(!show)}>
                      <Icon name={show ? 'password-visible' : 'password-hidden'} style={style.passwordIcons} />
                    </Pressable>
                  }
                />

                <Field
                  name="confirmPassword"
                  label="Confirm your new password"
                  component={TextInput}
                  secureTextEntry={show ? false : true}
                  InputRightElement={
                    <Pressable onPress={() => setShow(!show)}>
                      <Icon name={show ? 'password-visible' : 'password-hidden'} style={style.passwordIcons} />
                    </Pressable>
                  }
                />
              </Content>
              <Footer>
                <Button
                  // isDisabled={isDisabled || isSubmitting}
                  isLoading={isSubmitting}
                  variant="primary"
                  onPress={() => {
                    formRef.current?.handleSubmit();
                  }}
                >
                  Confirm
                </Button>
              </Footer>
            </Box>
          );
        }}
      </Formik>
    </Box>
  );
};

interface FormValues {
  newPassword: string;
  confirmPassword: string;
}

const style = StyleSheet.create({
  passwordIcons: {
    marginRight: '4%'
  }
});

export default ResetPassword;
