import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { AuthenticationNavigatorParams } from '@types';
import AuthMenu from './pages/AuthMenu';
import SignUp from './pages/SignUp';
import ForgotPassword from './pages/ForgotPassword';
import ResetPassword from './pages/ResetPassword';
import ResetPasswordSuccessful from './pages/ResetPasswordSuccessful';
import Onboarding from './pages/Onboarding';
import Login from './pages/Login';
import LoginProcessing from './pages/LoginProcessing';
import VerifyOTP from './pages/VerifyOTP';

const AuthStack = createNativeStackNavigator<AuthenticationNavigatorParams>();

const AuthenticationNavigator: React.FC<any> = () => {
  return (
    <AuthStack.Navigator initialRouteName="Login" screenOptions={{ headerShown: false }}>
      <AuthStack.Screen name="AuthMenu" component={AuthMenu} />
      <AuthStack.Screen name="Login" component={Login} />
      <AuthStack.Screen name="LoginProcessing" component={LoginProcessing} />
      <AuthStack.Screen name="SignUp" component={SignUp} />
      <AuthStack.Screen name="ForgotPassword" component={ForgotPassword} />
      <AuthStack.Screen name="ResetPassword" component={ResetPassword} />
      <AuthStack.Screen name="ResetPasswordSuccessful" component={ResetPasswordSuccessful} />
      <AuthStack.Screen name="Onboarding" component={Onboarding} />
      <AuthStack.Screen name="VerifyOTP" component={VerifyOTP} />
    </AuthStack.Navigator>
  );
};

export default AuthenticationNavigator;
