import React, { useState, useEffect } from 'react';
import { InteractionManager } from 'react-native';
import BackgroundTimer from 'react-native-background-timer';
import { Button, Center, HStack, Text, View } from 'native-base';
import { pushError } from '@configs/helpers';
import moment from 'moment';
import { requestOTP } from '@api/auth.api';
import { requestSMSLogin } from '@store/actions/auth.actions';

let defaultCountDownTime = 60;
type Props = { phone: string; countryCode: string; onChange?: () => void; onPress: (object: any) => any };
const ResendCode: React.FC<Props> = ({ phone, onChange, onPress, countryCode }) => {
  const [countdown, setCountdown] = useState(defaultCountDownTime);
  const [isEnd, setIsEnd] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      startTimer();
    });
  }, []);

  useEffect(() => {
    if (countdown <= 0) {
      BackgroundTimer.stopBackgroundTimer();
      setCountdown(defaultCountDownTime);
      setIsEnd(true);
    }
  }, [countdown]);

  const startTimer = () => {
    BackgroundTimer.runBackgroundTimer(() => {
      setCountdown(prev => prev - 1);
    }, 1000);
  };

  const onSubmit = async () => {
    try {
      if (onChange) onChange();
      // requestOTP({ countryCode: '+60', phone: phone });
      setIsEnd(false);
      startTimer();
      onPress({ phone: phone, phoneCountryCode: countryCode });
    } catch (e) {
      pushError(e);
    }
  };

  // const smsLogin = async (phoneNumber : any) =>{
  //   setIsSubmitting(true);

  //   try{
  //     if(phoneNumber){
  //       await requestSMSLogin(phoneNumber);
  //       // if (onSuccess) onSuccess();
  //     }
  //   }catch(e){
  //     pushError(e);
  //   }
  //   setIsSubmitting(false);
  // }

  const formatted = moment.utc(countdown * 1000).format('m:ss');

  return (
    <View>
      <Text fontSize="sm" color="neutrals.gray50" mr={1} mb={4}>
        Did not receive the SMS?
      </Text>
      {!isEnd && (
        <Center>
          <Text fontSize="xs" color="neutrals.gray90" mb={10}>
            {`Resend code in ${formatted}`}
          </Text>
        </Center>
      )}
      {isEnd && (
        <Button
          onPress={onSubmit}
          isDisabled={!isEnd}
          variant="unstyled"
          _pressed={{ bg: 'transparent' }}
          p={0}
          fontSize={14}
          _text={{ color: 'primary.1' }}
          mb={10}
        >
          Resend code
        </Button>
      )}
    </View>
  );
};

export default ResendCode;
