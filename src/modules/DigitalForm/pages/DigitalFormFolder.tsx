import { AppBar, Content, Footer } from '@commons';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { pushError, pushSuccess } from '@src/configs';
import { COLORS } from '@src/constants';
import { TextInput } from '@src/inputs';
import apolloClient from '@src/lib/apollo';
import { DigitalFormNavigatorParams, RootNavigatorParams } from '@src/types';
import { Field, Formik, FormikProps } from 'formik';
import _ from 'lodash';
import { Box, Button } from 'native-base';
import React, { useRef, useState } from 'react';

type Props = CompositeScreenProps<
  BottomTabScreenProps<DigitalFormNavigatorParams, 'DigitalFormFolder'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const DigitalFormFolder: React.FC<Props> = ({ route, navigation }) => {
  const formRef = useRef<FormikProps<FormValues>>(null);
  const category = route.params.category as Gql.CategoryType;
  const pageIndex = Number(_.get(route, 'params.pageIndex', '0'));
  const projectDocumentId = route.params.projectDocumentId;
  const refetch = _.get(route, 'params.refetch', null);
  const [isSubmitting, setSubmitting] = useState<boolean>(false);

  const initialValues = {
    name: ''
  };

  const onSubmit = async (values: FormValues) => {
    if (values.name.trim() === '') {
      pushError('Please enter folder names');
      return;
    }
    const { name } = values;
    const fileSystemType = Gql.FileSystemType.Folder;
    const type = 'folder';
    setSubmitting(true);
    try {
      await apolloClient.mutate<Gql.CreateOneProjectDocumentMutation>({
        mutation: Gql.CreateOneProjectDocumentDocument,
        variables: {
          input: {
            projectDocument: {
              name,
              category,
              fileSystemType,
              type,
              projectDocumentId
            }
          }
        }
      });
      pushSuccess('Folder added successfully');
      navigation.goBack();
      refetch();
    } catch (e) {
      pushError(e);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <AppBar
        goBack
        barStyle={'dark-content'}
        title={'Add Folder'}
        // title={id ? 'Edit folder' : 'Add Folder'}
        noRight
      />

      <Formik onSubmit={onSubmit} initialValues={initialValues} innerRef={formRef}>
        {() => {
          return (
            <Box flex={1} bg="#FFFFFF">
              <Content pt={5}>
                <Field autoFocus name="name" label="Folder name" component={TextInput} />
              </Content>
              <Footer>
                <Button
                  isLoading={isSubmitting}
                  variant="primary"
                  onPress={() => {
                    formRef.current?.handleSubmit();
                  }}
                >
                  Confirm
                </Button>
              </Footer>
            </Box>
          );
        }}
      </Formik>
    </Box>
  );
};

interface FormValues {
  name: string;
}

export default DigitalFormFolder;
