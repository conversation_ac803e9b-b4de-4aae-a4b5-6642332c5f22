import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { ScrollView } from 'react-native';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { Box, Button, Divider, Input, Pressable, Text } from 'native-base';

// Local imports
import { AppBar, Footer, Icon } from '@commons';
import { COLORS } from '@src/constants';
import { Gql } from '@src/api';
import useDeleteRequestForSignature from '@src/mutation/request-for-signature/useDeleteRequestForSignature';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import { useDispatch, useSelector } from '@src/store';
import useDebouncedSearch from '@src/hooks/useDebounceSearch';
import { ProjectUserList } from '../components/ProjectUserList';
import { ErrorBoundary } from '@commons/ErrorBoundary';
import { useProjectUserSearch } from '../hooks/useProjectUserSearch';
import { Assignee, AddAssigneeScreenParams, ProjectUser } from '../hooks/types';

type Props = BottomTabScreenProps<
  {
    AddAssignee: AddAssigneeScreenParams;
  },
  'AddAssignee'
>;

/**
 * AddAssignee Screen Component
 *
 * This component provides functionality to:
 * 1. Search and select users
 * 2. Add or remove assignees
 * 3. Support both Dynamic and non-Dynamic workflows
 */
export const AddAssignee: React.FC<Props> = ({ route, navigation }) => {
  // States
  const [selectedUsers, setSelectedUsers] = useState<Assignee[]>(route.params.ass ?? []);
  const [deletedRequestForSignatureIds, setDeleteRequestForSignatureIds] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Hooks
  const dispatch = useDispatch();
  const { user } = useSelector(states => states?.auth);
  const workflow = route.params.type;
  const workspaceDocument = useSelector(states => states.documentWorkspace);
  const { inputValue, debouncedValue, setInputValue } = useDebouncedSearch(300);
  const { mutateAsync: mutateDeleteRequest } = useDeleteRequestForSignature();

  // Custom hooks
  const { projectUsers, isLoading, error, retry, hasMore, loadMore } = useProjectUserSearch(debouncedValue ?? '');

  // Memoized values
  const selectedIds = useMemo(() => {
    return new Set(selectedUsers.map(user => String(user.signById)));
  }, [selectedUsers]);

  // Effects
  useEffect(() => {
    if (route.params.ass) {
      setSelectedUsers(route.params.ass);
    }
  }, [route.params.ass]);

  // Handlers
  const handleSearch = useCallback(
    (value: string) => {
      setInputValue(value);
    },
    [setInputValue]
  );

  const handleClear = useCallback(() => {
    setInputValue('');
  }, [setInputValue]);

  const handleSelectUser = useCallback(
    (item: ProjectUser) => {
      handleClear();
      if (selectedIds.has(String(item.userId))) {
        if (item.id) {
          setDeleteRequestForSignatureIds(prev => [...prev, item.id]);
        }
        setSelectedUsers(prev => prev.filter(user => user.signById !== item.userId));
      } else {
        const newAssignee: Assignee = {
          ownerId: parseInt(user?.id as string, 10),
          signById: item.userId,
          projectDocumentId: workspaceDocument?.DocumentDetails?.remoteId,
          user: {
            id: String(item.userId),
            name: item.name,
            email: item.email,
            avatarUrl: item.avatarUrl,
            userId: item.userId
          },
          status: Gql.RequestForSignatureStatus.Sent,
          localProjectDocumentId: workspaceDocument.selectedDocumentId as string,
          ...(workflow === Gql.WorkflowType.Dynamic && { assigneeNo: 2 })
        };

        setSelectedUsers(prev => (workflow === Gql.WorkflowType.Dynamic ? [newAssignee] : [...prev, newAssignee]));
      }
    },
    [
      selectedIds,
      user?.id,
      workflow,
      workspaceDocument?.DocumentDetails?.remoteId,
      workspaceDocument.selectedDocumentId,
      handleClear
    ]
  );

  const handleSubmit = useCallback(async () => {
    try {
      setIsSubmitting(true);

      if (deletedRequestForSignatureIds.length > 0) {
        await mutateDeleteRequest(deletedRequestForSignatureIds);
      }

      dispatch(
        documentWorkspaceActions.openDocumentDetailsModal({
          ...workspaceDocument.DocumentDetails,
          id: workspaceDocument.DocumentDetails?.id ?? '',
          selectedAssigneeIds: selectedUsers as unknown as string[]
        })
      );
      dispatch(documentWorkspaceActions.ShouldSetInitialState(false));
      navigation.goBack();
    } catch (error) {
    } finally {
      setIsSubmitting(false);
    }
  }, [
    deletedRequestForSignatureIds,
    workspaceDocument.DocumentDetails,
    selectedUsers,
    dispatch,
    navigation,
    mutateDeleteRequest
  ]);

  return (
    <Box flex={1} bg="#FFFFFF">
      <AppBar
        onPressTitle={() => null}
        header
        goBack
        onGoBack={() => {
          navigation.goBack();
          route.params.onPressBack?.();
        }}
        title="Add Assignee"
        noRight
      />
      <Box flex={1} bg="#FFFFFF">
        <ScrollView style={{ paddingHorizontal: 20 }}>
          <Text mb={4} color={COLORS.neutrals.gray90}>
            {workflow === Gql.WorkflowType.Dynamic ? 'Add Assignee (Single Selection)' : 'Add Assignee'}
          </Text>
          <Divider mb={4} />
          <Input
            placeholder={
              workflow === Gql.WorkflowType.Dynamic
                ? "Enter a member's name (only one user can be selected)"
                : "Enter a member's name"
            }
            width="full"
            borderRadius="4"
            fontSize="14"
            isDisabled={workflow === Gql.WorkflowType.Dynamic && selectedUsers.length > 0}
            onChangeText={handleSearch}
            value={inputValue}
            mb={4}
            {...(!!inputValue?.trim() && {
              InputRightElement: (
                <Pressable px="4" py="6" onPress={handleClear}>
                  <Icon name="cancel" />
                </Pressable>
              )
            })}
          />
          <ErrorBoundary error={error as Error} onRetry={retry}>
            <ProjectUserList
              data={projectUsers}
              selectedIds={selectedIds}
              onSelect={handleSelectUser}
              onEndReached={loadMore}
              isLoading={isLoading}
            />
          </ErrorBoundary>
        </ScrollView>
        <Footer>
          <Button variant="primary" onPress={handleSubmit} isDisabled={isSubmitting} isLoading={isSubmitting}>
            Add Assignee
          </Button>
        </Footer>
      </Box>
    </Box>
  );
};

export default React.memo(AddAssignee);
