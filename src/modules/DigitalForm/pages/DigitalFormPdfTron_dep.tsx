import { Config, DocumentView, RNPdftron, PDFViewCtrl } from 'react-native-pdftron';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { useGetCorrespondenseOutLazyQuery } from '@src/api/graphql';
import { AppBar, Footer } from '@src/commons';
import { pushError, pushSuccess, generateRNFile } from '@src/configs';
import { COLORS } from '@src/constants';
import apolloClient from '@src/lib/apollo';
import { DigitalFormNavigatorParams, RootNavigatorParams } from '@src/types';
import _ from 'lodash';
import { Box, Button } from 'native-base';
import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import { <PERSON><PERSON>, BackHandler, InteractionManager, ModalProps, Platform } from 'react-native';
import RNFetchBlob from 'rn-fetch-blob';
import { Buffer } from 'buffer';
import DocumentNameModal from '../components/Document/DocumentNameModal';

type Props = CompositeScreenProps<
  BottomTabScreenProps<DigitalFormNavigatorParams, 'DigitalFormPdfTron'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

function blobToFile(theBlob: any, fileName: any) {
  theBlob.lastModifiedDate = new Date();
  theBlob.name = fileName;
  return theBlob;
}

const DigitalFormPdfTron: React.FC<Props> = ({ navigation, route }) => {
  const { data: userData, loading: userLoading } = Gql.useGetUserMeQuery({});
  const id = route?.params.id;
  const role = route?.params.role;
  const assigneeIds = route?.params.assigneeIds;
  const addedBy = route?.params.addedBy;
  const viewerRef = useRef<any>();
  const docNameModalRef = useRef<any>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  let view = [] as any;
  const [getPdfDoc, { data }] = Gql.useProjectDocumentLazyQuery({ variables: { id: id } });
  const fileName = data?.projectDocument?.name; // show the file name in header

  const FillAndSign = {
    [Config.CustomToolbarKey.Id]: 'FillAndSign',
    [Config.CustomToolbarKey.Name]: 'Fill And Sign',
    [Config.CustomToolbarKey.Icon]: Config.ToolbarIcons.FillAndSign,
    [Config.CustomToolbarKey.Items]: [
      Config.Tools.annotationCreateSignature,
      Config.Tools.annotationCreateFreeText,
      Config.Tools.formCreateCheckboxField,
      Config.Tools.annotationCreateRubberStamp,
      Config.Tools.annotationCreateFreeTextDate,
      Config.Buttons.undo
    ]
  };

  if (
    assigneeIds === '' ||
    role === 'CloudCoordinator' ||
    role === 'ProjectOwner' ||
    assigneeIds.includes(userData?.getUserMe.id) ||
    addedBy === userData?.getUserMe.id
  ) {
    view = [Config.DefaultToolbars.View, FillAndSign];
  } else {
    view = [Config.DefaultToolbars.View];
  }

  const [updateFile] = Gql.useUpdateOneProjectDocumentMutation({
    onCompleted: () => {
      pushSuccess('File saved successfully');
      setTimeout(() => {
        navigation.goBack();
      }, 1000);
    },
    onError: (err: any) => {
      pushError(err.message);
    }
  });

  const onUpdate = (files: any) => {
    updateFile({
      variables: {
        input: {
          id: _.toString(file?.id),
          update: {
            fileUrl: files
          }
        }
      }
    });
  };

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getPdfDoc();
    });
    RNPdftron.initialize(
      'Bina Cloudtech sdn bhd (bina.cloud):OEM:Bina::IA:AMS(20230928):D7863732B60C5D58B999E101404F3D38DD43F51255922C4C9BD65ABAB6F5C7'
    );
    RNPdftron.enableJavaScript(true);
  }, []);

  const path = data?.projectDocument?.fileUrl ?? '';
  const file = data?.projectDocument;

  if (!path) return null;

  const onLeadingNavButtonPressed = () => {
    if (Platform.OS === 'ios') {
      Alert.alert('App', 'onLeadingNavButtonPressed', [{ text: 'OK', onPress: () => {} }], {
        cancelable: true
      });
    } else {
      navigation.goBack();
    }
  };

  // save the signature / any xfdf
  // const onSavedXfdf = async (xfdf: string) => {
  //   setIsSubmitting(true);
  //   try {
  //     await apolloClient.mutate<Gql.UpdateOneProjectDocumentMutation>({
  //       mutation: Gql.UpdateOneProjectDocumentDocument,
  //       variables: {
  //         input: {
  //           id: _.toString(id),
  //           update: {
  //             xfdf
  //           }
  //         }
  //       }
  //     });
  //     pushSuccess('Saved successfully');
  //   } catch (e) {
  //     pushError(e);
  //   } finally {
  //     setIsSubmitting(false);
  //   }
  // };

  // // duplicate standard form to all form
  // const duplicateStandardForm = async (id: number, xfdf: string) => {
  //   if (file?.category === Gql.CategoryType.StandardForm) {
  //     await apolloClient.mutate<Gql.DuplicateEditedStandardFormInputDto>({
  //       mutation: Gql.CreateOneProjectDocumentDocument,
  //       variables: {
  //         id,
  //         xfdf
  //       }
  //     });
  //   }
  // };

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <AppBar
        goBack
        barStyle={'dark-content'}
        title={fileName}
        onPressTitle={() => {
          docNameModalRef?.current?.pushModal();
        }}
        noRight
      />
      {path && (
        <DocumentView
          ref={viewerRef}
          document={path}
          leadingNavButtonIcon={Platform.OS === 'ios' ? 'ic_close_black_24px.png' : 'ic_arrow_back_white_24dp'}
          showSavedSignatures={true}
          annotationToolbars={view}
          onLeadingNavButtonPressed={onLeadingNavButtonPressed}
          onDocumentLoaded={() => {
            if (file?.category === Gql.CategoryType.AllForm) {
              viewerRef?.current?.importAnnotations(file?.xfdf);
            }
          }}
        />
      )}
      <Footer>
        <Button
          variant="primary"
          isLoading={isSubmitting}
          onPress={async () => {
            await viewerRef.current.flattenAnnotations(false).then(() => {
              viewerRef.current.saveDocument().then((files: any) => {
                const newFile = generateRNFile({
                  uri: files,
                  name: file?.name as string,
                  type: 'application/pdf'
                });
                onUpdate(newFile);
              });
            });

            // setTimeout(() => {
            //   navigation.goBack();
            // }, 1750);

            // viewerRef.current.exportAnnotations().then((filePath: any) => {
            //   //   onSavedXfdf(filePath);
            //   duplicateStandardForm(Number(id), filePath);
            // });
          }}
        >
          Save
        </Button>
      </Footer>

      <DocumentNameModal ref={docNameModalRef} title={fileName} />
    </Box>
  );
};

export default DigitalFormPdfTron;
