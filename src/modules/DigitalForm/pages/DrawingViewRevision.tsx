import React, { useEffect, useRef, useState, useMemo, useCallback } from 'react';
import { useSelector } from '@src/store';
import { ScrollView, TouchableOpacity } from 'react-native';
import { AppBar, Icon, Image } from '@commons';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import DrawingRevisionThreeOptionModal from '@src/modules/Drawing/components/DrawingRevisionThreeOptionModal';
import { Box, Card, HStack, Spinner, Text, VStack, View } from 'native-base';
import Obsolete from '@assets/obsolete.png';
import DrawingRevisionIcon from '@assets/drawing-revision-icon.jpg';
import { useGetDrawingRevision } from '@src/queries/drawing-revision/useGetDrawingRevision';
import { DrawingNavigatorParams, RootNavigatorParams } from '@src/types';

type Props = CompositeScreenProps<
  BottomTabScreenProps<DrawingNavigatorParams, 'DrawingRevision'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

interface DrawingRevisionNode {
  _raw: {
    fileUrl: string;
    fileName: string;
    version: string;
    versionName: string;
    notes?: string;
    id: number;
    remoteId: number;
  };
  id: number;
  remoteId?: number;
  fileUrl?: string;
  localFileUrl?: string;
  isHigherVersion?: boolean;
  version: string;
}

type RevisionItemType = {
  fileName: string;
  version: string;
  id: number;
  versionName: string;
  notes?: string;
};

const ViewRevision: React.FC<Props> = ({ route, navigation }) => {
  const data = route?.params.data;
  const project = useSelector(state => state.project);
  const drawingRevisionThreeOptionModal = useRef<any>(null);
  const [id, setId] = useState<number | null>(null);
  const [obj, setObj] = useState<RevisionItemType | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const {
    data: drawingRevision,
    isRefetching,
    refetch: refetchDrawingRevision
  } = useGetDrawingRevision(data?.remoteId);

  const drawingRevisions: DrawingRevisionNode[] = useMemo(
    () => drawingRevision?.pages.map((page: any) => page.drawingRevisions).flat() || [],
    [drawingRevision, isRefetching]
  );

  const revisionItems: RevisionItemType[] = useMemo(
    () =>
      drawingRevisions.map(node => ({
        fileName: node._raw.fileName,
        version: node._raw.version,
        id: node._raw.id,
        versionName: node._raw.versionName,
        notes: node._raw.notes
      })),
    [drawingRevisions]
  );

  useEffect(() => {
    if (drawingRevisions) {
      setLoading(false);
    }
  }, [drawingRevisions]);

  useEffect(() => {
    if (isRefetching) {
      setLoading(true);
    }
  }, [isRefetching]);

  const handleOptionPress = useCallback((item: RevisionItemType) => {
    setId(item.id);
    setObj(item);
    drawingRevisionThreeOptionModal?.current.pushModal();
  }, []);

  const handlePress = useCallback(
    (item: RevisionItemType) => {
      const selectedRevision = drawingRevisions?.find?.((file: any) => file?.id === item.id);
      return navigation.navigate('PdftronNav', {
        screen: 'Pdftron',
        params: {
          id: selectedRevision?.remoteId ?? selectedRevision?.id,
          modules: 'Revision',
          ...(!selectedRevision?.remoteId && selectedRevision?.fileUrl
            ? { fileUrl: selectedRevision.fileUrl }
            : undefined),
          ...(selectedRevision.localFileUrl && { fileUrl: selectedRevision?.localFileUrl as any }),
          role: project.projectUserRole as string,
          assigneeIds: null,
          projectDocumentId: data?.remoteId,
          isHigherVersion: selectedRevision?.isHigherVersion,
          revisionData: drawingRevisions || [],
          version: selectedRevision?.version
        }
      });
    },
    [drawingRevisions, navigation, data?.remoteId, project.projectUserRole]
  );

  return (
    <View flex={1} bg="#FFFFFF">
      <AppBar
        onPressTitle={() => null}
        header
        goBack
        onGoBack={() => navigation.goBack()}
        title="Drawing Revision"
        noRight
      />
      {loading ? (
        <Spinner size="lg" />
      ) : error ? (
        <Box p={6} alignItems="center">
          <Text color="red.500" fontSize="md">
            {error}
          </Text>
          <TouchableOpacity
            onPress={() => {
              setError(null);
              setLoading(true);
            }}
            style={{ marginTop: 16, padding: 12, backgroundColor: '#007AFF', borderRadius: 8 }}
          >
            <Text color="white">Retry</Text>
          </TouchableOpacity>
        </Box>
      ) : (
        <ScrollView>
          <Box p={6}>
            <VStack>
              {revisionItems.map((item, index) => (
                <Card key={index} mb={4} borderWidth={1} borderColor={'#EEE'} borderRadius={8} alignItems={'center'}>
                  <Text>{item.fileName}</Text>
                  <VStack alignItems="center" space={2}>
                    <HStack space={2} alignItems="center" justifyContent="space-between" width="100%">
                      <View style={{ width: 35 }} />
                      <TouchableOpacity onPress={() => handlePress(item)}>
                        <View style={{ position: 'relative' }}>
                          <Image source={DrawingRevisionIcon} style={{ margin: 15, width: 100, height: 120 }} />
                          {index !== 0 && (
                            <Image
                              source={Obsolete}
                              style={{
                                position: 'absolute',
                                top: '50%',
                                left: '50%',
                                transform: [{ translateX: -50 }, { translateY: -50 }]
                              }}
                            />
                          )}
                        </View>
                      </TouchableOpacity>
                      <TouchableOpacity
                        onPress={() => handleOptionPress(item)}
                        hitSlop={40}
                        style={{ marginRight: 15 }}
                      >
                        <Icon
                          name="three-dots-v"
                          style={{
                            width: 20,
                            height: 20
                          }}
                        />
                      </TouchableOpacity>
                    </HStack>
                  </VStack>
                  <Text>{`Version Name: ${item.versionName}`}</Text>
                  <Text>{`Version No: ${item.version}`}</Text>
                </Card>
              ))}
            </VStack>
          </Box>
        </ScrollView>
      )}
      <DrawingRevisionThreeOptionModal
        ref={drawingRevisionThreeOptionModal}
        data={id}
        obj={obj}
        role={project.projectUserRole as string}
        refetch={() => {
          refetchDrawingRevision();
        }}
      ></DrawingRevisionThreeOptionModal>
    </View>
  );
};

export default ViewRevision;
