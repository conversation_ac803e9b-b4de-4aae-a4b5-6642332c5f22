import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { AppBar, Icon } from '@src/commons';
import { DigitalFormNavigatorParams, RootNavigatorParams } from '@src/types';
import _ from 'lodash';
import { Box, Circle, Skeleton, Stack, VStack } from 'native-base';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import { useSelector } from '@src/store';
import TemplateAddModal from '../components/Template/TemplateAddModal';
import OptionModal from '../components/selector-component/OptionModal';
import { useGetTemplate } from '@src/queries/workspace/useGetWorkspaceTemplate';
import useDebouncedSearch from '@src/hooks/useDebounceSearch';
import ProjectDocumentModel from '@src/database/model/project-document.model';
import SearchInput from '@src/commons/SearchInput';
import DocumentList from '@src/commons/list/DocumentList';
import { getLinkId } from '@src/database/utils/numeric';
import useDeleteWorkspaceTemplate from '@src/mutation/workspace/useDeleteWorkspaceTemplate';
import { pushSuccess } from '@src/configs';
import { copyPdfFile } from '@src/database/utils/copyPdfFile';

type Props = CompositeScreenProps<
  BottomTabScreenProps<DigitalFormNavigatorParams, 'DigitalFormSubFolder'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const DigitalFormSubFolder: React.FC<Props> = ({ navigation, route }) => {
  const id = route.params.id;
  const folderName = route.params.name;
  const category = route.params.category as Gql.CategoryType;
  const addModalRef = useRef<any>(null);
  const pageIndex = Number(_.get(route, 'params.pageIndex', '0'));
  const [filteredValue, setFilteredValue] = useState<string | null>(null);
  const [selectedDocumentId, setSelectedDocumentId] = useState<string | null>(null);
  const [selectedDoc, setSelectedDoc] = useState<any>(null);
  // const [hasNextPage, setHasNextPage] = useState<boolean>(true);
  const project = useSelector((state: any) => state.project);
  const role = project?.role;
  const optionModalRef = useRef<any>(null);
  const email = useSelector(state => state.auth.user?.email);
  const linkId = getLinkId(id);

  const { debouncedValue, setInputValue } = useDebouncedSearch();
  const refetch = () => {};

  const { mutateAsync: deleteDocument } = useDeleteWorkspaceTemplate();

  const {
    data: getProjectDocuments,
    isLoading,
    fetchNextPage,
    hasNextPage
  } = useGetTemplate(debouncedValue ?? '', {
    projectId: project.projectId,
    limit: 10,
    category: Gql.CategoryType.StandardForm,
    pageIndex: 1,
    sorting: 'template',
    ...linkId
  });

  const projectDocuments = useMemo(() => {
    if (!getProjectDocuments) return [];
    return getProjectDocuments.pages.map((page: { results: any[] }) => page.results).flat();
  }, [getProjectDocuments]);

  const loadMoreDocuments = useCallback(() => {
    if (hasNextPage && !isLoading) {
      fetchNextPage();
    }
  }, [hasNextPage, isLoading, fetchNextPage]);

  const onSearchInputChange = (newSearchTerm: any) => {
    setFilteredValue(newSearchTerm);
    setInputValue(newSearchTerm);
  };

  const onItemPress = async (item: ProjectDocumentModel) => {
    if (item.fileSystemType === Gql.FileSystemType.Folder) {
      navigation.push('DigitalFormNav', {
        screen: 'DigitalFormSubFolder',
        params: {
          id: item.remoteId ? (item.remoteId as any) : item.id,
          name: item.name ?? '',
          category: item.category ?? '',
          refetch
        }
      });
    }
    if (item.fileSystemType === Gql.FileSystemType.Document) {
      navigation.navigate('PdftronNav', {
        screen: 'Pdftron',
        params: {
          id: item?.remoteId as any,
          role: role ?? '',
          assigneeIds: '',
          addedBy: item.addedBy as any,
          modules: 'Templates',
          ...(item.localFileUrl && {
            fileUrl: await copyPdfFile(item?.name, item?.localFileUrl, project?.projectId as any)
          }),
          ...(item.remoteId === 0 && {
            fileUrl: await copyPdfFile(item?.name, item?.fileUrl as any, project?.projectId as any)
          }),
          ...linkId,
          xfdf: item.xfdf as any
        }
      });
    }
  };

  const onOptionPress = (item: ProjectDocumentModel) => {
    optionModalRef?.current?.pushModal();
    setSelectedDoc(item);
  };

  const deleteDocumentHandler = async (id: string) => {
    try {
      await deleteDocument(id).then(() => {
        pushSuccess('Document deleted successfully');
        optionModalRef?.current?.closeModal();
      });
    } catch (error) {}
  };

  return (
    <Box height="full" bg="white">
      <AppBar goBack barStyle={'dark-content'} title={folderName} noRight onPressTitle={() => null} />

      {/* Add button */}
      <TouchableOpacity
        style={[styles.addButton, { zIndex: 1000 }]}
        onPress={() => {
          addModalRef?.current?.pushModal();
        }}
      >
        <Circle size="65px" bg="#0695D7">
          <Icon name="plus" />
        </Circle>
      </TouchableOpacity>

      <SearchInput
        filteredValue={filteredValue}
        setFilteredValue={onSearchInputChange}
        placeholder="Search for drawings"
      />

      {/* All Data */}
      {isLoading ? (
        <SkeletonComponent />
      ) : (
        <DocumentList
          documents={projectDocuments}
          isLoading={isLoading}
          loadMoreDocuments={loadMoreDocuments}
          refetch={() => {}}
          onItemPress={onItemPress}
          onOptionPress={onOptionPress}
          onAction={false}
          setOnAction={() => {}}
        />
      )}
      <OptionModal
        ref={optionModalRef}
        selectedDocumentId={selectedDoc?.id}
        onDelete={value => deleteDocumentHandler(value)}
        data={selectedDoc}
        refetch={() => {
          refetch();
        }}
      />
      <TemplateAddModal ref={addModalRef} projectDocumentId={id} category={category} refetch={() => refetch()} />
    </Box>
  );
};

const SkeletonComponent: React.FC<any> = () => {
  const SkeletonItem = () => {
    return (
      <VStack mb={5}>
        <Skeleton style={{ height: 70, padding: 14, borderRadius: 12 }} />
        <VStack space={1}></VStack>
      </VStack>
    );
  };
  return (
    <Stack p={5}>
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
    </Stack>
  );
};

const styles = StyleSheet.create({
  box: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden'
  },
  addButton: {
    position: 'absolute',
    bottom: 10,
    marginBottom: 50,
    alignSelf: 'flex-end',
    right: '7%',
    shadowColor: 'rgba(0, 0, 0, 0.5)',
    shadowOpacity: 1,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 6 },
    backgroundColor: '#FFFFFF',
    borderRadius: 9999
  }
});

export default DigitalFormSubFolder;
