import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ScrollableTabBar } from '@src/commons';
import CustomAppBar from '@src/commons/CustomAppBar';
import { COLORS } from '@src/constants';
import { DigitalFormNavigatorParams, RootNavigatorParams } from '@src/types';
import _ from 'lodash';
import { Box, Button, HStack, VStack } from 'native-base';
import React, { useRef } from 'react';
import { ScrollView, StyleSheet, TouchableOpacity } from 'react-native';
import DocumentComponent from '../components/MovingFile/DocumentComponent';
import TemplateComponent from '../components/MovingFile/TemplateComponent';

type Props = CompositeScreenProps<
  BottomTabScreenProps<DigitalFormNavigatorParams, 'MovingFile'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const MovingFile: React.FC<Props> = ({ navigation, route }) => {
  const pageIndex = Number(_.get(route, 'params.pageIndex', '0'));
  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <CustomAppBar header barStyle={'light-content'} />

      <ScrollView
        key={pageIndex}
        // renderTabBar={() => (
        //   <ScrollableTabBar
        //     activeTextColor="#0695D7"
        //     inactiveTextColor={COLORS.neutrals.gray70}
        //     containerStyle={styles.tabContainer}
        //     ref={tabBarRef}
        //   />
        // )}
      >
        {/* <DocumentComponent tabLabel="Documents" /> */}
        <TemplateComponent docId={_.get(route, 'params.docId', 'params.category')} />
      </ScrollView>
    </Box>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    backgroundColor: '#FFFFFF'
  }
});

export default MovingFile;
