import { AppBar, Icon } from '@commons';
import { FormikProps } from 'formik';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps, useFocusEffect, useNavigation } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackNavigationProp } from '@react-navigation/stack';
import { Gql } from '@src/api';
import LinkedToModal from '@src/commons/LinkedToModal';
import { pushError, pushMessaage } from '@src/configs';
import { COLORS } from '@src/constants';
import { manageDocuments } from '@src/lib/authority';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import { useDispatch, useSelector } from '@src/store';
import { DigitalFormNavigatorParams, RootNavigatorParams } from '@src/types';
import _ from 'lodash';
import { <PERSON>, Button, Divider, <PERSON><PERSON>tack, ScrollView, Text } from 'native-base';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import useSyncDisabler from '@src/hooks/useSyncDisabler';
import { Alert, StyleSheet } from 'react-native';
import CommentComponent from '../components/Document/CommentComponent';
import NonConformanceReport from '../components/Document/NonConformanceReport';
import AddAttachmentPhotoModal from '../components/selector-component/AddAttachmentPhotoModal';
import GroupSelectorModal from '../components/selector-component/GroupSelectorModal';
import { useGetDocumentDetail } from '@src/queries/workspace/useGetWorkspaceDocumentDetail';
import useValidationLinear from '@src/mutation/request-for-signature/useValidationLinear';
import { DocumentForm } from '../components/Document/DocumentForm';
import { CreatedBy } from '../components/Document/CreatedBy';
import StatusComponent from './component/Status';
import GroupComponent from './component/Group';
import AssigneeComponent from './component/Assignee';
import CcComponent from './component/Cc';
import AttachmentsComponent from './component/Attachments';
import MediaComponent from './component/Media';
import LinkedDocumentsComponent from './component/LinkedToDocument';
import ProjectDocumentModel from '@src/database/model/project-document';

type Props = CompositeScreenProps<
  BottomTabScreenProps<DigitalFormNavigatorParams, 'DocumentDetail'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const DocumentDetail: React.FC<Props> = props => {
  // Disable sync while in document detail page
  useSyncDisabler();

  const selectActivityRef = useRef<any>(null);
  const addAttachmentPhotoRef = useRef<any>(null);
  const linkedToRef = useRef<any>(null);
  const groupSelectorModalRef = useRef<any>(null);
  const formRef = useRef<FormikProps<any>>(null);
  const [type, setType] = useState('Comments');
  const [loading, setLoading] = useState(false);
  const project = useSelector(state => state.project);
  const workspaceDocuments = useSelector(state => state.documentWorkspace);
  const params: any = props.navigation.getState().routes[props.navigation.getState().index].params;
  const redirect = _.get(props?.route, 'params.redirect', null);
  const scrollViewRef = useRef<any>(null);

  const { user: me } = useSelector(state => state.auth);
  const loggedInUser = me?.id;

  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();

  const dispatch = useDispatch();
  const states = useSelector(state => state.documentWorkspace);
  const userMeData = useMemo(() => me, [me]);

  // hooks //
  const {
    data: getProjectDocuments,
    isFetching,
    isPending
  } = useGetDocumentDetail(states?.selectedDocumentId as string);
  const { mutateAsync: updateValidation } = useValidationLinear();

  // For backward compatibility with older code
  const isLoading = isPending;

  // load the document details
  useFocusEffect(
    React.useCallback(() => {
      if (states.selectedDocumentId && !isFetching && getProjectDocuments && workspaceDocuments?.setInitialState) {
        dispatch(
          documentWorkspaceActions.setDocumentDetails({
            id: getProjectDocuments?.document?.id,
            name: getProjectDocuments?.document?.name,
            description: getProjectDocuments?.document?.description,
            group: getProjectDocuments?.group,
            attachment: getProjectDocuments?.attachments,
            photos: getProjectDocuments?.photos,
            linkedDocuments: getProjectDocuments?.linkedToDocument,
            selectedAssigneeIds: getProjectDocuments?.requestForSignature,
            selectedCcs: getProjectDocuments?.workspaceCcs,
            status: getProjectDocuments?.status,
            remoteId: getProjectDocuments?.document?.remoteId,
            createdBy: getProjectDocuments?.createdBy
          })
        );
      }
    }, [states.selectedDocumentId, isFetching, workspaceDocuments?.setInitialState])
  );

  const isRFI = getProjectDocuments?.group?.parent?.name === 'Request For Information';
  const isNCR = getProjectDocuments?.group?.parent?.name === 'Non Conformance Report';

  const isDocumentOwner = useMemo(
    () => getProjectDocuments?.createdBy?.userId === parseInt(me?.id ?? '0'),
    [getProjectDocuments, me]
  );

  const currentUserStatus = getProjectDocuments?.requestForSignature
    ?.filter((signature: any) => signature.signById === loggedInUser)
    .map((signature: any) => signature.status);

  const workspaceAssignees = getProjectDocuments?.requestForSignature?.map((ass: any) => ass.signById) ?? [];
  const isAssigned = workspaceAssignees.includes(me?.id as any);

  const role = manageDocuments(project.projectUserRole ?? '', '', getProjectDocuments?.status as any, isAssigned);

  const isDraftandApproved =
    getProjectDocuments?.status === Gql.ProjectDocumentStatus.Draft ||
    getProjectDocuments?.status === Gql.ProjectDocumentStatus.Approved;

  const currentUserSignature = getProjectDocuments?.requestForSignature?.find((s: any) => {
    return s.signById == loggedInUser;
  });

  const isAlreadySigned = currentUserSignature?.status === 'Approved';

  const chooseLinkedDocument = async (file: ProjectDocumentModel) => {
    const { id, ...rest } = file?._raw as any;

    dispatch(
      documentWorkspaceActions.setDocumentDetails({
        ...workspaceDocuments.DocumentDetails,
        id: getProjectDocuments?.document?.id,
        linkedDocuments: [
          ...(workspaceDocuments.DocumentDetails?.linkedDocuments ?? []),
          {
            ...rest,
            documentId: rest?.remoteId as number,
            projectDocumentId: workspaceDocuments?.DocumentDetails?.remoteId as number,
            localProjectDocumentId: getProjectDocuments?.document?.id
          }
        ] as any
      })
    );
  };

  const onBackPress = () => {
    dispatch(documentWorkspaceActions.ShouldSetInitialState(false));
  };

  const handleAction = async (action: string, type: string) => {
    const confirmMessage =
      action === 'approve'
        ? `Are you sure you want to ${isRFI ? 'answer' : 'approve'} this document?`
        : action === 'reject'
          ? 'Are you sure you want to reject this document?'
          : action === 'requestAmend'
            ? 'Are you sure you want to amend this document?'
            : action === 'resubmit'
              ? 'Are you sure you want to resubmit this document?'
              : action === 'inReview'
                ? 'Are you sure you want to review this document?'
                : 'Are you sure you want to make partial approval this document?';

    const successMessage =
      action === 'approve'
        ? 'Document Approved'
        : action === 'reject'
          ? 'Document Rejected'
          : action === 'requestAmend'
            ? 'Document Amended'
            : action === 'resubmit'
              ? 'Document Resubmitted'
              : action === 'inReview'
                ? 'Document In Review'
                : 'Sent to owner';

    const confirm = await new Promise<boolean>(resolve =>
      Alert.alert(
        'Confirmation',
        confirmMessage,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Confirm',
            onPress: () => resolve(true)
          }
        ],
        { cancelable: false }
      )
    );

    if (confirm) {
      try {
        await updateValidation({
          documentId: getProjectDocuments?.document?.id,
          requestForSignatureId: currentUserSignature?.id,
          type: type as any
        });
        dispatch(documentWorkspaceActions.ShouldSetInitialState(true));
        pushMessaage(successMessage, 'success');
      } catch (e) {
        pushError((e as any).message);
      }
    }
  };

  const onView = async () => {
    const assigneeId = getProjectDocuments?.requestForSignature?.map((obj: any) => obj.signById);
    dispatch(documentWorkspaceActions.closeAll());
    navigation.navigate('PdftronNav', {
      screen: 'Pdftron',
      params: {
        id: getProjectDocuments?.document?.remoteId?.toString?.() ?? '',
        localId: getProjectDocuments?.document?.id ?? '',
        role: project.projectUserRole ?? '',
        assigneeIds: assigneeId ?? '',
        addedBy: getProjectDocuments?.document?.addedBy ?? '',
        modules: Gql.CategoryType.AllForm,
        driveType: getProjectDocuments?.document?.driveType,
        status: getProjectDocuments?.document?.status as any,
        currentUserStatus: currentUserStatus as any,
        workflow: getProjectDocuments?.document?.workflow,
        ...(getProjectDocuments?.document?._status !== 'synced' &&
          getProjectDocuments?.document?.fileUrl !== null && {
            fileUrl:
              !getProjectDocuments?.document?.remoteId || !getProjectDocuments?.document?.fileUrl?.startsWith('All')
                ? getProjectDocuments?.document?.fileUrl
                : null
          })
      }
    });
  };

  const onInReview = () => handleAction('inReview', 'In Review');
  const onReject = () => handleAction('reject', 'Reject');
  const onApprove = () => handleAction('approve', 'Approve');

  useEffect(() => {
    if (params?.id) {
      dispatch(documentWorkspaceActions.setDocumentId(params?.id));
    }
  }, [params?.id]);

  const redirectHandler = useCallback(() => {
    if (!redirect || states?.isFetchingComments) return;

    if (redirect === 'comment') {
      scrollViewRef?.current?.scrollToEnd({ animated: true });
    }
  }, [redirect, states?.isFetchingComments]);

  useEffect(() => {
    redirectHandler();
  }, [redirectHandler]);

  let getBtn;

  if (getProjectDocuments?.group?.parent?.name === 'Non Conformance Report') {
    getBtn = (
      <NonConformanceReport
        currentUserSignature={currentUserSignature}
        onInReview={onInReview}
        onApprove={onApprove}
        onReject={onReject}
        onView={onView}
        workflow="Linear"
        documentStatus={getProjectDocuments?.status as any}
      />
    );
  } else {
    getBtn = (
      <HStack space={2} alignItems="center">
        {getProjectDocuments?.status == Gql.ProjectDocumentStatus.Submitted &&
          (currentUserSignature &&
          (currentUserSignature?.status === 'Sent' || currentUserSignature?.status === 'Pending') ? (
            <Button h={38} onPress={onInReview} alignSelf="flex-end" height={10} backgroundColor={'#F29100'} mt={1}>
              In Review
            </Button>
          ) : (
            <Button h={38} alignSelf="flex-end" height={10} disabled backgroundColor={COLORS.neutrals.gray70} mt={1}>
              Pending Approval
            </Button>
          ))}

        <Button
          alignSelf="flex-end"
          h={10}
          pb={1}
          backgroundColor={COLORS.neutrals.white}
          mt={1}
          style={{
            borderColor: 'silver',
            borderWidth: 1
          }}
          onPress={onView}
        >
          <Text color={'#0695D7'}>View</Text>
        </Button>
        {getProjectDocuments?.status == Gql.ProjectDocumentStatus.InReview &&
        currentUserSignature &&
        currentUserSignature?.status !== 'Approved' &&
        currentUserSignature?.status !== 'Rejected' ? (
          <>
            <Button h={38} onPress={onApprove} alignSelf="flex-end" height={10} backgroundColor={'#18A601'} mt={1}>
              {!isRFI ? 'Approve' : 'Answer'}
            </Button>
            {!isRFI && (
              <Button h={38} onPress={onReject} alignSelf="flex-end" height={10} backgroundColor={'#F40F02'} mt={1}>
                Reject
              </Button>
            )}
          </>
        ) : null}
      </HStack>
    );
  }

  return (
    <Box bg="white" height="full">
      <AppBar
        goBack
        onGoBack={() => {
          dispatch(documentWorkspaceActions.resetAll());
          dispatch(documentWorkspaceActions.ShouldSetInitialState(true));
          return props?.navigation?.goBack();
        }}
        barStyle={'dark-content'}
        title={`Document Detail ${
          getProjectDocuments?.document?.allFormCode ? `#${getProjectDocuments?.document?.allFormCode}` : ''
        }`}
        rightComponent={<Icon name="linear-workspace"></Icon>}
        onPressTitle={() => {}}
      />

      <ScrollView height={'80%'} keyboardShouldPersistTaps="handled" ref={scrollViewRef}>
        <Box flexDirection="row" mt={4} justifyContent="space-between" px={6} minHeight={50}>
          {getBtn}

          {getProjectDocuments?.status !== Gql.ProjectDocumentStatus.Approved && (
            <Button
              h={38}
              alignSelf="flex-end"
              height={10}
              variant="primary"
              mt={1}
              onPress={() => formRef.current?.submitForm()}
            >
              Save
            </Button>
          )}
        </Box>
        <Box padding={6}>
          <>
            <DocumentForm
              initialData={{
                name: getProjectDocuments?.document?.name ?? '',
                description: states.DocumentDetails?.description ?? ''
              }}
              formRef={formRef}
              onBackPress={onBackPress}
              refetch={props?.route.params?.refetch}
              navigation={props?.navigation}
              status={getProjectDocuments?.status}
            />
            <CreatedBy
              user={{
                id: getProjectDocuments?.createdBy?.userId,
                name: getProjectDocuments?.createdBy?.name,
                avatarUrl: getProjectDocuments?.createdBy?.avatarUrl
              }}
            />
            <Divider />
            <StatusComponent status={getProjectDocuments?.status} />
            <Divider />
            <GroupComponent
              documentStatus={getProjectDocuments?.status}
              isDocumentOwner={isDocumentOwner}
              role={role}
              isDraftandApproved={isDraftandApproved}
              groupSelectorModalRef={groupSelectorModalRef}
              groupName={workspaceDocuments?.DocumentDetails?.group?.name ?? ''}
            />
            <Divider />
            <AssigneeComponent
              assignees={workspaceDocuments?.DocumentDetails?.selectedAssigneeIds}
              role={role}
              isDocumentOwner={isDocumentOwner}
              isDraftandApproved={isDraftandApproved}
              dispatch={dispatch}
              documentWorkspaceActions={documentWorkspaceActions}
              navigation={navigation}
              onBackPress={onBackPress}
              status={getProjectDocuments?.status}
              workflow={Gql.WorkflowType.Linear}
              currentAssigneeId={workspaceDocuments?.DocumentDetails?.currentAssigneeId ?? null}
              //@ts-ignore
              localCurrentAssigneeId={workspaceDocuments?.DocumentDetails?.localCurrentAssigneeId}
            />
            <Divider />
            <CcComponent
              ccs={workspaceDocuments?.DocumentDetails?.selectedCcs}
              role={role}
              isDocumentOwner={isDocumentOwner}
              isDraftandApproved={isDraftandApproved}
              dispatch={dispatch}
              documentWorkspaceActions={documentWorkspaceActions}
              navigation={navigation}
              isAlreadySigned={isAlreadySigned}
              status={getProjectDocuments?.status}
            />
            <Divider />
            <AttachmentsComponent
              getProjectDocuments={getProjectDocuments}
              loading={loading}
              onBackPress={onBackPress}
              navigation={navigation}
              isDocumentOwner={isDocumentOwner}
              role={role}
              isAssigned={isAssigned}
              isAlreadySigned={isAlreadySigned}
              attachments={workspaceDocuments?.DocumentDetails?.attachment as any}
              dispatch={dispatch}
            />
            <Divider />
            <MediaComponent
              loading={loading}
              onBackPress={onBackPress}
              navigation={navigation}
              addAttachmentPhotoRef={addAttachmentPhotoRef}
              isDocumentOwner={isDocumentOwner}
              role={role}
              isAssigned={isAssigned}
              isAlreadySigned={isAlreadySigned}
              photos={workspaceDocuments?.DocumentDetails?.photos as any}
              dispatch={dispatch}
              documentStatus={getProjectDocuments?.status}
            />
            <Divider />
            <LinkedDocumentsComponent
              onBackPress={onBackPress}
              navigation={navigation}
              linkedToRef={linkedToRef}
              isDocumentOwner={isDocumentOwner}
              role={role}
              isAssigned={isAssigned}
              isAlreadySigned={isAlreadySigned}
              dispatch={dispatch}
              documentStatus={getProjectDocuments?.status}
              linkedDocuments={workspaceDocuments?.DocumentDetails?.linkedDocuments as any}
              key={getProjectDocuments?.linkedToDocument?.length}
            />
            {getProjectDocuments?.document?.status !== Gql.ProjectDocumentStatus.Draft && (
              <>
                <Box flexDirection="row" justifyContent="space-between" mt={5}>
                  <Text fontWeight={600} mt={2}>
                    Activity
                  </Text>
                  <Box>
                    <HStack alignItems="center">
                      <Text color="neutrals.gray90"> Show: </Text>
                      <Box style={styles.box}>
                        <HStack alignItems="center">
                          <Text lineHeight="34">{type}</Text>
                        </HStack>
                      </Box>
                    </HStack>
                  </Box>
                </Box>
                <CommentComponent
                  selectedDocumentId={getProjectDocuments?.document?.remoteId}
                  comments={getProjectDocuments?.comments}
                />
              </>
            )}
          </>
        </Box>
      </ScrollView>
      <AddAttachmentPhotoModal ref={addAttachmentPhotoRef} />
      <GroupSelectorModal ref={groupSelectorModalRef} />
      <LinkedToModal ref={linkedToRef} setLinkedDocument={chooseLinkedDocument} />
    </Box>
  );
};

const styles = StyleSheet.create({
  box: {
    borderWidth: 1,
    borderColor: '#E8E8E8',
    borderRadius: 36,
    width: 114,
    height: 38,
    alignItems: 'center'
  }
});

DocumentDetail.displayName = 'DocumentDetail';
export default DocumentDetail;
