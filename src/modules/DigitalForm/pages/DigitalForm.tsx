import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import CustomAppBar from '@src/commons/CustomAppBar';
import { COLORS } from '@src/constants';
import { DigitalFormNavigatorParams, RootNavigatorParams } from '@src/types';
import _ from 'lodash';
import { Box } from 'native-base';
import React, { useRef, useState } from 'react';
import { Dimensions, StyleSheet } from 'react-native';
import ScrollableTabView, { DefaultTabBar } from 'react-native-scrollable-tab-view';
import AllForm from '../components/Document/AllForm';
import Overview from '../components/Overview/Overview';
import StandardForm from '../components/Template/StandardForm';
import { useDispatch, useSelector } from '@src/store';
import { ProjectAccess } from '@src/constants/subscription';
import { Gql } from '@src/api';
import { isAllowed } from '@src/lib/helper';

type Props = CompositeScreenProps<
  BottomTabScreenProps<DigitalFormNavigatorParams, 'DigitalForm'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const DigitalForm: React.FC<Props> = ({ route }) => {
  const tabBarRef = useRef<any>(null);
  // const pageIndex = Number(_.get(route, 'params.pageIndex', '0'));
  const pageIndex = '0';
  const [currentTab, setCurrentTab] = useState('0');
  const dispatch = useDispatch();
  const { filterItems } = useSelector(state => state.documentWorkspace);
  const WINDOW_WIDTH = Dimensions.get('window').width;
  //   variables: {
  //     filter: {
  //       projectId: { eq: project.projectId ?? '' },
  //       category: { eq: Gql.CategoryType.AllForm },
  //       projectDocumentId: { eq: null },
  //       name: { like: `%${filteredValue}%` }
  //     },
  //     paging: {
  //       limit: 20,
  //       offset: 0
  //     }
  //   },
  //   fetchPolicy: 'cache-and-network'
  // });

  //! please enable this later
  // const companySubscriptions = Gql.useCompanySubscriptionsQuery({
  //   variables: {
  //     sorting: {
  //       direction: Gql.SortDirection.Desc,
  //       field: Gql.CompanySubscriptionSortFields.SubscriptionEndDate
  //     },
  //     paging: {
  //       limit: 1
  //     }
  //   },
  //   fetchPolicy: 'cache-and-network'
  // });

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <CustomAppBar
        header
        barStyle={'light-content'}
        // rightComponent={
        //   currentTab == 1 && (
        //     <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        //       <Button
        //         _pressed={{
        //           bg: 'transparent',
        //           opacity: 0.8
        //         }}
        //         bg="transparent"
        //         justifyContent="flex-end"
        //         onPress={() => dispatch(documentWorkspaceActions.openDocumentFilterModal())}
        //       >
        //         <Icon name="filtering" />
        //       </Button>
        //       {filterItems.assignToMe === true ||
        //         filterItems.allFormCode ||
        //         filterItems.assigneeId ||
        //         filterItems.assigneeName ||
        //         filterItems.groupId ||
        //         filterItems.groupName ||
        //         filterItems.id ||
        //         filterItems.status ? (
        //         <Box
        //           zIndex={1}
        //           width={3}
        //           height={3}
        //           borderRadius={'full'}
        //           position="absolute"
        //           top={2.5}
        //           right={3}
        //           bg={'red.500'}
        //         />
        //       ) : null}
        //     </View>
        //   )
        // }
      />

      {/* {companySubscriptions && ( */}
      <ScrollableTabView
        key={pageIndex}
        initialPage={1}
        style={{ backgroundColor: '#FFFFFF' }}
        tabBarInactiveTextColor="#000"
        tabBarActiveTextColor="#0695D7"
        tabBarUnderlineStyle={{ backgroundColor: '#0695D7', height: 2 }}
        scrollWithoutAnimation={true}
        locked={true}
        // renderTabBar={() => (
        //   <ScrollableTabBar
        //     activeTextColor="#0695D7"
        //     inactiveTextColor={COLORS.neutrals.gray70}
        //     containerStyle={styles.tabContainer}
        //     ref={tabBarRef}
        //     onChange={(tab: number) => setCurrentTab(tab)}
        //   />
        // )}
        renderTabBar={() => <DefaultTabBar style={{ height: 40, marginTop: 10 }} tabStyle={{ height: 40 }} />}
      >
        {/* {isAllowed(companySubscriptions, ProjectAccess.WORKSPACE_DOCUMENT) && <Overview tabLabel="Overview" />}
          {isAllowed(companySubscriptions, ProjectAccess.WORKSPACE_DOCUMENT) && <AllForm tabLabel="Documents" />}
          {isAllowed(companySubscriptions, ProjectAccess.WORKSPACE_TEMPLATE) && <StandardForm tabLabel="Templates" />} */}
        <Overview tabLabel="Overview" />
        <AllForm tabLabel="Documents" />
        <StandardForm tabLabel="Templates" />
      </ScrollableTabView>
      {/* )} */}
    </Box>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    backgroundColor: '#FFFFFF'
  }
});

export default DigitalForm;
