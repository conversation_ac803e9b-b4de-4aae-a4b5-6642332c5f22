import { AppBar, Content, Footer } from '@commons';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { pushError, pushSuccess } from '@src/configs';
import { COLORS } from '@src/constants';
import { TextInput } from '@src/inputs';
// import apolloClient from '@src/lib/apollo';
import { DigitalFormNavigatorParams } from '@src/types';
import { Field, Formik, FormikProps } from 'formik';
import _ from 'lodash';
import { Box, Button } from 'native-base';
import React, { useRef, useState } from 'react';
import useCreateProjectDocument from '@src/mutation/cloud-docs/useCreateCloudDocs';
import { CreateProjectDocumentInput } from 'cloud-docs';
import { useSelector } from '@src/store';
import { getLinkId } from '@src/database/utils/numeric';

type Props = NativeStackScreenProps<DigitalFormNavigatorParams, 'CreateFolder'>;

const Folder: React.FC<Props> = ({ route, navigation }) => {
  const id = route?.params?.id;
  const projectDocumentId = route?.params?.projectDocumentId;
  const refetch = _.get(route, 'params.refetch', null);
  const formRef = useRef<FormikProps<FormValues>>(null);
  const { data, loading } = Gql.useProjectDocumentQuery({ variables: { id: id ?? '' } });
  const [isSubmitting, setSubmitting] = useState<boolean>(false);
  const driveType = route.params.driveType ?? '';
  const { mutate: createFolder, isPending } = useCreateProjectDocument();
  const project = useSelector(state => state.project);

  const initialValues = {
    name: data?.projectDocument?.name ?? ''
  };

  if (loading) return null;

  const onSubmit = async (values: FormValues) => {
    if (values.name.trim() === '') {
      pushError('Please enter folder name');
      return;
    }
    const { name } = values;
    const fileSystemType = Gql.FileSystemType.Folder;
    const type = 'folder';
    const category = route?.params?.category;
    const linkId = getLinkId(projectDocumentId);

    const newDocs: CreateProjectDocumentInput = {
      name: name,
      projectId: parseInt(project?.projectId ?? '0'),
      category: category,
      fileSystemType: fileSystemType,
      type: type,
      ...linkId,
      ...(driveType ? { driveType } : {})
    };

    setSubmitting(true);
    try {
      createFolder([newDocs]);
      pushSuccess('Folder name updated successfully');
      refetch();
    } catch (e) {
      pushError(e);
    } finally {
      setSubmitting(false);
      navigation?.goBack();
    }
  };

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <AppBar goBack barStyle={'dark-content'} title={id ? 'Edit folder' : 'Create ' + driveType + ' Folder'} noRight />

      <Formik onSubmit={onSubmit} initialValues={initialValues} innerRef={formRef}>
        {() => {
          return (
            <Box flex={1} bg="#FFFFFF">
              <Content pt={5}>
                <Field autoFocus name="name" label="Folder name" component={TextInput} />
              </Content>
              <Footer>
                <Button
                  variant="primary"
                  isLoading={isSubmitting}
                  onPress={() => {
                    formRef.current?.handleSubmit();
                  }}
                >
                  {'Create ' + driveType + ' Folder'}
                </Button>
              </Footer>
            </Box>
          );
        }}
      </Formik>
    </Box>
  );
};

interface FormValues {
  name: string;
}

export default Folder;
