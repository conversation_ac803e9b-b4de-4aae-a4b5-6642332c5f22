import React from 'react';
import { Box, HStack, Text, VStack, FlatList } from 'native-base';
import { TouchableOpacity } from 'react-native';
import { Icon } from '@src/commons/Icon';
import { FlashList } from '@shopify/flash-list';
import { Gql } from '@src/api';
import Avatar from '@commons/Avatar';

interface CcComponentProps {
  ccs: any;
  navigation: any;
  dispatch: React.Dispatch<any>;
  documentWorkspaceActions: any;
  isDocumentOwner: boolean;
  role: any;
  isDraftandApproved: boolean;
  isAlreadySigned: boolean;
  status: Gql.ProjectDocumentStatus;
}

const CcComponent: React.FC<CcComponentProps> = ({
  ccs,
  navigation,
  dispatch,
  documentWorkspaceActions,
  isDocumentOwner,
  role,
  isDraftandApproved,
  isAlreadySigned,
  status
}) => {
  return (
    <HStack mt={4} mb={4}>
      <Box flex={1} flexDirection="row" alignItems="center">
        <Icon name="assignee" />
        <Text color="neutrals.gray90"> Cc</Text>
      </Box>
      <Box flex={2}>
        <TouchableOpacity
          onPress={() => {
            if (status !== 'Approved') {
              navigation.navigate('DigitalFormNav', {
                screen: 'AddCc',
                params: {
                  documentId: '',
                  ccs: ccs ?? []
                }
              });
              dispatch(documentWorkspaceActions.closeAll());
            }
          }}
          disabled={(!isDocumentOwner && !role.canEdit) || !isDraftandApproved || isAlreadySigned}
        >
          <FlatList
            data={ccs}
            keyExtractor={(item: any) => item?.id?.toString?.()}
            renderItem={({ item }) => (
              <HStack space={1} flexWrap="wrap" alignItems="flex-start" mt={1}>
                {/* <Avatar size="20px" source={{ uri: item?.user?.avatarUrl ?? undefined }} /> */}
                <Avatar
                  assignees={[{ assigneeNo: '1', avatarUrl: item.user?.avatarUrl ?? '', name: item.user?.name ?? '' }]}
                  maxVisible={1}
                  type={'task'}
                />
                <Text numberOfLines={1} ellipsizeMode="tail" maxWidth={'80%'} mt={1}>
                  {item?.user?.name}
                </Text>
              </HStack>
            )}
            ListEmptyComponent={
              <Text color={!role.canEdit && !isDocumentOwner ? 'gray.400' : 'neutrals.gray90'}>No Cc set</Text>
            }
          />
          {status !== 'Approved' && (
            <Text color={!role.canEdit && !isDocumentOwner ? 'gray.400' : 'neutrals.gray90'}>+ Add Cc</Text>
          )}
        </TouchableOpacity>
      </Box>
    </HStack>
  );
};

export default CcComponent;
