import React, { useCallback } from 'react';
import { Box, HStack, Text } from 'native-base';
import { TouchableOpacity } from 'react-native';
import { Icon } from '@src/commons/Icon';
import { Gql } from '@src/api';
import { FlashList } from '@shopify/flash-list';
import Avatar from '@commons/Avatar';

interface AssigneeComponentProps {
  assignees: any;
  navigation: any;
  onBackPress: () => void;
  dispatch: React.Dispatch<any>;
  documentWorkspaceActions: any;
  isDocumentOwner: boolean;
  role: any;
  isDraftandApproved: boolean;
  status: Gql.ProjectDocumentStatus;
  workflow: Gql.WorkflowType;
  currentAssigneeId: string | null;
  localCurrentAssigneeId: string;
}

const AssigneeComponent: React.FC<AssigneeComponentProps> = ({
  assignees,
  navigation,
  onBackPress,
  dispatch,
  documentWorkspaceActions,
  isDocumentOwner,
  role,
  isDraftandApproved,
  status,
  workflow,
  currentAssigneeId,
  localCurrentAssigneeId
}) => {
  const getColour = (user: any) => {
    if (user.status === 'Approved') return '#DEFABB';
    if (user.status === 'Rejected') return '#F5B6DA';
    return 'transparent';
  };

  const renderItem = useCallback(
    ({ item }: { item: any }) => {
      return (
        <HStack
          space={1}
          flexWrap="wrap"
          alignItems="flex-start"
          mt={1}
          style={{
            backgroundColor: getColour(item)
          }}
        >
          <Avatar
            assignees={[{ assigneeNo: '1', avatarUrl: item.user?.avatarUrl ?? '', name: item.user?.name ?? '' }]}
            maxVisible={1}
            type={'task'}
          />
          <Text numberOfLines={1} ellipsizeMode="tail" maxWidth={'80%'} mt={1}>
            {item.user?.name}
          </Text>
          {workflow === Gql.WorkflowType.Dynamic &&
            (item.remoteId === parseInt(currentAssigneeId ?? '0') || item?.id === localCurrentAssigneeId) && (
              <Box py={0} px={2} borderRadius={'full'} backgroundColor={'#058DD8'} ml={2}>
                <Text fontSize={12} color={'white'}>
                  Current
                </Text>
              </Box>
            )}
        </HStack>
      );
    },
    [currentAssigneeId]
  );

  return (
    <HStack mt={4} mb={4}>
      <Box flex={1} flexDirection="row" alignItems="center">
        <Icon name="assignee" />
        <Text color="neutrals.gray90"> Assignee</Text>
      </Box>
      <Box flex={2}>
        <TouchableOpacity
          onPress={() => {
            if (status === 'Draft') {
              navigation.navigate('DigitalFormNav', {
                screen: 'AddAssignee',
                params: {
                  type: workflow,
                  documentId: '',
                  ass: assignees,
                  onPressBack: onBackPress
                }
              });
              dispatch(documentWorkspaceActions.closeAll());
            }
          }}
          disabled={(!isDocumentOwner && !role.canEdit) || !isDraftandApproved}
        >
          <FlashList
            data={assignees}
            renderItem={renderItem}
            keyExtractor={(item, index) => `item_${index}`}
            ListEmptyComponent={
              <Text color={!role.canEdit && !isDocumentOwner ? 'gray.400' : 'neutrals.gray90'}>No assignees set</Text>
            }
            estimatedItemSize={12}
          />
          {status === 'Draft' ? (
            <Text color={!role.canEdit && !isDocumentOwner ? 'gray.400' : 'neutrals.gray90'}>+ Add assignee</Text>
          ) : null}
        </TouchableOpacity>
      </Box>
    </HStack>
  );
};

export default AssigneeComponent;
