import React from 'react';
import { Box, HStack, VStack, Text } from 'native-base';
import { TouchableOpacity } from 'react-native';
import { Icon } from '@src/commons/Icon';
import WorkspacePhotoModel from '@src/database/model/workspace-photos.model';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import { useSelector } from '@src/store';
import useDeleteWorkspaceMedia from '@src/mutation/workspace-media/useDeleteWorkspaceMedia';
import useDeleteConfirmation from '@src/hooks/useDeleteConfirmation';
import { Gql } from '@src/api';

interface Role {
  canEdit: boolean;
}

interface MediaComponentProps {
  photos: WorkspacePhotoModel[];
  dispatch: React.Dispatch<any>;
  navigation: any;
  onBackPress: () => void;
  addAttachmentPhotoRef: React.RefObject<{ pushModal: () => void }>;
  isDocumentOwner: boolean;
  role: Role;
  isAssigned: boolean;
  isAlreadySigned: boolean;
  loading: boolean;
  documentStatus: Gql.ProjectDocumentStatus;
}

const useNavigationHandlers = (dispatch: React.Dispatch<any>, navigation: any, onBackPress: () => void) => {
  const workspaceDocuments = useSelector(state => state.documentWorkspace);
  const { mutateAsync: deleteWorkspaceMedia } = useDeleteWorkspaceMedia();
  const deleteConfirmation = useDeleteConfirmation();

  const navigateToMedia = (file: WorkspacePhotoModel) => {
    dispatch(documentWorkspaceActions.closeAll());
    navigation.navigate(file.type === 'mp4' || file.type === 'MOV' ? 'VideoNav' : 'PdftronNav', {
      screen: file.type === 'mp4' || file.type === 'MOV' ? 'VideoViewer' : 'Pdftron',
      params: {
        id: file.remoteId,
        type: file.type,
        name: file.name,
        modules: 'WorkspaceMedia',
        onPressBack: onBackPress,
        ...(!file.remoteId && { fileUrl: file.fileUrl }),
        ...(file.localFileUrl ? { fileUrl: file.localFileUrl } : {})
      }
    });
  };

  const handleDeletePhoto = async (file: WorkspacePhotoModel) => {
    try {
      deleteConfirmation.showDeleteConfirmation({
        fileName: file.name,
        action: async () => {
          return await deleteWorkspaceMedia(file.id).then(() => {
            const removedPhotos = workspaceDocuments?.DocumentDetails?.photos?.filter((photo: any) => photo !== file);
            dispatch(
              documentWorkspaceActions.setDocumentDetails({
                ...workspaceDocuments.DocumentDetails,
                photos: removedPhotos
              })
            );
          });
        }
      });
    } catch (error) {
      throw new Error(`Failed to delete photo: ${error}`);
    }
  };

  return { navigateToMedia, handleDeletePhoto };
};

interface PhotoItemProps {
  file: WorkspacePhotoModel;
  navigateToMedia: (file: WorkspacePhotoModel) => void;
  handleDeletePhoto: (file: WorkspacePhotoModel) => void;
  loading: boolean;
  status: Gql.ProjectDocumentStatus;
}

const PhotoItem: React.FC<PhotoItemProps> = ({ file, navigateToMedia, handleDeletePhoto, loading, status }) => (
  <HStack key={file.id} alignItems="center" justifyContent="space-between">
    <Text
      numberOfLines={1}
      ellipsizeMode="tail"
      w="80%"
      color="blue.500"
      onPress={() => navigateToMedia(file)}
      disabled={loading || !file.fileUrl}
    >
      {file.name}
    </Text>
    {status !== Gql.ProjectDocumentStatus.Approved && status !== Gql.ProjectDocumentStatus.Rejected && (
      <TouchableOpacity onPress={() => handleDeletePhoto(file)}>
        <Icon name="garbage" />
      </TouchableOpacity>
    )}
  </HStack>
);

interface AddPhotoButtonProps {
  addAttachmentPhotoRef: React.RefObject<{ pushModal: () => void }>;
  disabled: boolean;
}

const AddPhotoButton: React.FC<AddPhotoButtonProps> = ({ addAttachmentPhotoRef, disabled }) => (
  <TouchableOpacity onPress={() => addAttachmentPhotoRef.current?.pushModal()} disabled={disabled}>
    <Text color="neutrals.gray90">+ Add photos</Text>
  </TouchableOpacity>
);

const MediaComponent: React.FC<MediaComponentProps> = ({
  photos,
  dispatch,
  navigation,
  onBackPress,
  addAttachmentPhotoRef,
  isDocumentOwner,
  role,
  isAssigned,
  isAlreadySigned,
  loading,
  documentStatus
}) => {
  const { navigateToMedia, handleDeletePhoto } = useNavigationHandlers(dispatch, navigation, onBackPress);
  const canAddPhotos =
    (!isDocumentOwner && !role.canEdit && !isAssigned) || isAlreadySigned || documentStatus === 'Approved';

  return (
    <HStack mt={4} mb={4}>
      <Box flex={1} flexDirection="row" alignItems="center">
        <Icon name="image" />
        <Text color="neutrals.gray90" ml={1}>
          Media
        </Text>
      </Box>
      <Box flex={2}>
        <VStack>
          {photos?.map?.((file, index) => (
            <PhotoItem
              key={file.id || index}
              file={file}
              navigateToMedia={navigateToMedia}
              handleDeletePhoto={handleDeletePhoto}
              loading={loading}
              status={documentStatus}
            />
          ))}
          <AddPhotoButton addAttachmentPhotoRef={addAttachmentPhotoRef} disabled={canAddPhotos} />
        </VStack>
      </Box>
    </HStack>
  );
};

export default MediaComponent;
