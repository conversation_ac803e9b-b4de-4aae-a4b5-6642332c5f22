import React from 'react';
import { Box, HStack, Text } from 'native-base';
import { Icon } from '@src/commons/Icon';

interface StatusComponentProps {
  status: string;
}

const statusIcons = {
  Approved: (
    <HStack space={2} alignItems="center">
      <Icon name="approved" />
      <Text color={'#000'}>Approved</Text>
    </HStack>
  ),
  Draft: (
    <HStack space={2} alignItems="center">
      <Icon name="draft" />
      <Text color={'#000'}>Draft</Text>
    </HStack>
  ),
  InReview: (
    <HStack space={2} alignItems="center">
      <Icon name="in-review" />
      <Text color={'#000'}>In Review</Text>
    </HStack>
  ),
  Amend: (
    <HStack space={2} alignItems="center">
      <Icon name="amend" />
      <Text color="gray.400">Amend</Text>
    </HStack>
  ),
  Rejected: (
    <HStack space={2} alignItems="center">
      <Icon name="reject" />
      <Text color={'#000'}>Rejected</Text>
    </HStack>
  ),
  Submitted: (
    <HStack space={2} alignItems="center">
      <Icon name="submit" />
      <Text color={'#000'}>Submitted</Text>
    </HStack>
  ),
  Pending: (
    <HStack space={2} alignItems="center">
      <Icon name="pending" />
      <Text color={'#000'}>Pending</Text>
    </HStack>
  ),
  InProgress: (
    <HStack space={2} alignItems="center">
      <Icon name="dynamic-in-progress" />
      <Text color={'#000'}>In Progress</Text>
    </HStack>
  )
};

const StatusComponent: React.FC<StatusComponentProps> = ({ status }) => {
  const getStatus = (type: string) => {
    //@ts-ignore
    return (
      statusIcons[type] ?? (
        <HStack space={2} alignItems="center">
          <Text color={'gray.400'}>Unknown Status</Text>
        </HStack>
      )
    );
  };

  return (
    <HStack mt={4} mb={4}>
      <Box flex={1} flexDirection="row" alignItems="center">
        <Icon name="check-square" />
        <Text color="neutrals.gray90"> Status</Text>
      </Box>
      <Box flex={2}>{getStatus(status)}</Box>
    </HStack>
  );
};

export default StatusComponent;
