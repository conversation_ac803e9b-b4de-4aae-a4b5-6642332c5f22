import React from 'react';
import { Box, HStack, Text } from 'native-base';
import { TouchableOpacity } from 'react-native';
import { Icon } from '@src/commons/Icon';

interface SubGroupComponentProps {
  groupSelectorModalRef: React.RefObject<any>;
  isDocumentOwner: boolean;
  role: { canEdit: boolean };
  isDraftandApproved: boolean;
  documentStatus: string;
  groupName: string;
}

const GroupComponent: React.FC<SubGroupComponentProps> = ({
  groupSelectorModalRef,
  isDocumentOwner,
  role,
  isDraftandApproved,
  documentStatus,
  groupName
}) => {
  return (
    <HStack mt={4} mb={4}>
      <Box flex={1} flexDirection="row" alignItems="center">
        <Icon name="group" />
        <Text color="neutrals.gray90"> Sub-Group</Text>
      </Box>
      <Box flex={2}>
        <TouchableOpacity
          onPress={() => groupSelectorModalRef.current?.pushModal()}
          disabled={(!isDocumentOwner && !role.canEdit) || documentStatus === 'Approved'}
        >
          <Text color="neutrals.gray90">{groupName}</Text>
        </TouchableOpacity>
      </Box>
    </HStack>
  );
};

export default GroupComponent;
