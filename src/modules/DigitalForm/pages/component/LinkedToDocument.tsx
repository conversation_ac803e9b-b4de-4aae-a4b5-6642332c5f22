import React, { useCallback } from 'react';
import { Box, HStack, VStack, Text } from 'native-base';
import { TouchableOpacity } from 'react-native';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import { Icon } from '@src/commons/Icon';
import { useSelector } from '@src/store';
import { pushError } from '@src/configs';
import useDeleteConfirmation from '@src/hooks/useDeleteConfirmation';
import WorkspaceDocumentModel from '@src/database/model/workspace-document.model';

interface Document {
  id: string;
  name: string;
  documentId: string;
  fileUrl: string;
  remoteId: number;
}

interface Role {
  canEdit: boolean;
}

interface LinkedDocumentsComponentProps {
  linkedDocuments: Document[];
  dispatch: React.Dispatch<any>;
  navigation: any;
  onBackPress: () => void;
  linkedToRef: React.RefObject<any>;
  isDocumentOwner: boolean;
  role: Role;
  isAssigned: boolean;
  isAlreadySigned: boolean;
  documentStatus: string;
}

const LinkedDocumentsComponent = React.memo<LinkedDocumentsComponentProps>(
  ({
    linkedDocuments,
    dispatch,
    navigation,
    onBackPress,
    linkedToRef,
    isDocumentOwner,
    role,
    isAssigned,
    isAlreadySigned,
    documentStatus
  }) => {
    const deleteConfirmation = useDeleteConfirmation();

    const workspaceDocuments = useSelector(state => state.documentWorkspace);
    const handlePressDocument = useCallback(
      (file: Document) => {
        dispatch(documentWorkspaceActions.closeAll());
        navigation.navigate('PdftronNav', {
          screen: 'Pdftron',
          params: {
            id: file.documentId ?? '',
            modules: 'CloudDocs',
            onPressBack: onBackPress,
            ...(!file.documentId && { fileUrl: file.fileUrl }),
            ...(file?.localFileUrl && { fileUrl: file?.localFileUrl })
          }
        });
      },
      [dispatch, navigation, onBackPress]
    );

    const handleRemoveDocument = useCallback(
      (file: Document) => {
        try {
          return deleteConfirmation.showDeleteConfirmation({
            fileName: file.name,
            action: async () => {
              return await WorkspaceDocumentModel.deleteWorkspaceDocument(file.id).then(() => {
                const removedLinkedDocuments = workspaceDocuments?.DocumentDetails?.linkedDocuments?.filter(
                  (linkedDocument: any) => linkedDocument.id !== file.id
                );

                dispatch(
                  documentWorkspaceActions.setDocumentDetails({
                    ...workspaceDocuments.DocumentDetails,
                    linkedDocuments: removedLinkedDocuments
                  })
                );
              });
            }
          });
        } catch (error) {
          pushError('Failed to remove linked document', error);
        }
      },
      [workspaceDocuments.DocumentDetails]
    );

    const handleAddDocument = useCallback(() => {
      linkedToRef.current?.pushModal();
    }, [linkedToRef]);

    const canAddDocument =
      (!isDocumentOwner && !role.canEdit && !isAssigned) || isAlreadySigned || documentStatus === 'Approved';

    return (
      <HStack mt={4} mb={4}>
        <Box flex={1} flexDirection="row" alignItems="center">
          <Icon name="link-document" />
          <Text color="neutrals.gray90"> Linked to</Text>
        </Box>
        <Box flex={2}>
          <VStack>
            {linkedDocuments?.map?.((file, index) => {
              return (
                <HStack key={`project_document_${index}`} alignItems={'center'} justifyContent={'space-between'}>
                  <TouchableOpacity onPress={() => handlePressDocument(file)}>
                    <Text numberOfLines={1} ellipsizeMode="tail" maxWidth={'90%'} color={'blue.500'}>
                      {file.name}
                    </Text>
                  </TouchableOpacity>
                  {documentStatus !== 'Approved' && (
                    <TouchableOpacity onPress={() => handleRemoveDocument(file)}>
                      <Icon name="garbage" />
                    </TouchableOpacity>
                  )}
                </HStack>
              );
            })}
            {linkedDocuments?.length < 5 && (
              <TouchableOpacity onPress={handleAddDocument} disabled={canAddDocument}>
                <Text color="neutrals.gray90">+ Add linked document</Text>
              </TouchableOpacity>
            )}
          </VStack>
        </Box>
      </HStack>
    );
  }
);

export default LinkedDocumentsComponent;
