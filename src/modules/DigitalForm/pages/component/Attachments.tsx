import React, { useCallback } from 'react';
import { Box, HStack, VStack, Text } from 'native-base';
import moment from 'moment';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import { Platform, TouchableOpacity } from 'react-native';
import Icon from '@src/commons/Icon';
import DocumentPicker from 'react-native-document-picker';
import { pushError } from '@src/configs';
import { useSelector } from '@src/store';
import WorkspaceAttachmentModel from '@src/database/model/workspace-attachments.model';
import useDeleteConfirmation from '@src/hooks/useDeleteConfirmation';
import useDeleteWorkspaceAttachment from '@src/mutation/workspace-attachment/useDeleteWorkspaceAttachment';
import RNFS from 'react-native-fs';
import { Gql } from '@src/api';

interface AttachmentsComponentProps {
  attachments: WorkspaceAttachmentModel[];
  getProjectDocuments: any;
  dispatch: React.Dispatch<any>;
  navigation: any;
  onBackPress: () => void;
  isDocumentOwner: boolean;
  role: any;
  isAssigned: boolean;
  isAlreadySigned: boolean;
  loading: boolean;
}

const AttachmentsComponent: React.FC<AttachmentsComponentProps> = ({
  attachments,
  getProjectDocuments,
  dispatch,
  navigation,
  onBackPress,
  isDocumentOwner,
  role,
  isAssigned,
  isAlreadySigned,
  loading
}) => {
  const workspaceDocuments = useSelector(state => state.documentWorkspace);
  const { user } = useSelector(state => state.auth);
  const deleteConfirmation = useDeleteConfirmation();
  const { mutateAsync: deleteWorkspaceAttachment } = useDeleteWorkspaceAttachment();
  const status: Gql.ProjectDocumentStatus = getProjectDocuments?.status;

  // Maximum size per file in MB
  const MAX_FILE_SIZE_MB = 50;
  // Maximum total batch size in MB
  const MAX_TOTAL_SIZE_MB = 100;

  const chooseDocument = async () => {
    try {
      const res = await DocumentPicker.pick({
        type: [DocumentPicker.types.pdf],
        allowMultiSelection: true
      });

      // Get current existing attachments count
      const existingCount = workspaceDocuments?.DocumentDetails?.attachment?.length || 0;
      const availableSlots = 10 - existingCount;

      if (availableSlots <= 0) {
        pushError('Maximum of 10 attachments allowed');
        return;
      }

      // Check if selection exceeds available slots
      let filesToProcess = res;
      if (res.length > availableSlots) {
        filesToProcess = res.slice(0, availableSlots);
        pushError(`Only adding ${availableSlots} file(s) to stay within the 10 attachment limit`);
      }

      // Check individual file sizes
      const oversizedFiles = filesToProcess.filter(file => {
        const fileSizeMB = (file.size || 0) / (1024 * 1024);
        return fileSizeMB > MAX_FILE_SIZE_MB;
      });

      if (oversizedFiles.length > 0) {
        pushError(`Some files exceed the ${MAX_FILE_SIZE_MB}MB limit: ${oversizedFiles.map(f => f.name).join(', ')}`);
        return;
      }

      // Check total batch size
      const totalSizeBytes = filesToProcess.reduce((acc, file) => acc + (file.size || 0), 0);
      const totalSizeMB = totalSizeBytes / (1024 * 1024);

      if (totalSizeMB > MAX_TOTAL_SIZE_MB) {
        pushError(`Total file size (${totalSizeMB.toFixed(1)}MB) exceeded ${MAX_TOTAL_SIZE_MB}MB`);
        return;
      }

      const newFiles = [];

      // Loop over filesToProcess instead of res
      for (const element of filesToProcess) {
        const realURI = Platform.select({
          android: element.uri,
          ios: decodeURI(element.uri)
        });

        const originalFileName = element.name || 'document';
        const lastDotIndex = originalFileName.lastIndexOf('.');
        let ext = '';
        let baseName = originalFileName;

        if (lastDotIndex !== -1) {
          ext = originalFileName.slice(lastDotIndex + 1);
          baseName = originalFileName.slice(0, lastDotIndex);
        }

        let destPath = `${RNFS.DocumentDirectoryPath}/${baseName}${ext ? `.${ext}` : ''}`;
        let fileCounter = 0;

        // Generate unique filename
        while (await RNFS.exists(destPath)) {
          fileCounter++;
          destPath = `${RNFS.DocumentDirectoryPath}/${baseName}_${fileCounter}${ext ? `.${ext}` : ''}`;
        }

        if (realURI && destPath) {
          await RNFS.copyFile(realURI, destPath);
        } else {
          throw new Error('Invalid source or destination path');
        }

        const newFileName = destPath.split('/').pop() as string;

        newFiles.push({
          name: newFileName,
          type: element.type || 'application/pdf',
          projectDocumentId: workspaceDocuments?.DocumentDetails?.remoteId as number,
          userId: parseInt(user?.id ?? '0'),
          fileUrl: `file://${destPath}`,
          uri: `file://${destPath}`,
          localProjectDocumentId: workspaceDocuments.selectedDocumentId
        });
      }

      dispatch(
        documentWorkspaceActions.openDocumentDetailsModal({
          ...workspaceDocuments.DocumentDetails,
          attachment: [...(workspaceDocuments?.DocumentDetails?.attachment as any), ...newFiles]
        })
      );
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
        // User canceled the picker
      } else {
      }
    }
  };

  const navigationToPdftron = useCallback(
    (file: WorkspaceAttachmentModel) => {
      if (getProjectDocuments.status !== 'Approved') {
        dispatch(documentWorkspaceActions.closeAll());
        navigation.navigate('PdftronNav', {
          screen: 'Pdftron',
          params: {
            id: file.remoteId ?? '',
            modules: 'WorkspaceAttachment',
            onPressBack: onBackPress,
            ...(!file.remoteId && { fileUrl: file.fileUrl }),
            ...(file?.localFileUrl && { fileUrl: file?.localFileUrl })
          }
        });
      }
    },
    [workspaceDocuments?.DocumentDetails?.attachment]
  );

  const handleDeleteAttachment = async (file: WorkspaceAttachmentModel) => {
    try {
      deleteConfirmation.showDeleteConfirmation({
        fileName: file.name,
        action: async () => {
          return await deleteWorkspaceAttachment(file.id).then(() => {
            const removedAttachment = workspaceDocuments?.DocumentDetails?.attachment?.filter(
              (attachment: any) => attachment !== file
            );
            dispatch(
              documentWorkspaceActions.setDocumentDetails({
                ...workspaceDocuments.DocumentDetails,
                attachment: removedAttachment
              })
            );
          });
        }
      });
    } catch (error) {}
  };

  return (
    <HStack mt={4} mb={4}>
      <Box flex={1} flexDirection="row" alignItems="center">
        <Icon name="attached" />
        <Text color="neutrals.gray90"> Attached</Text>
      </Box>
      <Box flex={2}>
        <VStack>
          {attachments?.map?.((file, index) => (
            <HStack key={file.id || moment().unix() + index} alignItems={'center'} justifyContent={'space-between'}>
              <Text
                numberOfLines={1}
                ellipsizeMode="tail"
                w={'85%'}
                color={'blue.500'}
                onPress={() => navigationToPdftron(file)}
                disabled={loading || !file.fileUrl}
              >
                {file.name}
              </Text>
              {status !== Gql.ProjectDocumentStatus.Approved && status !== Gql.ProjectDocumentStatus.Rejected && (
                <TouchableOpacity onPress={() => handleDeleteAttachment(file as any)}>
                  <Icon name="garbage" />
                </TouchableOpacity>
              )}
            </HStack>
          ))}
          {attachments && attachments.length > 10 && <Text color={'red.500'}>Attachments exceeded 10</Text>}
          <TouchableOpacity
            onPress={chooseDocument}
            disabled={
              (!isDocumentOwner && !role.canEdit && !isAssigned) ||
              isAlreadySigned ||
              getProjectDocuments?.status === 'Approved'
            }
          >
            <Text color="neutrals.gray90">
              {!isDocumentOwner && !role.canEdit && !isAssigned ? ' + Insufficient role to add' : ' + Add attachment'}
            </Text>
          </TouchableOpacity>
        </VStack>
      </Box>
    </HStack>
  );
};

export default AttachmentsComponent;
