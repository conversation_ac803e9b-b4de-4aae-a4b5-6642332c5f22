import { AppBar, Content, Footer, Icon } from '@commons';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { COLORS } from '@src/constants';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import { useDispatch } from '@src/store';
import { DigitalFormNavigatorParams, RootNavigatorParams } from '@src/types';
import _ from 'lodash';
import { Avatar, Box, Button, Divider, FlatList, HStack, Input, Text, VStack, Pressable } from 'native-base';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet, TouchableOpacity, View } from 'react-native';

type Props = CompositeScreenProps<
  BottomTabScreenProps<DigitalFormNavigatorParams, 'AddCcDocumentDetails'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const AddCcDocumentDetails: React.FC<Props> = ({ route, navigation }) => {
  const documentId = route.params.documentId;
  const [selectedUsers, setSelectedUsers] = useState<string[]>(route.params.ccs ?? []);
  const [filteredValue, setFilteredValue] = useState<string>();

  const dispatch = useDispatch();

  const { data: projectUserData, loading: projectDataLoading } = Gql.useProjectUsersQuery({
    variables: {
      filter: {
        role: { neq: Gql.ProjectUserRoleType.CanView },
        user: { name: { like: `%${filteredValue}%` } }
      },
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.ProjectUserSortFields.CreatedAt
        }
      ]
    }
  });

  const { data: recentlyJoinedData } = Gql.useProjectUsersQuery({
    variables: {
      filter: {
        role: { neq: Gql.ProjectUserRoleType.CanView },
        user: { name: { isNot: null } }
      },
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.ProjectUserSortFields.CreatedAt
        }
      ]
    }
  });

  const { data: addedUserData } = Gql.useProjectUsersQuery({
    variables: {
      filter: {
        user: {
          id: selectedUsers.length > 0 ? { in: selectedUsers } : { is: null }
        }
      }
    }
  });

  const inviteProjectUser = projectUserData?.projectUsers?.nodes.map((u: any) => {
    return {
      name: u.user.name,
      email: u.user.email,
      id: u.user.id,
      value: u.user.id,
      avatar: u.user.avatar
    };
  });

  const recentlyJoinedUsers = recentlyJoinedData?.projectUsers?.nodes.map((u: any) => {
    return {
      name: u.user.name,
      email: u.user.email,
      id: u.user.id,
      value: u.user.id,
      avatar: u.user.avatar
    };
  });

  const addedUser = addedUserData?.projectUsers?.nodes?.map((u: any) => {
    return {
      name: u.user.name,
      email: u.user.email,
      id: u.user.id,
      avatar: u.user.avatar
    };
  });

  // const { data } = Gql.useRequestForSignaturesQuery({
  //   variables: {
  //     filter: { projectDocumentId: { eq: documentId } }
  //   }
  // });

  // const invitedUser = data?.requestForSignatures?.nodes?.map((member: any) => {
  //   return {
  //     id: member.id,
  //     name: member.signBy.name,
  //     email: member.signBy.email,
  //     avatar: member.signBy.avatar
  //   };
  // });

  return (
    <Box flex={1} bg="#FFFFFF">
      <AppBar
        header
        goBack
        title="Request Approval"
        noRight
        onPressTitle={() => {}}
        onGoBack={() => {
          dispatch(documentWorkspaceActions.openDocumentDetailsModal({ selectedCcs: selectedUsers }));
          dispatch(documentWorkspaceActions.ShouldSetInitialState(false));
          navigation.goBack();
        }}
      />
      <Box flex={1} bg="#FFFFFF">
        <Content pt={5}>
          <Text mb={4} color={COLORS.neutrals.gray90}>
            Add Cc
          </Text>

          <Divider mb={4} />

          <Input
            placeholder="Enter a member's name"
            width="full"
            borderRadius="4"
            fontSize="14"
            onChangeText={text => setFilteredValue(text)}
            value={filteredValue}
            {...(!!filteredValue && {
              InputRightElement: (
                <Pressable px="4" py="6" onPress={() => setFilteredValue('')}>
                  <Icon name="cancel" />
                </Pressable>
              )
            })}
          />

          {!!filteredValue ? (
            <>
              {projectDataLoading && (
                <View style={[styles.container, styles.horizontal]}>
                  <ActivityIndicator />
                </View>
              )}
              <FlatList
                keyboardShouldPersistTaps="handled"
                data={inviteProjectUser}
                keyExtractor={item => item.id}
                renderItem={({ item, index }) => (
                  <Box style={styles.flatList} key={index}>
                    {index === 0 && (
                      <>
                        <Text color={COLORS.neutrals.black}>Search results</Text>
                        <Divider my={1} />
                      </>
                    )}
                    <TouchableOpacity
                      onPress={() => {
                        setSelectedUsers(pre => {
                          const findPreId = pre.find(preId => preId === item.id);
                          if (findPreId) {
                            return pre.filter(preId => preId !== findPreId);
                          } else {
                            // setFilteredValue('');
                            return [...pre, item.id];
                          }
                        });
                        setFilteredValue('');
                      }}
                    >
                      <HStack alignItems="center" space={3} mt={4}>
                        <Avatar
                          size="32px"
                          source={{
                            uri: item.avatar
                          }}
                        />

                        <VStack width="76%">
                          <Text numberOfLines={1} ellipsizeMode="tail" style={{ fontWeight: '600', fontSize: 14 }}>
                            {item?.name?.toUpperCase?.()}
                          </Text>
                          <Text style={{ fontWeight: '400', color: COLORS.neutrals.gray90 }}>
                            {item?.email?.toLowerCase?.()}
                          </Text>
                        </VStack>

                        {selectedUsers.find(preId => preId === item.id) && <Icon name="tick" fill="#0695D7" />}
                      </HStack>
                    </TouchableOpacity>
                  </Box>
                )}
              />
            </>
          ) : null}

          {selectedUsers.length >= 1 ? (
            <>
              {/* Current add assignee */}
              <Box mt={4}>
                {/* <Text color={COLORS.neutrals.gray90}>
                  Assigned users
                </Text>
                <Divider mt={1} /> */}
                <FlatList
                  keyboardShouldPersistTaps="handled"
                  data={addedUser}
                  keyExtractor={item => item.id}
                  renderItem={({ item, index }) => (
                    <Box key={index}>
                      <HStack alignItems="center" space={3} mt={4}>
                        <Avatar
                          size="32px"
                          source={{
                            uri: item.avatar
                          }}
                        />
                        <VStack width="78%">
                          <Text style={{ fontWeight: '600', fontSize: 14 }}>{item.name.toUpperCase()}</Text>
                          <Text style={{ fontWeight: '400', color: COLORS.neutrals.gray90 }}>
                            {item.email.toLowerCase()}
                          </Text>
                        </VStack>
                        <TouchableOpacity
                          onPress={() => {
                            setSelectedUsers(pre => {
                              const findPreId = pre.find(preId => preId === item.id);
                              if (findPreId) {
                                return pre.filter(preId => preId !== findPreId);
                              } else {
                                return [...pre, item.id];
                              }
                            });
                          }}
                        >
                          <Icon name="cancel" fill="#0695D7" />
                        </TouchableOpacity>
                      </HStack>
                    </Box>
                  )}
                />
              </Box>
            </>
          ) : (
            <Box>
              <Text mb={2} mt={6} color={COLORS.neutrals.gray90}>
                Recently joined
              </Text>

              {projectDataLoading && (
                <View style={[styles.container, styles.horizontal]}>
                  <ActivityIndicator />
                </View>
              )}
              <Divider mb={4} />
              <FlatList
                keyboardShouldPersistTaps="handled"
                data={recentlyJoinedUsers}
                keyExtractor={item => item.id}
                renderItem={({ item, index }) => (
                  <Box key={index}>
                    <TouchableOpacity
                      onPress={() => {
                        setSelectedUsers(pre => {
                          const findPreId = pre.find(preId => preId === item.id);
                          if (findPreId) {
                            return pre.filter(preId => preId !== findPreId);
                          } else {
                            return [...pre, item.id];
                          }
                        });
                      }}
                    >
                      <HStack alignItems="center" space={3}>
                        <Avatar
                          size="32px"
                          source={{
                            uri: item.avatar
                          }}
                        />

                        <VStack width="85%">
                          <Text style={{ fontWeight: '600', fontSize: 14 }}>{item?.name?.toUpperCase?.()}</Text>
                          <Text style={{ fontWeight: '400', color: COLORS.neutrals.gray90 }}>
                            {item.email.toLowerCase()}
                          </Text>
                        </VStack>
                      </HStack>
                      <Divider my={2} />
                    </TouchableOpacity>
                  </Box>
                )}
              />
            </Box>
          )}
        </Content>
        <Footer>
          <Button
            variant="primary"
            onPress={() => {
              dispatch(documentWorkspaceActions.openDocumentDetailsModal({ selectedCcs: selectedUsers }));
              dispatch(documentWorkspaceActions.ShouldSetInitialState(false));
              navigation.goBack();
            }}
          >
            Add CC
          </Button>
        </Footer>
      </Box>
    </Box>
  );
};

const styles = StyleSheet.create({
  memberBox: {
    borderWidth: 1,
    borderColor: COLORS.neutrals.gray40,
    borderRadius: 8,
    paddingLeft: 10,
    alignItem: 'center'
  },
  flatList: {
    borderWidth: 1,
    borderColor: COLORS.neutrals.gray40,
    padding: 12,
    backgroundColor: '#E6F1FF'
  },
  container: {
    flex: 1,
    justifyContent: 'center'
  },
  horizontal: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 10
  }
});

export default AddCcDocumentDetails;
