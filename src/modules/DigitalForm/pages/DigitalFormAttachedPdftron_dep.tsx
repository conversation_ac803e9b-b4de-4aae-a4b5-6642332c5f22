import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { COLORS } from '@src/constants';
import { DigitalFormNavigatorParams, RootNavigatorParams } from '@src/types';
import { Box, Button } from 'native-base';
import React, { useEffect, useRef, useState } from 'react';
import { Config, DocumentView, RNPdftron } from 'react-native-pdftron';
import { Alert, BackHandler, InteractionManager, Platform } from 'react-native';
import { AppBar, Footer } from '@src/commons';
import { Gql } from '@src/api';
import apolloClient from '@src/lib/apollo';
import _ from 'lodash';
import { pushError, pushSuccess } from '@src/configs';

type Props = CompositeScreenProps<
  BottomTabScreenProps<DigitalFormNavigatorParams, 'DigitalFormAttachedPdftron'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const AttachPdfTron: React.FC<Props> = ({ route, navigation }) => {
  const id = route.params.id;
  const viewerRef = useRef<any>();
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getDoc();
    });
    RNPdftron.initialize(
      'Bina Cloudtech sdn bhd (bina.cloud):OEM:Bina::IA:AMS(20230928):D7863732B60C5D58B999E101404F3D38DD43F51255922C4C9BD65ABAB6F5C7'
    );
    RNPdftron.enableJavaScript(true);
  }, []);

  const [getDoc, { data }] = Gql.useWorkspaceAttachmentLazyQuery({ variables: { id: id } });

  const path = data?.workspaceAttachment?.fileUrl ?? '';
  const fileName = data?.workspaceAttachment?.name;
  const file = data?.workspaceAttachment;

  const onLeadingNavButtonPressed = () => {
    if (Platform.OS === 'ios') {
      Alert.alert('App', 'onLeadingNavButtonPressed', [{ text: 'OK', onPress: () => {} }], {
        cancelable: true
      });
    } else {
      navigation.goBack();
      route?.params?.onPressBackPdftron();
    }
  };

  // save the signature / any xfdf
  const onSavedXfdf = async (xfdf: string) => {
    setIsSubmitting(true);
    try {
      await apolloClient.mutate<Gql.UpdateOneProjectDocumentMutation>({
        mutation: Gql.UpdateOneProjectDocumentDocument,
        variables: {
          input: {
            id: _.toString(id),
            update: {
              xfdf
            }
          }
        }
      });
      pushSuccess('Saved successfully');
    } catch (e) {
      pushError(e);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!path) return null;

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <AppBar
        onGoBack={() => {
          navigation.goBack();
          route?.params?.onPressBackPdftron();
        }}
        goBack={Platform.OS === 'ios' ? true : undefined}
        barStyle={'dark-content'}
        title={fileName}
        noRight={Platform.OS === 'ios' ? true : undefined}
        onPressTitle={() => null}
      />
      <DocumentView
        ref={viewerRef}
        document={path}
        annotationToolbars={[Config.DefaultToolbars.View]}
        // leadingNavButtonIcon={Platform.OS === 'ios' ? 'ic_close_black_24px.png' : 'ic_arrow_back_white_24dp'}
        onLeadingNavButtonPressed={onLeadingNavButtonPressed}
        onDocumentLoaded={() => {
          // viewerRef?.current?.importAnnotations(file?.xfdf);
        }}
      />
      <Footer>
        <Button
          disabled={true}
          variant="grey"
          isLoading={isSubmitting}
          onPress={() => {
            viewerRef.current.exportAnnotations().then((filePath: any) => {
              onSavedXfdf(filePath);
            });
          }}
        >
          Save
        </Button>
      </Footer>
    </Box>
  );
};

export default AttachPdfTron;
