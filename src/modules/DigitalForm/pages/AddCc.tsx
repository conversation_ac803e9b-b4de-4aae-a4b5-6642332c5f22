import React, { useC<PERSON>back, useEffect, useMemo, useState } from 'react';
import { ScrollView } from 'react-native';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { Box, Button, Divider, Input, Pressable, Text } from 'native-base';

// Local imports
import { AppB<PERSON>, Footer, Icon } from '@commons';
import { ErrorBoundary } from '@commons/ErrorBoundary';
import { COLORS } from '@src/constants';
import useDeleteWorkspaceCc from '@src/mutation/workspace-cc/useDeleteWorkspaceCc';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import { useDispatch, useSelector } from '@src/store';
import useDebouncedSearch from '@src/hooks/useDebounceSearch';
import { ProjectUserList } from '../components/ProjectUserList';
import { useProjectUserSearch } from '../hooks/useProjectUserSearch';
import { ProjectUser } from '../hooks/types';

type CcUser = {
  id?: string;
  ccId: number | string;
  user: {
    id: string;
    name: string;
    email: string;
    avatarUrl?: string;
  };
  localProjectDocumentId: string;
  ownerId: number;
  projectDocumentId?: number;
};

type Props = BottomTabScreenProps<
  {
    AddCc: {
      documentId: string;
      ccs?: CcUser[];
      onPressBack?: () => void;
    };
  },
  'AddCc'
>;

/**
 * AddCc Screen Component
 *
 * This component provides functionality to:
 * 1. Search and select users for CC
 * 2. Add or remove CCs from a document
 * 3. Manage CC list state
 */
export const AddCc: React.FC<Props> = ({ route, navigation }) => {
  // States
  const [selectedUsers, setSelectedUsers] = useState<CcUser[]>(route.params.ccs ?? []);
  const [deletedCcIds, setDeletedCcIds] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Hooks
  const dispatch = useDispatch();
  const { user } = useSelector(states => states?.auth);
  const workspaceDocument = useSelector(states => states.documentWorkspace);
  const { inputValue, debouncedValue, setInputValue } = useDebouncedSearch(300);
  const { mutateAsync: deleteWorkspaceCc } = useDeleteWorkspaceCc();

  // Custom hooks
  const { projectUsers, isLoading, error, retry, hasMore, loadMore } = useProjectUserSearch(debouncedValue ?? '');

  // Memoized values
  const selectedIds = useMemo(() => {
    return new Set(selectedUsers.map(user => String(user.ccId)));
  }, [selectedUsers]);

  // Effects
  useEffect(() => {
    if (route.params.ccs) {
      setSelectedUsers(route.params.ccs);
    }
  }, [route.params.ccs]);

  // Handlers
  const handleSearch = useCallback(
    (value: string) => {
      setInputValue(value);
    },
    [setInputValue]
  );

  const handleClear = useCallback(() => {
    setInputValue('');
  }, [setInputValue]);

  const handleSelectUser = useCallback(
    (item: ProjectUser) => {
      handleClear();
      if (selectedIds.has(String(item.userId))) {
        const existingUser = selectedUsers.find(user => String(user.ccId) === String(item.userId));
        const id = existingUser?.id;
        if (id) {
          setDeletedCcIds(prev => [...prev, id]);
        }
        setSelectedUsers(prev => prev.filter(user => String(user.ccId) !== String(item.userId)));
      } else {
        const remoteId = workspaceDocument?.DocumentDetails?.remoteId;
        const newCc: CcUser = {
          ccId: item.userId,
          user: {
            id: String(item.userId),
            name: item.name,
            email: item.email,
            avatarUrl: item.avatarUrl
          },
          ownerId: parseInt(user?.id as string, 10),
          localProjectDocumentId: workspaceDocument.selectedDocumentId as string,
          ...(remoteId && { projectDocumentId: remoteId })
        };
        setSelectedUsers(prev => [...prev, newCc]);
      }
    },
    [
      selectedIds,
      workspaceDocument.selectedDocumentId,
      workspaceDocument.DocumentDetails?.remoteId,
      user?.id,
      handleClear
    ]
  );

  const handleSubmit = useCallback(async () => {
    try {
      setIsSubmitting(true);

      if (deletedCcIds.length > 0) {
        await deleteWorkspaceCc(deletedCcIds);
      }

      dispatch(
        documentWorkspaceActions.openDocumentDetailsModal({
          ...workspaceDocument.DocumentDetails,
          id: workspaceDocument.DocumentDetails?.id ?? '',
          selectedCcs: selectedUsers as unknown as string[]
        })
      );
      dispatch(documentWorkspaceActions.ShouldSetInitialState(false));
      navigation.goBack();
    } catch (error) {
    } finally {
      setIsSubmitting(false);
    }
  }, [deletedCcIds, workspaceDocument.DocumentDetails, selectedUsers, dispatch, navigation, deleteWorkspaceCc]);

  return (
    <Box flex={1} bg="#FFFFFF">
      <AppBar
        onPressTitle={() => null}
        header
        goBack
        onGoBack={() => {
          navigation.goBack();
          route.params.onPressBack?.();
        }}
        title="Add Cc"
        noRight
      />
      <Box flex={1} bg="#FFFFFF">
        <ScrollView style={{ paddingHorizontal: 20 }}>
          <Text mb={4} color={COLORS.neutrals.gray90}>
            Add Cc
          </Text>
          <Divider mb={4} />
          <Input
            placeholder="Enter a member's name"
            width="full"
            borderRadius="4"
            fontSize="14"
            onChangeText={handleSearch}
            value={inputValue}
            mb={4}
            {...(!!inputValue?.trim() && {
              InputRightElement: (
                <Pressable px="4" py="6" onPress={handleClear}>
                  <Icon name="cancel" />
                </Pressable>
              )
            })}
          />
          <ErrorBoundary error={error as Error} onRetry={retry}>
            <ProjectUserList
              data={projectUsers}
              selectedIds={selectedIds}
              onSelect={handleSelectUser}
              onEndReached={loadMore}
              isLoading={isLoading}
            />
          </ErrorBoundary>
        </ScrollView>
        <Footer>
          <Button variant="primary" onPress={handleSubmit} isDisabled={isSubmitting} isLoading={isSubmitting}>
            Add Cc
          </Button>
        </Footer>
      </Box>
    </Box>
  );
};

export default React.memo(AddCc);
