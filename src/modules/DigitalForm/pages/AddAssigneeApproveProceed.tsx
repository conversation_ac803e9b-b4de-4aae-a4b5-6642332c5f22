import { App<PERSON><PERSON>, Footer, Icon } from '@commons';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { FlashList } from '@shopify/flash-list';
import { Gql } from '@src/api';
import { pushError, pushMessaage } from '@src/configs';
import { COLORS } from '@src/constants';
import useDebouncedSearch from '@src/hooks/useDebounceSearch';
import ProjectUserModel from '@src/database/model/project-user.model';
import useValidationWorkspace from '@src/mutation/request-for-signature/useValidationWorkspace';
import { useGetProjectAdded } from '@src/queries/project-user/useGetProjectAdded';
import { useGetProjectUser } from '@src/queries/project-user/useGetProjectUser';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import { useDispatch, useSelector } from '@src/store';
import { DigitalFormNavigatorParams, RootNavigatorParams } from '@src/types';
import { Box, Button, Divider, FlatList, HStack, Input, Pressable, Text, VStack } from 'native-base';
import React, { useCallback, useMemo, useState } from 'react';
import { ActivityIndicator, Alert, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import Avatar from '@commons/Avatar';

type Props = CompositeScreenProps<
  BottomTabScreenProps<DigitalFormNavigatorParams, 'AddAssigneeApproveProceed'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const AddAssigneeApproveProceed: React.FC<Props> = ({ route, navigation }) => {
  const [selectedUsers, setSelectedUsers] = useState<ProjectUserModel[] | []>([]);
  const [filteredValue, setFilteredValue] = useState<string>();
  const isNCR = route.params.groupName === 'Non Conformance Report';
  const { mutateAsync: updateValidation, isPaused, error } = useValidationWorkspace();

  const { debouncedValue, setInputValue } = useDebouncedSearch();

  const {
    data: projectUserDatas,
    fetchNextPage: fetchUsers,
    hasNextPage: usersNextPage,
    isLoading
  } = useGetProjectUser(debouncedValue ?? '');

  const projectUserData = useMemo(() => {
    if (!projectUserDatas) return [];
    return projectUserDatas.pages.map(page => page.items).flat();
  }, [projectUserDatas]);

  const { data: addedUserDatas } = useGetProjectAdded({
    assigneesId: selectedUsers?.map?.((item: any) => item?.userId)
  });

  const addedUserData = useMemo(() => {
    if (!addedUserDatas) return [];
    return addedUserDatas.pages.map(page => page.items).flat();
  }, [addedUserDatas]);

  const dispatch = useDispatch();

  const onInProgress = async () => {
    Alert.alert(
      'Confirmation',
      'Are you sure you want to make Partial Approval request?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Confirm',
          onPress: async () => {
            try {
              return await updateValidation({
                documentId: route?.params?.documentId,
                requestForSignatureId: route?.params?.currentAssigneeId,
                type: 'Work In Progress',
                newRequestedSignatureId: selectedUsers[0]
              }).then(() => {
                dispatch(documentWorkspaceActions.resetAll());
                dispatch(documentWorkspaceActions.ShouldSetInitialState(true));
                dispatch(documentWorkspaceActions.setDocumentId(route?.params?.documentId));
                pushMessaage('Document Proceeded', 'success');
                navigation.goBack();
              });
            } catch (e) {
              pushError(e.message);
            }
          }
        }
      ],
      { cancelable: false }
    );
  };

  const onSubmit = async () => {
    Alert.alert(
      'Confirmation',
      'Are you sure you want to assign to next assignee?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Approve',
          onPress: async () => {
            try {
              return await updateValidation({
                documentId: route?.params?.documentId,
                requestForSignatureId: route?.params?.currentAssigneeId,
                type: 'Approve Proceed',
                newRequestedSignatureId: selectedUsers[0]
              }).then(() => {
                dispatch(documentWorkspaceActions.resetAll());
                dispatch(documentWorkspaceActions.ShouldSetInitialState(true));
                dispatch(documentWorkspaceActions.setDocumentId(route?.params?.documentId));
                pushMessaage('Document Proceeded', 'success');
                navigation.goBack();
              });
            } catch (e) {
              pushError(e.message);
            }
          }
        }
      ],
      { cancelable: false }
    );
  };

  if (error) {
    pushError(error.message);
  }

  const onPress = useCallback((item: any) => {
    const { _raw: user } = item;
    setSelectedUsers((pre: any) => {
      const currentUsers = pre || [];

      const findPreId = currentUsers.find((preId: any) => preId === user);
      if (findPreId) {
        return currentUsers.filter((preId: any) => preId !== findPreId);
      } else {
        return [...currentUsers, user];
      }
    });
  }, []);

  return (
    <Box flex={1} bg="#FFFFFF">
      <AppBar
        onPressTitle={() => null}
        header
        goBack
        onGoBack={() => {
          dispatch(documentWorkspaceActions.openDocumentDetailsModal({ selectedAssigneeIds: selectedUsers }));
          navigation.goBack();
          dispatch(documentWorkspaceActions.ShouldSetInitialState(true));
          route.params.onPressBack?.();
        }}
        title="Proceed"
        noRight
      />

      <Box flex={1} bg="#FFFFFF">
        <ScrollView keyboardShouldPersistTaps="handled" style={{ paddingHorizontal: 20 }}>
          <Text mb={4} color={COLORS.neutrals.gray90}>
            Add Assignee
          </Text>

          <Divider mb={4} />

          <Input
            placeholder="Enter a member's name"
            width="full"
            borderRadius="4"
            fontSize="14"
            isDisabled={selectedUsers?.length === 1}
            onChangeText={text => setInputValue(text)}
            value={filteredValue}
            {...(!!filteredValue && {
              InputRightElement: (
                <Pressable px="4" py="6" onPress={() => setFilteredValue('')}>
                  <Icon name="cancel" />
                </Pressable>
              )
            })}
          />

          {!!filteredValue ? (
            <>
              {isLoading && (
                <View style={[styles.container, styles.horizontal]}>
                  <ActivityIndicator />
                </View>
              )}
              <FlashList
                keyboardShouldPersistTaps="handled"
                data={projectUserData}
                keyExtractor={item => item.id}
                renderItem={({ item, index }) => (
                  <Box style={styles.flatList} key={index}>
                    {index === 0 && (
                      <>
                        <Text color={COLORS.neutrals.black}>Search results</Text>
                        <Divider my={1} />
                      </>
                    )}
                    <TouchableOpacity onPress={() => onPress(item)}>
                      <HStack alignItems="center" space={3} mt={4}>
                        {/* <Avatar
                          size="32px"
                          source={{
                            uri: item.avatar
                          }}
                        /> */}

                        <VStack width="76%">
                          <Text numberOfLines={1} ellipsizeMode="tail" style={{ fontWeight: '600', fontSize: 14 }}>
                            {item.name.toUpperCase()}
                          </Text>
                          <Text style={{ fontWeight: '400', color: COLORS.neutrals.gray90 }}>
                            {item.email.toLowerCase()}
                          </Text>
                        </VStack>

                        {selectedUsers?.find?.(preId => preId === item.id) && <Icon name="tick" fill="#0695D7" />}
                      </HStack>
                    </TouchableOpacity>
                  </Box>
                )}
              />
            </>
          ) : null}

          {selectedUsers?.length >= 1 ? (
            <>
              {/* Current add assignee */}
              <Box mt={4}>
                {/* <Text color={COLORS.neutrals.gray90}>
                  Assigned users
                </Text>
                <Divider mt={1} /> */}
                <FlatList
                  data={addedUserData}
                  keyExtractor={item => item.id}
                  renderItem={({ item, index }) => (
                    <Box key={index}>
                      <HStack alignItems="center" space={3} mt={4}>
                        <Avatar
                          assignees={[
                            { assigneeNo: '1', avatarUrl: item?.avatarUrl ?? '', name: item.user?.name ?? '' }
                          ]}
                          maxVisible={1}
                          type={'task'}
                        />
                        <VStack width="78%">
                          <Text style={{ fontWeight: '600', fontSize: 14 }} numberOfLines={1} ellipsizeMode="tail">
                            {item.name.toUpperCase()}
                          </Text>
                          <Text style={{ fontWeight: '400', color: COLORS.neutrals.gray90 }}>
                            {item.email.toLowerCase()}
                          </Text>
                        </VStack>
                        <TouchableOpacity onPress={() => setSelectedUsers([])}>
                          <Icon name="cancel" fill="#0695D7" />
                        </TouchableOpacity>
                      </HStack>
                    </Box>
                  )}
                />
              </Box>
            </>
          ) : (
            <Box>
              <Text mb={2} mt={6} color={COLORS.neutrals.gray90}>
                Recently joined
              </Text>

              {isLoading && (
                <View style={[styles.container, styles.horizontal]}>
                  <ActivityIndicator />
                </View>
              )}
              <Divider mb={4} />
              <FlatList
                data={projectUserData}
                keyExtractor={item => item.id}
                renderItem={({ item, index }) => (
                  <Box key={index}>
                    <TouchableOpacity onPress={() => onPress(item)}>
                      <HStack alignItems="center" space={3}>
                        <Avatar
                          assignees={[
                            { assigneeNo: '1', avatarUrl: item?.avatarUrl ?? '', name: item?.user?.name ?? '' }
                          ]}
                          maxVisible={1}
                          type={'task'}
                        />

                        <VStack width="85%">
                          <Text style={{ fontWeight: '600', fontSize: 14 }} numberOfLines={1} ellipsizeMode="tail">
                            {item?.name?.toUpperCase?.()}
                          </Text>
                          <Text style={{ fontWeight: '400', color: COLORS.neutrals.gray90 }}>
                            {item?.email?.toLowerCase?.()}
                          </Text>
                        </VStack>
                      </HStack>
                      <Divider my={2} />
                    </TouchableOpacity>
                  </Box>
                )}
              />
            </Box>
          )}
        </ScrollView>
        <Footer flexDirection={'column'}>
          <Button my={6} variant="primary" disabled={isPaused} onPress={onSubmit}>
            Submit
          </Button>

          <Divider />
          {!isNCR && (
            <>
              <Text my={6}>To provide Partial Approval for this documentation, click the button below.</Text>
              <Box bg="#9ce255" p={3} rounded={'xl'}>
                <Button
                  disabled={isPaused}
                  variant="primary"
                  bg="#f0efef"
                  _text={{ color: 'black' }}
                  p={1}
                  m={0}
                  onPress={onInProgress}
                >
                  Work In Progress
                </Button>
              </Box>

              <Text my={6} color={COLORS.neutrals.gray70} bold>
                * Please note that this ticket will be referred to workflow creator to be reassigned.
              </Text>
            </>
          )}
        </Footer>
      </Box>
    </Box>
  );
};

const styles = StyleSheet.create({
  memberBox: {
    borderWidth: 1,
    borderColor: COLORS.neutrals.gray40,
    borderRadius: 8,
    paddingLeft: 10,
    alignItem: 'center'
  },
  flatList: {
    borderWidth: 1,
    borderColor: COLORS.neutrals.gray40,
    padding: 12,
    backgroundColor: '#E6F1FF'
  },
  container: {
    flex: 1,
    justifyContent: 'center'
  },
  horizontal: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 10
  }
});

export default AddAssigneeApproveProceed;
