import { Config, DocumentView, RNPdftron } from 'react-native-pdftron';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { AppBar, Footer } from '@src/commons';
import { generateRNFile, pushError, pushSuccess } from '@src/configs';
import { COLORS } from '@src/constants';
import { DigitalFormNavigatorParams, RootNavigatorParams } from '@src/types';
import _ from 'lodash';
import { Box, Button } from 'native-base';
import React, { useEffect, useRef, useState } from 'react';
import { Alert, BackHandler, InteractionManager, Platform } from 'react-native';
import StandardFormPdfTronRenameModal from '../components/Template/StandardFormPdfTronRenameModal_dep';

type Props = CompositeScreenProps<
  BottomTabScreenProps<DigitalFormNavigatorParams, 'StandardFormPdfTron'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

function blobToFile(theBlob: any, fileName: any) {
  theBlob.lastModifiedDate = new Date();
  theBlob.name = fileName;
  return theBlob;
}

const StandardFormPdfTron: React.FC<Props> = ({ navigation, route }) => {
  const { data: userData, loading: userLoading } = Gql.useGetUserMeQuery({});
  const id = route?.params.id;
  const role = route?.params.role;
  const assigneeIds = route?.params.assigneeIds;
  const viewerRef = useRef<any>();
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  let view = [] as any;
  const [getPdfDoc, { data }] = Gql.useProjectDocumentLazyQuery({ variables: { id: id } });
  const fileName = data?.projectDocument?.name; // show the file name in header
  const renameModalRef = useRef<any>(null);
  const [newFile, setNewFile] = useState<any>(null);

  const FillAndSign = {
    [Config.CustomToolbarKey.Id]: 'FillAndSign',
    [Config.CustomToolbarKey.Name]: 'Fill And Sign',
    [Config.CustomToolbarKey.Icon]: Config.ToolbarIcons.FillAndSign,
    [Config.CustomToolbarKey.Items]: [
      Config.Tools.formCreateSignatureField,
      Config.Tools.formCreateTextField,
      Config.Tools.formCreateCheckboxField,
      Config.Tools.annotationCreateRubberStamp,
      Config.Tools.annotationCreateFreeTextDate,
      Config.Buttons.undo
    ]
  };

  if (assigneeIds === '') {
    // File from Templates
    view = [Config.DefaultToolbars.View, FillAndSign];
  } else {
    // File from Documents
    if (role === 'CloudCoordinator' || role === 'ProjectOwner' || assigneeIds.includes(userData?.getUserMe.id)) {
      view = [Config.DefaultToolbars.View, FillAndSign];
    } else {
      view = [Config.DefaultToolbars.View];
    }
  }
  const [updateFile] = Gql.useUpdateOneProjectDocumentMutation({
    onCompleted: () => {
      pushSuccess('File saved successfully');
    },
    onError: (err: any) => {
      pushError(err.message);
    }
  });

  const onUpdate = (files: any) => {
    updateFile({
      variables: {
        input: {
          id: _.toString(file?.id),
          update: {
            fileUrl: files
          }
        }
      }
    });
  };

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getPdfDoc();
    });
    RNPdftron.initialize(
      'Bina Cloudtech sdn bhd (bina.cloud):OEM:Bina::IA:AMS(20230928):D7863732B60C5D58B999E101404F3D38DD43F51255922C4C9BD65ABAB6F5C7'
    );
    RNPdftron.enableJavaScript(true);
  }, []);

  const path = data?.projectDocument?.fileUrl ?? '';
  const file = data?.projectDocument;

  if (!path) return null;

  const onLeadingNavButtonPressed = () => {
    if (Platform.OS === 'ios') {
      Alert.alert('App', 'onLeadingNavButtonPressed', [{ text: 'OK', onPress: () => {} }], {
        cancelable: true
      });
    } else {
      BackHandler.exitApp();
    }
  };

  // save the signature / any xfdf
  // const onSavedXfdf = async (xfdf: string) => {
  //   setIsSubmitting(true);
  //   try {
  //     await apolloClient.mutate<Gql.UpdateOneProjectDocumentMutation>({
  //       mutation: Gql.UpdateOneProjectDocumentDocument,
  //       variables: {
  //         input: {
  //           id: _.toString(id),
  //           update: {
  //             xfdf
  //           }
  //         }
  //       }
  //     });
  //     pushSuccess('Saved successfully');
  //   } catch (e) {
  //     pushError(e);
  //   } finally {
  //     setIsSubmitting(false);
  //   }
  // };

  // // duplicate standard form to all form
  // const duplicateStandardForm = async (id: number, xfdf: string) => {
  //   if (file?.category === Gql.CategoryType.StandardForm) {
  //     await apolloClient.mutate<Gql.DuplicateEditedStandardFormInputDto>({
  //       mutation: Gql.CreateOneProjectDocumentDocument,
  //       variables: {
  //         id,
  //         xfdf
  //       }
  //     });
  //   }
  // };

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <AppBar goBack barStyle={'dark-content'} title={fileName} noRight onPressTitle={() => null} />
      {path && (
        <DocumentView
          ref={viewerRef}
          document={path}
          onLoadComplete={(filePath: any) => {
            fetch(filePath)
              .then(response => response.blob())
              .then(blob => {});
          }}
          leadingNavButtonIcon={Platform.OS === 'ios' ? 'ic_close_black_24px.png' : 'ic_arrow_back_white_24dp'}
          showSavedSignatures={true}
          annotationToolbars={view}
          onLeadingNavButtonPressed={onLeadingNavButtonPressed}
          onDocumentLoaded={() => {
            if (file?.category === Gql.CategoryType.AllForm) {
              viewerRef?.current?.importAnnotations(file?.xfdf);
            }
          }}
        />
      )}
      <Footer>
        <Button
          variant="primary"
          isLoading={isSubmitting}
          onPress={async () => {
            await viewerRef.current.flattenAnnotations(false).then(() => {
              viewerRef.current.saveDocument().then((files: any) => {
                const newFile = generateRNFile({
                  uri: files,
                  name: file?.name as string,
                  type: 'application/pdf'
                });

                setNewFile(newFile);
                renameModalRef?.current?.pushModal();

                // onUpdate(newFile);
              });
            });
            // setTimeout(() => {
            //   navigation.goBack();
            // }, 1750);

            // viewerRef.current.exportAnnotations().then((filePath: any) => {
            //   //   onSavedXfdf(filePath);
            //   duplicateStandardForm(Number(id), filePath);
            // });
          }}
        >
          Save
        </Button>
      </Footer>
      <StandardFormPdfTronRenameModal
        ref={renameModalRef}
        refetch={() => {}}
        data={newFile}
        onSave={() => {
          navigation.goBack();
        }}
      />
    </Box>
  );
};

export default StandardFormPdfTron;
