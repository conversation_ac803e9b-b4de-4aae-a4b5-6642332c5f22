import { AppBar, Content, Footer } from '@commons';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { COLORS } from '@src/constants';
import { DigitalFormNavigatorParams, RootNavigatorParams } from '@src/types';
import { Avatar, Box, Button, Divider, FlatList, HStack, Text, VStack } from 'native-base';
import React, { useEffect, useState } from 'react';
import { StyleSheet } from 'react-native';

import SelectBox from 'react-native-multi-selectbox';

import _ from 'lodash';
import { pushError, pushSuccess } from '@src/configs';
import apolloClient from '@src/lib/apollo';
import { CreateManyRequestForSignaturesDocument } from '@src/api/graphql';

type Props = CompositeScreenProps<
  BottomTabScreenProps<DigitalFormNavigatorParams, 'RequestSignature'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const RequestSignature: React.FC<Props> = ({ route }) => {
  const documentId = route.params.documentId;

  const [selectedTeams, setSelectedTeams] = useState([]);

  const { data: projectUserData } = Gql.useProjectUsersQuery({
    variables: {
      filter: {
        role: { neq: Gql.ProjectUserRoleType.CanView }
      }
    }
  });

  const inviteProjectUser = projectUserData?.projectUsers?.nodes.map((u: any) => {
    return {
      item: `${u.user.name} - ${u.user.email}`,
      id: u.user.id,
      value: u.user.id
    };
  });

  const { data, refetch } = Gql.useRequestForSignaturesQuery({
    variables: {
      filter: { projectDocumentId: { eq: documentId } }
    }
  });

  const invitedUser = data?.requestForSignatures?.nodes?.map((member: any) => {
    return {
      id: member.id,
      name: member.signBy.name,
      email: member.signBy.email,
      avatar: member.signBy.avatar
    };
  });

  const onMultiChange = () => {
    return (value: any) => setSelectedTeams(_.xorBy(selectedTeams, [value], 'id') as any);
  };

  const onInvite = async () => {
    const newData: { signById: any; projectDocumentId: any }[] = await Promise.all(
      selectedTeams.map((s: any) => {
        return {
          signById: parseFloat(s.id),
          projectDocumentId: parseFloat(documentId)
        };
      })
    );

    try {
      await apolloClient.mutate<Gql.CreateManyRequestForSignaturesMutation>({
        mutation: CreateManyRequestForSignaturesDocument,
        variables: {
          input: {
            requestForSignatures: newData
          }
        }
      });
      pushSuccess('Invitation sent successfully');
    } catch (e) {
      pushError(e);
    }
  };

  return (
    <Box flex={1} bg="#FFFFFF">
      <AppBar header goBack title="Request Signature" noRight />

      <Box flex={1} bg="#FFFFFF">
        <Content pt={5}>
          <Text mb={4} color={COLORS.neutrals.gray90}>
            Invite user
          </Text>

          <Divider mb={4} />
          <Box style={styles.memberBox}>
            <SelectBox
              label=""
              inputPlaceholder="Select member's name or email"
              searchInputProps={{ placeholder: 'Search a member`s name or email here....' }}
              options={inviteProjectUser}
              selectedValues={selectedTeams}
              onMultiSelect={onMultiChange()}
              onTapClose={onMultiChange()}
              isMulti
              arrowIconColor={COLORS.neutrals.gray90}
              searchIconColor={COLORS.neutrals.gray70}
              toggleIconColor={COLORS.neutrals.gray70}
              multiOptionContainerStyle={{ backgroundColor: COLORS.primary[1] }}
              optionsLabelStyle={{ color: COLORS.neutrals.gray90, fontWeight: '400', fontSize: 14 }}
              containerStyle={{ borderBottomWidth: 1 }}
            />
          </Box>

          <Text mb={4} mt={7} color={COLORS.neutrals.gray90}>
            Who can view this document
          </Text>

          <Divider mb={4} />
          <FlatList
            data={invitedUser}
            keyExtractor={item => item.id}
            renderItem={({ item }) => (
              <Box>
                <HStack alignItems="center" space={3}>
                  <Avatar
                    size="32px"
                    source={{
                      uri: item.avatar
                    }}
                  />

                  <VStack width="85%">
                    <Text style={{ fontWeight: '600', fontSize: 14 }}>{item.name}</Text>
                    <Text style={{ fontWeight: '400', color: COLORS.neutrals.gray90 }}>{item.email}</Text>
                  </VStack>
                </HStack>
              </Box>
            )}
          />
        </Content>
        <Footer>
          <Button
            variant="primary"
            onPress={() => {
              onInvite();
            }}
          >
            Invite
          </Button>
        </Footer>
      </Box>
    </Box>
  );
};

interface FormValues {
  name: string[];
}

const styles = StyleSheet.create({
  memberBox: {
    borderWidth: 1,
    borderColor: COLORS.neutrals.gray40,
    borderRadius: 8,
    paddingLeft: 10,
    alignItem: 'center'
  }
});

export default RequestSignature;
