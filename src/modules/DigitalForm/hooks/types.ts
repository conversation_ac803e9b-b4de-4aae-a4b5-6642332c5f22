import { Gql } from '@src/api';

export interface ProjectUser {
  id: string;
  name: string;
  email: string;
  avatarUrl: string;
  userId: number;
}

export interface ProjectUserResponse {
  data: {
    pages: Array<{
      items: ProjectUser[];
      hasNextPage?: boolean;
    }>;
  };
  fetchNextPage: () => void;
  hasNextPage: boolean;
  isLoading: boolean;
  error: unknown;
  refetch: () => void;
}

export type Assignee = {
  id?: string;
  ownerId: number;
  signById: number;
  projectDocumentId?: number | null;
  user: {
    id: string;
    name: string;
    email: string;
    avatarUrl: string;
    userId?: number;
  };
  assigneeNo?: number;
  status: Gql.RequestForSignatureStatus;
  localProjectDocumentId: string;
};

export type AddAssigneeScreenParams = {
  type: Gql.WorkflowType;
  ass: Assignee[];
  onPressBack?: () => void;
};
