import { useCallback, useState } from 'react';
import { useGetProjectUser } from '@src/queries/project-user/useGetProjectUser';
import { ProjectUser, ProjectUserResponse } from './types';

export const useProjectUserSearch = (searchTerm: string) => {
  const [isRetrying, setIsRetrying] = useState(false);

  const result = useGetProjectUser(searchTerm ?? '') as ProjectUserResponse;

  const retry = useCallback(async () => {
    try {
      setIsRetrying(true);
      await result.refetch();
    } finally {
      setIsRetrying(false);
    }
  }, [result.refetch]);

  const projectUsers = result.data?.pages?.map(page => page.items).flat() ?? [];

  return {
    projectUsers,
    isLoading: result.isLoading || isRetrying,
    error: result.error,
    retry,
    hasMore: result.hasNextPage,
    loadMore: result.fetchNextPage,
    isEmpty: projectUsers.length === 0 && !result.isLoading && !isRetrying
  } as const;
};
