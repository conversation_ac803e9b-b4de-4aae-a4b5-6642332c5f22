import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { DigitalFormNavigatorParams } from '@src/types';
import React from 'react';
import AddAssignee from './pages/AddAssignee';
import AddCc from './pages/AddCc';
import AddCcDocumentDetails from './pages/AddCcDocumentDetails';
import CreateFolder from './pages/CreateFolder';
import DigitalForm from './pages/DigitalForm';
import DigitalFormFolder from './pages/DigitalFormFolder';
import DigitalFormPdfTron from './pages/DigitalFormPdfTron_dep';
import DigitalFormSubFolder from './pages/DigitalFormSubFolder';
import MovingFile from './pages/MovingFile';
import RequestSignature from './pages/RequestSignature';
import StandardFormPdfTron from './pages/StandardFormPdftron_dep';
import DigitalFormAttachedPdftron from './pages/DigitalFormAttachedPdftron_dep';
import DigitalFromPhotosPdftron from './pages/DigitalFormPhotosPdftron_dep';
import LinearDocumentDetail from './pages/LinearDocumentDetail';
import DynamicDocumentDetail from './pages/DynamicDocumentDetail';
import AddAssigneeApproveProceed from './pages/AddAssigneeApproveProceed';

const DigitalFormStack = createNativeStackNavigator<DigitalFormNavigatorParams>();

const DigitalFormNavigator: React.FC<any> = () => {
  return (
    <DigitalFormStack.Navigator initialRouteName="DigitalForm" screenOptions={{ headerShown: false }}>
      <DigitalFormStack.Screen name="DigitalForm" component={DigitalForm} />
      <DigitalFormStack.Screen name="DigitalFormFolder" component={DigitalFormFolder} />
      <DigitalFormStack.Screen name="DigitalFormSubFolder" component={DigitalFormSubFolder} />
      <DigitalFormStack.Screen name="RequestSignature" component={RequestSignature} />
      <DigitalFormStack.Screen name="DigitalFormPdfTron" component={DigitalFormPdfTron} />
      <DigitalFormStack.Screen name="AddAssignee" component={AddAssignee} />
      <DigitalFormStack.Screen name="AddAssigneeApproveProceed" component={AddAssigneeApproveProceed} />
      <DigitalFormStack.Screen name="AddCc" component={AddCc} />
      <DigitalFormStack.Screen name="AddCcDocumentDetails" component={AddCcDocumentDetails} />
      <DigitalFormStack.Screen name="MovingFile" component={MovingFile} />
      <DigitalFormStack.Screen name="CreateFolder" component={CreateFolder} />
      <DigitalFormStack.Screen name="StandardFormPdfTron" component={StandardFormPdfTron} />
      <DigitalFormStack.Screen name="DigitalFormAttachedPdftron" component={DigitalFormAttachedPdftron} />
      <DigitalFormStack.Screen name="DigitalFormPhotosPdftron" component={DigitalFromPhotosPdftron} />
      <DigitalFormStack.Screen name="DocumentDetail" component={LinearDocumentDetail} />
      <DigitalFormStack.Screen name="DynamicDocumentDetail" component={DynamicDocumentDetail} />
    </DigitalFormStack.Navigator>
  );
};

export default DigitalFormNavigator;
