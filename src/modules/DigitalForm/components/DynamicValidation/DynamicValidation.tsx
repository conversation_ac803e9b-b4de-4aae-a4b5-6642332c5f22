import React, { forwardRef, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { Icon, Modal } from '@commons';
import { Gql } from '@src/api';
import { pushError, pushMessaage } from '@src/configs';
import { useDispatch, useSelector } from '@src/store';
import { Box, Button, Divider, Flex, HStack, Text, View } from 'native-base';
import { Alert, StyleSheet, TouchableOpacity } from 'react-native';
import NonConformanceReportBtn from '../Document/NonConformanceReport';
import useValidationWorkspace, {
  dynamicWorkspaceValidation
} from '@src/mutation/request-for-signature/useValidationWorkspace';
import RequestForSignaturesModel from '@src/database/model/request-for-signatures.model';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';

export interface ModalRef {
  pushModal: () => void;
}

interface Props {
  refetch?: () => void;
  data: any;
  navigation: any;
}

const ValidationModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const { data } = props;
  const { user: me } = useSelector(state => state.auth);
  const dispatch = useDispatch();
  const currentUserId = parseInt(me?.id || '0', 10);
  const { mutateAsync: updateValidation } = useValidationWorkspace();

  useImperativeHandle(ref, () => ({
    pushModal: () => modalRef.current?.pushModal()
  }));

  const isRFI = data?.document?.group?.parent?.name === 'Request For Information';
  const documentStatus = data?.status;

  const currentUserSignature = useMemo(() => {
    if (data?.document?.currentUserId) {
      return data?.requestForSignature?.find(
        (s: RequestForSignaturesModel) => s.remoteId === data?.document?.currentUserId
      );
    }

    return data?.requestForSignature?.find(
      (s: RequestForSignaturesModel) => s.id === data?.document?.localCurrentUserId
    );
  }, [data]);

  const currentSignatureTurn = currentUserSignature
    ? currentUserSignature.status !== Gql.RequestForSignatureStatus.Approved
      ? currentUserSignature
      : undefined
    : undefined;

  const getAmendUsers = data?.requestForSignature
    ?.filter((obj: RequestForSignaturesModel) => obj.status === 'Amend')
    .map((obj: any) => obj.signById);

  const showRequestAmend =
    currentUserSignature?.status === 'Sent' &&
    currentUserSignature?.signById === currentUserSignature?.ownerId &&
    currentUserSignature?.assigneeNo === '1';

  const handleAction = async (action: string, type: string) => {
    const confirmMessage =
      action === 'approve'
        ? `Are you sure you want to ${isRFI ? 'answer' : 'approve'} this document?`
        : action === 'reject'
          ? 'Are you sure you want to reject this document?'
          : action === 'requestAmend'
            ? 'Are you sure you want to amend this document?'
            : action === 'resubmit'
              ? 'Are you sure you want to resubmit this document?'
              : 'Are you sure you want to make partial approval this document?';

    const successMessage =
      action === 'approve'
        ? 'Document Approved'
        : action === 'reject'
          ? 'Document Rejected'
          : action === 'requestAmend'
            ? 'Document Amended'
            : action === 'resubmit'
              ? 'Document Resubmitted'
              : 'Sent to owner';

    const mutateParams: dynamicWorkspaceValidation = {
      documentId: data?.document?.id,
      requestForSignatureId: currentUserSignature?.id,
      type: type as dynamicWorkspaceValidation['type']
    };

    const confirm = await new Promise<boolean>(resolve =>
      Alert.alert(
        'Confirmation',
        confirmMessage,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Confirm',
            onPress: () => resolve(true)
          }
        ],
        { cancelable: false }
      )
    );

    if (confirm) {
      try {
        await updateValidation(mutateParams);

        dispatch(documentWorkspaceActions.ShouldSetInitialState(true));
        pushMessaage(successMessage, 'success');
        modalRef.current?.pushModal();
      } catch (e) {
        pushError((e as any).message);
      }
    }
  };

  const onApprove = () => handleAction('approve', 'Approve Close');
  const onReject = () => handleAction('reject', 'Reject');
  const onRequestAmend = () => handleAction('requestAmend', 'Amend');
  const onResubmit = () => handleAction('resubmit', 'Resubmit');
  const onInProgress = () => handleAction('inProgress', 'Work In Progress');

  const onProceed = () => {
    modalRef.current?.pushModal();
    dispatch(documentWorkspaceActions.ShouldSetInitialState(true));
    props.navigation.navigate('DigitalFormNav', {
      screen: 'AddAssigneeApproveProceed',
      params: {
        documentId: data?.document?.id,
        currentAssigneeId: currentUserSignature?.id,
        groupName: data?.group?.parent?.name,
        onPressBack: () => modalRef.current?.pushModal()
      }
    });
  };

  const onInReview = () => handleAction('inReview', 'In Review');

  let button;

  if (
    documentStatus === Gql.ProjectDocumentStatus.Submitted ||
    documentStatus === Gql.ProjectDocumentStatus.Pending ||
    documentStatus === Gql.ProjectDocumentStatus.InProgress ||
    documentStatus === Gql.ProjectDocumentStatus.Amend
  ) {
    if (currentSignatureTurn?.status !== 'Approved' && currentSignatureTurn?.signById === currentUserId) {
      button = (
        <TouchableOpacity onPress={onInReview}>
          <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4} m={5}>
            <HStack space={2}>
              <Icon name="flag" fill="#F29100" />
              <Text fontSize="16px" color="#8F8989">
                In Review
              </Text>
            </HStack>
            {/* {filter.status?.includes(Gql.ProjectDocumentStatus.InReview) ? <Icon name="tick" /> : <></>} */}
          </Box>
        </TouchableOpacity>
      );
    } else {
      button = (
        <Box flexDirection="row" alignItems="center" justifyContent="space-between" mx={5} mt={4} mb={4}>
          <HStack space={2}>
            <Icon name="flag" fill="#74caec" />
            <Text fontSize="16px" color="#8F8989">
              Pending {getAmendUsers?.includes(currentUserId) ? 'Amendment' : 'Approval'}
            </Text>
          </HStack>
          {/* {filter.status?.includes(Gql.ProjectDocumentStatus.Rejected) ? <Icon name="tick" /> : <></>} */}
        </Box>
      );
    }
  } else if (documentStatus === Gql.ProjectDocumentStatus.InReview) {
    if (currentUserSignature?.status === 'Amend' && currentUserSignature?.signById === currentUserId) {
      button = (
        <Flex direction="column" px={5}>
          {currentUserSignature?.ownerId !== currentUserSignature?.signById ||
          currentUserSignature?.assigneeNo !== 1 ? (
            <TouchableOpacity onPress={onRequestAmend}>
              <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
                <HStack space={2}>
                  <Icon name="flag" fill="#F29100" />
                  <Text fontSize="16px" color="#8F8989">
                    Request Previous Amendment
                  </Text>
                </HStack>
                {/* {filter.status?.includes(Gql.ProjectDocumentStatus.Approved) ? <Icon name="tick" /> : <></>} */}
              </Box>
            </TouchableOpacity>
          ) : null}
          <Divider mt={4} />
          <TouchableOpacity onPress={onResubmit}>
            <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
              <HStack space={2}>
                <Icon name="flag" fill="#3E78CF" />
                <Text fontSize="16px" color="#8F8989">
                  Resubmit
                </Text>
              </HStack>
              {/* {filter.status?.includes(Gql.ProjectDocumentStatus.Submitted) ? <Icon name="tick" /> : <></>} */}
            </Box>
          </TouchableOpacity>
          {!isRFI ? (
            <>
              <Divider mt={4} />
              <TouchableOpacity onPress={onReject}>
                <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
                  <HStack space={2}>
                    <Icon name="flag" fill="#F40000" />
                    <Text fontSize="16px" color="#8F8989">
                      Reject
                    </Text>
                  </HStack>
                  {/* {filter.status?.includes(Gql.ProjectDocumentStatus.InReview) ? <Icon name="tick" /> : <></>} */}
                </Box>
              </TouchableOpacity>
            </>
          ) : null}
          <Divider mt={4} />
        </Flex>
      );
    } else if (currentUserSignature?.status === 'Amend' || currentUserSignature?.signById !== currentUserId) {
      button = (
        <TouchableOpacity onPress={() => {}}>
          <Box flexDirection="row" alignItems="center" justifyContent="space-between" mx={5} mt={4} mb={4}>
            <HStack space={2}>
              <Icon name="flag" fill="#74caec" />
              <Text fontSize="16px" color="#8F8989">
                Pending {getAmendUsers?.includes(currentUserId) ? 'Amendment' : 'Approval'}
              </Text>
            </HStack>
            {/* {filter.status?.includes(Gql.ProjectDocumentStatus.Rejected) ? <Icon name="tick" /> : <></>} */}
          </Box>
        </TouchableOpacity>
      );
    } else if (
      currentUserSignature?.status !== 'Approved' &&
      currentUserSignature?.status !== 'Rejected' &&
      currentUserSignature
    ) {
      button = (
        <Flex direction="column" px={5}>
          <TouchableOpacity onPress={onApprove}>
            <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
              <HStack space={2}>
                <Icon name="flag" fill="#18A601" />
                {!isRFI ? (
                  <Text fontSize="16px" color="#8F8989">
                    Approved {`>`} Close
                  </Text>
                ) : (
                  <Text fontSize="16px" color="#8F8989">
                    Answer
                  </Text>
                )}
              </HStack>
              {/* {filter.status?.includes(Gql.ProjectDocumentStatus.Approved) ? <Icon name="tick" /> : <></>} */}
            </Box>
          </TouchableOpacity>
          <Divider mt={4} />
          <TouchableOpacity onPress={onProceed}>
            <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
              <HStack space={2}>
                <Icon name="flag" fill="#3E78CF" />
                <Text fontSize="16px" color="#8F8989">
                  Approve {`>`} Proceed
                </Text>
              </HStack>
              {/* {filter.status?.includes(Gql.ProjectDocumentStatus.Submitted) ? <Icon name="tick" /> : <></>} */}
            </Box>
          </TouchableOpacity>
          {!showRequestAmend ? (
            <>
              <Divider mt={4} />
              <TouchableOpacity onPress={onRequestAmend}>
                <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
                  <HStack space={2}>
                    <Icon name="flag" fill="#F29100" />
                    <Text fontSize="16px" color="#8F8989">
                      Request Amendment
                    </Text>
                  </HStack>
                  {/* {filter.status?.includes(Gql.ProjectDocumentStatus.InReview) ? <Icon name="tick" /> : <></>} */}
                </Box>
              </TouchableOpacity>
            </>
          ) : null}
          <Divider mt={4} />
          {!isRFI ? (
            <TouchableOpacity onPress={onReject}>
              <Box
                flexDirection="row"
                alignItems="center"
                justifyContent="space-between"
                mt={4}
                mb={
                  currentUserSignature?.signById === currentUserSignature?.ownerId &&
                  currentUserSignature?.assigneeNo !== '1'
                    ? 0
                    : 4
                }
              >
                <HStack space={2}>
                  <Icon name="flag" fill="#F40000" />
                  <Text fontSize="16px" color="#8F8989">
                    Reject
                  </Text>
                </HStack>
                {/* {filter.status?.includes(Gql.ProjectDocumentStatus.Rejected) ? <Icon name="tick" /> : <></>} */}
              </Box>
            </TouchableOpacity>
          ) : null}
          {currentUserSignature?.signById === currentUserSignature?.ownerId &&
          currentUserSignature?.assigneeNo !== '1' ? (
            <>
              <Divider mt={4} />
              <TouchableOpacity onPress={() => onInProgress()}>
                <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4} mb={4}>
                  <HStack space={2}>
                    <Icon name="flag" fill="#9ce255" />
                    <Text fontSize="16px" color="#8F8989">
                      Work In Progress
                    </Text>
                  </HStack>
                  {/* {filter.status?.includes(Gql.ProjectDocumentStatus.Rejected) ? <Icon name="tick" /> : <></>} */}
                </Box>
              </TouchableOpacity>
            </>
          ) : null}
        </Flex>
      );
    }
  }

  let getBtn = button;
  if (data?.projectDocument?.workspaceGroup?.parent?.name === 'Non Conformance Report') {
    getBtn = (
      <NonConformanceReportBtn
        ownerId={data?.projectDocument?.owner?.id}
        requestForSignature={data?.projectDocument?.requestForSignatures}
        onInReview={onInReview}
        onRequestAmend={onRequestAmend}
        onResubmit={onResubmit}
        onApprove={onApprove}
        onReject={onReject}
        onInProgress={() => onInProgress()}
        onProceed={onProceed}
        getAmendUsers={getAmendUsers}
        currentUserSignature={currentUserSignature}
        navigation={props.navigation}
        currentUserId={data?.projectDocument?.currentUserId}
        showRequestAmend={showRequestAmend}
        documentStatus={documentStatus}
        currentSignatureTurn={currentSignatureTurn}
        workflow="Dynamic"
        me={me}
      />
    );
  }

  return (
    <Modal ref={modalRef} type="bottom">
      <View>
        <Flex direction="row" style={style.container} width="100%" paddingBottom={1} justifyContent="space-between">
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            bg="transparent"
            onPress={() => modalRef.current.closeModal()}
          >
            <HStack>
              <Icon name={'chevron-left'} fill={'#0695D7'} />
              <Text color="#0695D7">Back</Text>
            </HStack>
          </Button>
        </Flex>
        {getBtn}
      </View>
    </Modal>
  );
});

const style = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    height: 48,
    justifyContent: 'space-between',
    padding: 6,
    alignItems: 'center'
  }
});

ValidationModal.displayName = 'ValidationModal';
export default ValidationModal;
