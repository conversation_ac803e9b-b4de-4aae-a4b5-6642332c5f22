import React, { useCallback } from 'react';
import { VStack } from 'native-base';
import { FlashList } from '@shopify/flash-list';
import type { ProjectUser } from '../../hooks/types';
import { ProjectUserItem } from '@commons/project-user/ProjectUserItem';
import { SkeletonItem } from '@commons/project-user/SkeletonItem';

interface ProjectUserListProps {
  data: ProjectUser[];
  selectedIds: Set<string>;
  onSelect: (item: ProjectUser) => void;
  onEndReached: () => void;
  isLoading: boolean;
}

export const ProjectUserList = React.memo(
  ({ data, selectedIds, onSelect, onEndReached, isLoading }: ProjectUserListProps) => {
    if (isLoading) {
      return (
        <VStack space={2}>
          {Array.from({ length: 10 }).map((_, index) => (
            <SkeletonItem key={index} />
          ))}
        </VStack>
      );
    }

    const renderItem = useCallback(
      ({ item }: { item: ProjectUser }) => (
        <ProjectUserItem item={item} selectedIds={selectedIds} onSelect={onSelect} />
      ),
      [selectedIds, onSelect]
    );

    const keyExtractor = useCallback((item: ProjectUser) => String(item.userId), []);

    return (
      <FlashList
        keyboardShouldPersistTaps="handled"
        data={data}
        keyExtractor={keyExtractor}
        renderItem={renderItem}
        estimatedItemSize={50}
        onEndReachedThreshold={0.5}
        onEndReached={onEndReached}
        extraData={selectedIds}
      />
    );
  },
  (prev, next) => prev.data === next.data && prev.selectedIds === next.selectedIds && prev.isLoading === next.isLoading
);

ProjectUserList.displayName = 'ProjectUserList';
