import { Icon, Modal } from '@commons';
import { generateRNFile } from '@src/configs';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import { useDispatch, useSelector } from '@src/store';
import { ReactNativeFile } from 'apollo-upload-client';
import { Box, Button, HStack, Text } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState, useEffect } from 'react';
import { Alert, ModalProps, Platform, StyleSheet } from 'react-native';
import ImagePicker from 'react-native-image-crop-picker';
import prompt from 'react-native-prompt-android';

interface Props {}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
  closeModal: (v?: any) => void;
}

const AddAttachmentPhotoModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const [totalSize, setTotalSize] = useState(0);
  const [file, setFile] = useState<any>(null);

  const { user: userMeData } = useSelector(state => state.auth);
  const workspaceDocuments = useSelector(state => state.documentWorkspace);
  const dispatch = useDispatch();

  const choosePhoto = async (Files: ReactNativeFile[]) => {
    try {
      const updatedFiles = Files.map(file => ({
        ...file,
        projectDocumentId: workspaceDocuments?.DocumentDetails?.remoteId as number,
        userId: parseInt(userMeData?.id ?? '0'),
        fileUrl: file.uri,
        localProjectDocumentId: workspaceDocuments.selectedDocumentId,
        uri: file.uri
      }));

      dispatch(
        documentWorkspaceActions.setDocumentDetails({
          ...workspaceDocuments.DocumentDetails,
          photos: [...(workspaceDocuments?.DocumentDetails?.photos as any), ...updatedFiles]
        })
      );
    } catch (error) {}
  };

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    },
    closeModal: () => {
      modalRef?.current?.closeModal();
    }
  }));

  //Handling Video File Limit
  useEffect(() => {
    if (totalSize > 104857600) {
      Alert.alert('Reminder', 'Video size must be less than 100MB per upload');
      return;
    } else {
      const newFile = generateRNFile({
        uri: file?.path,
        name:
          Platform.OS === 'ios'
            ? (file?.filename as string)
            : file?.path.replace('_cloud.bina.android', '').substring(file?.path.lastIndexOf('/') + 1),
        type: file?.mime as string
      });
      if (newFile) {
        choosePhoto([newFile]);
        setTotalSize(0);
        setFile(null);
      }
    }
  }, [totalSize, file]);

  // Choose Photo
  const openPhotoLibrary = () => {
    ImagePicker.openPicker({
      cropping: true,
      multiple: true,
      forceJpg: true
    }).then(async images => {
      let files = [] as any;
      images.forEach(element => {
        const newFile = generateRNFile({
          uri: element.path,
          name:
            Platform.OS === 'ios'
              ? (element.filename as string)
              : element.path.replace('_cloud.bina.android', '').substring(element.path.lastIndexOf('/') + 1),
          type: element.mime as string
        });
        files.push(newFile);
      });
      choosePhoto?.(files as any);
    });
  };

  // Choose Video
  const openVideoLibrary = () => {
    ImagePicker.openPicker({
      mediaType: 'video'
    }).then(async image => {
      const newTotalSize = +image.size + +totalSize;
      setTotalSize(newTotalSize);
      setFile(image);
    });
  };

  // Take Photo
  const openCamera = () => {
    ImagePicker.openCamera({
      width: 500,
      height: 500,
      cropping: true,
      forceJpg: true,
      includeBase64: true
    }).then(image => {
      //set timeout to wait for image to be saved
      setTimeout(() => {
        if (Platform.OS === 'ios') {
          Alert.prompt('Photo Name', 'Please enter photo name', [
            {
              text: 'Cancel',
              onPress: () => {},
              style: 'cancel'
            },
            {
              text: 'OK',
              onPress: (value: any) => {
                if (!value.endsWith('.jpg')) {
                  value = value + '.jpg';
                }
                addPhotoName(value, image?.path);
              }
            }
          ]);
        } else if (Platform.OS === 'android') {
          prompt('Photo Name', 'Please enter photo name', [
            {
              text: 'Cancel',
              onPress: () => {},
              style: 'cancel'
            },
            {
              text: 'OK',
              onPress: (value: any) => {
                if (!value.endsWith('.jpg')) {
                  value = value + '.jpg';
                }
                addPhotoName(value, image?.path);
              }
            }
          ]);
        }
      }, 400);
    });
  };

  const addPhotoName = async (value: any, imagePath: any) => {
    let files: any = [];
    const image: any = generateRNFile({ uri: imagePath, name: value, type: 'image/jpeg' });
    files.push(image);
    choosePhoto?.(files);
    modalRef.current?.closeModal();
  };

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="bottom">
      <HStack justifyContent={'space-evenly'} px={0}>
        <Button
          width={'50%'}
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          my={6}
          bg="transparent"
          onPress={() => {
            Alert.alert('Choose Media', 'Which file would you want to upload?', [
              {
                text: 'Video',
                onPress: () => {
                  openVideoLibrary();
                },
                style: 'default'
              },
              {
                text: 'Photos',
                onPress: () => {
                  openPhotoLibrary();
                },
                style: 'default'
              }
            ]);
          }}
          justifyContent={'center'}
        >
          <Box
            alignItems={'center'}
            justifyContent={'center'}
            width={16}
            height={16}
            alignSelf={'center'}
            borderRadius={32}
            borderWidth={2}
            borderColor={'neutrals.gray6'}
          >
            <Icon name="add-contact" />
          </Box>
          <Text color="#6F6D6D">Browse Gallery</Text>
        </Button>

        <Button
          width={'50%'}
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          my={6}
          bg="transparent"
          onPress={() => {
            openCamera();
          }}
          justifyContent={'center'}
        >
          <Box
            alignItems={'center'}
            justifyContent={'center'}
            width={16}
            height={16}
            alignSelf={'center'}
            borderRadius={32}
            borderWidth={2}
            borderColor={'neutrals.gray6'}
          >
            <Icon name="take-photo" />
          </Box>
          <Text color="#6F6D6D">Take Photo</Text>
        </Button>
      </HStack>
    </Modal>
  );
});

const styles = StyleSheet.create({
  folder: {
    borderWidth: 2,
    borderColor: '#756D6D',
    borderRadius: 100,
    backgroundColor: 'transparent',
    height: 52,
    width: 52
  }
});

AddAttachmentPhotoModal.displayName = 'AddAttachmentPhotoModal';
export default AddAttachmentPhotoModal;
