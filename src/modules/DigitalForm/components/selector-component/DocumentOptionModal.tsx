import { Modal } from '@commons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Gql } from '@src/api';
import { workspace } from '@src/lib/authority';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import { useDispatch, useSelector } from '@src/store';
import { RootNavigatorParams } from '@src/types';
import { Button, Text } from 'native-base';
import React, { FC, memo, useMemo, lazy, Suspense } from 'react';
import { Alert } from 'react-native';
import { pushError, pushSuccess } from '@src/configs';
import useUpdateWorkspaceDocument from '@src/mutation/workspace/useUpdateWorkspaceDocument';

// Lazy load the RequestSignatureModal component
const RequestSignatureModal = lazy(() => import('../Document/RequestSignatureModal'));

interface Props {
  onChange?: (value: string) => void;
  onDelete?: (value: string) => void;
  refetch?: () => void;
  data?: any;
}

const DocumentOptionModal: FC<Props> = props => {
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const dispatch = useDispatch();
  const states = useSelector(state => state.documentWorkspace);
  const project = useSelector(state => state.project);
  const perm = workspace(project.projectUserRole);
  const { mutateAsync: updateWorkspaceDocument } = useUpdateWorkspaceDocument();

  const changeWorkflow = async () => {
    const currentWorkflow = props.data?.workflow;
    let updatedWorkflow: Gql.WorkflowType.Linear | Gql.WorkflowType.Dynamic;

    if (currentWorkflow === Gql.WorkflowType.Linear) {
      updatedWorkflow = Gql.WorkflowType.Dynamic;
    } else {
      updatedWorkflow = Gql.WorkflowType.Linear;
    }

    try {
      const data = props.data || {};

      await updateWorkspaceDocument({
        id: data.id,
        workflow: updatedWorkflow,
        status: data.status,
        assignees: []
      });

      pushSuccess('Successfully changed workflow');
      props.refetch?.();
      dispatch(documentWorkspaceActions.closeAll());
    } catch (error) {
      pushError('Failed to change workflow.');
    }
  };

  // Only compute this when needed
  const isModalVisible = useMemo(() => states.isDocumentOptionModalOpen, [states.isDocumentOptionModalOpen]);

  // Don't render anything if modal is not visible
  if (!isModalVisible) {
    return null;
  }

  return (
    <>
      <Modal
        isVisible={isModalVisible}
        onClose={() => {
          dispatch(documentWorkspaceActions.closeAll());
        }}
        style={{ marginTop: 40 }}
        type="bottom"
      >
        {props?.data?.status == Gql.ProjectDocumentStatus.Draft && perm.documents.canRequestApproval && (
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            mt={2}
            mb={4}
            bg="transparent"
            onPress={() => {
              // if (props?.data?.xfdf) {
              //   Alert.alert('Warning > Draft Mode', 'Please save the document before requesting approval.', [
              //     {
              //       text: 'View Document',
              //       style: 'default',
              //       onPress: () => {
              //         dispatch(documentWorkspaceActions.closeAll());
              //         navigation.navigate('PdftronNav', {
              //           screen: 'Pdftron',
              //           params: {
              //             id: props.data?.id ?? '',
              //             // role: project.projectUserRole ?? '',
              //             assigneeIds: '',
              //             // addedBy: props.data?.projectDocument?.addedBy ?? '',
              //             modules: 'Workspace',
              //             // driveType: data?.projectDocument?.driveType,
              //             status: props.data?.status as any
              //           }
              //         });
              //       }
              //     },
              //     {
              //       text: 'Cancel',
              //       style: 'cancel'
              //     }
              //   ]);
              // } else {
              dispatch(documentWorkspaceActions.openRequestApprovalModal());
              // }
            }}
          >
            <Text>Request Approval</Text>
          </Button>
        )}

        {props?.data?.workflow !== null && props.data?.status === Gql.ProjectDocumentStatus.Draft && (
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            mb={4}
            bg="transparent"
            disabled={props.data?.status !== Gql.ProjectDocumentStatus.Draft}
            onPress={() => {
              const getNewWorkflow =
                props?.data?.workflow == Gql.WorkflowType.Linear ? Gql.WorkflowType.Dynamic : Gql.WorkflowType.Linear;

              Alert.alert(
                'Change Workflow',
                `Are you sure you want to change workflow to ${getNewWorkflow}?`,
                [
                  {
                    text: 'Cancel',
                    style: 'cancel'
                  },
                  {
                    text: 'Confirm',
                    onPress: changeWorkflow
                  }
                ],
                { cancelable: false }
              );
            }}
          >
            <Text>Change Workflow</Text>
          </Button>
        )}

        {perm.documents.canDelete && (
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            mb={4}
            bg="transparent"
            onPress={() => {
              Alert.alert(
                'Delete Document',
                'Are you sure you want to delete this document?',
                [
                  {
                    text: 'Cancel',
                    style: 'cancel'
                  },
                  {
                    text: 'Delete',
                    onPress: () => {
                      props.onDelete?.(states.selectedDocumentId!);
                    }
                  }
                ],
                { cancelable: false }
              );
            }}
          >
            <Text color="semantics.danger">Delete</Text>
          </Button>
        )}
        <Suspense fallback={null}>
          {isModalVisible && (
            <RequestSignatureModal
              data={props.data}
              refetch={() => {
                props?.refetch?.();
              }}
            />
          )}
        </Suspense>
      </Modal>
    </>
  );
};

DocumentOptionModal.displayName = 'DocumentOptionModal';
export default memo(DocumentOptionModal);
