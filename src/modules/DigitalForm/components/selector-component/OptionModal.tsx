import { Modal } from '@commons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { CategoryType } from '@src/api/graphql';
import { workspace } from '@src/lib/authority';
import { useSelector } from '@src/store';
import { RootNavigatorParams } from '@src/types';
import { Button, Text } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { ActivityIndicator, Alert, ModalProps } from 'react-native';
import TemplateRenameModal from '../Template/TemplateRenameModal';

interface Props {
  onChange?: (values: string) => void;
  onDelete?: (values: string) => void;
  refetch?: () => void;
  selectedDocumentId: string;
  data?: any;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
  closeModal: (v?: any) => void;
}

const OptionModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const renameModalRef = useRef<any>(null);
  const project = useSelector(state => state.project);
  const perm = workspace(project.projectUserRole);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    },
    closeModal: () => {
      modalRef?.current?.closeModal();
    }
  }));

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="bottom">
      {/* <Button
        _pressed={{
          bg: 'transparent',
          opacity: 0.8
        }}
        mt={2}
        mb={4}
        bg="transparent"
        onPress={() => {
          props?.onChange?.(props.selectedDocumentId);
        }}
      >
        <Text>Download Document</Text>
      </Button> */}

      <Button
        _pressed={{
          bg: 'transparent',
          opacity: 0.8
        }}
        mb={4}
        bg="transparent"
        onPress={() => {
          renameModalRef?.current?.pushModal();
        }}
      >
        <Text>Rename</Text>
      </Button>

      {props?.data?.category == CategoryType.StandardForm && perm.templates.canDelete && (
        <Button
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          mb={4}
          bg="transparent"
          onPress={() => {
            Alert.alert('Delete', 'Are you sure you want to delete this?', [
              {
                text: 'Cancel',
                style: 'cancel'
              },
              {
                text: 'Delete',
                style: 'destructive',
                onPress: () => {
                  props?.onDelete?.(props.selectedDocumentId);
                }
              }
            ]);
          }}
        >
          <Text color="semantics.danger">Delete</Text>
        </Button>
      )}

      {/* <Button
        _pressed={{
          bg: 'transparent',
          opacity: 0.8
        }}
        mb={4}
        bg="transparent"
        onPress={() => {
          navigation.navigate('DigitalFormNav', { screen: 'MovingFile', params: { docId: +props?.selectedDocumentId, category: props?.data?.category } });
          modalRef?.current?.closeModal();
        }}
      >
        <Text>Move File</Text>
      </Button> */}

      <TemplateRenameModal
        ref={renameModalRef}
        refetch={() => {
          props?.refetch?.();
          modalRef?.current?.closeModal();
        }}
        data={props?.data}
      />
    </Modal>
  );
});

OptionModal.displayName = 'OptionModal';
export default OptionModal;
