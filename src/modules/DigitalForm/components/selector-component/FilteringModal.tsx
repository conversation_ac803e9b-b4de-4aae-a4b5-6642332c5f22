import { Icon, Modal } from '@commons';
import { <PERSON>, But<PERSON>, Divider, Flex, Text, View } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, memo, useMemo } from 'react';
import { ModalProps, StyleSheet, TouchableOpacity } from 'react-native';
import { useDispatch, useSelector } from '@src/store';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import _ from 'lodash';
import { getDocumentStatus } from '@src/configs';
import { Gql } from '@src/api';

// Import modal components directly instead of lazy loading
import AssignedToModal from '../Filtering/AssignedToModal';
import CcModal from '../Filtering/CcModal';
import GroupModal from '../Filtering/GroupModal';
import StatusModal from '../Filtering/StatusModal';
import AllFormCodeModal from '../Filtering/AllFormCodeModal';
import GroupCodeModal from '../Filtering/GroupCodeModal';

export interface DocumentFilterInterface {
  status: string[];
  assigneeTo: string[];
  cc: string[];
  group: string;
  allFormCode: number[];
}
interface Props {
  onFilter?: (values: DocumentFilterInterface) => void;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const FilteringModal = memo(
  forwardRef<ModalRef, Props>((props, ref) => {
    const modalRef = useRef<any>(null);
    const statusModalRef = useRef<any>(null);
    const groupCodeModalRef = useRef<any>(null);
    const assignedToModalRef = useRef<any>(null);
    const ccModalRef = useRef<any>(null);
    const groupModalRef = useRef<any>(null);
    const allFormCodeModalRef = useRef<any>(null);
    const states = useSelector(state => state.documentWorkspace);
    const dispatch = useDispatch();
    const { filter } = useSelector(state => state.documentWorkspace);
    const role = useSelector(state => state.project.projectUserRole);
    const isViewer = role === Gql.ProjectUserRoleType.CanView;

    const idFilter = filter.allFormCode ? filter.allFormCode : 'None';

    // We're using isVisible directly from Redux state, so we don't need to use modalRef
    useImperativeHandle(ref, () => ({
      pushModal: () => {
        // Instead of using modalRef, we'll directly dispatch the action to open the modal
        dispatch(documentWorkspaceActions.openDocumentFilterModal());
      }
    }));

    // Memoize the visibility state to prevent unnecessary re-renders
    const isVisible = useMemo(() => states.isDocumentFilterModalOpen, [states.isDocumentFilterModalOpen]);

    // Make sure we render the modals even when the main modal is not visible
    // This ensures the refs are properly initialized
    if (!isVisible) {
      return (
        <>
          <AllFormCodeModal ref={allFormCodeModalRef} />
          <StatusModal ref={statusModalRef} />
          <AssignedToModal ref={assignedToModalRef} />
          <CcModal ref={ccModalRef} />
          <GroupModal ref={groupModalRef} />
          <GroupCodeModal ref={groupCodeModalRef} />
        </>
      );
    }

    return (
      <Modal
        ref={modalRef}
        isVisible={isVisible}
        onClose={() => {
          dispatch(documentWorkspaceActions.closeAll());
        }}
        type="bottom"
      >
        <View>
          <Flex direction="row" style={style.container} width="100%" mb={2}>
            <Button
              _pressed={{
                bg: 'transparent',
                opacity: 0.8
              }}
              bg="transparent"
              pb={1}
              onPress={() => {
                dispatch(documentWorkspaceActions.clearFilter());
              }}
            >
              <Text color="#0695D7">Clear Filters</Text>
            </Button>

            <Button
              _pressed={{
                bg: 'transparent',
                opacity: 0.8
              }}
              pb={1}
              bg="transparent"
              onPress={() => {
                dispatch(documentWorkspaceActions.setFilterItems(states.filter));
                dispatch(documentWorkspaceActions.closeAll());
              }}
            >
              <Text color="#0695D7">Apply Filters</Text>
            </Button>
          </Flex>
          <View bg="#F0F0F0" h="40px" justifyContent="center">
            <Text fontSize="16" fontWeight="600px" pl={5}>
              Filter
            </Text>
          </View>
          <Flex direction="column" p={5}>
            <TouchableOpacity
              onPress={() => {
                allFormCodeModalRef?.current?.pushModal?.();
              }}
            >
              <Box flexDirection="row" justifyContent="space-between">
                <Box flexDirection="row" alignItems="center">
                  <Icon name="id" />
                  <Text fontSize="16px"> ID</Text>
                </Box>

                <Box flexDirection="row">
                  <Text color="#8B8B8B" fontSize="16px">
                    {idFilter}
                    {/* {filteredValue.allFormCode && filteredValue.allFormCode.length > 0
                    ? filteredValue.allFormCode.join(', ')
                    : 'Choose IDs'} */}
                  </Text>
                  <Icon name="chevron-right" />
                </Box>
              </Box>
            </TouchableOpacity>

            <Divider mt={4} />

            <TouchableOpacity
              onPress={() => {
                groupCodeModalRef?.current?.pushModal?.();
              }}
            >
              <Box flexDirection="row" justifyContent="space-between" mt={4}>
                <Box flexDirection="row" alignItems="center">
                  <Icon name="group-code" />
                  <Text fontSize="16px"> Group Code</Text>
                </Box>

                <Box flexDirection="row">
                  <Text color="#8B8B8B" fontSize="16px">
                    {filter?.groupCode && filter?.parentCode
                      ? `${filter?.parentCode}-${filter?.groupCode}`
                      : filter?.parentCode && !filter?.groupCode
                        ? filter?.parentCode
                        : !filter?.parentCode && filter?.groupCode
                          ? filter?.groupCode
                          : 'Choose a Group Code'}
                  </Text>
                  <Icon name="chevron-right" />
                </Box>
              </Box>
            </TouchableOpacity>

            <Divider mt={4} />

            {!isViewer && (
              <>
                <TouchableOpacity
                  onPress={() => {
                    statusModalRef?.current?.pushModal?.();
                  }}
                >
                  <Box flexDirection="row" justifyContent="space-between" mt={4}>
                    <Box flexDirection="row" alignItems="center">
                      <Icon name="check-square" />
                      <Text fontSize="16px"> Status</Text>
                    </Box>

                    <Box flexDirection="row">
                      <Text color="#8B8B8B" fontSize="16px">
                        {filter?.status && filter?.status?.length > 0
                          ? filter?.status?.length < 2
                            ? getDocumentStatus(filter?.status[0])
                            : `${filter?.status?.length} Status`
                          : 'Choose an Option'}
                      </Text>
                      <Icon name="chevron-right" />
                    </Box>
                  </Box>
                </TouchableOpacity>

                <Divider mt={4} />
              </>
            )}

            <TouchableOpacity
              onPress={() => {
                groupModalRef?.current?.pushModal?.();
              }}
            >
              <Box flexDirection="row" justifyContent="space-between" mt={4}>
                <Box flexDirection="row" alignItems="center">
                  <Icon name="group" />
                  <Text fontSize="16px"> Group</Text>
                </Box>

                <Box flexDirection="row">
                  <Text color="#8B8B8B" fontSize="16px">
                    {filter.groupId ? filter.groupName : 'Choose a Group'}
                  </Text>
                  <Icon name="chevron-right" />
                </Box>
              </Box>
            </TouchableOpacity>

            <Divider mt={4} />

            <TouchableOpacity
              onPress={() => {
                assignedToModalRef?.current?.pushModal?.();
              }}
            >
              <Box flexDirection="row" justifyContent="space-between" mt={4}>
                <Box flexDirection="row" alignItems="center">
                  <Icon name="assignee" />
                  <Text fontSize="16px"> Assigned To</Text>
                </Box>

                <Box flexDirection="row">
                  <Text color="#8B8B8B" fontSize="16px">
                    {filter.assigneeName && filter.assigneeName.length < 2 && !_.isEmpty(filter.assigneeName)
                      ? filter.assigneeName[0]
                      : filter.assigneeName && filter.assigneeName.length > 0
                        ? filter.assigneeName[0] + ' & ' + (filter.assigneeName.length - 1) + ' others'
                        : 'Choose Assignee'}
                  </Text>

                  <Icon name="chevron-right" />
                </Box>
              </Box>
            </TouchableOpacity>

            {/* <TouchableOpacity
            onPress={() => {
              ccModalRef.current.pushModal();
            }}
          >
            <Box flexDirection="row" justifyContent="space-between" mt={4}>
              <Box flexDirection="row" alignItems="center">
                <Icon name="assignee" />
                <Text fontSize="16px"> Cc</Text>
              </Box>

              <Box flexDirection="row">
                <Text color="#8B8B8B" fontSize="16px">
                  {cc && cc.length < 2 ? cc[0] : cc[0] + " & " + (cc.length - 1) + " others"}
                  {cc.length === 0 ? "Choose CC" : ""}
                </Text>
                <Icon name="chevron-right" />
              </Box>
            </Box>
          </TouchableOpacity>

          <Divider mt={4} /> */}
          </Flex>
        </View>

        <AllFormCodeModal ref={allFormCodeModalRef} />
        <StatusModal ref={statusModalRef} />
        <AssignedToModal ref={assignedToModalRef} />
        <CcModal ref={ccModalRef} />
        <GroupModal ref={groupModalRef} />
        <GroupCodeModal ref={groupCodeModalRef} />
      </Modal>
    );
  })
);

const style = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    height: 48,
    justifyContent: 'space-between',
    padding: 6,
    alignItems: 'center'
  },
  filterButton: {
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20
  }
});

FilteringModal.displayName = 'FilteringModal';
export default memo(FilteringModal);
