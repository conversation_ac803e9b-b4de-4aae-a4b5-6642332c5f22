import { Box, Divider, HStack, Text, VStack } from 'native-base';
import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import { Icon } from '@src/commons';
import { Gql } from '@src/api';

type Props = {
  title: string;
  isParent?: boolean;
  item: Gql.WorkspaceGroup;
  onPressChild?: (item: any, users: any) => void;
  onLongPressChild?: () => void;
  handleThreeDots?: (item: any) => void;
};

const Accordion = (props: Props) => {
  const [isOpen, setIsOpen] = useState(false);

  const isFixturesGroup =
    props?.title === 'Ungroup Documents' || props?.title === 'Site Diary' || props?.title === 'Ungroup Tasks';

  const toggleAccordion = () => {
    setIsOpen(!isOpen);
  };

  const handlePress = () => {
    isFixturesGroup ? props?.onPressChild?.(props.item, null) : toggleAccordion();
  };

  return (
    <TouchableOpacity onPress={handlePress} style={{ width: '100%' }}>
      <VStack mx={3} my={2}>
        <Box alignContent="center">
          <HStack alignItems="center" space={3}>
            {isFixturesGroup ? (
              <Icon name="group" />
            ) : props?.isParent ? (
              <Icon name={isOpen ? 'arrow-down' : 'arrow-right'} />
            ) : null}
            <Text>{props?.title ?? ''}</Text>
          </HStack>
        </Box>
        {isOpen
          ? props?.item?.children?.map?.((child: any, index: number) => (
              <TouchableOpacity
                onPress={() =>
                  props?.onPressChild?.(
                    child,
                    props.item.workspaceGroupUsers?.map((user: any) => user?.userId)
                  )
                }
                style={{ width: '100%' }}
                key={index}
              >
                <Divider my={3} />
                <VStack>
                  <Box style={styles.box}>
                    <HStack alignItems="center" space={3} ml={8}>
                      <Text>{(child?.name || child?.title) ?? ''}</Text>
                    </HStack>
                  </Box>
                </VStack>
              </TouchableOpacity>
            ))
          : null}
        <Divider mt={3} />
      </VStack>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  box: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden'
  },
  addButton: {
    position: 'absolute',
    bottom: 10,
    alignSelf: 'flex-end',
    right: '7%',
    shadowColor: 'rgba(0, 0, 0, 0.5)',
    shadowOpacity: 1,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 6 },
    backgroundColor: '#FFFFFF',
    borderRadius: 9999
  },
  threeDots: {
    width: '10%',
    position: 'absolute',
    right: 0
  }
});

export default Accordion;
