import { Modal } from '@commons';
import { FlashList } from '@shopify/flash-list';
import NotFound from '@src/commons/NotFound';
import Accordion from '@src/modules/DigitalForm/components/selector-component/Accordion';
import { useGetWorkspaceGroup } from '@src/queries/workspace/useGetWorkspaceGroups';
import { taskActions } from '@src/slice/tasks.slice';
import { useDispatch, useSelector } from '@src/store';
import { ScrollView, Text, View } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps } from 'react-native';
import { useMemo } from 'react';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';

interface Props {
  setGroup?: any;
  action?: string;
  groups?: any;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const GroupSelectorModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const dispatch = useDispatch();
  const states = useSelector(state => state.documentWorkspace);
  const workspaceDetail = states?.DocumentDetails;

  const { data } = useGetWorkspaceGroup('');

  const groups = useMemo(() => {
    if (!data) return [];
    return data.pages.map(page => page.items).flat();
  }, [data]);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const keyExtractor = (item: any, index: number) => index?.toString();

  const handlePress = (item: any) => {
    dispatch(
      documentWorkspaceActions.setDocumentDetails({
        ...workspaceDetail,
        group: item
      })
    );

    modalRef?.current?.pushModal();
  };

  return (
    <Modal ref={modalRef} type="bottom">
      <ScrollView style={{ height: '60%' }} keyboardShouldPersistTaps="handled" contentContainerStyle={{ flexGrow: 1 }}>
        <View bg="#F0F0F0" h="40px" justifyContent="center">
          <Text fontSize="16" fontWeight="600px" pl={5}>
            Choose Group
          </Text>
        </View>

        <FlashList
          data={groups}
          ListEmptyComponent={<NotFound />}
          keyExtractor={keyExtractor}
          onEndReachedThreshold={0.1}
          estimatedItemSize={50}
          renderItem={({ item }: any) => (
            <Accordion
              item={item}
              title={item?.name ?? ''}
              isParent={!item?.workspaceGroupId}
              onPressChild={handlePress}
            />
          )}
        />
      </ScrollView>
    </Modal>
  );
});

GroupSelectorModal.displayName = 'GroupSelectorModal';
export default GroupSelectorModal;
