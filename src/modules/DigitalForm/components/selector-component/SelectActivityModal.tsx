import { Modal } from '@commons';
import { Button, Text } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { ModalProps, StyleSheet } from 'react-native';

interface Props {
  onChange: (value: string) => void;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const SelectActivityModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="bottom">
      <Button
        _pressed={{
          bg: 'transparent',
          opacity: 0.8
        }}
        mt={2}
        mb={4}
        bg="transparent"
        onPress={() => {
          props.onChange('Comments');
          modalRef.current.closeModal();
        }}
      >
        <Text>Comment</Text>
      </Button>

      <Button
        _pressed={{
          bg: 'transparent',
          opacity: 0.8
        }}
        mt={2}
        mb={4}
        bg="transparent"
        onPress={() => {
          props.onChange('Log');
          modalRef.current.closeModal();
        }}
      >
        <Text>Activity Log</Text>
      </Button>
    </Modal>
  );
});

SelectActivityModal.displayName = 'SelectActivityModal';
export default SelectActivityModal;
