import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Gql } from '@src/api';
import { Icon } from '@src/commons';
import { getFileIcon } from '@src/commons/FileIcon';
import { COLORS } from '@src/constants';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import { useDispatch, useSelector } from '@src/store';
import { RootNavigatorParams } from '@src/types';
import _ from 'lodash';
import moment from 'moment';
import {
  Box,
  Center,
  Circle,
  Divider,
  FlatList,
  HStack,
  Input,
  Pressable,
  Skeleton,
  Stack,
  Text,
  VStack
} from 'native-base';
import React, { useEffect, useRef, useState } from 'react';
import { InteractionManager, StyleSheet, TouchableOpacity } from 'react-native';
import DocumentOptionModal from '../selector-component/DocumentOptionModal';
import MoveSelectorModal from './MoveSelectorModal';

const DocumentComponent: React.FC<any> = props => {
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const addModalRef = useRef<any>(null);
  const moveSelectorRef = useRef<any>(null);
  const [projectDocumentId, setProjectDocumentId] = useState('');

  // const documentOptionModalRef = useRef<any>(null);
  const dispatch = useDispatch();

  const project = useSelector(state => state.project);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getProjectDocs();
    });
    getProjectDocs();
  }, []);

  const [getProjectDocs, { data, refetch, loading, fetchMore }] = Gql.useGetProjectDocumentsLazyQuery({
    variables: {
      filter: {
        projectId: { eq: project.projectId ?? '' },
        category: { eq: Gql.CategoryType.AllForm },
        projectDocumentId: { eq: null },
        fileSystemType: { eq: Gql.FileSystemType.Folder }
      },
      paging: {
        limit: 20,
        offset: 0
      }
    },
    fetchPolicy: 'cache-and-network'
  });

  const projectDocs = data?.getProjectDocuments?.nodes.map((doc: any) => {
    return {
      id: doc.id,
      name: doc.name,
      lastUpdate: moment(doc.updatedAt).format('DD MMM YYYY'),
      fileSize: doc.fileSize,
      fileType: doc.type,
      category: doc.category,
      status: doc.status
    };
  });
  if (!projectDocs) return null;

  const onLoaded = () => {
    if (!data?.getProjectDocuments.pageInfo.hasNextPage) return;
    fetchMore({
      variables: {
        paging: {
          offset: data?.getProjectDocuments.nodes.length,
          limit: 20
        }
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;
        const result = Object.assign({}, prev, {
          getProjectDocuments: {
            ...fetchMoreResult.getProjectDocuments,
            nodes: [...prev.getProjectDocuments.nodes, ...fetchMoreResult.getProjectDocuments.nodes]
          }
        });
        return result;
      }
    });
  };

  return (
    <Box bg="white">
      <Divider />
      {/* Add button */}
      <TouchableOpacity
        style={[styles.addButton, { zIndex: 1000 }]}
        onPress={() => {
          addModalRef?.current?.pushModal();
        }}
      >
        <Circle size="65px" bg="#0695D7">
          <Icon name="plus" />
        </Circle>
      </TouchableOpacity>

      {/* Search Input */}
      <Box>
        <Center>
          <Input
            bg="#F5F7FB"
            mt="4"
            width="335px"
            borderRadius="4"
            fontSize="14"
            InputLeftElement={
              <Box pl="6" py="6">
                <Icon name="search" />
              </Box>
            }
            InputRightElement={
              <Pressable pr="2" py="6" onPress={() => {}}>
                <Icon name="cancel" />
              </Pressable>
            }
            onChangeText={value => {
              // onSearch(value);
            }}
          />
        </Center>
      </Box>

      {loading ? (
        <SkeletonComponent />
      ) : (
        <FlatList
          keyboardShouldPersistTaps="handled"
          height="90%"
          contentContainerStyle={{ paddingHorizontal: 20, paddingTop: 20 }}
          data={projectDocs}
          keyExtractor={item => item.id}
          onEndReachedThreshold={0.3}
          onEndReached={onLoaded}
          renderItem={({ item }: any) => (
            <Box flexDirection="row" alignItems="center">
              <TouchableOpacity
                onPress={() => {
                  moveSelectorRef?.current?.pushModal();
                  setProjectDocumentId(item?.id);
                }}
              >
                <Box style={styles.box}>
                  <HStack alignItems="center" mb={2}>
                    <Box width="15%">{getFileIcon(item.fileType)}</Box>
                    <VStack width="85%">
                      <Text fontWeight={600} numberOfLines={1} ellipsizeMode="tail">
                        {item.name}
                      </Text>

                      <HStack space={1}>{getStatus(item.status)}</HStack>
                    </VStack>
                  </HStack>
                  <Divider mt={2} mx={10} />
                </Box>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => {
                  dispatch(documentWorkspaceActions.openDocumentOptionModal(item.id));
                }}
              >
                <Box width={50}>
                  <Icon name="option-dot" />
                </Box>
              </TouchableOpacity>
            </Box>
          )}
        />
      )}

      <DocumentOptionModal />
      <MoveSelectorModal
        ref={moveSelectorRef}
        projectDocument={projectDocumentId}
        refetch={() => {
          refetch();
        }}
      />
    </Box>
  );
};

// based on status to show icon
const getStatus = (type: string) => _.get(status, type) ?? '';
const status = {
  Approved: (
    <HStack space={2} alignItems="center">
      <Icon name="approved" />
      <Text color={COLORS.neutrals.gray70}>Approved</Text>
    </HStack>
  ),
  Draft: (
    <HStack space={2} alignItems="center">
      <Icon name="draft" />
      <Text color={COLORS.neutrals.gray70}>Draft</Text>
    </HStack>
  ),
  InReview: (
    <HStack space={2} alignItems="center">
      <Icon name="in-review" />
      <Text color={COLORS.neutrals.gray70}>In Review</Text>
    </HStack>
  ),
  Rejected: (
    <HStack space={2} alignItems="center">
      <Icon name="reject" />
      <Text color={COLORS.neutrals.gray70}>Rejected</Text>
    </HStack>
  )
};

const SkeletonComponent: React.FC<any> = () => {
  const SkeletonItem = () => {
    return (
      <VStack mb={5}>
        <Skeleton style={{ height: 70, padding: 14, borderRadius: 12 }} />
        <VStack space={1}></VStack>
      </VStack>
    );
  };
  return (
    <Stack p={5}>
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
    </Stack>
  );
};

const styles = StyleSheet.create({
  box: {
    backgroundColor: '#FFFFFF',
    padding: 14,
    borderRadius: 12,
    overflow: 'hidden'
  },
  addButton: {
    position: 'absolute',
    bottom: 10,
    alignSelf: 'flex-end',
    right: 30
  }
});

export default DocumentComponent;
