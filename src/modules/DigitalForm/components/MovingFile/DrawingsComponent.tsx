import { Gql } from '@src/api';
import { Icon } from '@src/commons';
import { getFileIcon } from '@src/commons/FileIcon';
import { COLORS } from '@src/constants';
import { useDispatch, useSelector } from '@src/store';
import moment from 'moment';
import {
  Box,
  Center,
  Circle,
  Divider,
  FlatList,
  HStack,
  Input,
  Pressable,
  Skeleton,
  Stack,
  Text,
  VStack
} from 'native-base';
import React, { useEffect, useRef, useState } from 'react';
import { InteractionManager, StyleSheet, TouchableOpacity } from 'react-native';
import { Button } from 'native-base';
import MoveSelectorModal from './MoveSelectorModal';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootNavigatorParams } from '@src/types';
import apolloClient from '@src/lib/apollo';
import { drawingsActions } from '@src/slice/drawings.slice';
import { pushSuccess } from '@src/configs';

interface Props {
  docId?: any;
  category?: any;
  refetch: () => void;
}

const DrawingsComponent: React.FC<Props> = props => {
  const optionModalRef = useRef<any>(null);
  const moveSelectorRef = useRef<any>(null);
  const addModalRef = useRef<any>(null);
  const [projectDocumentId, setProjectDocumentId] = useState('');
  const project = useSelector(state => state.project);
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const states = useSelector(state => state.drawings);
  const dispatch = useDispatch();
  const category = props?.category;

  // Todo: search function

  // useEffect(() => {
  //   InteractionManager.runAfterInteractions(() => {
  //     getProjectDocs();
  //   });
  //   getProjectDocs();
  // }, []);

  const { data, refetch, loading, fetchMore, updateQuery } = Gql.useProjectDocumentsQuery({
    variables: {
      filter: {
        id: { neq: props?.docId },
        category: { eq: category },
        projectDocumentId: {
          ...(projectDocumentId ? { eq: projectDocumentId.toString() } : { is: null })
        },
        fileSystemType: { eq: Gql.FileSystemType.Folder }
      },
      sorting: [
        {
          direction: Gql.SortDirection.Asc,
          field: Gql.ProjectDocumentSortFields.Type
        },
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.ProjectDocumentSortFields.UpdatedAt
        }
      ]
    }
  });

  const projectDocs = data?.projectDocuments?.nodes.map((doc: any) => {
    return {
      id: doc.id,
      name: doc.name,
      lastUpdate: moment(doc.updatedAt).format('DD MMM YYYY'),
      fileSize: doc.fileSize,
      fileType: doc.type,
      category: doc.category
    };
  });
  if (!projectDocs) return null;

  const onLoaded = () => {
    if (!data?.projectDocuments.pageInfo.hasNextPage) return;
    fetchMore({
      variables: {
        paging: {
          offset: data?.projectDocuments.nodes.length,
          limit: 20
        }
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;
        const result = Object.assign({}, prev, {
          projectDocuments: {
            ...fetchMoreResult.projectDocuments,
            nodes: [...prev.projectDocuments.nodes, ...fetchMoreResult.projectDocuments.nodes]
          }
        });
        return result;
      }
    });
  };

  const onMoveFolder = async () => {
    try {
      await apolloClient.mutate<Gql.UpdateOneProjectDocumentMutation, Gql.UpdateOneProjectDocumentMutationVariables>({
        mutation: Gql.UpdateOneProjectDocumentDocument,
        variables: {
          input: {
            id: props.docId ?? '',
            update: {
              projectDocumentId: projectDocumentId ? projectDocumentId : null
            }
          }
        }
      });
      props?.refetch?.();
      dispatch(drawingsActions.resetAll());
      pushSuccess('Moved file sucessfully');
      navigation.goBack();
    } catch (error) {}
  };

  return (
    <Box bg="#fff" height="full">
      <Divider />

      {/* Add button */}
      {/* <TouchableOpacity
        style={[styles.addButton, { zIndex: 1000 }]}
        onPress={() => {
          addModalRef?.current?.pushModal();
        }}
      >
        <Circle size="65px" bg="#0695D7">
          <Icon name="plus" />
        </Circle>
      </TouchableOpacity> */}

      {/* Search Input */}
      <Box>
        <Center>
          <Input
            bg="#F5F7FB"
            mt="4"
            width="335px"
            borderRadius="4"
            fontSize="14"
            InputLeftElement={
              <Box pl="6" py="6">
                <Icon name="search" />
              </Box>
            }
            InputRightElement={
              <Pressable pr="2" py="6" onPress={() => {}}>
                <Icon name="cancel" />
              </Pressable>
            }
            onChangeText={value => {
              // onSearch(value);
            }}
          />
        </Center>
      </Box>

      {/* All Data */}
      {loading ? (
        <SkeletonComponent />
      ) : (
        <>
          <FlatList
            keyboardShouldPersistTaps="handled"
            // height="100%"
            contentContainerStyle={{ paddingHorizontal: 20, paddingTop: 20 }}
            data={projectDocs}
            keyExtractor={item => item.id}
            onEndReachedThreshold={0.3}
            onEndReached={onLoaded}
            renderItem={({ item }: any) => (
              <TouchableOpacity
                onPress={() => {
                  if (item.fileType === 'folder') {
                    setProjectDocumentId(item?.id);
                  }
                }}
              >
                <Box style={styles.box}>
                  <HStack alignItems="center" mb={2}>
                    <Box width="15%">{getFileIcon(item.fileType)}</Box>
                    <VStack width="85%">
                      <Text fontWeight={600} numberOfLines={1} ellipsizeMode="tail">
                        {item.name}
                      </Text>
                      <HStack space={1}>
                        <Text color={COLORS.neutrals.gray90}>{item.lastUpdate}</Text>
                      </HStack>
                    </VStack>
                    <TouchableOpacity
                      onPress={() => {
                        optionModalRef?.current?.pushModal();
                      }}
                    >
                      <Icon name="option-dot" />
                    </TouchableOpacity>
                  </HStack>
                  <Divider mt={2} mx={10} />
                </Box>
              </TouchableOpacity>
            )}
          />
          <HStack alignContent={'center'} justifyContent={'flex-end'} my={10} backgroundColor="#DFF5FF" flex={1}>
            <TouchableOpacity>
              <Button
                mx={4}
                my={4}
                variant="ghost"
                color={'#0695D7'}
                alignSelf={'flex-end'}
                onPress={() => {
                  navigation.goBack();
                }}
              >
                Cancel
              </Button>
            </TouchableOpacity>
            <TouchableOpacity>
              <Button
                mx={4}
                color={'#0695D7'}
                my={4}
                alignSelf={'flex-end'}
                variant="ghost"
                onPress={() => onMoveFolder()}
              >
                Move Here
              </Button>
            </TouchableOpacity>
          </HStack>
        </>
      )}

      <MoveSelectorModal
        projectDocument={projectDocumentId}
        ref={moveSelectorRef}
        refetch={() => {
          props?.refetch?.();
        }}
        docId={props?.docId}
      />
    </Box>
  );
};

const SkeletonComponent: React.FC<any> = () => {
  const SkeletonItem = () => {
    return (
      <VStack mb={5}>
        <Skeleton style={{ height: 70, padding: 14, borderRadius: 12 }} />
        <VStack space={1}></VStack>
      </VStack>
    );
  };
  return (
    <Stack p={5}>
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
    </Stack>
  );
};

const styles = StyleSheet.create({
  box: {
    backgroundColor: '#FFFFFF',
    padding: 14,
    borderRadius: 12,
    overflow: 'hidden'
  },
  addButton: {
    position: 'absolute',
    bottom: 10,
    alignSelf: 'flex-end',
    right: 30
  }
});

export default DrawingsComponent;
