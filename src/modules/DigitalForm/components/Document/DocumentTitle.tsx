import { Input, Text } from 'native-base';
import React, { memo } from 'react';
import { Alert } from 'react-native';
import { Gql } from '@src/api';

interface DocumentTitleProps {
  name: string;
  status: Gql.ProjectDocumentStatus;
  maxLength?: number;
  testID?: string;
  error?: string | false;
  onChangeText: (text: string) => void;
  onBlur?: (e: any) => void;
}

const DocumentTitle: React.FC<DocumentTitleProps> = ({
  name = '',
  status,
  maxLength = 100,
  testID = 'document-title-input',
  error,
  onChangeText,
  onBlur
}) => {
  if (status === Gql.ProjectDocumentStatus.Draft) {
    return (
      <>
        <Input
          testID={testID}
          fontWeight={600}
          fontSize={18}
          autoCapitalize="none"
          color="neutrals.black"
          borderColor={error ? 'error.500' : 'neutrals.gray40'}
          variant="unstyled"
          placeholder="Add your title here..."
          _focus={{ borderColor: 'neutrals.gray40', bg: '#FFF' }}
          onChangeText={onChangeText}
          onBlur={onBlur}
          maxLength={maxLength}
          value={name}
          accessibilityLabel="Document title input"
          aria-label="Document title input"
          aria-invalid={!!error}
          aria-errormessage={error || undefined}
        />
        {error ? (
          <Text color="error.500" fontSize="xs" mt={1} testID={`${testID}-error`} accessibilityLabel={error}>
            {error}
          </Text>
        ) : null}
      </>
    );
  }

  return (
    <Text
      testID={`${testID}-readonly`}
      fontWeight={600}
      fontSize={18}
      color="neutrals.black"
      borderColor="neutrals.gray40"
      variant="unstyled"
      onPress={() => {
        Alert.alert(`${name}`, '', [
          {
            text: 'Done',
            style: 'default'
          }
        ]);
      }}
      accessibilityLabel={`Document title: ${name}`}
      accessibilityRole="text"
    >
      {name}
    </Text>
  );
};

// Memoize the component to prevent unnecessary re-renders
const MemoizedDocumentTitle = memo(DocumentTitle);

export { MemoizedDocumentTitle as DocumentTitle };
