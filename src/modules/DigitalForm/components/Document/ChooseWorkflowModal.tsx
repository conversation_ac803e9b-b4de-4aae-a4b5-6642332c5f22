import dynamicWorkspace from '@assets/dynamic-workspace.png';
import linearWorkspace from '@assets/linear-workspace.png';
import { Modal } from '@commons';
// Navigation imports not needed
// import { useNavigation } from '@react-navigation/native';
// import { StackNavigationProp } from '@react-navigation/stack';
import { Gql } from '@src/api';
import FastImage from '@src/commons/FastImage';
// import { pushError } from '@src/configs';
import useUpdateWorkspaceDocument from '@src/mutation/workspace/useUpdateWorkspaceDocument';
// import { RootNavigatorParams } from '@src/types';
import { Pressable, Text, View } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState, lazy, Suspense, memo } from 'react';

// Lazy load the UploadFileModal component
const UploadFileModal = lazy(() => import('@src/commons/UploadFileModal'));
import { ActivityIndicator, ModalProps, StyleSheet } from 'react-native';

interface Props {
  document: any;
  refetch: (workflow: Gql.WorkflowType) => void;
  setSelectedDoc: (v: any) => void;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const ChooseWorkflowModal = memo(
  forwardRef<ModalRef, Props>((props, ref) => {
    // Only need uploadFileModalRef for the UploadFileModal
    const uploadFileModalRef = useRef<any>(null);
    const [loading, setLoading] = useState<boolean>(false);

    // Track modal visibility state directly from the Modal component
    const [isVisible, setIsVisible] = useState(false);

    // Move the hook call to the top level of the component
    const { mutateAsync: updateWorkspaceWorkflow } = useUpdateWorkspaceDocument();

    // Simplified imperative handle that directly controls visibility
    useImperativeHandle(ref, () => ({
      pushModal: () => {
        // Just set visibility to true - no need to call another ref method
        setIsVisible(true);
      }
    }));

    // update workspace workflow mutation
    // const [updateWorkspaceWorkflow, { loading: updateWorkspaceWorkflowLoading }] = Gql.useUpdateWorkspaceWorkflowMutation(
    //   {
    //     onCompleted: async () => {

    //       await modalRef?.current?.pushModal();
    //       await props.refetch();
    //     },
    //     onError: (error) => {
    //       pushError(error);
    //     }
    //   },
    // );

    return (
      <Modal
        // No need for ref here since we're controlling visibility directly
        isVisible={isVisible}
        style={{ marginTop: 40 }}
        type="middle"
        onClose={() => setIsVisible(false)}
      >
        {loading ? (
          <ActivityIndicator size="large" color="#808080" />
        ) : (
          <>
            <View pt={4} style={styles?.modal} alignItems={'center'}>
              <Text fontSize="3xl" fontWeight={'700'} color={'white'} pt={6}>
                Choose your
              </Text>
              <Text fontSize="3xl" fontWeight={'700'} pt={4} color={'white'} mb={4}>
                workflow
              </Text>
              <Pressable
                onPress={async () => {
                  // return Alert.alert('Linear workflow are not available yet on mobile app.')

                  props.setSelectedDoc({ id: props?.document?.id, workflow: Gql.WorkflowType.Linear });
                  await updateWorkspaceWorkflow({
                    // variables: {
                    //   input: { id: props.document?.id, workflow: Gql.WorkflowType.Linear }
                    // }
                    id: props.document?.id,
                    workflow: Gql.WorkflowType.Linear
                  }).then(async () => {
                    // Close the modal after successful workflow selection
                    setIsVisible(false);
                    props.refetch(Gql.WorkflowType.Linear);
                  });
                }}
              >
                <FastImage source={linearWorkspace} height={200} width={300} />
              </Pressable>

              <Pressable
                mt={4}
                onPress={async () => {
                  props.setSelectedDoc({ id: props?.document?.id, workflow: Gql.WorkflowType.Dynamic });
                  await updateWorkspaceWorkflow({
                    // variables: {
                    //   input: { id: props.document?.id, workflow: Gql.WorkflowType.Dynamic }
                    // }
                    id: props.document?.id,
                    workflow: Gql.WorkflowType.Dynamic
                  }).then(async () => {
                    // Close the modal after successful workflow selection
                    setIsVisible(false);
                    props.refetch(Gql.WorkflowType.Dynamic);
                  });
                }}
              >
                <FastImage source={dynamicWorkspace} height={200} width={300} />
              </Pressable>
            </View>
          </>
        )}
        {/* </HStack> */}
        {/* </Center> */}
        {/* Only render UploadFileModal when needed */}
        {isVisible && (
          <Suspense fallback={<ActivityIndicator size="large" color="#808080" />}>
            <UploadFileModal
              ref={uploadFileModalRef}
              category={Gql.CategoryType.AllForm}
              onSaved={() => {
                // props?.refetch?.();
              }}
              onLoading={(loading: boolean) => setLoading(loading)}
            />
          </Suspense>
        )}
      </Modal>
    );
  })
);

const styles = StyleSheet.create({
  modal: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: -130,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 9999,
    backgroundColor: 'rgba(0, 0, 0, 0.0)'
  }
});

ChooseWorkflowModal.displayName = 'ChooseWorkflowModal';
export default ChooseWorkflowModal;
