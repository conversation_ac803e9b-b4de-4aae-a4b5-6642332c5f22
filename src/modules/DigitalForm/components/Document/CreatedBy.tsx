import { Box, HStack, Text } from 'native-base';
import React, { memo } from 'react';
import { Icon } from '@commons';
import Avatar from '@commons/Avatar';

interface CreatedByUser {
  id?: string | number;
  name?: string;
  avatarUrl?: string;
}

interface CreatedByProps {
  user?: CreatedByUser;
  testID?: string;
}

const CreatedBy: React.FC<CreatedByProps> = ({ user, testID = 'document-created-by' }) => {
  if (!user) {
    return null;
  }

  return (
    <>
      <HStack mt={4} mb={4}>
        <Box flex={1} flexDirection="row" alignItems="center">
          <Icon name="assignee" />
          <Text color="neutrals.gray90" testID={`${testID}-label`} accessibilityRole="text">
            Created By
          </Text>
        </Box>
        <Box flex={2}>
          <HStack key={user.id} space={2} flexWrap="wrap" alignItems="flex-start" mt={1} testID={testID}>
            <Avatar
              assignees={[
                {
                  assigneeNo: '1',
                  avatarUrl: user.avatarUrl ?? '',
                  name: user.name ?? ''
                }
              ]}
              maxVisible={1}
              type={'task'}
            />
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              maxWidth={'80%'}
              mt={1}
              testID={`${testID}-name`}
              accessibilityLabel={`Created by ${user.name}`}
            >
              {user.name ?? ''}
            </Text>
          </HStack>
        </Box>
      </HStack>
    </>
  );
};

// Memoize the component to prevent unnecessary re-renders
const MemoizedCreatedBy = memo(CreatedBy);

export { MemoizedCreatedBy as CreatedBy };
