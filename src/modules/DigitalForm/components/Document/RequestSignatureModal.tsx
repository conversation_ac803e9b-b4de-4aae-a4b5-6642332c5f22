import { Modal } from '@commons';
import { Q } from '@nozbe/watermelondb';
import { Gql } from '@src/api';
import { pushError, pushSuccess } from '@src/configs';
import database from '@src/database/index.native';
import useUpdateWorkspaceDocument from '@src/mutation/workspace/useUpdateWorkspaceDocument';
import { useGetDocumentDetail } from '@src/queries/workspace/useGetWorkspaceDocumentDetail';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import { useDispatch, useSelector } from '@src/store';
import { Box, Button, Divider, HStack, ScrollView, Text, VStack } from 'native-base';
import React, { FC } from 'react';

interface Props {
  data: any; // This is the document data passed in as props.
  refetch?: () => void;
}

const RequestSignatureModal: FC<Props> = props => {
  const dispatch = useDispatch();
  const states = useSelector(state => state.documentWorkspace);
  const { mutateAsync: updateWorkspaceDocument, isPending } = useUpdateWorkspaceDocument();

  const { data } = useGetDocumentDetail(states.selectedDocumentId ?? '');
  const documentStatus = data?.document?.status;

  const onRequest = async () => {
    try {
      // Use props.data for workflow validation
      if (!props.data?.workflow) {
        dispatch(documentWorkspaceActions.closeAll());
        return pushError('No workflow set for this document');
      }

      const conditions = [];

      if (
        data?.document?.remoteId !== undefined &&
        data?.document?.remoteId !== null &&
        data?.document?.remoteId !== 0
      ) {
        conditions.push(Q.where('projectDocumentId', data.document.remoteId));
      } else {
        conditions.push(Q.where('localProjectDocumentId', data?.document?.id ?? null));
      }

      let localCurrentUserId;
      let currentUserId;
      let firstAssignee;

      if (props.data?.workflow === Gql.WorkflowType.Dynamic) {
        const secondAssignee = await database.collections
          .get('request_for_signatures')
          .query(Q.where('assigneeNo', 2), ...conditions)
          .unsafeFetchRaw();

        localCurrentUserId = secondAssignee[0]?.id;
        currentUserId = secondAssignee[0].remoteId ?? null;

        if (!localCurrentUserId) {
          return pushError('No assignees set for this document');
        }

        firstAssignee = [
          {
            ownerId: props.data?.addedBy,
            signById: props.data?.addedBy,
            projectDocumentId: props.data?.remoteId,
            assigneeNo: 1,
            status: Gql.RequestForSignatureStatus.Sent,
            localProjectDocumentId: states.selectedDocumentId
          }
        ];
      }

      return await updateWorkspaceDocument({
        id: states.selectedDocumentId ?? '',
        status: Gql.ProjectDocumentStatus.Submitted,
        submittedAt: new Date().toISOString(),
        ...(currentUserId && {
          currentUserId
        }),
        ...(props.data?.workflow === Gql.WorkflowType.Dynamic && {
          localCurrentUserId,
          assignees: props.data?.remoteId ? [] : firstAssignee
        })
      }).then(() => {
        pushSuccess('Request approval sent successfully');
        dispatch(documentWorkspaceActions.closeAll());
      });
    } catch (e: any) {
      pushError(e.message);
    } finally {
      dispatch(documentWorkspaceActions.setDocumentId(''));
      dispatch(documentWorkspaceActions.resetAll());
    }
  };

  return (
    <Modal
      style={{ marginTop: 40 }}
      type="bottom"
      isVisible={states.isRequestApprovalModalOpen}
      onClose={() => {
        dispatch(documentWorkspaceActions.resetAll());
      }}
    >
      <ScrollView height="80%">
        <Box padding={6}>
          <VStack>
            <Text fontWeight={600} fontSize={20}>
              Requesting approval from:
            </Text>
            <Divider mt={4} mb={4} />
            {data?.requestForSignature?.length !== 0 &&
              data?.requestForSignature?.map((obj: any, index: any) => {
                return (
                  <Text key={index} color="neutrals.gray90">
                    {' '}
                    {index + 1 + '. ' + obj?.user?.name ?? '-'}
                  </Text>
                );
              })}
            {data?.requestForSignature?.length === 0 && (
              <Text style={{ color: 'red', marginTop: 4 }}>Alert: No assignees set for this document</Text>
            )}
            {data?.requestForSignature?.length !== 0 && (
              <VStack mt={10}>
                <HStack>
                  <Text>_____</Text>
                </HStack>
                <HStack>
                  <Text>Note:</Text>
                </HStack>
                <HStack>
                  <Text>
                    1. Approved Status: Every assignee must approve the document for the status to be 'Approved'
                  </Text>
                </HStack>
                <HStack>
                  <Text>2. Rejected Status: Any assignee must reject the document for the status to be 'Rejected'</Text>
                </HStack>
              </VStack>
            )}

            <Box flexDirection="row" mt={4} justifyContent="space-between">
              <Button
                variant="outlineGray"
                h={38}
                onPress={() => {
                  dispatch(documentWorkspaceActions.closeAll());
                }}
              >
                Dismiss
              </Button>
              {documentStatus === Gql.ProjectDocumentStatus.Draft && (
                <Button
                  variant="primary"
                  h={38}
                  alignSelf="flex-end"
                  isLoading={isPending}
                  isLoadingText={'Requesting...'}
                  isDisabled={data?.requestForSignature?.length === 0}
                  onPress={() => {
                    if (data?.requestForSignature?.length !== 0) onRequest();
                    else pushError('Alert: No assignees set for this document');
                  }}
                >
                  Request approval
                </Button>
              )}
            </Box>
          </VStack>
        </Box>
      </ScrollView>
    </Modal>
  );
};

RequestSignatureModal.displayName = 'RequestSignatureModal';
export default RequestSignatureModal;
