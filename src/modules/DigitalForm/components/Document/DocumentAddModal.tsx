import { Icon, Modal } from '@commons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Gql } from '@src/api';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import { useDispatch } from '@src/store';
import { RootNavigatorParams } from '@src/types';
import { Box, Button, Text } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState, lazy, Suspense } from 'react';

// Lazy load the UploadFileModal component
const UploadFileModal = lazy(() => import('@src/commons/UploadFileModal'));
import { ActivityIndicator, ModalProps, StyleSheet, TouchableOpacity } from 'react-native';

interface Props {
  category: string;
  refetch: () => void;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const AddModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const uploadFileModalRef = useRef<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const dispatch = useDispatch();

  // Only render the modal content when it's visible
  const [isVisible, setIsVisible] = useState(false);

  // Set up the imperative handle with visibility tracking
  useImperativeHandle(ref, () => ({
    pushModal: () => {
      setIsVisible(true);
      modalRef?.current?.pushModal();
    }
  }));

  // Don't render anything if not visible
  if (!isVisible) {
    return null;
  }

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="bottom" onClose={() => setIsVisible(false)}>
      {/* <Center height={209}> */}
      {/* <HStack space={20}> */}
      {/* <TouchableOpacity
            onPress={() => {
              navigation.navigate('DigitalFormNav', {
                screen: 'CreateFolder',
                params: {
                  category: props.category,
                  refetch: () => {
                    props?.refetch?.();
                  }
                }
              });
              modalRef?.current?.closeModal();
            }}
          >
            <VStack alignItems="center" space={3}>
              <Button style={styles.folder}>
                <Icon name="folder-outline" />
              </Button>
              <Text color="#6F6D6D">Create Folder</Text>
            </VStack>
          </TouchableOpacity> */}
      {loading ? (
        <ActivityIndicator size="large" color="#808080" />
      ) : (
        <Button
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          my={6}
          bg="transparent"
          onPress={() => {
            dispatch(documentWorkspaceActions.resetAll());
            uploadFileModalRef?.current?.pushModal();
          }}
          justifyContent={'center'}
        >
          <Box
            alignItems={'center'}
            justifyContent={'center'}
            width={16}
            height={16}
            alignSelf={'center'}
            borderRadius={32}
            borderWidth={2}
            borderColor={'neutrals.gray6'}
          >
            <Icon name="add-file" />
          </Box>
          <Text color="#6F6D6D">Create Document</Text>
        </Button>
      )}

      {/* </HStack> */}
      {/* </Center> */}
      <Suspense fallback={null}>
        {isVisible && (
          <UploadFileModal
            ref={uploadFileModalRef}
            category={Gql.CategoryType.AllForm}
            status={Gql.ProjectDocumentStatus.Draft}
            onSaved={() => {
              // props?.refetch?.();
            }}
            onLoading={(loading: boolean) => setLoading(loading)}
          />
        )}
      </Suspense>
    </Modal>
  );
});

// const styles = StyleSheet.create({
//   folder: {
//     borderWidth: 2,
//     borderColor: '#756D6D',
//     borderRadius: 100,
//     backgroundColor: 'transparent',
//     height: 52,
//     width: 52
//   }
// });

AddModal.displayName = 'AddModal';
export default AddModal;
