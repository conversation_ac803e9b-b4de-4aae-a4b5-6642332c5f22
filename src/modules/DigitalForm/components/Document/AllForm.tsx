import React, { useCallback, useEffect, useMemo, useRef, useState, memo } from 'react';
import { Alert, StyleSheet, TouchableOpacity } from 'react-native';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useDispatch, useSelector } from '@src/store';
import { Gql } from '@src/api';
import { Icon } from '@src/commons';
import { pushError, pushSuccess } from '@src/configs';
import { RootNavigatorParams } from '@src/types';
import { Box, Circle, Divider, HStack, Select, Skeleton, Stack, VStack } from 'native-base';
import _ from 'lodash';
import DocumentOptionModal from '../selector-component/DocumentOptionModal';
import AddModal from './DocumentAddModal';
import { useGetDocuments } from '@src/queries/workspace/useGetWorkspaceDocuments';
import useDebouncedSearch from '@src/hooks/useDebounceSearch';
import SearchInput from '@src/commons/SearchInput';
import DocumentList from '@src/commons/list/DocumentList';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import FilteringModal from '../selector-component/FilteringModal';
import useDeleteWorkspaceDocument from '@src/mutation/workspace/useDeleteWorkspaceDocument';
import ChooseWorkflowModal from './ChooseWorkflowModal';
import ProjectDocumentModel from '@src/database/model/project-document';

const AllForm: React.FC<any> = props => {
  const addModalRef = useRef<any>(null);
  const chooseWorkflowModalRef = useRef<any>(null);

  // Hooks
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const dispatch = useDispatch();
  const { debouncedValue, setInputValue } = useDebouncedSearch();
  const { mutateAsync: deleteWorkspaceDocs } = useDeleteWorkspaceDocument();

  // State
  const [filteredValue, setFilteredValue] = useState('');
  const [selectedDoc, setSelectedDoc] = useState<any>();

  // Selectors
  const project = useSelector(state => state.project);
  const documentWorkspace = useSelector(state => state.documentWorkspace);
  const { filterItems } = documentWorkspace;
  const { user } = useSelector(state => state.auth);
  const role = project?.projectUserRole;

  // Computed Values
  const isShowFilterBadge = useMemo(
    () => filterItems.assignToMe === true || Object.values(filterItems).some(value => Boolean(value)),
    [filterItems]
  );

  const {
    data: getProjectDocuments,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage
  } = useGetDocuments(debouncedValue ?? '', {
    projectId: project.projectId,
    limit: 10,
    category: Gql.CategoryType.AllForm,
    projectDocumentId: null as any,
    pageIndex: 1,
    status: documentWorkspace.driveType,
    sorting: documentWorkspace.driveType === 'Draft' ? 'Draft' : 'submittedAt',
    userId: parseInt(user?.id ?? '0')
  });

  const projectDocuments = useMemo(() => {
    if (!getProjectDocuments) return [];
    return getProjectDocuments.pages.map((page: { results: any[] }) => page.results).flat();
  }, [getProjectDocuments]);

  const loadMoreDocuments = useCallback(() => {
    if (hasNextPage && !isLoading && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isLoading, isFetchingNextPage, fetchNextPage]);

  const onSearchInputChange = (newSearchTerm: any) => {
    setFilteredValue(newSearchTerm);
    setInputValue(newSearchTerm);
  };

  useFocusEffect(
    React.useCallback(() => {
      dispatch(documentWorkspaceActions.ShouldSetInitialState(true));
    }, [])
  );

  const onItemPress = useCallback(
    (item: ProjectDocumentModel) => {
      dispatch(documentWorkspaceActions.setDocumentId(item.id));
      setSelectedDoc(item);

      if (item?.workflow === null) {
        setSelectedDoc(item);
        chooseWorkflowModalRef?.current?.pushModal();
        return;
      }

      // if (item.workflow === 'Linear') {
      //   return Alert.alert('Linear workflow are not available yet on mobile app.');
      // }

      // Immediately navigate to prevent blocking UI
      const getType = item?.workflow === 'Linear' ? 'DocumentDetail' : 'DynamicDocumentDetail';
      navigation.navigate('DigitalFormNav', {
        screen: getType,
        params: {
          refetch: () => {}
        }
      });
    },
    [navigation, role]
  );

  const onOptionPress = (item: ProjectDocumentModel) => {
    dispatch(documentWorkspaceActions.resetAll());
    dispatch(documentWorkspaceActions.setDocumentId(item.id));
    dispatch(documentWorkspaceActions.openDocumentOptionModal(item.id));
    setSelectedDoc(item);
    // dispatch(documentWorkspaceActions.setDocumentDetails({ document: item }));
  };

  const onValueChange = (e: any) => {
    const temp = e.toString() ?? null;
    dispatch(documentWorkspaceActions.setDriveType(temp));
    dispatch(documentWorkspaceActions.clearFilter());
  };

  useFocusEffect(
    React.useCallback(() => {
      dispatch(documentWorkspaceActions.resetAll());
    }, [])
  );

  // Delete function
  const onDelete = async (id: string) => {
    if (id) {
      try {
        await deleteWorkspaceDocs(id);
        pushSuccess('Delete document successfully');
        dispatch(documentWorkspaceActions.closeAll());
      } catch (e) {
        pushError(e);
      }
    }
  };

  return (
    <Box bg="white" height="full">
      <Divider />
      <TouchableOpacity
        style={[styles.addButton, { zIndex: 1000 }]}
        onPress={() => {
          addModalRef?.current?.pushModal();
        }}
      >
        <Circle size="65px" bg="#0695D7">
          <Icon name="plus" />
        </Circle>
      </TouchableOpacity>
      <HStack alignItems="center">
        <SearchInput
          filteredValue={filteredValue}
          setFilteredValue={onSearchInputChange}
          placeholder="Search for Document"
          style={{ flex: 1 }}
        />
        <Select
          borderRadius={8}
          borderColor="#E6E6E6"
          minW="120px"
          mt={3}
          fontWeight="600"
          color={'blue.600'}
          selectedValue={documentWorkspace.driveType as any}
          onValueChange={onValueChange}
          accessibilityLabel={documentWorkspace.driveType as any}
          _selectedItem={{ bg: 'gray.300' }}
          style={{ flex: 1 }}
        >
          <Select.Item key={0} label="Processed" value={''} />
          <Select.Item key={0} label="Draft" value={'Draft'} />
        </Select>
        {(documentWorkspace.driveType as Gql.ProjectDocumentStatus) !== Gql.ProjectDocumentStatus.Draft ? (
          <TouchableOpacity
            onPress={() => dispatch(documentWorkspaceActions.openDocumentFilterModal())}
            style={{ paddingLeft: 10, marginTop: 10 }}
          >
            {isShowFilterBadge ? (
              <Box
                zIndex={1}
                width={3}
                height={3}
                borderRadius={'full'}
                position="absolute"
                top={0.5}
                right={0.1}
                bg={'red.500'}
              />
            ) : null}
            <Icon name="filtering-grey" width={30} height={40} />
          </TouchableOpacity>
        ) : null}
      </HStack>

      {/* All Data */}
      {isLoading ? (
        <SkeletonComponent />
      ) : (
        <DocumentList
          documents={projectDocuments}
          isLoading={isLoading}
          loadMoreDocuments={loadMoreDocuments}
          refetch={() => {}}
          onItemPress={onItemPress}
          onOptionPress={onOptionPress}
          onAction={false}
          setOnAction={() => {}}
          modules={Gql.CategoryType.AllForm}
          status={documentWorkspace.driveType}
          isFetchingNextPage={isFetchingNextPage}
        />
      )}

      <AddModal
        ref={addModalRef}
        category={Gql.CategoryType?.AllForm}
        refetch={() => {
          // refetch();
        }}
      />

      <DocumentOptionModal
        onChange={value => {
          // onDownload(value);
        }}
        onDelete={value => {
          onDelete(value);
        }}
        refetch={() => {
          // refetch();
        }}
        data={selectedDoc}
      />

      <ChooseWorkflowModal
        ref={chooseWorkflowModalRef}
        setSelectedDoc={setSelectedDoc}
        document={selectedDoc}
        refetch={async (workflow: Gql.WorkflowType) => {
          dispatch(documentWorkspaceActions.setDocumentId(selectedDoc?.id));
          const getType = workflow === Gql.WorkflowType.Linear ? 'DocumentDetail' : 'DynamicDocumentDetail';
          navigation.navigate('DigitalFormNav', {
            screen: getType,
            params: {
              refetch: () => {}
            }
          });
        }}
      />

      {/* We don't need to use the ref for FilteringModal since it's controlled by Redux */}
      <FilteringModal
        onFilter={values => {
          dispatch(documentWorkspaceActions.closeAll());
        }}
      />
    </Box>
  );
};

const SkeletonComponent: React.FC<any> = () => {
  const SkeletonItem = () => {
    return (
      <VStack mb={5}>
        <Skeleton style={{ height: 70, padding: 14, borderRadius: 12 }} />
        <VStack space={1}></VStack>
      </VStack>
    );
  };
  return (
    <Stack p={5}>
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
    </Stack>
  );
};

const styles = StyleSheet.create({
  box: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden'
  },
  addButton: {
    position: 'absolute',
    bottom: 10,
    alignSelf: 'flex-end',
    right: 30
  }
});

export default memo(AllForm);
