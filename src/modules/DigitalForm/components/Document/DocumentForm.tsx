import { Box } from 'native-base';
import React, { memo, useState, useCallback } from 'react';
import { Formik, FormikProps } from 'formik';
import { Alert } from 'react-native';
import { Gql } from '@src/api';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import { useDispatch, useSelector } from '@src/store';
import { DocumentTitle } from './DocumentTitle';
import { DocumentDescription } from './DocumentDescription';
import { composeValidators, required, maxLength, filterNewEntities } from '@src/configs/utils';
import { pushMessaage, pushSuccess } from '@src/configs';
import useUpdateWorkspaceDocument from '@src/mutation/workspace/useUpdateWorkspaceDocument';
import RequestForSignaturesModel from '@src/database/model/request-for-signatures.model';
import WorkspaceCcModel from '@src/database/model/workspace_ccs.model';
import WorkspaceAttachmentModel from '@src/database/model/workspace-attachments.model';
import WorkspacePhotoModel from '@src/database/model/workspace-photos.model';
import WorkspaceDocumentModel from '@src/database/model/workspace-document.model';

interface DocumentFormProps {
  initialData: {
    name: string;
    description: string;
  };
  status: Gql.ProjectDocumentStatus;
  onBackPress?: () => void;
  refetch?: () => void;
  navigation?: any;
  formRef?: React.RefObject<FormikProps<FormValues>>;
}

interface FormValues {
  name: string;
  description: string;
}

interface DocumentDetails {
  id: string;
  remoteId?: string;
  name?: string;
  description?: string;
  group?: {
    remoteId: string;
  };
  selectedAssigneeIds?: RequestForSignaturesModel[];
  selectedCcs?: WorkspaceCcModel[];
  attachment?: WorkspaceAttachmentModel[];
  photos?: WorkspacePhotoModel[];
  linkedDocuments?: WorkspaceDocumentModel[];
}

const DocumentForm: React.FC<DocumentFormProps> = ({
  initialData,
  status,
  onBackPress,
  refetch,
  navigation,
  formRef
}) => {
  const dispatch = useDispatch();
  const workspaceDocuments = useSelector(state => state.documentWorkspace);
  const [loading, setLoading] = useState(false);
  const { mutateAsync: updateDocument } = useUpdateWorkspaceDocument();

  const formatDocumentName = useCallback((name: string): string => {
    return name.endsWith('.pdf') ? name : `${name}.pdf`;
  }, []);

  const validate = (values: FormValues) => {
    const errors: { [key: string]: string } = {};

    const nameError = composeValidators(required, maxLength(100))(values.name);
    if (nameError) errors.name = nameError;

    const descriptionError = composeValidators(maxLength(500))(values.description);
    if (descriptionError) errors.description = descriptionError;

    return errors;
  };

  const checkAttachmentLimit = () => {
    if (
      workspaceDocuments?.DocumentDetails?.attachment &&
      workspaceDocuments?.DocumentDetails?.attachment?.length > 10
    ) {
      Alert.alert('Error', 'You can only upload 10 attachments');
      return true;
    }
    return false;
  };

  const handleSubmit = async (values: FormValues) => {
    setLoading(true);

    if (checkAttachmentLimit()) {
      setLoading(false);
      return;
    }

    const documentDetails = workspaceDocuments?.DocumentDetails as DocumentDetails;

    if (!documentDetails) {
      setLoading(false);
      pushMessaage('Document details are missing', 'error');
      return;
    }

    try {
      const formattedName = formatDocumentName(values.name);

      await updateDocument({
        id: documentDetails.id || '',
        remoteId: documentDetails.remoteId || ('' as any),
        ...(formattedName !== documentDetails.name && { name: formattedName }),
        ...(values.description !== documentDetails.description && {
          description: values.description
        }),
        ...(documentDetails.group?.remoteId && {
          workspaceGroupId: parseInt(documentDetails.group.remoteId, 10)
        }),
        status,
        assignees: documentDetails.selectedAssigneeIds || [],
        ccs: documentDetails.selectedCcs || [],
        attachments: filterNewEntities<WorkspaceAttachmentModel>(documentDetails.attachment || []),
        media: filterNewEntities<WorkspacePhotoModel>(documentDetails.photos || []),
        linkedDocuments: filterNewEntities<WorkspaceDocumentModel>(documentDetails.linkedDocuments || [])
      });

      refetch?.();
      dispatch(documentWorkspaceActions.clearDocumentDetails());
      pushSuccess('Saved successfully');
      setLoading(false);
      dispatch(documentWorkspaceActions.closeAll());
      dispatch(documentWorkspaceActions.ShouldSetInitialState(true));

      if (navigation) {
        navigation.goBack();
      }
    } catch (e: any) {
      setLoading(false);
      pushMessaage(e.message, 'error');
    }
  };

  return (
    <Formik<FormValues>
      initialValues={{
        name: initialData.name,
        description: initialData.description
      }}
      validate={validate}
      enableReinitialize
      onSubmit={handleSubmit}
      innerRef={formRef}
    >
      {({ values, errors, touched, setFieldValue }) => (
        <Box>
          <DocumentTitle
            name={values.name}
            status={status}
            maxLength={100}
            error={touched.name && errors.name}
            onChangeText={(text: string) => {
              setFieldValue('name', text);
            }}
          />

          <DocumentDescription
            description={values.description}
            status={status}
            maxLength={500}
            error={touched.description && errors.description}
            onChangeText={(text: string) => {
              setFieldValue('description', text);
            }}
          />
        </Box>
      )}
    </Formik>
  );
};

export const MemoizedDocumentForm = memo(DocumentForm);
export { MemoizedDocumentForm as DocumentForm };
