import { FlashList } from '@shopify/flash-list';
import { Gql } from '@src/api';
import { Icon } from '@src/commons';
import { pushError } from '@src/configs';
import WorkspaceCommentModel from '@src/database/model/workspace-comment.model';
import CommentModal from '@src/modules/Task/components/Tasks/CommentModal';
import useCreateWorkspaceComment from '@src/mutation/workspace-comment/useCreateWorkspaceComment';
import useDeleteWorkspaceComment from '@src/mutation/workspace-comment/useDeleteWorkspaceComment';
import { useDispatch, useSelector } from '@src/store';
import _ from 'lodash';
import moment from 'moment';
import { Box, FlatList, HStack, Pressable, Text, VStack } from 'native-base';
import React, { useCallback, useEffect, useRef } from 'react';
import { Alert, StyleSheet, TouchableOpacity } from 'react-native';
import ParsedText from 'react-native-parsed-text';
import Avatar from '@commons/Avatar';

type Props = {
  selectedDocumentId: number;
  comments: WorkspaceCommentModel[];
};

export const CommentComponent: React.FC<Props> = props => {
  const commentRef = useRef<any>(null);
  let regexArr: any[] = [];
  const project = useSelector(state => state.project);
  const role = project.projectUserRole;
  const canDelete = role === Gql.ProjectUserRoleType.CloudCoordinator || role === Gql.ProjectUserRoleType.ProjectOwner;
  const dispatch = useDispatch();
  const workspaceDocument = useSelector(state => state.documentWorkspace);

  const { user } = useSelector(state => state.auth);

  // hooks
  const { mutateAsync: createComment } = useCreateWorkspaceComment();
  const { mutateAsync: deleteComment } = useDeleteWorkspaceComment();

  const createCommentHandler = useCallback(
    async (message: string) => {
      const parsedUserId = parseInt(user?.id ?? '0', 10);

      try {
        await createComment({
          projectDocumentId: props.selectedDocumentId,
          comment: message,
          userId: parsedUserId,
          localProjectDocumentId: workspaceDocument?.selectedDocumentId
        });
      } catch (error) {
        pushError(error);
      }
    },
    [createComment, dispatch, props.selectedDocumentId]
  );

  const deleteCommentHandler = useCallback(
    async (commentId: string) => {
      try {
        await deleteComment(commentId);
      } catch (error) {
        pushError(error);
      }
    },
    [deleteComment]
  );

  return (
    <Box>
      <Box>
        <FlashList
          keyboardShouldPersistTaps="handled"
          data={props?.comments}
          renderItem={({ item }) => {
            var matches = item?.message?.match(/\[(.*?)\]/g);
            matches?.map((itemMatch: string) => {
              let replaceName1 = itemMatch.replace('[', '');
              let replaceName2 = replaceName1.replace(']', '');
              item.message =
                item?.message != undefined ? item?.message?.replace(itemMatch, replaceName2) : item?.message;
              let reg = new RegExp('@' + replaceName2);
              let pattern1 = {
                pattern: reg,
                style: {
                  color: '#2a64f9'
                }
              };
              regexArr.push(pattern1);
            });
            // check more than 5 minute or not
            const isMoreThan5Minute = moment().diff(moment(item.createdAt), 'minutes') > 5;

            // check is user or not
            const isUser = user?.id === item.userId;

            return (
              <Box pl={['0', '4']} pr={['0', '5']} py="3">
                <HStack space={[2, 3]} justifyContent="space-between">
                  <Avatar
                    type="task"
                    assignees={[{ assigneeNo: '1', avatarUrl: item?.user?.avatarUrl, name: item?.user?.name }]}
                    maxVisible={1}
                  />
                  <VStack flex={1}>
                    <HStack space={2}>
                      <Text
                        _dark={{
                          color: 'warmGray.50'
                        }}
                        color="coolGray.800"
                        bold
                        ellipsizeMode="tail"
                        numberOfLines={1}
                        maxWidth={'50%'}
                      >
                        {item?.user?.name}
                      </Text>
                      <Text
                        fontSize="xs"
                        _dark={{
                          color: 'warmGray.50'
                        }}
                        color="coolGray.800"
                        alignSelf="flex-start"
                      >
                        {moment(item.createdAt).format('D MMM YYYY, h:mma')}
                      </Text>
                    </HStack>

                    <ParsedText
                      style={{ color: 'black', fontSize: 15, maxWidth: '90%' }}
                      parse={regexArr}
                      childrenProps={{ allowFontScaling: false }}
                    >
                      {item?.message?.replace(/ *\([^)]*\)*/g, '')}
                    </ParsedText>
                  </VStack>
                  {(!isMoreThan5Minute && isUser) || canDelete ? (
                    <Pressable
                      onPress={async () => {
                        Alert.alert('Delete Comment', 'Are you sure you want to delete this comment?', [
                          {
                            text: 'Cancel',
                            style: 'cancel'
                          },
                          {
                            text: 'Delete',
                            style: 'destructive',
                            onPress: async () => {
                              await deleteCommentHandler(item.id);
                            }
                          }
                        ]);
                      }}
                    >
                      <Box flex={1} justifyContent={'center'}>
                        <Icon name="delete-bin" width={15} height={15} />
                      </Box>
                    </Pressable>
                  ) : null}
                </HStack>
              </Box>
            );
          }}
          keyExtractor={item => item.id}
        />
      </Box>
      <Box flexDirection="row" mt={4}>
        <Box flex={0.7} mr={2}>
          <Avatar
            type="task"
            assignees={[{ assigneeNo: '1', avatarUrl: user?.avatar ?? '', name: user?.name ?? '' }]}
            maxVisible={1}
          />
        </Box>
        <Box flex={6}>
          <TouchableOpacity onPressIn={() => commentRef.current?.pushModal()} style={styles.mentionInput} />
        </Box>
      </Box>
      <CommentModal createComment={createCommentHandler} ref={commentRef} module="projectDocument" />
    </Box>
  );
};

const styles = StyleSheet.create({
  mentionInput: {
    borderWidth: 1,
    borderColor: 'neutrals.gray50',
    borderRadius: 5,
    padding: 5,
    textAlignVertical: 'top',
    height: 70
  }
});

export default CommentComponent;
