import { Input, Text } from 'native-base';
import React, { memo } from 'react';
import { Gql } from '@src/api';

interface DocumentDescriptionProps {
  description: string;
  status: Gql.ProjectDocumentStatus;
  maxLength?: number;
  testID?: string;
  error?: string | false;
  onChangeText: (text: string) => void;
  onBlur?: (e: any) => void;
}

const DocumentDescription: React.FC<DocumentDescriptionProps> = ({
  description = '',
  status,
  maxLength = 500,
  testID = 'document-description-input',
  error,
  onChangeText,
  onBlur
}) => {
  const isApproved = status === Gql.ProjectDocumentStatus.Approved;

  return (
    <>
      <Input
        testID={testID}
        color={isApproved ? 'neutrals.gray70' : 'neutrals.black'}
        isDisabled={isApproved}
        mt={1.5}
        fontSize={14}
        placeholder="Add your description here..."
        autoCapitalize="none"
        value={description}
        borderColor={error ? 'error.500' : 'neutrals.gray40'}
        variant="unstyled"
        _focus={{ borderColor: 'neutrals.gray40', bg: '#FFF' }}
        onChangeText={onChangeText}
        onBlur={onBlur}
        maxLength={maxLength}
        isReadOnly={isApproved}
        accessibilityLabel="Document description input"
        aria-label="Document description input"
        aria-invalid={!!error}
        aria-errormessage={error || undefined}
      />
      {error ? (
        <Text color="error.500" fontSize="xs" mt={1} testID={`${testID}-error`} accessibilityLabel={error}>
          {error}
        </Text>
      ) : null}
    </>
  );
};

// Memoize the component to prevent unnecessary re-renders
const MemoizedDocumentDescription = memo(DocumentDescription);

export { MemoizedDocumentDescription as DocumentDescription };
