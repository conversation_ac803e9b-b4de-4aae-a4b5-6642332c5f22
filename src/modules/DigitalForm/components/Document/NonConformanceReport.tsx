import React, { FC, memo } from 'react';
import { Gql } from '@src/api';
import { TouchableOpacity } from 'react-native';
import { Box, Button, Divider, Flex, HStack, Text } from 'native-base';
import { Icon } from '@src/commons';
import { useSelector } from '@src/store';
import { COLORS } from '@src/constants';

type NonConformanceReportProps = {
  onInReview?: () => void;
  onRequestAmend?: () => void;
  onResubmit?: () => void;
  onApprove?: () => void;
  onReject?: () => void;
  onInProgress?: () => void;
  onProceed?: () => void;
  onSubmit?: () => void;
  onView?: () => void;
  documentStatus?: Gql.ProjectDocumentStatus;
  currentSignatureTurn?: any;
  me?: any;
  getAmendUsers?: string[];
  currentUserSignature?: any;
  navigation?: any;
  currentUserId?: string;
  requestForSignature?: any;
  showRequestAmend?: boolean;
  ownerId?: number;
  workflow?: string;
  loading?: boolean;
};
const NonConformanceReportBtn: FC<NonConformanceReportProps> = ({
  workflow,
  loading,
  documentStatus,
  currentSignatureTurn,
  requestForSignature,
  ownerId,
  onView,
  onInReview,
  onSubmit,
  onRequestAmend,
  onResubmit,
  onApprove,
  onReject,
  onInProgress,
  onProceed,
  me,
  getAmendUsers,
  currentUserSignature,
  showRequestAmend
}) => {
  const { filter } = useSelector(state => state.documentWorkspace);

  // exlcude index 1 from requestForSignature
  const requestForSignatureExcluded = requestForSignature?.filter((_: any, index: any) => index !== 1);
  const getOwnerAssigned = requestForSignatureExcluded?.find((item: any) => Number(item.signById) === Number(ownerId));
  const isAfterOwnerAssigned = Number(currentUserSignature?.assigneeNo) >= Number(getOwnerAssigned?.assigneeNo);

  let dynamicBtn;

  if (
    documentStatus === Gql.ProjectDocumentStatus.Submitted ||
    documentStatus === Gql.ProjectDocumentStatus.Pending ||
    documentStatus === Gql.ProjectDocumentStatus.InProgress ||
    documentStatus === Gql.ProjectDocumentStatus.Amend
  ) {
    if (currentSignatureTurn?.status !== 'Approved' && currentSignatureTurn?.signById === me?.id) {
      dynamicBtn = (
        <TouchableOpacity onPress={onInReview}>
          <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4} m={5}>
            <HStack space={2}>
              <Icon name="flag" fill="#F29100" />
              <Text fontSize="16px" color="#8F8989">
                In Review
              </Text>
            </HStack>
            {filter.status?.includes(Gql.ProjectDocumentStatus.InReview) ? <Icon name="tick" /> : <></>}
          </Box>
        </TouchableOpacity>
      );
    } else {
      dynamicBtn = (
        <Box flexDirection="row" alignItems="center" justifyContent="space-between" mx={5} mt={4} mb={4}>
          <HStack space={2}>
            <Icon name="flag" fill="#74caec" />
            <Text fontSize="16px" color="#8F8989">
              Pending {getAmendUsers?.includes(me?.id) ? 'Amendment' : 'Approval'}
            </Text>
          </HStack>
          {filter.status?.includes(Gql.ProjectDocumentStatus.Rejected) ? <Icon name="tick" /> : <></>}
        </Box>
      );
    }
  } else if (documentStatus === Gql.ProjectDocumentStatus.InReview) {
    if (currentUserSignature?.status === 'Amend' && currentUserSignature?.signById === me?.id) {
      dynamicBtn = (
        <Flex direction="column" px={5}>
          {(currentUserSignature?.ownerId !== currentUserSignature?.signById ||
            currentUserSignature?.assigneeNo !== '1') && (
            <TouchableOpacity onPress={onRequestAmend}>
              <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
                <HStack space={2}>
                  <Icon name="flag" fill="#F29100" />
                  <Text fontSize="16px" color="#8F8989">
                    Request Previous Amendment
                  </Text>
                </HStack>
                {filter.status?.includes(Gql.ProjectDocumentStatus.Approved) ? <Icon name="tick" /> : <></>}
              </Box>
            </TouchableOpacity>
          )}

          <Divider mt={4} />

          <TouchableOpacity onPress={onResubmit}>
            <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
              <HStack space={2}>
                <Icon name="flag" fill="#3E78CF" />
                <Text fontSize="16px" color="#8F8989">
                  Resubmit
                </Text>
              </HStack>
              {filter.status?.includes(Gql.ProjectDocumentStatus.Submitted) ? <Icon name="tick" /> : <></>}
            </Box>
          </TouchableOpacity>

          <Divider mt={4} />
        </Flex>
      );
    } else if (currentUserSignature?.status === 'Amend' || currentUserSignature?.signById !== me?.id) {
      dynamicBtn = (
        <TouchableOpacity onPress={() => {}}>
          <Box flexDirection="row" alignItems="center" justifyContent="space-between" mx={5} mt={4} mb={4}>
            <HStack space={2}>
              <Icon name="flag" fill="#74caec" />
              <Text fontSize="16px" color="#8F8989">
                Pending {getAmendUsers?.includes(me?.id) ? 'Amendment' : 'Approval'}
              </Text>
            </HStack>
            {filter.status?.includes(Gql.ProjectDocumentStatus.Rejected) ? <Icon name="tick" /> : <></>}
          </Box>
        </TouchableOpacity>
      );
    } else if (
      //? only show approve and reject dynamicBtn if the current user is assigned to sign the document and the document is not approved or rejected

      currentUserSignature?.status !== 'Approved' &&
      currentUserSignature?.status !== 'Rejected' &&
      currentUserSignature
    ) {
      dynamicBtn = (
        // <Flex direction="column" style={style.container} width="100%" paddingBottom={1} justifyContent="space-between">
        <Flex direction="column" px={5}>
          <TouchableOpacity onPress={isAfterOwnerAssigned ? onApprove : onSubmit}>
            <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
              <HStack space={2}>
                <Icon name="flag" fill="#18A601" />
                <Text fontSize="16px" color="#8F8989">
                  {isAfterOwnerAssigned ? 'Approve' : 'Submit'}
                </Text>
              </HStack>
              {filter.status?.includes(Gql.ProjectDocumentStatus.Approved) ? <Icon name="tick" /> : <></>}
            </Box>
          </TouchableOpacity>

          <Divider mt={4} />

          <TouchableOpacity onPress={onProceed}>
            <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
              <HStack space={2}>
                <Icon name="flag" fill="#3E78CF" />
                <Text fontSize="16px" color="#8F8989">
                  Approve {`>`} Proceed
                </Text>
              </HStack>
              {filter.status?.includes(Gql.ProjectDocumentStatus.Submitted) ? <Icon name="tick" /> : <></>}
            </Box>
          </TouchableOpacity>

          {!showRequestAmend && currentSignatureTurn.assigneeNo !== '2' && (
            <>
              <Divider mt={4} />
              <TouchableOpacity onPress={onRequestAmend}>
                <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
                  <HStack space={2}>
                    <Icon name="flag" fill="#F29100" />
                    <Text fontSize="16px" color="#8F8989">
                      Request Amendment
                    </Text>
                  </HStack>
                  {filter.status?.includes(Gql.ProjectDocumentStatus.InReview) ? <Icon name="tick" /> : <></>}
                </Box>
              </TouchableOpacity>
            </>
          )}
        </Flex>
      );
    }
  }

  let linearBtn = (
    <HStack>
      {documentStatus == Gql.ProjectDocumentStatus.Submitted &&
        (currentUserSignature &&
        (currentUserSignature?.status === 'Sent' || currentUserSignature?.status === 'Pending') ? (
          <Button
            h={38}
            onPress={onInReview}
            style={{ marginLeft: 25 }}
            alignSelf="flex-end"
            height={10}
            backgroundColor={'#F29100'}
            mt={1}
            isLoading={loading}
          >
            In Review
          </Button>
        ) : (
          <Button
            h={38}
            style={{ marginLeft: 25 }}
            alignSelf="flex-end"
            height={10}
            disabled
            backgroundColor={COLORS.neutrals.gray70}
            mt={1}
            onPress={onSubmit}
          >
            Pending Approval
          </Button>
        ))}

      <Button
        // h={38}
        alignSelf="flex-end"
        h={10}
        pb={1}
        isLoading={loading}
        backgroundColor={COLORS.neutrals.white}
        mt={1}
        style={{
          borderColor: 'silver',
          borderWidth: 1,
          marginLeft: 25
        }}
        onPress={onView}
      >
        <Text color={'#0695D7'}>View</Text>
      </Button>

      {documentStatus == Gql.ProjectDocumentStatus.InReview &&
      currentUserSignature &&
      currentUserSignature?.status !== 'Approved' &&
      currentUserSignature?.status !== 'Rejected' ? (
        <>
          <Button
            // variant="outlineGray"
            h={38}
            onPress={onApprove}
            isLoading={loading}
            // isLoading={loading}
            // isLoadingText={'Updating...'}
            style={{ marginLeft: 25 }}
            alignSelf="flex-end"
            height={10}
            backgroundColor={'#18A601'}
            mt={1}
          >
            Submit
          </Button>
        </>
      ) : null}
    </HStack>
  );

  return <Box>{workflow === 'Linear' ? linearBtn : dynamicBtn}</Box>;
};

export default memo(NonConformanceReportBtn);
