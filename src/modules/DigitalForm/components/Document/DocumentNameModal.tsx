import { Modal } from '@commons';
import _ from 'lodash';
import { Box, Button, Text, VStack } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { ModalProps } from 'react-native';

interface Props {
  title?: any;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const DocumentNameModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="middle">
      <Box p={6}>
        <Text textAlign="center" mb={4} fontSize={16}>
          File name
        </Text>

        <VStack>
          <Text>{props.title}</Text>
        </VStack>

        <Box flexDirection="row" justifyContent="flex-end" mt={5}>
          <Button
            bg="transparent"
            _pressed={{ bg: 'transparent' }}
            onPress={() => {
              modalRef.current.closeModal();
            }}
          >
            <Text color="#0695D7">OK</Text>
          </Button>
        </Box>
      </Box>
    </Modal>
  );
});

DocumentNameModal.displayName = 'DocumentNameModal';
export default DocumentNameModal;
