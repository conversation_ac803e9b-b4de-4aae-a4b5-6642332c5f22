import { Gql } from '@src/api';
import { useDispatch, useSelector } from '@src/store';
import _, { isEqual } from 'lodash';
import moment from 'moment';
import { Avatar, Box, FlatList, HStack, Spacer, Spinner, Text, VStack } from 'native-base';
import React from 'react';

type Props = {
  selectedDocId: string;
};
export const ActivityComponent: React.FC<Props> = props => {
  const dispatch = useDispatch();
  const states = useSelector(state => state.tasks, isEqual);

  const { data, loading } = Gql.useGetActivityLogsQuery({
    variables: {
      filter: { resourceId: { eq: props.selectedDocId } },
      paging: {
        limit: 100
      },
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.AuditLogSortFields.CreatedAt
        }
      ]
    }
  });

  const activities = data?.auditLogs?.nodes ?? [];
  const activityData = activities?.map((activity: any) => {
    return {
      id: activity?.id,
      avatarUrl: activity?.user?.avatar,
      fullName: activity?.user?.name,
      content: activity?.content,
      createdAt: moment(activity?.createdAt).format('D MMM YYYY, h:mma'),
      action: activity?.action,
      module: activity?.module
    };
  });

  return (
    <Box>
      {loading && <Spinner></Spinner>}
      <FlatList
        data={activityData}
        renderItem={({ item }) => {
          if (item.module === Gql.AuditLogModuleType.Project) {
            return (
              <Box pl={['2', '4']} pr={['0', '5']} py="2">
                <HStack space={[2, 3]} justifyContent="space-between">
                  {/* <Avatar
                    size="32px"
                    source={{
                      uri: item.avatarUrl ?? ''
                    }}
                  /> */}
                  <VStack>
                    <Text
                      _dark={{
                        color: 'warmGray.50'
                      }}
                      color="coolGray.800"
                      bold
                    >
                      {item.fullName}
                    </Text>
                    <Text
                      fontSize="xs"
                      _dark={{
                        color: 'warmGray.50'
                      }}
                      color="coolGray.800"
                      alignSelf="flex-start"
                    >
                      {item.createdAt}
                    </Text>

                    <Text
                      color="coolGray.600"
                      _dark={{
                        color: 'warmGray.200'
                      }}
                    >
                      Document {''}
                      {item.action === Gql.AuditLogActionType.Create
                        ? 'Created'
                        : item.action === Gql.AuditLogActionType.Update
                          ? 'Updated'
                          : item.action === Gql.AuditLogActionType.Delete
                            ? 'Deleted'
                            : ''}
                    </Text>
                  </VStack>
                  <Spacer />
                </HStack>
              </Box>
            );
          } else if (item.module === Gql.AuditLogModuleType.Workspace) {
            // const title = item.content?.slice(0, item.content.indexOf('\n'));
            const description = item.content?.slice(item.content.indexOf('\n') + 1);
            return (
              <Box pl={['2', '4']} pr={['0', '5']} py="2">
                <HStack space={[2, 3]} justifyContent="space-between">
                  <Avatar
                    size="32px"
                    source={{
                      uri: item.avatarUrl
                    }}
                  />
                  <VStack>
                    <Text
                      fontSize="xs"
                      _dark={{
                        color: 'warmGray.50'
                      }}
                      color="coolGray.400"
                      alignSelf="flex-start"
                    >
                      {item.createdAt}
                    </Text>

                    <Box bg="#F0F4FF" width={291} style={{ borderRadius: 8, padding: 12 }}>
                      <VStack>
                        <Text
                          bold
                          fontSize="xs"
                          color="#2A64F9"
                          _dark={{
                            color: 'warmGray.200'
                          }}
                        >
                          Document {''} {item.action.replace(/([a-z])([A-Z])/g, '$1 $2')}
                        </Text>
                        <Text fontSize="xs">{description}</Text>
                      </VStack>
                    </Box>
                  </VStack>

                  <Spacer />
                </HStack>
              </Box>
            );
          } else {
            return null;
          }
        }}
        keyExtractor={item => item.id}
      />
    </Box>
  );
};

export default ActivityComponent;
