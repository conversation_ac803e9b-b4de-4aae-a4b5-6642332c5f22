import React from 'react';
import { <PERSON>, Divider, HS<PERSON>ck, Skeleton, Stack, VStack } from 'native-base';

/**
 * Skeleton component for document detail pages
 * Used for both Linear and Dynamic document detail pages
 */
const DocumentDetailSkeleton = () => {
  return (
    <Box bg="white" height="full">
      <Box px={6} pt={4}>
        {/* Action buttons skeleton */}
        <HStack justifyContent="space-between" mb={10}>
          <HStack space={2}>
            <Skeleton h={10} w={24} rounded="md" />
          </HStack>
          <Skeleton h={10} w={20} rounded="md" />
        </HStack>

        {/* Document form skeleton */}
        <VStack space={4} mb={4}>
          <Skeleton h={7} rounded="md" />
          <Skeleton h={20} rounded="md" />
        </VStack>

        {/* Created by skeleton */}
        <HStack justifyContent={'space-between'} mb={4} alignItems="center">
          <Skeleton w="30%" h={6} rounded="md" />
          <HStack flex={1} space={2} alignItems="center" justifyContent={'center'} w={'30%'}>
            <Skeleton rounded={'full'} size={10} />
            <Skeleton w={'40%'} h={6} rounded={'md'} />
          </HStack>
        </HStack>
        <Divider mb={4} />

        {/* Status skeleton */}
        <HStack justifyContent={'space-between'} mb={4} alignItems="center">
          <Skeleton w="30%" h={6} rounded="md" />
          <HStack flex={1} space={2} alignItems="center" justifyContent={'center'} w={'30%'}>
            <Skeleton w={'60%'} h={6} rounded={'md'} />
          </HStack>
        </HStack>
        <Divider mb={4} />

        {/* Group skeleton */}
        <HStack justifyContent={'space-between'} mb={4} alignItems="center">
          <Skeleton w="30%" h={6} rounded="md" />
          <HStack flex={1} space={2} alignItems="center" justifyContent={'center'} w={'30%'}>
            <Skeleton w={'60%'} h={6} rounded={'md'} />
          </HStack>
        </HStack>
        <Divider mb={4} />

        {/* Assignee skeleton */}
        <HStack justifyContent={'space-between'} mb={4} alignItems="center">
          <Skeleton w="30%" h={6} rounded="md" />
          <VStack flex={1} space={2} alignItems="center" justifyContent={'center'} w={'30%'}>
            <Skeleton w={'60%'} h={3} rounded={'md'} />
            <Skeleton w={'40%'} h={3} rounded={'md'} />
          </VStack>
        </HStack>
        <Divider mb={4} />

        {/* CC skeleton */}
        <HStack justifyContent={'space-between'} mb={4} alignItems="center">
          <Skeleton w="30%" h={6} rounded="md" />
          <VStack flex={1} space={2} alignItems="center" justifyContent={'center'} w={'30%'}>
            <Skeleton w={'60%'} h={3} rounded={'md'} />
            <Skeleton w={'40%'} h={3} rounded={'md'} />
          </VStack>
        </HStack>
        <Divider mb={4} />

        {/* Attachments skeleton */}
        <HStack justifyContent={'space-between'} mb={4} alignItems="center">
          <Skeleton w="30%" h={6} rounded="md" />
          <HStack flex={1} space={2} alignItems="center" justifyContent={'center'} w={'30%'}>
            <Skeleton w={'60%'} h={6} rounded={'md'} />
          </HStack>
        </HStack>
        <Divider mb={4} />

        {/* Media skeleton */}
        <HStack justifyContent={'space-between'} mb={4} alignItems="center">
          <Skeleton w="30%" h={6} rounded="md" />
          <HStack flex={1} space={2} alignItems="center" justifyContent={'center'} w={'30%'}>
            <Skeleton w={'60%'} h={6} rounded={'md'} />
          </HStack>
        </HStack>
        <Divider mb={4} />

        {/* Linked documents skeleton */}
        <HStack justifyContent={'space-between'} mb={12} alignItems="center">
          <Skeleton w="30%" h={6} rounded="md" />
          <HStack flex={1} space={2} alignItems="center" justifyContent={'center'} w={'30%'}>
            <Skeleton w={'60%'} h={6} rounded={'md'} />
          </HStack>
        </HStack>

        {/* Activity skeleton */}
        <Stack space={4} mb={4}>
          <HStack justifyContent={'space-between'} mb={4} alignItems="center">
            <Skeleton w="30%" h={6} rounded="md" />
            <Skeleton w="30%" h={6} rounded={'md'} />
          </HStack>

          <Skeleton h={20} rounded="md" />
        </Stack>
      </Box>
    </Box>
  );
};

export default DocumentDetailSkeleton;
