import { Icon, Modal } from '@commons';
import { <PERSON>, <PERSON><PERSON>, Divider, HStack, Text, View } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps, StyleSheet, TouchableOpacity } from 'react-native';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import { useDispatch, useSelector } from '@src/store';
import { Gql } from '@src/api';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootNavigatorParams } from '@src/types';
import { WorkspaceGroup } from '@src/api/graphql';
import Subgroup from './Subgroup';
import Members from './Members';
// import { COLORS } from '@src/constants';

interface Props {
  selectedGroupId?: string;
  selectedGroupName?: string;
  group?: WorkspaceGroup;
  handleThreeDots: (item: any) => void;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const GroupDetailsCardModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const dispatch = useDispatch();
  const { filter } = useSelector(state => state.documentWorkspace);
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const role = useSelector(state => state.project.projectUserRole);
  const isHighRole = role === Gql.ProjectUserRoleType.CloudCoordinator || role === Gql.ProjectUserRoleType.ProjectOwner;
  const groupUserCount = props.group?.workspaceGroupUsers?.length;

  const [selectedTab, setSelectedTab] = useState<'SUBGROUP' | 'MEMBERS' | null>('SUBGROUP'); // Add state for selected tab

  const getCountValues = (obj: any) => {
    return Object.keys(obj)
      .filter(key => key.toLowerCase().includes('count') && key !== 'totalCount') // Exclude 'totalCount'
      .map(key => ({ [key]: obj[key] }));
  };

  const mainGroupCounts = getCountValues(props.group);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const displayOrder = [
    'rejectedCount',
    'approvedCount',
    'inProgressCount',
    'amendCount',
    'inReviewCount',
    'pendingCount',
    'submittedCount'
  ];

  const sortedGroupCounts = displayOrder
    .map(key => mainGroupCounts.find(count => Object.keys(count)[0] === key))
    .filter(Boolean); // Filter out any undefined entries
  return (
    <Modal ref={modalRef} type="bottom">
      <Box p={5}>
        <HStack w="100%" alignItems="center" justifyContent="space-between">
          <Text lineHeight={30} fontSize="2xl">
            {props.selectedGroupName ?? '-'}
          </Text>
          <HStack alignItems={'center'}>
            <Icon name="lock" />
            <Text
              style={{ position: 'absolute', left: 12, bottom: 6 }}
              ml={1}
              color={COLORS.neutrals.gray70}
              fontSize={12}
            >
              {groupUserCount}
            </Text>
          </HStack>
        </HStack>
        <HStack mt={2} alignItems={'center'}>
          <Box borderWidth={2.1} borderRadius="lg" borderColor="coolGray.200" w={10} alignItems="center">
            <Text color={COLORS.neutrals.gray70}>{props.group?.code}</Text>
          </Box>
          <Text ml={2} color={COLORS.neutrals.gray70}>
            {props.group?.totalCount} Documents
          </Text>
        </HStack>
        <HStack mt={6} flexWrap="wrap" space={1}>
          {sortedGroupCounts.map((count: any, index) => {
            const key = Object.keys(count)[0];
            const value = count[key];
            const displayName = keyMappings[key]; // Get the mapped display name
            const backgroundColor = statusColors[key] || COLORS.neutrals.gray70; //
            if (value === 0) return null;

            return (
              <Box key={index} borderWidth={2.1} borderRadius="lg" borderColor="coolGray.200" width="32%" mb={2}>
                <HStack justifyContent={'space-between'} alignItems="center">
                  <Text ml={2}>
                    {displayName || key.replace('Count', '')} {/* Use mapped name or fallback */}
                  </Text>
                  <HStack mr={2} alignItems="center">
                    <Box
                      backgroundColor={backgroundColor}
                      borderRadius="full" // This makes it fully circular
                      alignItems="center"
                      justifyContent="center"
                      width={5} // Adjust the size as needed
                      height={5} // Adjust the size as needed
                    >
                      <Text color="white" fontWeight="bold">
                        {value}
                      </Text>
                    </Box>
                  </HStack>
                </HStack>
              </Box>
            );
          })}
        </HStack>

        {props.selectedGroupName !== 'Ungroup Documents' && props.selectedGroupName !== 'Site Diary' && (
          <>
            <Divider mt={6} />
            <HStack w={'60%'} mt={6} mb={4} justifyContent={'space-between'}>
              <TouchableOpacity onPress={() => setSelectedTab('SUBGROUP')}>
                <Box
                  px={2} // Add padding for horizontal spacing
                  py={1} // Add padding for vertical spacing
                  borderRadius="md" // Rounded button style
                  backgroundColor={selectedTab === 'SUBGROUP' ? '#1EA8E0' : 'white'} // Blue background if selected
                >
                  <Text
                    color={selectedTab === 'SUBGROUP' ? 'white' : '#1EA8E0'} // Highlight SUBGROUP if selected
                    fontSize={16}
                  >
                    SUBGROUP
                  </Text>
                </Box>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => setSelectedTab('MEMBERS')}>
                <Box
                  px={2} // Add padding for horizontal spacing
                  py={1} // Add padding for vertical spacing
                  borderRadius="md" // Rounded button style
                  backgroundColor={selectedTab === 'MEMBERS' ? '#1EA8E0' : 'white'} // Blue background if selected
                >
                  <Text
                    color={selectedTab === 'MEMBERS' ? 'white' : '#1EA8E0'} // Highlight MEMBERS if selected
                    fontSize={16}
                  >
                    MEMBERS
                  </Text>
                </Box>
              </TouchableOpacity>
            </HStack>
            {selectedTab === 'SUBGROUP' ? (
              //Last edited here
              <Subgroup
                navigation={navigation}
                children={props.group?.children as any}
                isHighRole
                handleThreeDots={props.handleThreeDots}
              />
            ) : (
              <Members workspaceGroupId={props?.group?.parentRemoteId as any} />
            )}
          </>
        )}
      </Box>
    </Modal>
  );
});

const keyMappings: Record<string, string> = {
  submittedCount: 'Submitted',
  inReviewCount: 'In Review',
  approvedCount: 'Approved',
  rejectedCount: 'Rejected',
  inProgressCount: 'In Progress',
  amendCount: 'To Amend',
  pendingCount: 'Pending'
};

const COLORS = {
  rejected: '#F28B82',
  approved: '#81C995',
  inProgress: '#81C995',
  amend: '#F8BBD0',
  inReview: '#FFD966',
  pending: '#A3D5F5',
  submitted: '#A3D5F5',
  neutrals: {
    gray70: '#4A4A4A'
  }
};

const statusColors: any = {
  rejectedCount: COLORS.rejected,
  approvedCount: COLORS.approved,
  inProgressCount: COLORS.inProgress,
  amendCount: COLORS.amend,
  inReviewCount: COLORS.inReview,
  pendingCount: COLORS.pending,
  submittedCount: COLORS.submitted
};

GroupDetailsCardModal.displayName = 'GroupDetailsCardModal';
export default GroupDetailsCardModal;
