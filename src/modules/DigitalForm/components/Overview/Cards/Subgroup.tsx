import React from 'react';
import { Box, Text, HStack, View, ScrollView } from 'native-base';
import { Icon } from '@src/commons';
import { Pressable, TouchableOpacity } from 'react-native';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import { useDispatch, useSelector } from '@src/store';
import * as Gql from '@src/api/graphql';

interface WorkspaceGroup {
  name: string;
  totalCount: number;
  rejectedCount: number;
  approvedCount: number;
  inReviewCount: number;
  inProgressCount: number;
  amendCount: number;
  pendingCount: number;
  submittedCount: number;
  workspaceGroupId: string;
}

interface SubgroupProps {
  children: WorkspaceGroup[];
  handleThreeDots?: (item: any) => void;
  isHighRole?: boolean;
  navigation?: any;
}

const statusColors = {
  rejected: '#F28B82',
  approved: '#81C995',
  inReview: '#FFD966',
  inProgress: '#81C995',
  amend: '#F8BBD0',
  pending: '#A3D5F5',
  submitted: '#A3D5F5'
};

const Subgroup: React.FC<SubgroupProps> = ({ children, isHighRole, handleThreeDots, navigation }) => {
  return (
    <ScrollView>
      <View height={'350'} padding={4} borderRadius="lg" bgColor={'primary.light'}>
        {children?.map(group => (
          <Box key={group.name} padding={4} bg={'white'} borderRadius="lg" borderColor="coolGray.200" mb={4}>
            {/* Group Title */}
            <HStack justifyContent="space-between" alignItems="center">
              <Icon name="subgroup" />
              <Text ml={2} flex={1} fontSize="lg" fontWeight="bold">
                {group.name} ({group.totalCount})
              </Text>
              {isHighRole && (
                <TouchableOpacity
                  hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
                  style={{ marginLeft: 8, paddingHorizontal: 12, paddingVertical: 12 }}
                  onPress={() => {
                    handleThreeDots?.(group);
                  }}
                >
                  <Icon name="three-dots" />
                </TouchableOpacity>
              )}
            </HStack>

            {/* Status Boxes - Horizontal ScrollView */}
            <ScrollView horizontal={true} mt={2} showsHorizontalScrollIndicator={false}>
              <HStack flexWrap="nowrap">
                {group.rejectedCount > 0 && (
                  <StatusBox
                    id={group?.workspaceGroupId}
                    name={group?.name}
                    navigation={navigation}
                    status={Gql.ProjectDocumentStatus.Rejected}
                    count={group.rejectedCount}
                    label="Rejected"
                    color={statusColors.rejected}
                  />
                )}
                {group.approvedCount > 0 && (
                  <StatusBox
                    id={group?.workspaceGroupId}
                    name={group?.name}
                    navigation={navigation}
                    status={Gql.ProjectDocumentStatus.Approved}
                    count={group.approvedCount}
                    label="Approved"
                    color={statusColors.approved}
                  />
                )}
                {group.inProgressCount > 0 && (
                  <StatusBox
                    id={group?.workspaceGroupId}
                    name={group?.name}
                    navigation={navigation}
                    status={Gql.ProjectDocumentStatus.InProgress}
                    count={group.inProgressCount}
                    label="In Progress"
                    color={statusColors.inProgress}
                  />
                )}
                {group.amendCount > 0 && (
                  <StatusBox
                    id={group?.workspaceGroupId}
                    name={group?.name}
                    navigation={navigation}
                    status={Gql.ProjectDocumentStatus.Amend}
                    count={group.amendCount}
                    label="To Amend"
                    color={statusColors.amend}
                  />
                )}
                {group.inReviewCount > 0 && (
                  <StatusBox
                    id={group?.workspaceGroupId}
                    name={group?.name}
                    navigation={navigation}
                    status={Gql.ProjectDocumentStatus.InReview}
                    count={group.inReviewCount}
                    label="In Review"
                    color={statusColors.inReview}
                  />
                )}
                {group.pendingCount > 0 && (
                  <StatusBox
                    id={group?.workspaceGroupId}
                    name={group?.name}
                    navigation={navigation}
                    status={Gql.ProjectDocumentStatus.Pending}
                    count={group.pendingCount}
                    label="Pending"
                    color={statusColors.pending}
                  />
                )}
                {group.submittedCount > 0 && (
                  <StatusBox
                    id={group?.workspaceGroupId}
                    name={group?.name}
                    navigation={navigation}
                    status={Gql.ProjectDocumentStatus.Submitted}
                    count={group.submittedCount}
                    label="Submitted"
                    color={statusColors.submitted}
                  />
                )}
              </HStack>
            </ScrollView>
          </Box>
        ))}
      </View>
    </ScrollView>
  );
};

interface StatusBoxProps {
  count: number;
  label: string;
  color: string;
  id: string;

  name?: string;
  navigation?: any;
  status: Gql.ProjectDocumentStatus;
}

const StatusBox: React.FC<StatusBoxProps> = ({ count, label, color, id, name, navigation, status }) => {
  const dispatch = useDispatch();
  const { filter } = useSelector(state => state.documentWorkspace);

  return (
    <Pressable
      onPress={() => {
        dispatch(
          documentWorkspaceActions.setFilterItems({
            ...filter,
            status: [status],
            groupId: id
          })
        );
        dispatch(
          documentWorkspaceActions.setFilter({
            ...filter,
            status: [status],
            groupId: id,
            groupName: name
          })
        );
        navigation.push('Tab', { screen: 'DigitalForm', params: { pageIndex: 1 } });
      }}
    >
      <Box borderWidth={2.1} borderRadius="lg" borderColor="coolGray.200" width="auto" m={1}>
        <HStack justifyContent={'space-between'} alignItems="center" px={1}>
          <Text>{label}</Text>
          <Box
            backgroundColor={color}
            borderRadius="full"
            alignItems="center"
            justifyContent="center"
            width={5}
            height={5}
            ml={2}
          >
            <Text color="white" fontWeight="bold">
              {count}
            </Text>
          </Box>
        </HStack>
      </Box>
    </Pressable>
  );
};

export default Subgroup;
