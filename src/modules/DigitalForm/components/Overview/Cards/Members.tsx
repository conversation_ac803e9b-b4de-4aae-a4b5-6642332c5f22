import React from 'react';
import { Box, HStack, Image, Text, View } from 'native-base';
import { Icon } from '@src/commons';
import { useGetWorkspaceGroupUser } from '@src/mutation/workspace-group/useGetWorkspaceGroupUser';
import Avatar from '@src/commons/Avatar';

interface MembersProps {
  workspaceGroupId: number | null;
}

const Members: React.FC<MembersProps> = ({ workspaceGroupId }) => {
  const { data, isLoading } = useGetWorkspaceGroupUser({ workspaceGroupId });

  if (!data) {
    return (
      <View>
        <Text>No members found</Text>
      </View>
    );
  }

  if (isLoading) {
    return (
      <View>
        <Text>Loading...</Text>
      </View>
    );
  }

  return (
    <View h={350} p={2}>
      <HStack ml={2} mb={4} justifyContent={'space-between'} alignItems={'center'}>
        <Icon name="twobluemen" />
        <Text fontSize={14} ml={2} color={'#1EA8E0'} flex={1}>
          {data.length} MEMBERS
        </Text>
        <Icon name="bluelock" />
      </HStack>

      <Box>
        {data.map(groupUser => (
          <View key={groupUser.user.id} flexDirection="row" alignItems="center" mb={3}>
            <Avatar assignees={[groupUser.user]} type="task" />
            <Text ml={3}>{groupUser.user.name}</Text>
          </View>
        ))}
      </Box>
    </View>
  );
};

export default Members;
