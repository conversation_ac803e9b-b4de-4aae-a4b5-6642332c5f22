import React, { useState } from 'react';
import { TouchableOpacity } from 'react-native';
import { Box, Flex, HStack, Text, VStack } from 'native-base';
import { FlashList } from '@shopify/flash-list';
import { Icon } from '@src/commons';
import { Gql } from '@src/api';
import { COLORS } from '@src/constants';
import { useSelector } from '@src/store';

type Props = {
  onPressChild?: (item: any) => void;
  onLongPressChild?: () => void;
  viewOnly?: boolean;
  handleThreeDots?: (item: any) => void;
  filteredValue?: string | null;
  selectedIcon?: string;
  item: any;
};

const CardView = (props: Props) => {
  const [isOpen, setIsOpen] = useState(false);
  const role = useSelector(state => state.project.projectUserRole);
  const isHighRole = role === Gql.ProjectUserRoleType.CloudCoordinator || role === Gql.ProjectUserRoleType.ProjectOwner;

  const toggleAccordion = (item: Gql.WorkspaceGroup) => {
    setIsOpen(!isOpen);
    return props?.onPressChild?.(item);
  };

  const renderItem = ({ item }: { item: any }) => (
    <Flex justify="center">
      <TouchableOpacity onPress={() => toggleAccordion(item)} style={{ width: 175, marginBottom: 16 }}>
        <Box height={150} borderWidth={1} borderRadius="lg" borderColor="coolGray.200" p={2}>
          <HStack alignItems="flex-start" justifyContent="space-between">
            <VStack>
              <Text fontWeight={600} numberOfLines={1} ellipsizeMode="tail">
                {item.name}
              </Text>
            </VStack>
          </HStack>
          <Box borderWidth={2.1} borderRadius="lg" borderColor="coolGray.200" w={10} alignItems="center">
            <Text color={COLORS.neutrals.gray70}>{item.code}</Text>
          </Box>
          <HStack mt="auto" alignItems="center" justifyContent="space-between" width="100%">
            {item.name !== 'Ungroup Documents' && item.name !== 'Site Diary' && isHighRole && (
              <TouchableOpacity
                onPress={() => props?.handleThreeDots?.(item)}
                hitSlop={{ top: 20, bottom: 10, left: 20, right: 10 }}
              >
                <Icon name="three-dots" />
              </TouchableOpacity>
            )}
          </HStack>
        </Box>
      </TouchableOpacity>
    </Flex>
  );

  return (
    <FlashList
      data={props.item}
      renderItem={renderItem}
      estimatedItemSize={150}
      keyExtractor={(item, index) => `${item.id || index}`} // Ensure items have unique keys
      contentContainerStyle={{ padding: 16 }}
      numColumns={2}
    />
  );
};

export default CardView;
