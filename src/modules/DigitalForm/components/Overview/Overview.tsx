import React, { useCallback, useMemo, useRef, useState, Suspense, lazy } from 'react';
import { Gql } from '@src/api';
import { pushError, pushSuccess, showFileName } from '@src/configs';
import _ from 'lodash';
import { Box, Circle, HStack } from 'native-base';
import { Alert, Platform, StyleSheet, TouchableOpacity } from 'react-native';
import { useSelector } from '@src/store';
import { workspace } from '@src/lib/authority';

import useDebouncedSearch from '@src/hooks/useDebounceSearch';
import SearchInput from '@src/commons/SearchInput';
import OverviewList from './OverviewList';
import { Icon } from '@src/commons';
import useCreateWorkspaceGroup from '@src/mutation/workspace-group/useCreateWorkspaceGroup';
import useDeleteWorkspaceGroup from '@src/mutation/workspace-group/useDeleteWorkspaceGroup';
import { useGetWorkspaceGroup } from '@src/queries/workspace/useGetWorkspaceGroups';
import OverviewSelector from '@src/commons/OverviewSelector';
import CardView from './Cards/CardView';
import GroupDetails from './GroupDetails';
import GroupDetailsCardModal from './Cards/GroupDetailsCard';
import OverviewOptionModal from './OverviewOptionModal';
import OverviewAddModal from './OverviewAddModal';

const Overview: React.FC<any> = props => {
  const [filteredValue, setFilteredValue] = useState<string | null>(null);
  const [selectedGroup, setSelectedGroup] = useState<any>({});
  const [selectedSubGroup, setSelectedSubGroup] = useState<any>({});
  const [isSubgroup, setIsSubgroup] = useState<boolean>(false);
  const [modalMutateAction, setModalMutateAction] = useState('create');
  const [selectedGroupName, setSelectedGroupName] = useState<any>('');
  const [selectedGroupId, setSelectedGroupId] = useState<any>('');
  const [selectedIcon, setSelectedIcon] = useState('three-list');

  const [canDelete, setCanDelete] = useState<boolean>(false);

  const addModalRef = useRef<any>(null);
  const overviewOptionModalRef = useRef<any>(null);
  const groupDetailsRef = useRef<any>(null);
  const groupDetailsCardRef = useRef<any>(null);

  const project = useSelector(state => state.project);
  const [openAccordion, setOpenAccordion] = useState(true);
  const perm = workspace(project.projectUserRole);
  const viewOnly = project.projectUserRole === 'CanView';

  const { debouncedValue, setInputValue } = useDebouncedSearch();
  const { data, fetchNextPage, hasNextPage, isLoading } = useGetWorkspaceGroup(debouncedValue ?? '');
  const { mutateAsync: createWorkspaceGroup } = useCreateWorkspaceGroup();
  const { mutateAsync: deleteWorkspaceGroup } = useDeleteWorkspaceGroup();

  const createNewGroup = async (value: string) => {
    try {
      if (!project.projectId) {
        return pushError('Project not found');
      }
      return await createWorkspaceGroup({
        name: value,
        projectId: parseInt(project.projectId),
        workspaceGroupId: null
      }).then(() => {
        pushSuccess('Created group successfully');
      });
    } catch (e) {
      pushError(e);
    }
  };

  const deleteGroup = async (value: string) => {
    try {
      if (!project.projectId) {
        return pushError('Project not found');
      }

      return await deleteWorkspaceGroup(value).then(() => {
        pushSuccess('Deleted group successfully');
      });
    } catch (e) {
      pushError(e);
    }
  };

  const handleOnPressList = (item: Gql.WorkspaceGroup) => {
    setSelectedGroup(item);
    setSelectedGroupId(item.remoteId);
    setSelectedGroupName(item.name);

    return groupDetailsRef.current.pushModal();
  };

  const datas = useMemo(() => {
    if (!data) return [];
    return data.pages.map(page => page.items).flat();
  }, [data]);

  const onSearchInputChange = (newSearchTerm: string) => {
    setFilteredValue(newSearchTerm);
    setInputValue(newSearchTerm || '');
  };

  const handleOverview = (item: any, type: string, hasChildren: boolean) => {
    setCanDelete(item?.workspaceGroupId !== null || item?.children?.length === 0);
    setModalMutateAction('update');
    setSelectedGroup(item);
    overviewOptionModalRef.current?.pushModal({ type, hasChildren });
  };

  const createButton = useMemo(
    () => (
      <TouchableOpacity
        style={[styles.addButton, { zIndex: 1000 }]}
        onPress={() => {
          addModalRef.current?.pushModal();
        }}
      >
        <Circle size="65px" bg="#0695D7">
          <Icon name="plus" />
        </Circle>
      </TouchableOpacity>
    ),
    [perm.overview.canCreate]
  );

  const handleEndReached = useCallback(() => {
    if (hasNextPage && !isLoading) {
      fetchNextPage();
    }
  }, [hasNextPage, isLoading, fetchNextPage]);

  const handleLongPress = (title: string) => {
    if (title === 'Ungroup Documents' || title === 'Site Diary') return;
    return showFileName('Group', title);
  };

  const handleOnPressCard = (item: Gql.WorkspaceGroup) => {
    setSelectedGroup(item);
    setSelectedGroupId(item.id);
    setSelectedGroupName(item.name);

    return groupDetailsCardRef.current.pushModal();
  };

  const handleThreeDots = (item: Gql.WorkspaceGroup) => {
    if (item?.__typename === 'WorkspaceGroup') {
      setCanDelete(item?.workspaceGroupId !== null || item?.children?.length === 0);
      setSelectedGroup(item);
      setIsSubgroup(false);
    } else {
      setIsSubgroup(true);
      setSelectedSubGroup(item);
    }
    return overviewOptionModalRef.current?.pushModal();
  };

  return (
    <Box bg="white" height="full">
      {createButton}
      <HStack alignItems="center">
        <SearchInput
          placeholder="Search for group name."
          filteredValue={filteredValue}
          onClose={() => setOpenAccordion(true)}
          setFilteredValue={(value: any) => onSearchInputChange(value)}
          width="80%"
        />
        <OverviewSelector selectedIcon={selectedIcon} onSelect={setSelectedIcon} />
      </HStack>

      {selectedIcon === 'three-list' ? (
        <OverviewList
          handleLongPress={handleLongPress}
          handleOnPress={handleOnPressList}
          handleOverview={handleOverview}
          openAccordion={openAccordion}
          viewOnly={viewOnly}
          filteredValue={debouncedValue}
          projectId={project?.projectId}
          key={project?.projectId}
          item={datas}
          handleReachedEnd={handleEndReached}
        />
      ) : (
        <CardView
          item={datas}
          onPressChild={handleOnPressCard}
          handleThreeDots={handleThreeDots}
          viewOnly={viewOnly}
          filteredValue={filteredValue}
          selectedIcon={selectedIcon}
        />
      )}
      <GroupDetails
        ref={groupDetailsRef}
        selectedGroupId={selectedGroupId}
        group={selectedGroup}
        selectedGroupName={selectedGroupName}
      />
      <GroupDetailsCardModal
        ref={groupDetailsCardRef}
        selectedGroupId={selectedGroupId}
        group={selectedGroup}
        selectedGroupName={selectedGroupName}
        handleThreeDots={handleThreeDots}
      />
      <OverviewOptionModal
        ref={overviewOptionModalRef}
        onDelete={deleteGroup}
        refetch={() => {
          // refetch();
        }}
        canDelete={canDelete}
        group={selectedGroup}
        subgroup={selectedSubGroup}
        isSubgroup={isSubgroup}
      />
      <OverviewAddModal ref={addModalRef} group={''} action={'Add'} />
    </Box>
  );
};

const styles = StyleSheet.create({
  addButton: {
    position: 'absolute',
    bottom: 10,
    alignSelf: 'flex-end',
    right: '7%',
    shadowColor: 'rgba(0, 0, 0, 0.5)',
    shadowOpacity: 1,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 6 },
    backgroundColor: '#FFFFFF',
    borderRadius: 9999
  }
});

export default Overview;
