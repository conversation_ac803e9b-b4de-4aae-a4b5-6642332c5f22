import { Icon, Modal } from '@commons';
import { Box, Divider, HStack, Text, View } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { ModalProps, StyleSheet, TouchableOpacity } from 'react-native';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import { useDispatch, useSelector } from '@src/store';
import { Gql } from '@src/api';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootNavigatorParams } from '@src/types';
import { WorkspaceGroup } from '@src/api/graphql';

interface Props {
  selectedGroupId?: string;
  selectedGroupName?: string;
  group?: WorkspaceGroup;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const GroupDetailsModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const dispatch = useDispatch();
  const { filter } = useSelector(state => state.documentWorkspace);
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const role = useSelector(state => state.project.projectUserRole);
  const isViewer = role === Gql.ProjectUserRoleType.CanView;

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="bottom">
      <Box p={3}>
        <Box p={5}>
          <HStack alignItems="center" space={3} pl={0}>
            <Icon name="group" />
            <Text fontSize={19}>{props.selectedGroupName ?? '-'}</Text>
          </HStack>
          {!isViewer && (
            <>
              <Divider mt={4} mb={4} />

              <TouchableOpacity
                onPress={() => {
                  dispatch(
                    documentWorkspaceActions.setFilterItems({
                      ...filter,
                      status: [Gql.ProjectDocumentStatus.Submitted],
                      groupId: props.selectedGroupId
                    })
                  );
                  dispatch(
                    documentWorkspaceActions.setFilter({
                      ...filter,
                      status: [Gql.ProjectDocumentStatus.Submitted],
                      groupId: props.selectedGroupId,
                      groupName: props.selectedGroupName
                    })
                  );
                  modalRef.current.closeModal();
                  navigation.push('Tab', { screen: 'DigitalForm', params: { pageIndex: 1 } });
                }}
              >
                <Box flexDirection="row" justifyContent="space-between">
                  <HStack space={2}>
                    <View style={[style.dot, { backgroundColor: '#3E78CF', alignSelf: 'center' }]} />
                    <Text fontSize={18} color="#8F8989">
                      Submitted
                    </Text>
                  </HStack>
                  <Box>
                    <Text fontSize={18} color="#8F8989">
                      {`(${props?.group?.submittedCount ?? 0})`}
                    </Text>
                  </Box>
                </Box>
              </TouchableOpacity>
              <Divider mt={4} mb={4} />

              <TouchableOpacity
                onPress={() => {
                  dispatch(
                    documentWorkspaceActions.setFilterItems({
                      ...filter,
                      status: [Gql.ProjectDocumentStatus.InReview],
                      groupId: props.selectedGroupId
                    })
                  );
                  dispatch(
                    documentWorkspaceActions.setFilter({
                      ...filter,
                      status: [Gql.ProjectDocumentStatus.InReview],
                      groupId: props.selectedGroupId,
                      groupName: props.selectedGroupName
                    })
                  );
                  modalRef.current.closeModal();
                  navigation.push('Tab', { screen: 'DigitalForm', params: { pageIndex: 1 } });
                }}
              >
                <Box flexDirection="row" justifyContent="space-between">
                  <HStack space={2}>
                    <View style={[style.dot, { backgroundColor: '#F29100', alignSelf: 'center' }]} />
                    <Text fontSize={18} color="#8F8989">
                      In Review
                    </Text>
                  </HStack>
                  <Box>
                    <Text fontSize={18} color="#8F8989">
                      {`(${props?.group?.inReviewCount ?? 0})`}
                    </Text>
                  </Box>
                </Box>
              </TouchableOpacity>

              <Divider mt={4} mb={4} />

              <TouchableOpacity
                onPress={() => {
                  dispatch(
                    documentWorkspaceActions.setFilterItems({
                      ...filter,
                      status: [Gql.ProjectDocumentStatus.Amend],
                      groupId: props.selectedGroupId
                    })
                  );
                  dispatch(
                    documentWorkspaceActions.setFilter({
                      ...filter,
                      status: [Gql.ProjectDocumentStatus.Amend],
                      groupId: props.selectedGroupId,
                      groupName: props.selectedGroupName
                    })
                  );
                  modalRef.current.closeModal();
                  navigation.push('Tab', { screen: 'DigitalForm', params: { pageIndex: 1 } });
                }}
              >
                <Box flexDirection="row" justifyContent="space-between">
                  <HStack space={2}>
                    <View style={[style.dot, { backgroundColor: '#FF25A1', alignSelf: 'center' }]} />
                    <Text fontSize={18} color="#8F8989">
                      Amend
                    </Text>
                  </HStack>
                  <Box>
                    <Text fontSize={18} color="#8F8989">
                      {`(${props?.group?.amendCount ?? 0})`}
                    </Text>
                  </Box>
                </Box>
              </TouchableOpacity>

              <Divider mt={4} mb={4} />

              <TouchableOpacity
                onPress={() => {
                  dispatch(
                    documentWorkspaceActions.setFilterItems({
                      ...filter,
                      status: [Gql.ProjectDocumentStatus.Pending],
                      groupId: props.selectedGroupId
                    })
                  );
                  dispatch(
                    documentWorkspaceActions.setFilter({
                      ...filter,
                      status: [Gql.ProjectDocumentStatus.Pending],
                      groupId: props.selectedGroupId,
                      groupName: props.selectedGroupName
                    })
                  );
                  modalRef.current.closeModal();
                  navigation.push('Tab', { screen: 'DigitalForm', params: { pageIndex: 1 } });
                }}
              >
                <Box flexDirection="row" justifyContent="space-between">
                  <HStack space={2}>
                    <View style={[style.dot, { backgroundColor: '#74caec', alignSelf: 'center' }]} />
                    <Text fontSize={18} color="#8F8989">
                      Pending
                    </Text>
                  </HStack>
                  <Box>
                    <Text fontSize={18} color="#8F8989">
                      {`(${props?.group?.pendingCount ?? 0})`}
                    </Text>
                  </Box>
                </Box>
              </TouchableOpacity>

              <Divider mt={4} mb={4} />

              <TouchableOpacity
                onPress={() => {
                  dispatch(
                    documentWorkspaceActions.setFilterItems({
                      ...filter,
                      status: [Gql.ProjectDocumentStatus.InProgress],
                      groupId: props.selectedGroupId
                    })
                  );
                  dispatch(
                    documentWorkspaceActions.setFilter({
                      ...filter,
                      status: [Gql.ProjectDocumentStatus.InProgress],
                      groupId: props.selectedGroupId,
                      groupName: props.selectedGroupName
                    })
                  );
                  modalRef.current.closeModal();
                  navigation.push('Tab', { screen: 'DigitalForm', params: { pageIndex: 1 } });
                }}
              >
                <Box flexDirection="row" justifyContent="space-between">
                  <HStack space={2}>
                    <View style={[style.dot, { backgroundColor: '#18A601', alignSelf: 'center' }]} />
                    <Text fontSize={18} color="#8F8989">
                      In Progress
                    </Text>
                  </HStack>
                  <Box>
                    <Text fontSize={18} color="#8F8989">
                      {`(${props?.group?.inProgressCount ?? 0})`}
                    </Text>
                  </Box>
                </Box>
              </TouchableOpacity>
            </>
          )}
          <Divider mt={4} mb={4} />

          <TouchableOpacity
            onPress={() => {
              dispatch(
                documentWorkspaceActions.setFilterItems({
                  ...filter,
                  status: [Gql.ProjectDocumentStatus.Approved],
                  groupId: props.selectedGroupId
                })
              );
              dispatch(
                documentWorkspaceActions.setFilter({
                  ...filter,
                  status: [Gql.ProjectDocumentStatus.Approved],
                  groupId: props.selectedGroupId,
                  groupName: props.selectedGroupName
                })
              );
              modalRef.current.closeModal();
              navigation.push('Tab', { screen: 'DigitalForm', params: { pageIndex: 1 } });
            }}
          >
            <Box flexDirection="row" justifyContent="space-between">
              <HStack space={2}>
                <View style={[style.dot, { backgroundColor: '#18A601', alignSelf: 'center' }]} />
                <Text fontSize={18} color="#8F8989">
                  Approved
                </Text>
              </HStack>
              <Box>
                <Text fontSize={18} color="#8F8989">
                  {`(${props?.group?.approvedCount ?? 0})`}
                </Text>
              </Box>
            </Box>
          </TouchableOpacity>

          {!isViewer && (
            <>
              <Divider mt={4} mb={4} />

              <TouchableOpacity
                onPress={() => {
                  dispatch(
                    documentWorkspaceActions.setFilterItems({
                      ...filter,
                      status: [Gql.ProjectDocumentStatus.Rejected],
                      groupId: props.selectedGroupId
                    })
                  );
                  dispatch(
                    documentWorkspaceActions.setFilter({
                      ...filter,
                      status: [Gql.ProjectDocumentStatus.Rejected],
                      groupId: props.selectedGroupId,
                      groupName: props.selectedGroupName
                    })
                  );
                  modalRef.current.closeModal();
                  navigation.push('Tab', { screen: 'DigitalForm', params: { pageIndex: 1 } });
                }}
              >
                <Box flexDirection="row" justifyContent="space-between">
                  <HStack space={2}>
                    <View style={[style.dot, { backgroundColor: '#F40000', alignSelf: 'center' }]} />
                    <Text fontSize={18} color="#8F8989">
                      Rejected
                    </Text>
                  </HStack>
                  <Box>
                    <Text fontSize={18} color="#8F8989">
                      {`(${props?.group?.rejectedCount ?? 0})`}
                    </Text>
                  </Box>
                </Box>
              </TouchableOpacity>
            </>
          )}
        </Box>
      </Box>
    </Modal>
  );
});

const style = StyleSheet.create({
  dot: {
    width: 10,
    height: 10,
    borderRadius: 5
  }
});

GroupDetailsModal.displayName = 'GroupDetailsModal';
export default GroupDetailsModal;
