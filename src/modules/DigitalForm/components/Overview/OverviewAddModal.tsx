import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Modal } from '@commons';
import { pushError, pushSuccess, pushMessaage } from '@src/configs';
import useCreateWorkspaceGroup from '@src/mutation/workspace-group/useCreateWorkspaceGroup';
import useUpdateWorkspaceGroup from '@src/mutation/workspace-group/useUpdateWorkspaceGroup';
import { useSelector } from '@src/store';
import { Box, Button, Input, Spinner, Text, VStack } from 'native-base';
import { ModalProps } from 'react-native';

interface Props {
  refetch?: () => void;
  group?: any;
  action?: 'AddSubGroup' | 'Add' | 'Rename' | 'RenameSubGroup' | null;
}

export interface ModalRef extends ModalProps {
  pushModal: () => void;
}

const OverviewAddModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const [value, setValue] = useState('');
  const [code, setCode] = useState('');
  const [loading, setLoading] = useState(false);
  const project = useSelector(state => state.project);

  const { mutateAsync: createGroup } = useCreateWorkspaceGroup();
  const { mutateAsync: updateGroup } = useUpdateWorkspaceGroup();

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const initializeValues = useCallback(() => {
    if (!props.action) return;

    const { group } = props;
    if (props.action === 'Rename' || props.action === 'RenameSubGroup') {
      setValue(group?.name || '');
      if (props.action === 'Rename') setCode(group?.code || '');
    }
  }, [props.action, props.group]);

  useEffect(() => {
    initializeValues();
  }, [initializeValues]);

  const handleFinish = async () => {
    setLoading(true);

    const workspaceGroupId = props.group?.remoteId
      ? { workspaceGroupId: props.group.remoteId }
      : { localGroupId: props.group?.id };

    if (!value.trim()) {
      pushMessaage('Error on group name', 'error', 'Group name is required!');
      closeModal();
      return;
    }

    if (props.action === 'Add' && (!code.trim() || code.length < 3)) {
      pushMessaage('Error on code', 'error', 'Code is required and must be 3 characters long!');
      closeModal();
      return;
    }

    if (value === 'Ungroup Documents') {
      pushError('This group name cannot be used.');
      closeModal();
      return;
    }

    try {
      if (props.action === 'Rename' || props.action === 'RenameSubGroup') {
        await updateGroup({
          name: value,
          code: props.action === 'Rename' ? code : '',
          workspaceGroupId: props.group?.id
        });
        pushSuccess('Group name updated successfully');
      } else {
        if (!project.projectId) {
          return pushError('Project not found');
        }

        await createGroup({
          name: value,
          ...workspaceGroupId,
          projectId: parseInt(project.projectId),
          code
        });
        pushSuccess('Group created successfully');
      }
      resetForm();
    } catch (e: any) {
      pushError(e.message);
    } finally {
      setLoading(false);
    }
  };

  const closeModal = () => {
    modalRef.current?.closeModal();
    setLoading(false);
  };

  const resetForm = () => {
    setValue('');
    setCode('');
    closeModal();
  };

  const getTitle = () => {
    switch (props.action) {
      case 'AddSubGroup':
        return 'Add Sub Group';
      case 'Rename':
        return 'Rename Group';
      case 'RenameSubGroup':
        return 'Rename Sub Group';
      default:
        return 'Add Group';
    }
  };

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="middle">
      <Box p={6}>
        <Text textAlign="center" mb={4} fontSize={16}>
          {getTitle()}
        </Text>

        <VStack>
          <Text>Group name</Text>
          <Input onChangeText={setValue} isRequired defaultValue={value} isReadOnly={value === 'Ungroup Documents'} />
        </VStack>

        {props.action !== 'AddSubGroup' && props.action !== 'RenameSubGroup' && (
          <VStack mt={2}>
            <Text>Code</Text>
            <Input onChangeText={text => setCode(text.toUpperCase())} isRequired defaultValue={code} maxLength={3} />
          </VStack>
        )}

        <Box flexDirection="row" justifyContent="flex-end" mt={5}>
          <Button bg="transparent" _pressed={{ bg: 'transparent' }} onPress={closeModal}>
            <Text color="#0695D7">Cancel</Text>
          </Button>
          {loading ? (
            <Spinner size="lg" />
          ) : (
            <Button bg="transparent" _pressed={{ bg: 'transparent' }} onPress={handleFinish}>
              <Text>{props.action === 'Add' || props.action === 'AddSubGroup' ? 'Create' : 'Update'}</Text>
            </Button>
          )}
        </Box>
      </Box>
    </Modal>
  );
});

OverviewAddModal.displayName = 'OverviewAddModal';
export default OverviewAddModal;
