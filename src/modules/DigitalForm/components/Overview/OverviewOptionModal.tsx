import { Modal } from '@commons';
import { Button, Text } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { Alert, ModalProps } from 'react-native';
import OverviewAddModal from './OverviewAddModal';
import { useSelector } from '@src/store';
import { workspace } from '@src/lib/authority';
import { Gql } from '@src/api';
import { pushError, pushSuccess } from '@src/configs';
import useDeleteWorkspaceGroup from '@src/mutation/workspace-group/useDeleteWorkspaceGroup';

interface Props {
  onChange?: (values: string) => void;
  onDelete?: (values: string) => void;
  refetch?: () => void;
  canDelete: boolean;
  group?: Gql.WorkspaceGroup;
  subgroup?: Gql.WorkspaceGroup;
  isSubgroup?: boolean;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const OverviewOptionModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const addModalRef = useRef<any>(null);
  const project = useSelector(state => state.project);
  const perm = workspace(project.projectUserRole);

  const [actions, setActions] = useState<'Add' | 'AddSubGroup' | 'Rename' | 'RenameSubGroup' | null>(null);
  const { mutateAsync: deleteWorkspaceGroup } = useDeleteWorkspaceGroup();

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const deleteGroup = async (id: string) => {
    try {
      await deleteWorkspaceGroup(id);
      pushSuccess('Delete group successfully');
    } catch (err) {
      pushError(err);
    }
  };

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="bottom">
      {perm.overview.canCreate && !props?.group?.workspaceGroupId && !props?.isSubgroup ? (
        <Button
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          mb={4}
          bg="transparent"
          onPress={() => {
            setActions('AddSubGroup');
            addModalRef?.current?.pushModal();
          }}
        >
          <Text>Add Sub Group</Text>
        </Button>
      ) : null}
      {!props?.group?.workspaceGroupId && !props?.isSubgroup && perm.overview.canRename && (
        <Button
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          mb={4}
          bg="transparent"
          onPress={() => {
            Alert.alert(
              'Share From Web',
              'For the best experience and accuracy of sharing with members, please share from the web platform.'
            );
          }}
        >
          <Text>Manage Member Access</Text>
        </Button>
      )}
      {perm.overview.canRename && (
        <Button
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          mb={4}
          bg="transparent"
          onPress={() => {
            setActions(props?.isSubgroup ? 'RenameSubGroup' : 'Rename');
            addModalRef?.current?.pushModal();
          }}
        >
          <Text>Rename</Text>
        </Button>
      )}
      {perm.overview.canDelete && props?.canDelete ? (
        <Button
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          mb={4}
          bg="transparent"
          onPress={() => {
            Alert.alert('Delete Group', 'Are you sure you want to delete this group?', [
              {
                text: 'Cancel',
                onPress: () => {},
                style: 'cancel'
              },
              {
                text: 'Delete',
                onPress: async () => {
                  if (!props?.group?.id) return pushError('Group id is required');

                  await deleteGroup(props?.group?.id);
                  modalRef?.current?.closeModal();
                }
              }
            ]);
          }}
        >
          <Text color="semantics.danger">{props?.group?.workspaceGroupId ? 'Delete Sub Group' : 'Delete Group'}</Text>
        </Button>
      ) : null}

      <OverviewAddModal
        ref={addModalRef}
        refetch={() => {
          props?.refetch?.();
          modalRef?.current?.closeModal();
        }}
        group={props?.isSubgroup ? props?.subgroup : props.group}
        action={actions}
      />
    </Modal>
  );
});

OverviewOptionModal.displayName = 'OverviewOptionModal';
export default OverviewOptionModal;
