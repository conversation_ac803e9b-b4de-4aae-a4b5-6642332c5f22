import { Modal } from '@commons';
import { pushError, pushSuccess } from '@src/configs';
import useUpdateWorkspaceDocument from '@src/mutation/workspace/useUpdateWorkspaceDocument';
import _ from 'lodash';
import { Box, Button, Input, Spinner, Text, VStack } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps } from 'react-native';

interface Props {
  refetch: () => void;
  data?: any;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const TemplateRenameModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const [value, setValue] = useState(props?.data?.name ?? '');
  const [loading, setLoading] = useState<boolean>(false);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const { mutateAsync: updateTemplate } = useUpdateWorkspaceDocument();

  const onUpdate = async (value: any) => {
    //   setLoading(true);
    //   if (!value || value === '') {
    //     pushMessaage('Error on name', 'error', 'Name is required!');
    //     modalRef.current.closeModal();
    //     setLoading(false);
    //   } else {
    //     updateName({
    //       variables: {
    //         input: {
    //           id: props?.data?.id.toString() ?? '',
    //           update: {
    //             name: value
    //           }
    //         }
    //       }
    //     });
    //   }

    await updateTemplate({
      id: props?.data?.id,
      name: value
    })
      .then(() => {
        modalRef.current.closeModal();
        pushSuccess('Update filename successfully');
      })
      .catch((err: any) => {
        pushError(err.message);
      });
  };

  // const [updateName] = Gql.useUpdateOneProjectDocumentMutation({
  //   onCompleted: () => {
  //     pushSuccess('Update filename successfully');
  //     props?.refetch?.();
  //     modalRef?.current?.closeModal();
  //     setLoading(false);
  //   },
  //   onError: (err: any) => {
  //     pushError(err.message);
  //     setLoading(false);
  //   }
  // });

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="middle">
      <Box p={6}>
        <Text textAlign="center" mb={4} fontSize={16}>
          Update new name
        </Text>

        <VStack>
          <Text>Enter name</Text>
          <Input
            onChangeText={value => {
              setValue(value);
            }}
            isRequired={true}
            defaultValue={props?.data?.name}
          />
        </VStack>

        <Box flexDirection="row" justifyContent="flex-end" mt={5}>
          <Button
            bg="transparent"
            _pressed={{ bg: 'transparent' }}
            onPress={() => {
              modalRef.current.closeModal();
            }}
          >
            <Text color="#0695D7">Cancel</Text>
          </Button>
          {loading ? (
            <Spinner size="lg" />
          ) : (
            <Button
              bg="transparent"
              _pressed={{ bg: 'transparent' }}
              onPress={() => {
                onUpdate(value);
              }}
            >
              <Text>Update</Text>
            </Button>
          )}
        </Box>
      </Box>
    </Modal>
  );
});

TemplateRenameModal.displayName = 'TemplateRenameModal';
export default TemplateRenameModal;
