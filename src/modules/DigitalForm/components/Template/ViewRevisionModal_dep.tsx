import { Image, Modal } from '@commons';
import { Icon } from '@src/commons';
import { Gql } from '@src/api';
import { pushError, pushMessaage, generateRNFile, pushSuccess } from '@src/configs';
import { Box, Button, Card, HStack, Input, ScrollView, Spinner, Text, VStack, View } from 'native-base';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps, Platform, TouchableOpacity } from 'react-native';
import PdfThumbnail from 'react-native-pdf-thumbnail';

interface Props {
  refetch: () => void;
  data?: any;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const ViewRevisionModal = forwardRef<ModalRef, Props>((props, ref) => {
  const [thumbnail, setThumbnail] = useState<any>([]);
  const modalRef = useRef<any>(null);
  const { data: drawingRevisionData, loading: drawingRevisionLoading } = Gql.useGetDrawingRevisionsQuery({
    variables: {
      filter: {
        projectDocumentId: { eq: parseInt(props?.data?.id) }
      }
    }
  });
  const [loading, setLoading] = useState<boolean>(false);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const generateThumbnailsForNodes = async (nodes: any) => {
    const thumbnails = [];
    for (const node of nodes) {
      const { fileUrl, fileName, version } = node;
      if (fileUrl) {
        const thumbnail = await generateThumbnail(fileUrl);
        thumbnails.push([thumbnail, fileName, version]);
      }
    }
    return thumbnails;
  };

  const generateThumbnail = async (url: any) => {
    if (!url) return;
    const page = 0;
    if (Platform.OS === 'android' || Platform.OS === 'ios') {
      const { uri } = await PdfThumbnail.generate(url, page);
      return uri;
    }
  };

  useEffect(() => {
    const nodes = drawingRevisionData?.drawingRevisions?.nodes;
    if (nodes && nodes.length > 0) {
      generateThumbnailsForNodes(nodes).then(thumbnails => {
        setThumbnail(thumbnails);
      });
    }
  }, [drawingRevisionData]);

  const onUpdate = (value: any) => {
    setLoading(true);
    if (!value || value === '') {
      pushMessaage('Error on name', 'error', 'Name is required!');
      modalRef.current.closeModal();
      setLoading(false);
    } else {
      updateName({
        variables: {
          input: {
            id: props?.data?.id.toString() ?? '',
            update: {
              name: value
            }
          }
        }
      });
    }
  };

  const [updateName] = Gql.useUpdateOneProjectDocumentMutation({
    onCompleted: () => {
      pushSuccess('Update filename successfully');
      props?.refetch?.();
      modalRef?.current?.closeModal();
      setLoading(false);
    },
    onError: (err: any) => {
      pushError(err.message);
      setLoading(false);
    }
  });

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="bottom">
      <ScrollView h={'80%'}>
        <Box p={6}>
          <Text textAlign="center" mb={4} fontSize={16}>
            Drawing Revisions
          </Text>

          {/* Render all thumbnails */}
          <VStack>
            {thumbnail.map((thumbnail: any, index: any) => (
              <Card key={index} mb={4} borderWidth={1} borderColor={'#EEE'} borderRadius={8} alignItems={'center'}>
                <Text>{thumbnail[1]}</Text>
                <HStack space={2} alignItems="center" justifyContent="space-between">
                  <Image source={{ uri: thumbnail[0] }} style={{ width: '80%', margin: 10 }} />
                  <TouchableOpacity
                    onPress={() => {}}
                    style={{
                      left: '70%',
                      width: 100,
                      height: 100,
                      position: 'absolute',
                      justifyContent: 'center',
                      alignItems: 'center'
                    }}
                  >
                    <Icon name="three-dots-v" />
                  </TouchableOpacity>
                </HStack>
                {/* <Text>{`Version Number ${thumbnail[2]}`}</Text> */}
              </Card>
            ))}
          </VStack>
        </Box>
      </ScrollView>
    </Modal>
  );
});

ViewRevisionModal.displayName = 'ViewRevisionModal';
export default ViewRevisionModal;
