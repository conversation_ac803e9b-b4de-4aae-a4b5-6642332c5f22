import { Modal } from '@commons';
import { Gql } from '@src/api';
import { pushError, pushMessaage, pushSuccess } from '@src/configs';
import _ from 'lodash';
import { Box, Button, Input, Text, VStack } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps } from 'react-native';

interface Props {
  refetch: () => void;
  data?: any;
  onSave?: () => void;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const TemplateRenameModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const [value, setValue] = useState(props?.data?.name ?? '');

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  // const onUpdate = (value: any) => {
  //   if(!value || value === '') {
  //     pushError('Error on name', 'error' );
  //     modalRef.current.closeModal();
  //   }
  //   else {
  //     updateName({
  //       variables: {
  //         input: {
  //           id: props?.data?.id.toString() ?? "",
  //           update: {
  //             name: value,
  //           },
  //         },
  //       },
  //     });
  //   }
  // };

  const onUpdate = (value: any) => {
    if (!value || value === '') {
      pushMessaage('Error on name', 'error', 'Name is required!');
      modalRef.current.closeModal();
    } else {
      updateName({
        variables: {
          input: {
            id: props?.data?.id?.toString() ?? '',
            update: {
              name: value
            }
          }
        }
      });
    }
  };

  const [updateName] = Gql.useUpdateOneProjectDocumentMutation({
    onCompleted: () => {
      pushSuccess('Update filename successfully');
      props?.refetch?.();
      modalRef?.current?.closeModal();
    },
    onError: (err: any) => {
      pushError(err.message);
      modalRef?.current?.closeModal();
    }
  });

  const [uploadFile, { loading: uploadFileLoading }] = Gql.useCreateOneProjectDocumentMutation({
    onCompleted: () => {
      pushSuccess('File upload successfully');
      props?.refetch?.();
      props?.onSave?.();
      modalRef.current.closeModal();
    },
    onError: (err: any) => {
      throw err;
    }
  });

  const onUpload = (element: any) => {
    if (!value || value === '') {
      pushMessaage('Error on name', 'error', 'Name is required!');
      modalRef.current.closeModal();
    } else {
      //check if value not end with pdf, add .pdf extension
      if (!_.endsWith(element, '.pdf')) {
        element = element + '.pdf';
      }
      props.data.name = element; //Change name to rename
      uploadFile({
        variables: {
          input: {
            projectDocument: {
              name: element as any,
              category: Gql.CategoryType.AllForm,
              fileSystemType: Gql.FileSystemType.Document,
              projectDocumentId: null,
              fileUrl: props?.data
            }
          }
        }
      });
    }
  };

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="middle">
      <Box p={6}>
        <Text textAlign="center" mb={4} fontSize={16}>
          Update new name
        </Text>

        <VStack>
          <Text>Enter name</Text>
          <Input
            onChangeText={value => {
              setValue(value);
            }}
            isRequired={true}
            defaultValue={props?.data?.name}
          />
        </VStack>

        <Box flexDirection="row" justifyContent="flex-end" mt={5}>
          <Button
            bg="transparent"
            _pressed={{ bg: 'transparent' }}
            onPress={() => {
              modalRef.current.closeModal();
            }}
          >
            <Text color="#0695D7">Cancel</Text>
          </Button>
          <Button
            bg="transparent"
            _pressed={{ bg: 'transparent' }}
            onPress={() => {
              onUpload(value);
            }}
            isLoading={uploadFileLoading}
          >
            <Text>Update</Text>
          </Button>
        </Box>
      </Box>
    </Modal>
  );
});

TemplateRenameModal.displayName = 'TemplateRenameModal';
export default TemplateRenameModal;
