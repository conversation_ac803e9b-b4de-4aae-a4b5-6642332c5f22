import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { Icon } from '@src/commons';
import { pushSuccess, showFileName } from '@src/configs';
import { useSelector } from '@src/store';
import { RootNavigatorParams } from '@src/types';
import { Box, Circle, Divider, Skeleton, Stack, VStack } from 'native-base';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import OptionModal from '../selector-component/OptionModal';
import TemplateAddModal from './TemplateAddModal';
import { useGetTemplate } from '@src/queries/workspace/useGetWorkspaceTemplate';
import useDebouncedSearch from '@src/hooks/useDebounceSearch';
import DocumentList from '@src/commons/list/DocumentList';
import SearchInput from '@src/commons/SearchInput';
import ProjectDocumentModel from '@src/database/model/project-document';
import useDeleteWorkspaceTemplate from '@src/mutation/workspace/useDeleteWorkspaceTemplate';
import { handleDocumentNavigation } from '@src/utils/documentNavigation';

const StandardForm: React.FC<any> = () => {
  const navigation = useNavigation<NativeStackNavigationProp<RootNavigatorParams>>();
  const optionModalRef = useRef<any>(null);
  const addModalRef = useRef<any>(null);
  const project = useSelector(state => state.project);
  const [filteredValue, setFilteredValue] = useState<string | null>(null);
  const [selectedDoc, setSelectedDoc] = useState<ProjectDocumentModel>();
  const role = project?.projectUserRole;

  const { debouncedValue, setInputValue } = useDebouncedSearch();

  const {
    data: getProjectDocuments,
    isLoading,
    fetchNextPage,
    hasNextPage
  } = useGetTemplate(debouncedValue ?? '', {
    projectId: project.projectId,
    limit: 10,
    category: Gql.CategoryType.StandardForm,
    projectDocumentId: null as any,
    pageIndex: 1,
    sorting: 'template'
  });

  const { mutateAsync: deleteDocument } = useDeleteWorkspaceTemplate();

  const projectDocuments = useMemo(() => {
    if (!getProjectDocuments) return [];
    return getProjectDocuments.pages.map((page: { results: any[] }) => page.results).flat();
  }, [getProjectDocuments]);

  const loadMoreDocuments = useCallback(() => {
    if (hasNextPage && !isLoading) {
      fetchNextPage();
    }
  }, [hasNextPage, isLoading, fetchNextPage]);

  const onSearchInputChange = (newSearchTerm: any) => {
    setFilteredValue(newSearchTerm);
    setInputValue(newSearchTerm);
  };

  const onItemPress = async (item: ProjectDocumentModel) => {
    await handleDocumentNavigation({
      item,
      role,
      navigation,
      modules: 'Templates',
      onRefetch: () => {}
    });
  };

  const onOptionPress = (item: ProjectDocumentModel) => {
    optionModalRef?.current?.pushModal();
    setSelectedDoc(item);
  };

  const deleteDocumentHandler = async (id: string) => {
    try {
      await deleteDocument(id).then(() => {
        pushSuccess('Document deleted successfully');
        optionModalRef?.current?.closeModal();
      });
    } catch (error) {}
  };

  const refetch = () => {};

  return (
    <Box bg="#fff" height="full">
      <Divider />

      {/* Add button */}
      <TouchableOpacity
        style={[styles.addButton, { zIndex: 1000 }]}
        onPress={() => {
          addModalRef?.current?.pushModal();
        }}
      >
        <Circle size="65px" bg="#0695D7">
          <Icon name="plus" />
        </Circle>
      </TouchableOpacity>

      <SearchInput
        filteredValue={filteredValue}
        setFilteredValue={onSearchInputChange}
        placeholder="Search for forms"
      />

      {/* All Data */}
      {isLoading ? (
        <SkeletonComponent />
      ) : (
        <DocumentList
          documents={projectDocuments}
          isLoading={isLoading}
          loadMoreDocuments={loadMoreDocuments}
          refetch={() => {}}
          onItemPress={onItemPress}
          onOptionPress={onOptionPress}
          onAction={false}
          setOnAction={() => {}}
          modules={Gql.CategoryType.StandardForm}
        />
      )}

      <OptionModal
        ref={optionModalRef}
        onDelete={value => deleteDocumentHandler(value)}
        selectedDocumentId={selectedDoc?.id ?? ''}
        data={selectedDoc}
        refetch={() => {}}
      />
      <TemplateAddModal ref={addModalRef} category={Gql.CategoryType.StandardForm} refetch={() => {}} />
    </Box>
  );
};

const SkeletonComponent: React.FC<any> = () => {
  const SkeletonItem = () => {
    return (
      <VStack mb={5}>
        <Skeleton style={{ height: 70, padding: 14, borderRadius: 12 }} />
        <VStack space={1}></VStack>
      </VStack>
    );
  };
  return (
    <Stack p={5}>
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
    </Stack>
  );
};

const styles = StyleSheet.create({
  box: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden'
  },
  addButton: {
    position: 'absolute',
    bottom: 10,
    alignSelf: 'flex-end',
    right: '7%',
    shadowColor: 'rgba(0, 0, 0, 0.5)',
    shadowOpacity: 1,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 6 },
    backgroundColor: '#FFFFFF',
    borderRadius: 9999
  }
});

export default StandardForm;
