import { Modal } from '@commons';
import { Gql } from '@src/api';
import { COLORS } from '@src/constants';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import { useDispatch, useSelector } from '@src/store';
import _ from 'lodash';
import { Box, Button, Divider, Flex, Input, Text, View } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps, StyleSheet } from 'react-native';

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const GroupCodeModal = forwardRef<ModalRef>((props, ref) => {
  const modalRef = useRef<any>(null);
  const [filteredValue, setFilteredValue] = useState<number>();
  const [selectedId, setSelectedId] = useState<number[]>([]);

  const { filter } = useSelector(state => state.documentWorkspace);
  const dispatch = useDispatch();

  const handleButtonPress = () => {
    if (filteredValue) {
      const { groupCode, parentCode } = getCodeFromValue(filteredValue.toString());

      dispatch(
        documentWorkspaceActions.setFilter({ ...filter, groupCode: groupCode?.toString?.(), parentCode: parentCode })
      );
      modalRef.current.closeModal();
    }
  };

  const getCodeFromValue = (value: string) => {
    // get the group code from the value, split by "-"
    // the first part is the group code (e.g. TXT-1234, group code is TXT)
    // the second part is the form code (e.g. TXT-1234, form code is 1234)

    // check if the value is all number (e.g. 1234) then it is group code
    // else it is group code (e.g. TXT)

    const numericRegex = /^\d+$/;

    // Check if the value is numeric using the regular expression.
    if (numericRegex.test(value)) {
      return {
        groupCode: value,
        parentCode: null
      };
    }

    const code = value?.toString();
    const codeParts = code?.split('-');
    const parentCode = codeParts?.[0];
    const groupCode = codeParts?.[1];

    return {
      groupCode,
      parentCode
    };
  };

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  return (
    <Modal ref={modalRef} type="bottom">
      <View h={400}>
        <Flex direction="row" style={style.container} paddingBottom={1} width="100%" justifyContent="space-between">
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            bg="transparent"
            onPress={() => {
              // props.onAllFormCode(selectedId);
              modalRef.current.closeModal();
            }}
          >
            <Text color="#0695D7">Back</Text>
          </Button>

          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            bg="transparent"
            onPress={() => {
              setSelectedId([]);
            }}
          >
            <Text color="#0695D7">Clear</Text>
          </Button>
        </Flex>
        <View bg="#F0F0F0" h="40px" justifyContent="center">
          <Text fontSize="16" fontWeight="600px" pl={5}>
            Filter
          </Text>
        </View>

        <Box p={5}>
          <Text mb={4} color={COLORS.neutrals.gray90}>
            Add Group Code: {selectedId.join(', ')}
          </Text>

          <Divider mb={4} />

          <Input
            placeholder="Enter Group Code"
            width="full"
            borderRadius="4"
            fontSize="14"
            maxLength={8}
            value={filteredValue?.toString()}
            onChangeText={(value: any) => setFilteredValue(value)}
          />

          <Button
            _pressed={{
              bg: 'blue.200',
              opacity: 0.8
            }}
            mt={5}
            bg="blue.300"
            onPress={() => handleButtonPress()}
          >
            <Text>Select Group Code</Text>
          </Button>
        </Box>
      </View>
    </Modal>
  );
});

const style = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    height: 48,
    justifyContent: 'space-between',
    padding: 6,
    alignItems: 'center'
  },
  filterButton: {
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20
  },
  flatList: {
    borderWidth: 1,
    borderColor: COLORS.neutrals.gray40,
    padding: 12
  }
});

GroupCodeModal.displayName = 'GroupCodeModal';
export default GroupCodeModal;
