import { Modal } from '@commons';
import { Gql } from '@src/api';
import { COLORS } from '@src/constants';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import { useDispatch, useSelector } from '@src/store';
import _, { filter, parseInt } from 'lodash';
import { Avatar, Box, Button, Divider, FlatList, Flex, HStack, Input, Text, View, VStack } from 'native-base';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps, StyleSheet, TouchableOpacity } from 'react-native';

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const AllFormCodeModal = forwardRef<ModalRef>((props, ref) => {
  const modalRef = useRef<any>(null);
  const [filteredValue, setFilteredValue] = useState<number>();
  const [selectedId, setSelectedId] = useState<number[]>([]);

  const { filter } = useSelector(state => state.documentWorkspace);
  const dispatch = useDispatch();

  const handleButtonPress = () => {
    if (filteredValue) {
      dispatch(documentWorkspaceActions.setFilter({ ...filter, allFormCode: filteredValue }));
      modalRef.current.closeModal();
    }
  };

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const setAllFormCodeValue = () => {
    const allFormCode = filter?.allFormCode;

    if (allFormCode) {
      setFilteredValue(allFormCode);
    }
  };

  return (
    <Modal ref={modalRef} type="bottom" onShow={setAllFormCodeValue}>
      <View h={400}>
        <Flex direction="row" style={style.container} paddingBottom={1} width="100%" justifyContent="space-between">
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            bg="transparent"
            onPress={() => {
              // props.onAllFormCode(selectedId);
              modalRef.current.closeModal();
            }}
          >
            <Text color="#0695D7">Back</Text>
          </Button>

          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            bg="transparent"
            onPress={() => {
              setFilteredValue(undefined);
              dispatch(documentWorkspaceActions.setFilter({ ...filter, allFormCode: undefined }));
            }}
          >
            <Text color="#0695D7">Clear</Text>
          </Button>
        </Flex>
        <View bg="#F0F0F0" h="40px" justifyContent="center">
          <Text fontSize="16" fontWeight="600px" pl={5}>
            Filter
          </Text>
        </View>

        <Box p={5}>
          <Text mb={4} color={COLORS.neutrals.gray90}>
            Add ID: {selectedId.join(', ')}
          </Text>

          <Divider mb={4} />

          <Input
            placeholder="Enter an ID"
            width="full"
            borderRadius="4"
            fontSize="14"
            maxLength={5}
            keyboardType="numeric"
            value={filteredValue?.toString()}
            onChangeText={(value: any) => {
              const numericValue = parseInt(value, 10); // Convert the input string to a number
              setFilteredValue(numericValue);
            }}
          />

          <Button
            _pressed={{
              bg: 'blue.200',
              opacity: 0.8
            }}
            mt={5}
            bg="blue.300"
            onPress={() => handleButtonPress()}
          >
            <Text>Select ID</Text>
          </Button>
        </Box>
      </View>
    </Modal>
  );
});

const style = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    height: 48,
    justifyContent: 'space-between',
    padding: 6,
    alignItems: 'center'
  },
  filterButton: {
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20
  },
  flatList: {
    borderWidth: 1,
    borderColor: COLORS.neutrals.gray40,
    padding: 12
  }
});

AllFormCodeModal.displayName = 'AllFormCodeModal';
export default AllFormCodeModal;
