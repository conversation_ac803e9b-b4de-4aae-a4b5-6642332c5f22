import { Icon, Modal } from '@commons';
import { Gql } from '@src/api';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import { useDispatch, useSelector } from '@src/store';
import { Box, Button, Divider, Flex, HStack, Text, View } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { ModalProps, StyleSheet, TouchableOpacity } from 'react-native';

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const StatusModal = forwardRef<ModalRef>((props, ref) => {
  const modalRef = useRef<any>(null);
  const { filter } = useSelector(state => state.documentWorkspace);
  const dispatch = useDispatch();

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  return (
    <Modal ref={modalRef} type="bottom">
      <HStack alignItems="center" w="100%" justifyContent="center" position="relative">
        <TouchableOpacity
          style={{ position: 'absolute', left: 16 }}
          onPress={() => {
            modalRef.current.closeModal();
          }}
        >
          <Icon name="chevron-left" />
        </TouchableOpacity>
        <Text fontSize="16" fontWeight="500">
          Filter Status
        </Text>
      </HStack>
      <Flex direction="column" p={5} pt={3}>
        <TouchableOpacity
          onPress={() => {
            dispatch(
              documentWorkspaceActions.setFilter({
                ...filter,
                status: filter.status?.includes(Gql.ProjectDocumentStatus.Submitted)
                  ? filter.status?.filter(o => o !== Gql.ProjectDocumentStatus.Submitted)
                  : [...(filter.status || []), Gql.ProjectDocumentStatus.Submitted]
              })
            );
          }}
        >
          <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
            <HStack space={2}>
              <View style={[style.dot, { backgroundColor: '#3E78CF', alignSelf: 'center' }]} />
              <Text fontSize="16px" color="#8F8989">
                Submitted
              </Text>
            </HStack>
            {filter.status?.includes(Gql.ProjectDocumentStatus.Submitted) ? <Icon name="tick" /> : <></>}
          </Box>
        </TouchableOpacity>

        <Divider mt={4} />
        <TouchableOpacity
          onPress={() => {
            dispatch(
              documentWorkspaceActions.setFilter({
                ...filter,
                status: filter.status?.includes(Gql.ProjectDocumentStatus.InReview)
                  ? filter.status?.filter(o => o !== Gql.ProjectDocumentStatus.InReview)
                  : [...(filter.status || []), Gql.ProjectDocumentStatus.InReview]
              })
            );
          }}
        >
          <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
            <HStack space={2}>
              <View style={[style.dot, { backgroundColor: '#F29100', alignSelf: 'center' }]} />
              <Text fontSize="16px" color="#8F8989">
                In Review
              </Text>
            </HStack>
            {filter.status?.includes(Gql.ProjectDocumentStatus.InReview) ? <Icon name="tick" /> : <></>}
          </Box>
        </TouchableOpacity>

        <Divider mt={4} />

        <TouchableOpacity
          onPress={() => {
            dispatch(
              documentWorkspaceActions.setFilter({
                ...filter,
                status: filter.status?.includes(Gql.ProjectDocumentStatus.Amend)
                  ? filter.status?.filter(o => o !== Gql.ProjectDocumentStatus.Amend)
                  : [...(filter.status || []), Gql.ProjectDocumentStatus.Amend]
              })
            );
          }}
        >
          <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
            <HStack space={2}>
              <View style={[style.dot, { backgroundColor: '#FF25A1', alignSelf: 'center' }]} />
              <Text fontSize="16px" color="#8F8989">
                Amend
              </Text>
            </HStack>
            {filter.status?.includes(Gql.ProjectDocumentStatus.Amend) ? <Icon name="tick" /> : <></>}
          </Box>
        </TouchableOpacity>

        <Divider mt={4} />

        <TouchableOpacity
          onPress={() => {
            dispatch(
              documentWorkspaceActions.setFilter({
                ...filter,
                status: filter.status?.includes(Gql.ProjectDocumentStatus.Pending)
                  ? filter.status?.filter(o => o !== Gql.ProjectDocumentStatus.Pending)
                  : [...(filter.status || []), Gql.ProjectDocumentStatus.Pending]
              })
            );
          }}
        >
          <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
            <HStack space={2}>
              <View style={[style.dot, { backgroundColor: '#74caec', alignSelf: 'center' }]} />
              <Text fontSize="16px" color="#8F8989">
                Pending
              </Text>
            </HStack>
            {filter.status?.includes(Gql.ProjectDocumentStatus.Pending) ? <Icon name="tick" /> : <></>}
          </Box>
        </TouchableOpacity>

        <Divider mt={4} />

        <TouchableOpacity
          onPress={() => {
            dispatch(
              documentWorkspaceActions.setFilter({
                ...filter,
                status: filter.status?.includes(Gql.ProjectDocumentStatus.InProgress)
                  ? filter.status?.filter(o => o !== Gql.ProjectDocumentStatus.InProgress)
                  : [...(filter.status || []), Gql.ProjectDocumentStatus.InProgress]
              })
            );
          }}
        >
          <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
            <HStack space={2}>
              <View style={[style.dot, { backgroundColor: '#9ce255', alignSelf: 'center' }]} />
              <Text fontSize="16px" color="#8F8989">
                In Progress
              </Text>
            </HStack>
            {filter.status?.includes(Gql.ProjectDocumentStatus.InProgress) ? <Icon name="tick" /> : <></>}
          </Box>
        </TouchableOpacity>

        <Divider mt={4} />

        <TouchableOpacity
          onPress={() => {
            dispatch(
              documentWorkspaceActions.setFilter({
                ...filter,
                status: filter.status?.includes(Gql.ProjectDocumentStatus.Approved)
                  ? filter.status?.filter(o => o !== Gql.ProjectDocumentStatus.Approved)
                  : [...(filter.status || []), Gql.ProjectDocumentStatus.Approved]
              })
            );
          }}
        >
          <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4}>
            <HStack space={2}>
              <View style={[style.dot, { backgroundColor: '#18A601', alignSelf: 'center' }]} />
              <Text fontSize="16px" color="#8F8989">
                Approved
              </Text>
            </HStack>
            {filter.status?.includes(Gql.ProjectDocumentStatus.Approved) ? <Icon name="tick" /> : <></>}
          </Box>
        </TouchableOpacity>

        <Divider mt={4} />

        <TouchableOpacity
          onPress={() => {
            dispatch(
              documentWorkspaceActions.setFilter({
                ...filter,
                status: filter.status?.includes(Gql.ProjectDocumentStatus.Rejected)
                  ? filter.status?.filter(o => o !== Gql.ProjectDocumentStatus.Rejected)
                  : [...(filter.status || []), Gql.ProjectDocumentStatus.Rejected]
              })
            );
          }}
        >
          <Box flexDirection="row" alignItems="center" justifyContent="space-between" mt={4} mb={4}>
            <HStack space={2}>
              <View style={[style.dot, { backgroundColor: '#F40000', alignSelf: 'center' }]} />
              <Text fontSize="16px" color="#8F8989">
                Rejected
              </Text>
            </HStack>
            {filter.status?.includes(Gql.ProjectDocumentStatus.Rejected) ? <Icon name="tick" /> : <></>}
          </Box>
        </TouchableOpacity>
      </Flex>
      <Flex direction="row" width="100%" justifyContent="space-between" padding={4} bg="#ffffff">
        <Button
          flex={1}
          variant="outline"
          marginRight={2}
          _pressed={{ bg: '#E0E0E0', opacity: 0.8 }}
          bg="#ffffff"
          onPress={() => {
            dispatch(documentWorkspaceActions.setFilter({ ...filter, status: [] }));
            modalRef.current.closeModal();
          }}
        >
          <Text color="#0695D7">Clear all</Text>
        </Button>
        <Button
          flex={1}
          _pressed={{ bg: '#007ACC', opacity: 0.8 }}
          bg="#0695D7"
          onPress={() => {
            modalRef.current.closeModal();
          }}
        >
          <Text color="#ffffff">Apply</Text>
        </Button>
      </Flex>
    </Modal>
  );
});

const style = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    height: 48,
    justifyContent: 'space-between',
    padding: 6,
    alignItems: 'center'
  },
  filterButton: {
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20
  },
  dot: {
    width: 10,
    height: 10,
    borderRadius: 5
  }
});

StatusModal.displayName = 'StatusModal';
export default StatusModal;
