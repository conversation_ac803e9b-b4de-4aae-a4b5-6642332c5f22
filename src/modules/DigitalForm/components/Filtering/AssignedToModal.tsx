import { Icon, Modal } from '@commons';
import { Gql } from '@src/api';
import { COLORS } from '@src/constants';
import _ from 'lodash';
import {
  Avatar,
  Box,
  Button,
  Divider,
  FlatList,
  Flex,
  HStack,
  Input,
  ScrollView,
  Text,
  View,
  VStack
} from 'native-base';
import React, { forwardRef, useCallback, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { ModalProps, StyleSheet, TouchableOpacity } from 'react-native';
import { useDispatch, useSelector } from '@src/store';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import { useGetProjectUser } from '@src/queries/project-user/useGetProjectUser';
import useDebouncedSearch from '@src/hooks/useDebounceSearch';
import { useGetProjectAdded } from '@src/queries/project-user/useGetProjectAdded';

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const AssignedToModal = forwardRef<ModalRef>((props, ref) => {
  const modalRef = useRef<any>(null);
  const [filteredValue, setFilteredValue] = useState<string>();
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [name, setName] = useState<string[]>([]);
  const { filter } = useSelector(state => state.documentWorkspace);
  const { debouncedValue, setInputValue } = useDebouncedSearch();
  const dispatch = useDispatch();

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const {
    data: projectUserDatas,
    fetchNextPage: fetchUsers,
    hasNextPage: usersNextPage,
    isLoading: usersLoading
  } = useGetProjectUser(debouncedValue ?? '');

  const projectUserData = useMemo(() => {
    if (!projectUserDatas) return [];
    return projectUserDatas.pages.map(page => page.items).flat();
  }, [projectUserDatas]);

  const loadMoreUsers = useCallback(() => {
    if (usersNextPage && !usersLoading) {
      fetchUsers();
    }
  }, [usersNextPage, usersNextPage, fetchUsers]);

  const { data: addedUserDatas } = useGetProjectAdded({ assigneesId: filter.assigneeId ?? [] });

  const addedUserData = useMemo(() => {
    if (!addedUserDatas) return [];
    return addedUserDatas.pages.map(page => page.items).flat();
  }, [addedUserDatas]);

  const loadMoreAdded = useCallback(() => {
    if (usersNextPage && !usersLoading) {
      fetchUsers();
    }
  }, [usersNextPage, usersNextPage, fetchUsers]);

  const { data } = Gql.useRequestForSignaturesQuery({});

  const invitedUser = data?.requestForSignatures?.nodes?.map((member: any) => {
    return {
      id: member.id,
      name: member.signBy.name,
      email: member.signBy.email,
      avatar: member.signBy.avatar
    };
  });

  const removeSelectedUser = (id: string) => {
    setSelectedUsers(pre => pre.filter(preId => preId !== id));
    setName(pre => pre.filter(preName => preName !== id));
  };

  return (
    <Modal ref={modalRef} type="bottom" avoidKeyboard={false}>
      <View style={{ height: '90%' }}>
        <Flex direction="row" style={style.container} paddingBottom={1} width="100%" justifyContent="space-between">
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            bg="transparent"
            onPress={() => {
              dispatch(documentWorkspaceActions.setFilter({ ...filter, assigneeId: [], assigneeName: [] }));
              modalRef.current.closeModal();
            }}
          >
            <Text color="#0695D7">Clear Filter</Text>
          </Button>

          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            bg="transparent"
            onPress={() => {
              modalRef.current.closeModal();
            }}
          >
            <Text color="#0695D7">Apply Filter</Text>
          </Button>
        </Flex>
        <View bg="#F0F0F0" h="40px" justifyContent="center">
          <Text fontSize="16" fontWeight="600px" pl={5}>
            Filter
          </Text>
        </View>

        <Box p={5}>
          <Text mb={4} color={COLORS.neutrals.gray90}>
            Add assignee
          </Text>

          <Divider mb={4} />

          <Input
            placeholder="Enter a member name or email"
            width="full"
            borderRadius="4"
            fontSize="14"
            // value={filteredValue}
            onChangeText={text => setInputValue(text)}
          />

          {!!debouncedValue ? (
            <>
              <FlatList
                keyboardShouldPersistTaps="handled"
                data={projectUserData}
                onEndReached={loadMoreUsers}
                keyExtractor={item => item.id}
                renderItem={({ item }) => {
                  return (
                    <Box style={style.flatList}>
                      <TouchableOpacity
                        onPress={() => {
                          dispatch(
                            documentWorkspaceActions.setFilter({
                              ...filter,
                              assigneeId: filter.assigneeId?.includes(item.userId)
                                ? filter.assigneeId?.filter(o => o !== item.userId)
                                : [...(filter.assigneeId || []), item.userId],
                              assigneeName: filter.assigneeName?.includes(item.name)
                                ? filter.assigneeName?.filter(o => o !== item.name)
                                : [...(filter.assigneeName || []), item.name]
                            })
                          );
                        }}
                      >
                        <HStack width="85%">
                          <Text style={{ fontWeight: '600', fontSize: 14 }} flex={1}>
                            {item.name}
                          </Text>
                          <Text style={{ fontWeight: '400', fontSize: 14, color: '#969696' }} flex={2}>
                            {item.email}
                          </Text>
                        </HStack>
                      </TouchableOpacity>
                    </Box>
                  );
                }}
              />
            </>
          ) : null}

          {addedUserData ? (
            <>
              <Box mt={4}>
                <Divider mb={4} />
                <FlatList
                  keyboardShouldPersistTaps="handled"
                  data={addedUserData}
                  keyExtractor={item => item.id}
                  contentContainerStyle={{ paddingBottom: 300 }}
                  scrollEnabled={true}
                  renderItem={({ item }) => (
                    <Box>
                      <HStack alignItems="center" justifyContent={'space-between'} space={3} mt={4}>
                        <Avatar
                          size="32px"
                          source={{
                            uri: item.avatar
                          }}
                        />

                        <VStack width="75%">
                          <Text style={{ fontWeight: '600', fontSize: 14 }}>{item.name}</Text>
                          <Text style={{ fontWeight: '400', color: COLORS.neutrals.gray90 }}>{item.email}</Text>
                        </VStack>

                        <VStack width="10%">
                          <TouchableOpacity
                            onPress={() =>
                              dispatch(
                                documentWorkspaceActions.setFilter({
                                  ...filter,
                                  assigneeId: filter.assigneeId?.filter(id => id !== item.id) || undefined,
                                  assigneeName: filter.assigneeName?.filter(name => name !== item.name) || undefined
                                })
                              )
                            }
                          >
                            <Icon name="cancel" color={COLORS.neutrals.gray90} />
                          </TouchableOpacity>
                        </VStack>
                      </HStack>
                    </Box>
                  )}
                />
              </Box>
            </>
          ) : (
            <>
              {/* Recently Join */}
              <Box>
                <Text mb={4} mt={7} color={COLORS.neutrals.gray90}>
                  Recently joined
                </Text>

                <Divider mb={4} />
                <FlatList
                  keyboardShouldPersistTaps="handled"
                  data={invitedUser}
                  keyExtractor={item => item.id}
                  renderItem={({ item }) => (
                    <Box>
                      <HStack alignItems="center" space={3}>
                        <Avatar
                          size="32px"
                          source={{
                            uri: item.avatar
                          }}
                        />

                        <VStack width="85%">
                          <Text style={{ fontWeight: '600', fontSize: 14 }}>{item.name}</Text>
                          <Text style={{ fontWeight: '400', color: COLORS.neutrals.gray90 }}>{item.email}</Text>
                        </VStack>
                      </HStack>
                    </Box>
                  )}
                />
              </Box>
            </>
          )}
        </Box>
      </View>
    </Modal>
  );
});

const style = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    height: 48,
    justifyContent: 'space-between',
    padding: 6,
    alignItems: 'center'
  },
  filterButton: {
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20
  },
  flatList: {
    borderWidth: 1,
    borderColor: COLORS.neutrals.gray40,
    padding: 12
  }
});

AssignedToModal.displayName = 'AssignedToModal';
export default AssignedToModal;
