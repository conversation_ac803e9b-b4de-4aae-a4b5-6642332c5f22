import { Content, Modal } from '@commons';
import { Gql } from '@src/api';
import { COLORS } from '@src/constants';
import _ from 'lodash';
import { Avatar, Box, Button, Divider, FlatList, Flex, HStack, Icon, Input, Text, View, VStack } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps, StyleSheet, TouchableOpacity } from 'react-native';

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const CcModal = forwardRef<ModalRef>((props, ref) => {
  const modalRef = useRef<any>(null);
  const [filteredValue, setFilteredValue] = useState<string>();
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [name, setName] = useState<string[]>([]);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const { data: projectUserData } = Gql.useProjectUsersQuery({
    variables: {
      filter: {
        role: { neq: Gql.ProjectUserRoleType.CanView },
        user: {
          name: {
            like: `%${filteredValue}%`
          }
        }
      }
    }
  });
  const { data: addedUserData } = Gql.useProjectUsersQuery({
    variables: {
      filter: {
        user: {
          id: selectedUsers.length > 0 ? { in: selectedUsers } : { is: null }
        }
      }
    }
  });

  const inviteProjectUser = projectUserData?.projectUsers?.nodes.map((u: any) => {
    return {
      name: u.user.name,
      email: u.user.email,
      id: u.user.id,
      value: u.user.id
    };
  });

  const addedUser = addedUserData?.projectUsers?.nodes?.map((u: any) => {
    return {
      name: u.user.name,
      email: u.user.email,
      id: u.user.id,
      avatar: u.user.avatar
    };
  });

  const { data } = Gql.useRequestForSignaturesQuery({});

  const invitedUser = data?.requestForSignatures?.nodes?.map((member: any) => {
    return {
      id: member.id,
      name: member.signBy.name,
      email: member.signBy.email,
      avatar: member.signBy.avatar
    };
  });

  return (
    <Modal ref={modalRef} type="bottom">
      <View h={400}>
        <Flex direction="row" style={style.container} paddingBottom={1} width="100%" justifyContent="space-between">
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            bg="transparent"
            onPress={() => {
              modalRef.current.closeModal();
            }}
          >
            <HStack space={1} alignItems="center">
              <Icon name="chevron-left" fill={COLORS.primary[1]} />
              <Text color="#0695D7">Back</Text>
            </HStack>
          </Button>

          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            bg="transparent"
            onPress={() => {
              setSelectedUsers([]);
              setName([]);
            }}
          >
            <Text color="#0695D7">Clear</Text>
          </Button>
        </Flex>
        <View bg="#F0F0F0" h="40px" justifyContent="center">
          <Text fontSize="16" fontWeight="600px" pl={5}>
            Filter
          </Text>
        </View>

        <Box p={5}>
          <Text mb={4} color={COLORS.neutrals.gray90}>
            Cc
          </Text>

          <Divider mb={4} />

          <Input
            placeholder="Enter a member name or email"
            width="full"
            borderRadius="4"
            fontSize="14"
            onChangeText={_.debounce(value => {
              setFilteredValue(value);
            }, 300)}
          />

          {!!filteredValue ? (
            <>
              <FlatList
                keyboardShouldPersistTaps="handled"
                data={inviteProjectUser}
                keyExtractor={item => item.id}
                renderItem={({ item }) => (
                  <Box style={style.flatList}>
                    <TouchableOpacity
                      onPress={() => {
                        setSelectedUsers(pre => {
                          const findPreId = pre.find(preId => preId === item.id);
                          if (findPreId) {
                            return pre.filter(preId => preId !== findPreId);
                          } else {
                            return [...pre, item.id];
                          }
                        });
                        setName(pre => {
                          const findPreId = pre.find(preName => preName === item.name);
                          if (findPreId) {
                            return pre.filter(preName => preName !== findPreId);
                          } else {
                            return [...pre, item.name];
                          }
                        });
                      }}
                    >
                      <HStack width="85%">
                        <Text style={{ fontWeight: '600', fontSize: 14 }} flex={1}>
                          {item.name}
                        </Text>
                        <Text style={{ fontWeight: '400', fontSize: 14, color: '#969696' }} flex={2}>
                          {item.email}
                        </Text>
                      </HStack>
                    </TouchableOpacity>
                  </Box>
                )}
              />
            </>
          ) : null}

          {addedUser ? (
            <>
              {/* Current add assignee */}
              <Box mt={4}>
                <Divider mb={4} />
                <FlatList
                  keyboardShouldPersistTaps="handled"
                  data={addedUser}
                  keyExtractor={item => item.id}
                  renderItem={({ item }) => (
                    <Box>
                      <HStack alignItems="center" space={3} mt={4}>
                        <Avatar
                          size="32px"
                          source={{
                            uri: item.avatar
                          }}
                        />

                        <VStack width="85%">
                          <Text style={{ fontWeight: '600', fontSize: 14 }}>{item.name}</Text>
                          <Text style={{ fontWeight: '400', color: COLORS.neutrals.gray90 }}>{item.email}</Text>
                        </VStack>
                      </HStack>
                    </Box>
                  )}
                />
              </Box>
            </>
          ) : (
            <>
              {/* Recently Join */}
              <Box>
                <Text mb={4} mt={7} color={COLORS.neutrals.gray90}>
                  Recently joined
                </Text>

                <Divider mb={4} />
                <FlatList
                  keyboardShouldPersistTaps="handled"
                  data={invitedUser}
                  keyExtractor={item => item.id}
                  renderItem={({ item }) => (
                    <Box>
                      <HStack alignItems="center" space={3}>
                        <Avatar
                          size="32px"
                          source={{
                            uri: item.avatar
                          }}
                        />

                        <VStack width="85%">
                          <Text style={{ fontWeight: '600', fontSize: 14 }}>{item.name}</Text>
                          <Text style={{ fontWeight: '400', color: COLORS.neutrals.gray90 }}>{item.email}</Text>
                        </VStack>
                      </HStack>
                    </Box>
                  )}
                />
              </Box>
            </>
          )}
        </Box>
      </View>
    </Modal>
  );
});

const style = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    height: 48,
    justifyContent: 'space-between',
    padding: 6,
    alignItems: 'center'
  },
  filterButton: {
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20
  },
  flatList: {
    borderWidth: 1,
    borderColor: COLORS.neutrals.gray40,
    padding: 12
  }
});

CcModal.displayName = 'CcModal';
export default CcModal;
