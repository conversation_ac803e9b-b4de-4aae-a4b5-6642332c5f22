import { Modal } from '@commons';
import { Gql } from '@src/api';
import { COLORS } from '@src/constants';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import { useDispatch, useSelector } from '@src/store';
import { <PERSON>ton, FlatList, Flex, Text, View } from 'native-base';
import React, { forwardRef, useCallback, useImperativeHandle, useMemo, useRef } from 'react';
import { ModalProps, StyleSheet } from 'react-native';
import Accordion from '../selector-component/Accordion';
import { useGetWorkspaceGroup } from '@src/queries/workspace/useGetWorkspaceGroups';

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const GroupModal = forwardRef<ModalRef>((props, ref) => {
  const modalRef = useRef<any>(null);
  const dispatch = useDispatch();
  const { filter } = useSelector(state => state.documentWorkspace);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const { data, fetchNextPage, hasNextPage, isLoading } = useGetWorkspaceGroup('' ?? '');

  const datas = useMemo(() => {
    if (!data) return [];
    return data.pages.map(page => page.items).flat();
  }, [data]);

  const handleEndReached = useCallback(() => {
    if (hasNextPage && !isLoading) {
      fetchNextPage();
    }
  }, [hasNextPage, isLoading, fetchNextPage]);

  const handlePress = (item: Gql.WorkspaceDocument) => {
    dispatch(
      documentWorkspaceActions.setFilter({ ...filter, groupId: item.remoteId.toString(), groupName: item.name })
    );
    modalRef.current.closeModal();
  };

  return (
    <Modal ref={modalRef} type="bottom">
      <View h={400}>
        <Flex direction="row" style={style.container} width="100%" justifyContent="space-between" mb={2}>
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            bg="transparent"
            onPress={() => {
              dispatch(documentWorkspaceActions.setFilter({ ...filter, groupId: undefined, groupName: undefined }));
              modalRef.current.closeModal();
            }}
            h={50}
          >
            <Text color="#0695D7">Clear Filter</Text>
          </Button>
        </Flex>
        <View bg="#F0F0F0" h="40px" justifyContent="center">
          <Text fontSize="16" fontWeight="600px" pl={5}>
            Filter Group
          </Text>
        </View>

        <FlatList
          keyboardShouldPersistTaps="handled"
          data={datas}
          keyExtractor={item => item.id}
          onEndReachedThreshold={0.1}
          onEndReached={handleEndReached}
          renderItem={({ item }: any) => (
            <Accordion
              item={item}
              title={item?.name ?? ''}
              isParent={!item?.workspaceGroupId}
              onPressChild={handlePress}
            />
          )}
        />
      </View>
    </Modal>
  );
});

const style = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    height: 48,
    justifyContent: 'space-between',
    padding: 6,
    alignItems: 'center'
  },
  filterButton: {
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20
  },
  flatList: {
    borderWidth: 1,
    borderColor: COLORS.neutrals.gray40,
    padding: 12
  }
});

GroupModal.displayName = 'GroupModal';
export default GroupModal;
