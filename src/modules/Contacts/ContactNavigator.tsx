import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { ContactNavigatorParams } from '@src/types';
import Contacts from './pages/Contacts';
import AddContact from './pages/AddContact';
import ContactDetails from './pages/ContactDetails';
import ReviewImportedContact from './pages/ReviewImportedContact';

const ContactStack = createNativeStackNavigator<ContactNavigatorParams>();

const MemberNavigator: React.FC<any> = () => {
  return (
    <ContactStack.Navigator initialRouteName="Contacts" screenOptions={{ headerShown: false }}>
      <ContactStack.Screen name="Contacts" component={Contacts} />
      <ContactStack.Screen name="AddContact" component={AddContact} />
      <ContactStack.Screen name="ContactDetails" component={ContactDetails} />
      <ContactStack.Screen name="ReviewImportedContact" component={ReviewImportedContact} />
    </ContactStack.Navigator>
  );
};

export default MemberNavigator;
