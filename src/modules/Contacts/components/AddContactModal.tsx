import { Modal, Icon } from '@commons';
import { Button, Text, Box } from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { ModalProps } from 'react-native';
import { useSelector } from '@src/store';

interface Props {
  onAdd: () => void;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const AddContactModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const addModalRef = useRef<any>(null);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="bottom">
      <Button
        _pressed={{
          bg: 'transparent',
          opacity: 0.8
        }}
        my={6}
        bg="transparent"
        onPress={() => {
          modalRef?.current?.pushModal();
          props.onAdd();
        }}
        justifyContent={'center'}
      >
        <Box
          alignItems={'center'}
          justifyContent={'center'}
          width={16}
          height={16}
          alignSelf={'center'}
          borderRadius={32}
          borderWidth={2}
          borderColor={'neutrals.gray6'}
        >
          <Icon name="add-contact" />
        </Box>
        <Text color={'neutrals.gray6'} mt={2}>
          Add Contact
        </Text>
      </Button>
    </Modal>
  );
});

AddContactModal.displayName = 'AddContactModal';
export default AddContactModal;
