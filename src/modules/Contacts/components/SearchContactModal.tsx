import { Icon, Modal } from '@commons';
import { Gql } from '@src/api';
import { COLORS } from '@src/constants';
import _ from 'lodash';
import {
  Box,
  Button,
  Center,
  Divider,
  FlatList,
  Flex,
  HStack,
  Input,
  Pressable,
  ScrollView,
  Text,
  VStack
} from 'native-base';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps, Platform, StyleSheet } from 'react-native';

interface Props {}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const SearchContactModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);

  const [filteredValue, setFilteredValue] = useState<string>();

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const { data } = Gql.useContactsQuery({
    variables: {
      filter: {
        or: [
          { name: { like: `%${filteredValue}%` } },
          { email: { like: `%${filteredValue}%` } },
          { phoneNo: { like: `%${filteredValue}%` } }
        ]
      }
    }
  });

  const onSearch = (values: string) => {
    setFilteredValue(values);
  };

  const contacts = data?.contacts.nodes.map((contact: any) => {
    return {
      id: contact.id,
      name: contact.name,
      companyName: contact.contactCompany.name,
      email: contact.email,
      phoneNo: contact.phoneNo
    };
  });

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="bottom">
      <ScrollView keyboardShouldPersistTaps="handled">
        <Flex direction="row" width="100%" alignItems="center">
          <Button
            _pressed={{
              bg: 'transparent',
              opacity: 0.8
            }}
            bg="transparent"
            onPress={() => {
              modalRef?.current?.closeModal();
            }}
          >
            <Icon name="cancel" />
          </Button>
          <Text ml={70} pl={7}>
            Search for a contact
          </Text>
        </Flex>
        <Divider />

        <Box bg="#F5F7FB" height={Platform.OS === 'ios' ? 500 : 300}>
          <Center>
            <Input
              placeholder="Name, email, phone number"
              mt="2"
              width="335px"
              borderRadius="4"
              fontSize="14"
              InputLeftElement={
                <Box pl="6" py="6">
                  <Icon name="search" />
                </Box>
              }
              InputRightElement={
                <Pressable
                  pr="2"
                  py="6"
                  onPress={() => {
                    setFilteredValue('');
                  }}
                >
                  <Icon name="cancel" />
                </Pressable>
              }
              value={filteredValue}
              onChangeText={value => {
                onSearch(value);
              }}
            />
          </Center>
          <Box height={600}>
            <FlatList
              keyboardShouldPersistTaps="handled"
              contentContainerStyle={{ paddingHorizontal: 20, paddingTop: 20 }}
              data={_.isEmpty(filteredValue) ? [] : contacts}
              keyExtractor={item => item.id}
              renderItem={({ item }) => (
                <Box style={styles.box}>
                  <VStack space={2}>
                    <Text fontWeight={600}>{item.name}</Text>
                    <HStack space={250}>
                      <Text style={styles.allUserText}>{item.companyName}</Text>
                    </HStack>
                    <HStack>
                      <Text style={styles.allUserText}>{item.email}</Text>
                      <Divider orientation="vertical" mx={3} />
                      <Text style={styles.allUserText}>{item.phoneNo}</Text>
                    </HStack>
                  </VStack>
                </Box>
              )}
            />
          </Box>
        </Box>
      </ScrollView>
    </Modal>
  );
});

const styles = StyleSheet.create({
  titleText: {
    fontWeight: '600',
    fontSize: 16,
    marginBottom: 10
  },
  box: {
    backgroundColor: '#FFFFFF',
    marginBottom: 20,
    padding: 14,
    borderRadius: 12,
    marginTop: 12
  },
  allUserText: {
    fontWeight: '400',
    color: COLORS.neutrals.gray90
  }
});

SearchContactModal.displayName = 'Sea';
export default SearchContactModal;
