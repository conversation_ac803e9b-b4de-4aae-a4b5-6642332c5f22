import { Modal } from '@commons';
import { Button, Text } from 'native-base';
import React, { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';
import { Alert, ModalProps } from 'react-native';
import { useSelector } from '@src/store';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootNavigatorParams } from '@src/types';
import { projectPerm } from '@src/lib/authority';
interface Props {
  onChange?: (values: string) => void;
  onDelete?: () => void;
  refetch?: () => void;
  selectedContactId: any;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const ContactDeleteModal = forwardRef<ModalRef, Props>((props, ref) => {
  const modalRef = useRef<any>(null);
  const project = useSelector(state => state.project);
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const { canDelete } = projectPerm(project.projectUserRole);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  return (
    <Modal ref={modalRef} style={{ marginTop: 40 }} type="bottom">
      <Button
        _pressed={{
          bg: 'transparent',
          opacity: 0.8
        }}
        bg="transparent"
        my={6}
        _text={{
          color: '#000'
        }}
        onPress={async () => {
          navigation.push('ContactsNav', {
            screen: 'ContactDetails',
            params: {
              id: props.selectedContactId,
              refetch: () => {}
            }
          });
          modalRef.current?.closeModal();
        }}
      >
        Edit
      </Button>
      {canDelete ? (
        <Button
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          bg="transparent"
          my={6}
          _text={{
            color: '#FF2020'
          }}
          onPress={async () => {
            Alert.alert(
              'Delete Contact',
              'Are you sure you want to delete this contact?',
              [
                {
                  text: 'Cancel',
                  style: 'cancel'
                },
                {
                  text: 'Delete',
                  onPress: () => {
                    props.onDelete && props.onDelete();
                    modalRef.current?.closeModal();
                  }
                }
              ],
              { cancelable: false }
            );
          }}
          disabled={!canDelete}
        >
          Delete
        </Button>
      ) : null}
    </Modal>
  );
});

ContactDeleteModal.displayName = 'ContactDeleteModal';
export default ContactDeleteModal;
