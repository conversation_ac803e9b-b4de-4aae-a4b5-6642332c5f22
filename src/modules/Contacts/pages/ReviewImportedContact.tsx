import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ContactImportApiService } from '@src/api/rest';
import { AppBar, Footer } from '@src/commons';
import { pushError, pushSuccess } from '@src/configs';
import { COLORS } from '@src/constants';
import { ContactNavigatorParams, RootNavigatorParams } from '@src/types';
import _ from 'lodash';
import { Box, Button, Checkbox, Divider, FlatList, HStack, Text, VStack } from 'native-base';
import React, { useState } from 'react';
import { StyleSheet } from 'react-native';

type Props = CompositeScreenProps<
  BottomTabScreenProps<ContactNavigatorParams, 'ReviewImportedContact'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const ReviewImportedContact: React.FC<Props> = ({ route, navigation }) => {
  const data = route.params.convertedCsv;
  const refetch = _.get(route, 'params.refetch', null);
  const [uncheckedContact, setUncheckedContact] = useState<number[]>([]);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const convertedData = data.map((importedContact: any, index: number) => {
    return {
      id: index,
      name: importedContact.name,
      companyName: importedContact.company,
      email: importedContact.email,
      phoneNo: importedContact.phoneNo
    };
  }) as [
    {
      id: string;
      name: string;
      companyName: string;
      email: string;
      phoneNo: string;
    }
  ];

  const onImport = async () => {
    setIsSubmitting(true);
    const confirmImport = data.filter((d: any, index: number) => !uncheckedContact.includes(index));
    try {
      await ContactImportApiService.import({
        body: { contacts: confirmImport }
      });
      refetch();
      pushSuccess('Contacts imported successfully');
      navigation.goBack();
    } catch (e) {
      pushError(e);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <AppBar goBack barStyle={'dark-content'} title="Review imported contact" noRight />
      <FlatList
        keyboardShouldPersistTaps="handled"
        contentContainerStyle={{ paddingHorizontal: 20, paddingTop: 20 }}
        data={convertedData}
        keyExtractor={item => item.id}
        renderItem={({ item, index }) => (
          <HStack alignItems="center" space={2}>
            <Checkbox
              value="false"
              color="#2A64F9"
              onChange={isChecked => {
                if (isChecked) {
                  setUncheckedContact(pre => pre.filter(id => id !== index));
                } else {
                  setUncheckedContact(pre => [...pre, index]);
                }
              }}
              defaultIsChecked
            />
            <Box style={styles.box}>
              <HStack>
                <VStack space={2}>
                  <Text fontWeight={600}>{item.name}</Text>
                  <HStack space={250}>
                    <Text style={styles.allUserText}>{item.companyName}</Text>
                  </HStack>
                  <HStack>
                    <Text style={styles.allUserText}>{item.email}</Text>
                    <Divider orientation="vertical" mx={3} />
                    <Text style={styles.allUserText}>{item.phoneNo}</Text>
                  </HStack>
                </VStack>
              </HStack>
            </Box>
          </HStack>
        )}
      />
      <Footer>
        <Button isLoading={isSubmitting} variant="primary" onPress={() => onImport()}>
          Import
        </Button>
      </Footer>
    </Box>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    height: 48,
    justifyContent: 'space-between',
    padding: 6,
    alignItems: 'center'
  },
  titleText: {
    fontWeight: '600',
    fontSize: 16,
    marginBottom: 10
  },
  box: {
    backgroundColor: '#FFFFFF',
    marginBottom: 20,
    padding: 14,
    borderRadius: 12,
    marginTop: 12,
    width: '90%'
  },
  allUserText: {
    fontWeight: '400',
    color: COLORS.neutrals.gray90
  }
});

export default ReviewImportedContact;
