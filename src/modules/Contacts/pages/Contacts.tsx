import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps, useFocusEffect } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { MoreHeaderBar, Icon } from '@src/commons';
import { generateRNFile, pushError } from '@src/configs';
import { COLORS } from '@src/constants';
import { ContactNavigatorParams, RootNavigatorParams } from '@src/types';
import {
  Box,
  Button,
  Divider,
  FlatList,
  Flex,
  HStack,
  Skeleton,
  Stack,
  Text,
  VStack,
  Circle,
  Center,
  Input,
  Pressable
} from 'native-base';
import React, { useEffect, useRef, useState } from 'react';
import { InteractionManager, RefreshControl, StyleSheet, TouchableOpacity } from 'react-native';
import DocumentPicker from 'react-native-document-picker';
import SearchContactModal from '../components/SearchContactModal';
import ContactDeleteModal from '../components/ContactDeleteModal';
import AddContactModal from '../components/AddContactModal';
import csv from 'csvtojson';
import { Linking } from 'react-native';

type Props = CompositeScreenProps<
  BottomTabScreenProps<ContactNavigatorParams, 'Contacts'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

type ContactType = {
  name: string;
  company: string;
  email: string;
  phoneNo: string;
};

const Contacts: React.FC<Props> = ({ navigation }) => {
  const searchContactModalRef = useRef<any>(null);
  const deleteContactModalRef = useRef<any>(null);
  const addContactModalRef = useRef<any>(null);
  const [convertedCsv, setConvertedCsv] = useState<any>();
  const [displaySearch, setDisplaySearch] = useState<boolean>();
  const [filteredValue, setFilteredValue] = useState<string>('');
  const [selectedContactId, setSelectedContactId] = useState('');

  useFocusEffect(
    React.useCallback(() => {
      InteractionManager.runAfterInteractions(() => {
        getContact();
      });
    }, [])
  );

  const [getContact, { data, refetch, loading, fetchMore }] = Gql.useContactsLazyQuery({
    notifyOnNetworkStatusChange: true,
    fetchPolicy: 'network-only',
    variables: {
      filter: {
        or: [
          { name: { like: `%${filteredValue}%` } },
          { email: { like: `%${filteredValue}%` } },
          { phoneNo: { like: `%${filteredValue}%` } }
        ]
      },
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.ContactSortFields.UpdatedAt
        }
      ],
      paging: {
        limit: 20,
        offset: 0
      }
    }
  });

  const [deleteContact] = Gql.useDeleteOneContactMutation({
    variables: {
      id: selectedContactId
    },
    onCompleted: () => {
      refetch();
    }
  });

  const contacts = data?.contacts.nodes.map((contact: any) => {
    return {
      id: contact.id,
      name: contact.name,
      companyName: contact.contactCompany.name,
      email: contact.email,
      phoneNo: contact.phoneNo
    };
  });

  const onSearch = (values: string) => {
    setFilteredValue(values);
  };

  // load more - pagination
  const onLoaded = () => {
    if (!data?.contacts.pageInfo.hasNextPage) return;
    fetchMore({
      variables: {
        paging: {
          offset: data?.contacts.nodes.length,
          limit: 20
        }
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;
        const result = Object.assign({}, prev, {
          contacts: {
            ...fetchMoreResult.contacts,
            nodes: [...prev.contacts.nodes, ...fetchMoreResult.contacts.nodes]
          }
        });
        return result;
      }
    });
  };

  // document picker
  const chooseDocument = async () => {
    try {
      const res = await DocumentPicker.pick({
        type: [DocumentPicker.types.allFiles]
      });

      const newFile = generateRNFile({ uri: res[0].uri, name: res[0].name as string, type: res[0].type as string });
      const data = await fetch(newFile?.uri ?? '');

      if (newFile) {
        csv()
          .fromString(await data.text())
          .then((data: any) => {
            const updatedData = data?.map((d: ContactType) => {
              const phoneNo = d.phoneNo?.startsWith('+') ? d.phoneNo : '+' + d.phoneNo;
              return { ...d, phoneNo };
            });
            navigation.push('ContactsNav', {
              screen: 'ReviewImportedContact',
              params: { convertedCsv: updatedData, refetch }
            });
          });
      }
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
      } else {
        throw err;
      }
    }
  };

  return (
    <Box flex={1} bg={COLORS.neutrals.white}>
      <MoreHeaderBar
        goBack
        rightComponent={
          <>
            <Button
              _pressed={{
                bg: 'transparent',
                opacity: 0.8
              }}
              bg="transparent"
              onPress={() => setDisplaySearch(!displaySearch)}
              marginRight={2}
              justifyContent="flex-start"
            >
              <Icon name="search" />
            </Button>
          </>
        }
        barStyle={'dark-content'}
        title="Contacts"
      />
      {/* Search Input */}
      {displaySearch && (
        <>
          <Center>
            <Input
              backgroundColor={COLORS.neutrals.white}
              placeholder="Search for contacts"
              mt="2"
              mx="4"
              borderRadius="8"
              fontSize="14"
              InputLeftElement={
                <Box pl="2" py="6">
                  <Icon name="search" />
                </Box>
              }
              InputRightElement={
                <Pressable
                  px="4"
                  py="6"
                  onPress={() => {
                    setFilteredValue('');
                  }}
                >
                  <Icon name="cancel" />
                </Pressable>
              }
              value={filteredValue}
              onChangeText={value => {
                onSearch(value);
              }}
              onFocus={() => setDisplaySearch(true)}
              onBlur={() => setDisplaySearch(false)}
            />
          </Center>
        </>
      )}

      <TouchableOpacity
        style={[styles.addButton, { zIndex: 1000 }]}
        onPress={() => {
          addContactModalRef.current.pushModal();
          // navigation.push('ContactsNav', { screen: 'AddContact', params: { refetch } })
        }}
      >
        <Circle size="65px" bg="#0695D7">
          <Icon name="plus" />
        </Circle>
      </TouchableOpacity>

      <Flex direction="row" justifyContent="flex-end" width="100%">
        {/* <Button
          variant="light"
          height={36}
          lineHeight={50}
          ml={6}
          mb={1}
          onPress={() => {
            chooseDocument();
          }}
        >
          <Text lineHeight={14} color="#2A64F9">
            Import
          </Text>
        </Button> */}

        {/* <Button
          variant="primary"
          width={112}
          height={36}
          mr={2}
          onPress={() => navigation.push('ContactsNav', { screen: 'AddContact', params: { refetch } })}
        >
          Add contact
        </Button> */}
      </Flex>
      {loading ? (
        <SkeletonComponent />
      ) : (
        <FlatList
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={{ paddingHorizontal: 20, paddingTop: 0 }}
          data={contacts}
          onEndReachedThreshold={0.3}
          onEndReached={onLoaded}
          refreshControl={<RefreshControl refreshing={loading} onRefresh={refetch} />}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <>
              <Box style={styles.box}>
                <VStack space={0} width={'95%'}>
                  <Text fontWeight={600} fontSize={14}>
                    {item.name}
                  </Text>
                  <Text style={styles.allUserText}>{item.companyName}</Text>
                  <HStack>
                    <TouchableOpacity
                      onPress={() => {
                        Linking.openURL(`tel:${item.phoneNo}`);
                      }}
                    >
                      <Text style={styles.allUserText}>{item.phoneNo}</Text>
                    </TouchableOpacity>
                    <Divider orientation="vertical" mx={2} />
                    <TouchableOpacity
                      onPress={() => {
                        Linking.openURL(`mailto:${item.email}`);
                      }}
                    >
                      <Text style={styles.allUserText}>{item.email}</Text>
                    </TouchableOpacity>
                  </HStack>
                </VStack>
                <TouchableOpacity
                  onPress={() => {
                    setSelectedContactId(item.id);
                    deleteContactModalRef.current.pushModal();
                  }}
                  style={{ width: '5%' }}
                >
                  <Box alignItems="center">
                    {item.name !== 'Ungroup Documents' && (
                      <Icon name="option-dot" style={{ marginLeft: 4, marginRight: 4 }} />
                    )}
                  </Box>
                </TouchableOpacity>
              </Box>
            </>
            // </TouchableOpacity>
          )}
        />
      )}
      <AddContactModal
        ref={addContactModalRef}
        onAdd={() => {
          navigation.push('ContactsNav', { screen: 'AddContact', params: { refetch } });
        }}
      />
      <ContactDeleteModal
        ref={deleteContactModalRef}
        selectedContactId={selectedContactId}
        onDelete={() => deleteContact()}
      />
      <SearchContactModal ref={searchContactModalRef} />
    </Box>
  );
};

const SkeletonComponent: React.FC<any> = () => {
  const SkeletonItem = () => {
    return (
      <VStack mb={5}>
        <Skeleton style={{ height: 90, padding: 14, borderRadius: 12 }} />
        <VStack space={1}></VStack>
      </VStack>
    );
  };
  return (
    <Stack p={5}>
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
    </Stack>
  );
};

const styles = StyleSheet.create({
  titleText: {
    fontWeight: '600',
    fontSize: 16,
    marginBottom: 10
  },
  box: {
    backgroundColor: '#fff',
    marginBottom: 20,
    padding: 10,
    marginTop: 12,
    alignItems: 'center',
    flexDirection: 'row'
  },
  allUserText: {
    fontWeight: '400',
    color: COLORS.neutrals.gray90
  },
  addButton: {
    position: 'absolute',
    bottom: 80,
    alignSelf: 'flex-end',
    right: 30,
    shadowColor: 'rgba(0, 0, 0, 0.5)',
    shadowOpacity: 1,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 6 },
    backgroundColor: '#FFFFFF',
    borderRadius: 9999
  }
});

export default Contacts;
