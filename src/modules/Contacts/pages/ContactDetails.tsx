import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { AppBar, Content, Footer } from '@src/commons';
import { pushError, pushSuccess } from '@src/configs';
import { COLORS } from '@src/constants';
import { PhoneInput, TextInput } from '@src/inputs';
import apolloClient from '@src/lib/apollo';
import { ContactNavigatorParams, RootNavigatorParams } from '@src/types';
import { Field, Formik, FormikProps } from 'formik';
import _ from 'lodash';
import moment from 'moment';
import { Avatar, Box, Button, Divider, Flex, HStack, Spinner, Text } from 'native-base';
import React, { useEffect, useRef, useState } from 'react';
import { InteractionManager, StyleSheet } from 'react-native';

type Props = CompositeScreenProps<
  BottomTabScreenProps<ContactNavigatorParams, 'ContactDetails'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const ContactDetails: React.FC<Props> = ({ navigation, route }) => {
  const formRef = useRef<FormikProps<FormValues>>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const contactId = route.params.id;
  const refetch = _.get(route, 'params.refetch', null);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getContact();
    });
  }, []);

  const [getContact, { data, loading }] = Gql.useContactLazyQuery({
    variables: { id: contactId }
  });

  const initialValues = {
    name: data?.contact?.name ?? '',
    phoneNo: data?.contact?.phoneNo?.replace('+60', '') ?? '',
    email: data?.contact?.email ?? '',
    companyName: data?.contact?.contactCompany?.name ?? ''
  };

  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true);
    try {
      const { name, email, companyName } = values;
      await apolloClient.mutate<Gql.UpdateContactMutation>({
        mutation: Gql.UpdateContactDocument,
        variables: {
          input: {
            id: _.toString(contactId),
            update: {
              name,
              phoneNo: '+60' + values.phoneNo,
              email,
              companyName
            }
          }
        }
      });
      pushSuccess('Contact details updated successfully');
      navigation.goBack();
      refetch();
    } catch (e) {
      pushError(e);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box flex={1} bg={COLORS.neutrals.white}>
      {loading ? (
        <Spinner size={'lg'} marginY={'auto'} />
      ) : (
        <>
          <AppBar goBack barStyle={'dark-content'} title="Contact Details" noRight />
          <Formik onSubmit={onSubmit} initialValues={initialValues} innerRef={formRef}>
            {({ errors }) => {
              return (
                <Box flex={1} height={600}>
                  <Content pt={5}>
                    {/* <Field autoFocus name="avatar" label="Profile Photo" component={AvatarPicker} /> */}
                    <Field autoFocus name="name" label="Contact name" component={TextInput} />
                    <Field autoFocus name="email" label="Email address" component={TextInput} />
                    <Field
                      autoFocus
                      name="phoneNo"
                      label="Phone number"
                      component={PhoneInput}
                      error={errors?.phoneNo}
                    />
                    <Field autoFocus name="companyName" label="Company name" component={TextInput} />

                    <Box>
                      <Text style={{ color: COLORS.neutrals.gray90, fontWeight: '400', fontSize: 14 }}>
                        Contact owner
                      </Text>
                      <HStack alignItems="center" space={2} mt={2} mb={4}>
                        <Avatar
                          size="32px"
                          source={{
                            uri: data?.contact?.owner?.avatar ?? ''
                          }}
                        />
                        <Text>{data?.contact?.owner?.name ?? ''}</Text>
                      </HStack>
                      <Divider />
                      <HStack mt={5} space={4}>
                        <Text style={{ color: COLORS.neutrals.gray90, fontWeight: '400', fontSize: 14 }}>
                          Last modified
                        </Text>
                        <Text style={{ color: COLORS.neutrals.gray90, fontWeight: '400', fontSize: 14 }}>
                          {moment(data?.contact?.updatedAt).format('DD MMM YYYY, hh:mma')}
                        </Text>
                      </HStack>
                      <HStack mt={5} space={8} mb={20}>
                        <Text style={{ color: COLORS.neutrals.gray90, fontWeight: '400', fontSize: 14 }}>
                          Created on
                        </Text>
                        <Text style={{ color: COLORS.neutrals.gray90, fontWeight: '400', fontSize: 14 }}>
                          {moment(data?.contact?.createdAt).format('DD MMM YYYY, hh:mma')}
                        </Text>
                      </HStack>
                    </Box>
                  </Content>
                  <Footer>
                    <Flex direction="row" justifyContent="space-between">
                      <Button
                        style={{}}
                        width={116}
                        variant="delete"
                        _text={{
                          color: '#969696'
                        }}
                        isLoading={isSubmitting}
                        onPress={() => {
                          navigation.goBack();
                        }}
                      >
                        Cancel
                      </Button>

                      <Button
                        width={203}
                        isLoading={isSubmitting}
                        variant="primary"
                        backgroundColor={'mention.blue'}
                        onPress={() => {
                          if (errors?.phoneNo) return pushError(errors?.phoneNo);
                          formRef.current?.handleSubmit();
                        }}
                      >
                        Save
                      </Button>
                    </Flex>
                  </Footer>
                </Box>
              );
            }}
          </Formik>
        </>
      )}
    </Box>
  );
};

const styles = StyleSheet.create({});

interface FormValues {
  // avatar: string;
  name: string;
  phoneNo: string;
  email: string;
  companyName: string;
}

export default ContactDetails;
