import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { AppBar, Content, Footer } from '@src/commons';
import { pushError, pushSuccess } from '@src/configs';
import { COLORS } from '@src/constants';
import { PhoneInput, TextInput } from '@src/inputs';
import apolloClient from '@src/lib/apollo';
import { ContactNavigatorParams, RootNavigatorParams } from '@src/types';
import { Field, Formik, FormikProps } from 'formik';
import _ from 'lodash';
import { Avatar, Box, Button, HStack, Text, Flex } from 'native-base';
import React, { useRef, useState } from 'react';

type Props = CompositeScreenProps<
  BottomTabScreenProps<ContactNavigatorParams, 'AddContact'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const AddContact: React.FC<Props> = ({ navigation, route }) => {
  const formRef = useRef<FormikProps<FormValues>>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const refetch = _.get(route, 'params.refetch', null);

  // Get contact owner
  const { data: userData, loading } = Gql.useGetUserMeQuery({});
  const contactOwner = userData?.getUserMe?.name;
  const contactOwnerAvatar = userData?.getUserMe?.avatar;

  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true);
    const { name, email, companyName } = values;

    const requiredFields: (keyof FormValues)[] = ['name', 'phoneNo', 'companyName'];

    for (const field of requiredFields) {
      if (!values[field]) {
        formRef.current?.setFieldError(field, `${field.charAt(0).toUpperCase() + field.slice(1)} is required`);
        setIsSubmitting(false);
        return;
      }
    }

    try {
      await apolloClient.mutate<Gql.CreateNewContactMutation>({
        mutation: Gql.CreateNewContactDocument,
        variables: {
          input: {
            name,
            phoneNo: '+60' + values.phoneNo,
            email,
            companyName
          }
        }
      });

      pushSuccess('Contact added successfully');
      navigation.goBack();
      refetch();
    } catch (e) {
      pushError(e);
    } finally {
      setIsSubmitting(false);
    }
  };

  const initialValues = {
    avatar: '',
    name: '',
    phoneNo: '',
    email: '',
    companyName: ''
  };

  return (
    <Box flex={1} bg={COLORS.neutrals.white}>
      <AppBar goBack barStyle={'dark-content'} title="Add contact" noRight />
      <Formik onSubmit={onSubmit} initialValues={initialValues} innerRef={formRef}>
        {({ errors }) => {
          return (
            <Box flex={1} height={600}>
              <Content pt={5}>
                <Field required name="name" label="Contact Name" component={TextInput} error={errors?.name} />
                <Field name="email" label="Email address" component={TextInput} />
                <Field required name="phoneNo" label="Phone number" component={PhoneInput} error={errors?.phoneNo} />
                <Field
                  required
                  name="companyName"
                  label="Company Name"
                  component={TextInput}
                  error={errors?.companyName}
                />

                <Box>
                  <Text style={{ color: COLORS.neutrals.gray90, fontWeight: '400', fontSize: 14 }}>Contact owner</Text>
                  <HStack alignItems="center" space={2} mt={2} mb={4}>
                    <Avatar
                      size="32px"
                      source={{
                        uri: contactOwnerAvatar ?? ''
                      }}
                    />
                    <Text>{contactOwner ?? ''}</Text>
                  </HStack>
                </Box>
              </Content>
              {/* <Footer>
                <Button
                  isLoading={isSubmitting}
                  variant="primary"
                  onPress={() => {
                    formRef.current?.handleSubmit();
                  }}
                >
                  Save
                </Button>
              </Footer> */}
              <Footer>
                <Flex direction="row" justifyContent="space-between">
                  <Button
                    style={{}}
                    width={116}
                    variant="delete"
                    _text={{
                      color: '#969696'
                    }}
                    isLoading={isSubmitting}
                    onPress={() => {
                      navigation.goBack();
                    }}
                  >
                    Cancel
                  </Button>

                  <Button
                    width={203}
                    isLoading={isSubmitting}
                    variant="primary"
                    backgroundColor={'mention.blue'}
                    onPress={() => {
                      if (errors?.phoneNo) return pushError(errors?.phoneNo);
                      formRef.current?.handleSubmit();
                    }}
                  >
                    Save
                  </Button>
                </Flex>
              </Footer>
            </Box>
          );
        }}
      </Formik>
    </Box>
  );
};

interface FormValues {
  name: string;
  phoneNo: string;
  email: string;
  companyName: string;
}

export default AddContact;
