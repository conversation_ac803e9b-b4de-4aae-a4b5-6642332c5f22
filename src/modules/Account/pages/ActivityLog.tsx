import { AppBar } from '@commons';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { COLORS, DIMENS } from '@src/constants';
import { AccountNavigatorParams } from '@src/types';
import _ from 'lodash';
import moment from 'moment';
import { Avatar, Box, FlatList, HStack, Skeleton, Spacer, Stack, Text, VStack } from 'native-base';
import React, { useEffect } from 'react';
import { InteractionManager, RefreshControl, StyleSheet } from 'react-native';

type Props = NativeStackScreenProps<AccountNavigatorParams, 'ActivityLog'>;

const ActivityLog: React.FC<Props> = ({}) => {
  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getActivityLog();
    });
  }, []);

  const [getActivityLog, { data, loading, fetchMore, refetch }] = Gql.useGetActivityLogsLazyQuery({
    variables: {
      paging: {
        limit: 20,
        offset: 0
      },
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.AuditLogSortFields.CreatedAt
        }
      ]
    },
    fetchPolicy: 'cache-and-network'
  });

  // const { data, loading, fetchMore } = Gql.useGetActivityLogsQuery({
  //   variables: {
  //     sorting: [
  //       {
  //         direction: Gql.SortDirection.Desc,
  //         field: Gql.AuditLogSortFields.CreatedAt
  //       }
  //     ],
  //     paging: {
  //       limit: 20,
  //       offset: 0
  //     }
  //   },
  //   fetchPolicy: 'cache-and-network'
  // });

  const activityLog = data?.auditLogs?.nodes ?? [];
  const groupedActivityData = _.groupBy(
    activityLog.map((activity: any) => {
      return {
        id: activity.id,
        avatarUrl: activity.user.avatar,
        fullName: activity.user.name,
        content: activity.content,
        createdAt: moment(activity.createdAt).format('D MMM YYYY, h:mma'),
        action: activity.action,
        module: activity.module
      };
    }),
    data => moment(data.createdAt, 'D MMM YYYY, h:mma').startOf('day').format('DD MMMM YYYY')
  );

  const onLoaded = () => {
    if (!data?.auditLogs.pageInfo.hasNextPage) return;
    fetchMore({
      variables: {
        paging: {
          offset: data?.auditLogs.nodes.length,
          limit: 20
        }
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;
        const result = Object.assign({}, prev, {
          auditLogs: {
            ...fetchMoreResult.auditLogs,
            nodes: [...prev.auditLogs.nodes, ...fetchMoreResult.auditLogs.nodes]
          }
        });
        return result;
      }
    });
  };

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <AppBar goBack barStyle={'dark-content'} title="Activity Log" noRight />
      {loading ? (
        <SkeletonComponent />
      ) : (
        <FlatList
          contentContainerStyle={{ paddingHorizontal: 20, paddingTop: 20 }}
          data={Object.entries(groupedActivityData)}
          onEndReachedThreshold={0.3}
          onEndReached={onLoaded}
          refreshControl={<RefreshControl refreshing={loading} onRefresh={refetch} />}
          renderItem={({ item: [date, items] }) => {
            return (
              <Box>
                <Text style={styles.date}>{date}</Text>
                {items.map(item => {
                  if (item.module === Gql.AuditLogModuleType.Project) {
                    return (
                      <Box py="2" style={styles.box}>
                        <Text style={styles.createdAt}>
                          {moment(item.createdAt, 'D MMM YYYY, h:mma').format('h:mma')}
                        </Text>

                        <HStack space={[2, 3]} justifyContent="space-between">
                          <VStack>
                            <HStack space={2} alignItems="center" mb={4}>
                              <Avatar
                                size="20px"
                                source={{
                                  uri: item.avatarUrl ?? ''
                                }}
                              />
                              <Text>{item.fullName}</Text>
                            </HStack>

                            <Text style={styles.text}>
                              Project {''}
                              {item.action === Gql.AuditLogActionType.Create
                                ? 'Created'
                                : item.action === Gql.AuditLogActionType.Update
                                  ? 'Updated'
                                  : item.action === Gql.AuditLogActionType.Delete
                                    ? 'Deleted'
                                    : ''}
                            </Text>
                          </VStack>
                          <Spacer />
                        </HStack>
                      </Box>
                    );
                  } else if (item.module === Gql.AuditLogModuleType.Task) {
                    const title = item.content?.slice(0, item.content.indexOf('\n'));
                    const description = item.content?.slice(item.content.indexOf('\n') + 1);
                    return (
                      <Box py="2" style={styles.box}>
                        <HStack space={[2, 3]} justifyContent="space-between">
                          <VStack>
                            <Text style={styles.createdAt}>
                              {moment(item.createdAt, 'D MMM YYYY, h:mma').format('h:mma')}
                            </Text>

                            <Text style={styles.text}>
                              Task {''}
                              {item.action === Gql.AuditLogActionType.Create ? 'Created' : ''}
                              {item.action === Gql.AuditLogActionType.Update ? 'Updated' : ''}
                              {item.action === Gql.AuditLogActionType.Delete ? 'Delete' : ''}
                              {item.action === Gql.AuditLogActionType.Unassigned ? 'Unassigned' : ''}
                              {item.action === Gql.AuditLogActionType.Assigned ? 'Assigned' : ''}
                            </Text>

                            <HStack space={2} mb={2} alignItems="center">
                              <Avatar
                                size="20px"
                                source={{
                                  uri: item.avatarUrl
                                }}
                              />
                              <Text>{item.fullName}</Text>
                            </HStack>

                            {item.action === Gql.AuditLogActionType.Create ? (
                              <Box bg="#F0F4FF" width={311} style={{ borderRadius: 8, padding: 12 }}>
                                <VStack>
                                  <Text color="#2A64F9">{title}</Text>
                                  <Text>{description}</Text>
                                </VStack>
                              </Box>
                            ) : (
                              ''
                            )}

                            {item.action === Gql.AuditLogActionType.Update ? (
                              <>
                                {_.isEmpty(item.content) ? (
                                  ''
                                ) : (
                                  <Box bg="#F0F4FF" width={311} style={{ borderRadius: 8, padding: 12 }}>
                                    <VStack>
                                      <Text>{item.content}</Text>
                                    </VStack>
                                  </Box>
                                )}
                              </>
                            ) : (
                              ''
                            )}
                          </VStack>

                          <Spacer />
                        </HStack>
                      </Box>
                    );
                  } else if (item.module === Gql.AuditLogModuleType.TaskComment) {
                    const description = item.content?.slice(item.content.indexOf('\n') + 1);
                    return (
                      <Box py="2" style={styles.box}>
                        <HStack space={[2, 3]} justifyContent="space-between">
                          <VStack>
                            <Text style={styles.createdAt}>
                              {moment(item.createdAt, 'D MMM YYYY, h:mma').format('h:mma')}
                            </Text>

                            <Text style={styles.text}>Left a comment</Text>
                            <HStack space={2} alignItems="center" mb={2}>
                              <Avatar
                                size="20px"
                                source={{
                                  uri: item.avatarUrl
                                }}
                              />
                              <Text>{item.fullName}</Text>
                            </HStack>

                            <Box bg="#F0F4FF" width={311} style={{ borderRadius: 8, padding: 12 }}>
                              {description}
                            </Box>
                          </VStack>
                          <Spacer />
                        </HStack>
                      </Box>
                    );
                  } else {
                    return null;
                  }
                })}
              </Box>
            );
          }}
          keyExtractor={([key]) => key}
        />
      )}
    </Box>
  );
};

const SkeletonComponent: React.FC<any> = () => {
  const SkeletonItem = () => {
    return (
      <VStack mb={5}>
        <Skeleton style={{ width: DIMENS.screenWidth / 1.5, height: 22 }} mb={2} />
        <Skeleton style={{ height: 90, padding: 14, borderRadius: 12 }} />
        <VStack space={1}></VStack>
      </VStack>
    );
  };
  return (
    <Stack p={5}>
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
    </Stack>
  );
};

const styles = StyleSheet.create({
  box: {
    padding: 14,
    backgroundColor: '#FFFFFF',
    marginBottom: 10,
    borderRadius: 12
  },
  text: {
    fontWeight: '600',
    fontSize: 14
  },
  date: {
    fontWeight: '400',
    fontSize: 14,
    color: 'neutrals.gray90'
  },
  createdAt: {
    fontWeight: '400',
    color: 'neutrals.gray70',
    alignSelf: 'flex-start'
  }
});
export default ActivityLog;
