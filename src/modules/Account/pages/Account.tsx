import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { useQueryClient } from '@tanstack/react-query';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import CustomAppBar from '@src/commons/CustomAppBar';
import Icon from '@src/commons/Icon';
import { ProjectAccess } from '@src/constants/subscription';
import { isAllowed } from '@src/lib/helper';
import { AuthActions } from '@src/slice/auth.slice';
import { notificationActions } from '@src/slice/notification.slice';
import { useDispatch, useSelector } from '@src/store';
import { AccountNavigatorParams, RootNavigatorParams } from '@src/types';
import { Avatar, Box, Divider, HStack, ScrollView, Text, VStack } from 'native-base';
import React, { useRef } from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import CompanyAppBarModal from '../components/CompanyAppBarModal';
import database from '@src/database/index.native'; // Import the database instance
import { sync } from '@src/database/sync'; // Import the sync function
import { pushError, pushSuccess } from '@src/configs';
import useSyncWithDownload from '@src/hooks/useSyncWithDownload';

type Props = CompositeScreenProps<
  BottomTabScreenProps<AccountNavigatorParams, 'Account'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const Account: React.FC<Props> = ({ navigation }) => {
  const queryClient = useQueryClient();
  const { syncAndDownload } = useSyncWithDownload();

  const handleResync = async () => {
    try {
      await database.write(async () => {
        await database.unsafeResetDatabase();
      });
      await syncAndDownload({
        syncMutateOptions: { dispatchStatus: true },
        offlineDownloadOptions: { id: '', dispatchStatus: false },
        showSyncModal: true,
        firstTimeSync: true
      });

      queryClient.invalidateQueries();
    } catch (error) {
      pushError('Failed to resync the database.');
    }
  };
  const dispatch = useDispatch();
  const projectAppBarModalRef = useRef<any>(null);
  const companyAppBarModalRef = useRef<any>(null);
  const project = useSelector(state => state.project);
  const appConfig = useSelector(state => state.app);

  const { OFFLINE_MODE } = appConfig;

  const { data } = Gql.useGetProjectQuery({ variables: { id: project.projectId ?? '' } });
  const projectTitle = data?.project?.title;

  const { data: userMeData } = Gql.useGetUserMeQuery({});

  const companySubscriptions = Gql.useCompanySubscriptionsQuery({
    variables: {
      sorting: {
        direction: Gql.SortDirection.Desc,
        field: Gql.CompanySubscriptionSortFields.SubscriptionEndDate
      },
      paging: {
        limit: 1
      }
    }
  });

  const onLogout = () => {
    dispatch(notificationActions.setUnreadCount(0));
    dispatch(AuthActions.logout());
  };

  return (
    <Box flex={1}>
      <CustomAppBar more barStyle={'light-content'} />
      <ScrollView height={800}>
        <Box style={styles.container}>
          <VStack>
            <HStack alignSelf={'center'} style={{ marginBottom: 16 }}>
              {userMeData?.getUserMe?.avatar && (
                <Avatar
                  source={{
                    uri: userMeData?.getUserMe?.avatar
                  }}
                  size={'20px'}
                  mr={1}
                />
              )}
              <Text style={{ color: '#000' }}>
                {userMeData?.getUserMe?.name ?? 'Loading name...'} ({getRole(project.projectUserRole)})
              </Text>
            </HStack>

            <Text style={styles.titleText}>COMPANY</Text>
            <TouchableOpacity
              style={{
                borderWidth: 1,
                borderColor: '#E8E8E8',
                borderRadius: 6,
                height: 47,
                padding: 12,
                marginBottom: 4
              }}
              onPress={() => {
                // projectAppBarModalRef?.current?.pushModal();
                companyAppBarModalRef?.current?.pushModal();
              }}
            >
              <HStack justifyContent="space-between" alignItems={'center'} alignContent={'center'}>
                <HStack>
                  <Text>{userMeData?.getUserMe?.company?.name ?? 'Loading company...'}</Text>
                </HStack>

                <Icon name="chevron-down-grey" />
              </HStack>
            </TouchableOpacity>
          </VStack>
        </Box>
        {/* <ProjectAppBarModal ref={projectAppBarModalRef} /> */}
        <CompanyAppBarModal ref={companyAppBarModalRef} user={userMeData?.getUserMe} />
        <Box>
          <Text style={styles.dashboard}>OTHERS</Text>
          <Box mt={4}>
            <VStack>
              {/* <TouchableOpacity onPress={() => navigation.push('DashboardNav', { screen: 'Dashboard' })}>
                <Box style={styles.dashboardBox}>
                  <HStack alignItems={'center'} space={5}>
                    <Icon name="dashboard" />
                    <Text style={styles.text}>Dashboard</Text>
                  </HStack>
                </Box>
              </TouchableOpacity>
              <Divider /> */}
              <TouchableOpacity
                onPress={() => navigation.push('OverviewNav', { screen: 'Overview' })}
                disabled={OFFLINE_MODE}
              >
                <Box style={styles.dashboardBox}>
                  <HStack alignItems={'center'} space={3}>
                    <Icon name="overview" />
                    <Text style={styles.text}>Overview</Text>
                  </HStack>
                </Box>
              </TouchableOpacity>
              <Divider />
              <TouchableOpacity
                onPress={() => navigation.push('CorrespondenceNav', { screen: 'Overview' })}
                disabled={OFFLINE_MODE}
              >
                <Box style={styles.dashboardBox}>
                  <HStack alignItems={'center'} space={3}>
                    <Icon name="correspondence" />
                    <Text style={styles.text}>Correspondence</Text>
                  </HStack>
                </Box>
              </TouchableOpacity>
              <Divider />
              {isAllowed(companySubscriptions, ProjectAccess.PHOTO) && (
                <>
                  <TouchableOpacity
                    onPress={() => navigation.push('PhotosNav', { screen: 'Photos', params: {} })}
                    disabled={OFFLINE_MODE}
                  >
                    <Box style={styles.dashboardBox}>
                      <HStack alignItems={'center'} space={3}>
                        <Icon name="photos" />
                        <Text style={styles.text}>Photos</Text>
                      </HStack>
                    </Box>
                  </TouchableOpacity>
                  <Divider />
                </>
              )}
              <TouchableOpacity
                onPress={() => navigation.push('ContactsNav', { screen: 'Contacts' })}
                disabled={OFFLINE_MODE}
              >
                <Box style={styles.dashboardBox}>
                  <HStack alignItems={'center'} space={3}>
                    <Icon name="contacts" />
                    <Text style={styles.text}>Contacts</Text>
                  </HStack>
                </Box>
              </TouchableOpacity>
              <Divider />

              {/* <TouchableOpacity onPress={() => navigation.push('MembersNav', { screen: 'Members' })}>
                <Box style={styles.dashboardBox}>
                  <HStack alignItems={'center'} space={5}>
                    <Icon name="members" />
                    <Text style={styles.text}>Members</Text>
                  </HStack>
                </Box>
              </TouchableOpacity> */}
            </VStack>
          </Box>
        </Box>

        <Box>
          <Text style={styles.dashboard}>ACCOUNT</Text>
          <Box mt={4}>
            <VStack>
              {/* <Divider />
              <Box style={styles.dashboardBox}>
                <HStack alignItems={'center'} space={5}>
                  <Icon name="field-report" />
                  <Text style={styles.text}>Field Report</Text>
                </HStack>
              </Box> */}

              {/* <Divider />
              <TouchableOpacity onPress={() => navigation.push('AccountNav', { screen: 'ActivityLog' })}>
                <Box style={styles.dashboardBox}>
                  <HStack alignItems={'center'} space={5}>
                    <Icon name="activity-log" />
                    <Text style={styles.text}>Activity Log</Text>
                  </HStack>
                </Box>
              </TouchableOpacity> */}

              {/* <Divider />
              <Box style={styles.dashboardBox}>
                <HStack alignItems={'center'} space={5}>
                  <Icon name="live-chat" />
                  <Text style={styles.text}>Live Chat</Text>
                </HStack>
              </Box> */}
              <Divider />
              <TouchableOpacity
                onPress={() => navigation.push('AccountNav', { screen: 'Settings' })}
                disabled={OFFLINE_MODE}
              >
                <Box style={styles.dashboardBox}>
                  <HStack alignItems={'center'} space={3}>
                    <Icon name="settings" />
                    <Text style={styles.text}>Settings</Text>
                  </HStack>
                </Box>
              </TouchableOpacity>
              <Divider />
              <TouchableOpacity onPress={handleResync}>
                <Box style={styles.dashboardBox} mb={10}>
                  <HStack alignItems={'center'} space={3}>
                    <Icon name="sync" width={20} height={20} />
                    <Text style={styles.text}>Resync</Text>
                  </HStack>
                </Box>
              </TouchableOpacity>
              <Divider />
              <TouchableOpacity onPress={onLogout}>
                <Box style={styles.dashboardBox} mb={10}>
                  <HStack alignItems={'center'} space={3}>
                    <Icon name="logout" />
                    <Text style={styles.text}>Log Out</Text>
                  </HStack>
                </Box>
              </TouchableOpacity>
            </VStack>
          </Box>
        </Box>
      </ScrollView>
    </Box>
  );
};
const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: '#FFFFFF',
    // height: 94,
    justifyContent: 'space-between',
    paddingTop: 12,
    paddingLeft: 20,
    paddingRight: 20
  },
  titleText: {
    color: 'neutrals.gray90',
    // lineHeight: 16,
    fontSize: 12,
    letterSpacing: 1
    // marginBottom: 5
  },
  dashboard: {
    color: '#000',
    lineHeight: 16,
    fontSize: 12,
    letterSpacing: 1,
    paddingTop: 12,
    paddingLeft: 20,
    paddingRight: 20
  },
  dashboardBox: {
    backgroundColor: 'white',
    height: 64,
    paddingLeft: 22,
    paddingTop: 20,
    paddingBottom: 20
  },
  text: {
    fontSize: 16,
    fontWeight: '700',
    lineHeight: 24
  }
});
const getRole = (role?: string) => {
  if (role === 'CanView') return 'Viewer';
  else if (role === 'CanEdit') return 'Editor';
  else if (role === 'CloudCoordinator') return 'Cloud Coordinator';
  else if (role === 'ProjectOwner') return 'Project Owner';
  else return '';
};

export default Account;
