import { AppBar, Content, Footer } from '@commons';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { pushError, pushSuccess } from '@src/configs';
import { COLORS } from '@src/constants';
import { TextInput } from '@src/inputs';
import apolloClient from '@src/lib/apollo';
import { AccountNavigatorParams } from '@src/types';
import { Field, Formik, FormikProps } from 'formik';
import _ from 'lodash';
import { Box, Button } from 'native-base';
import React, { useRef, useState } from 'react';
import { StyleSheet } from 'react-native';

type Props = NativeStackScreenProps<AccountNavigatorParams, 'FormCategories'>;

const FormCategories: React.FC<Props> = ({ navigation, route }) => {
  const id = route?.params?.id;
  const refetch = _.get(route, 'params.refetch', null);
  const formRef = useRef<FormikProps<FormValues>>(null);
  const [isSubmitting, setSubmitting] = useState<boolean>(false);

  const { data, loading } = Gql.useFormCategoryQuery({ variables: { id: id ?? '' } });

  if (loading) return null;

  const initialValues = {
    name: data?.formCategory?.name ?? ''
  };

  const onSubmit = async (values: FormValues) => {
    setSubmitting(true);
    try {
      if (_.isEmpty(id)) {
        await apolloClient.mutate<Gql.CreateOneFormCategoryMutation>({
          mutation: Gql.CreateOneFormCategoryDocument,
          variables: {
            input: {
              formCategory: { name: values.name }
            }
          }
        });
        pushSuccess('Form Category added successfully');
        navigation.goBack();
        refetch();
      } else {
        await apolloClient.mutate<Gql.UpdateOneFormCategoryMutation>({
          mutation: Gql.UpdateOneFormCategoryDocument,
          variables: {
            input: {
              id: id,
              update: {
                name: values.name
              }
            }
          }
        });
        pushSuccess('Form Category updated successfully');
        navigation.goBack();
        refetch();
      }
    } catch (e) {
      pushError(e);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <AppBar goBack barStyle={'dark-content'} title={id ? 'Edit category' : 'Add category'} noRight />

      <Formik onSubmit={onSubmit} initialValues={initialValues} innerRef={formRef}>
        {() => {
          return (
            <Box flex={1} bg="#FFFFFF">
              <Content pt={5}>
                <Field autoFocus name="name" label="Category Name" component={TextInput} />
              </Content>
              <Footer>
                <Button
                  variant="primary"
                  isLoading={isSubmitting}
                  onPress={() => {
                    formRef.current?.handleSubmit();
                  }}
                >
                  Continue
                </Button>
              </Footer>
            </Box>
          );
        }}
      </Formik>
    </Box>
  );
};

interface FormValues {
  name: string;
}

const styles = StyleSheet.create({});

export default FormCategories;
