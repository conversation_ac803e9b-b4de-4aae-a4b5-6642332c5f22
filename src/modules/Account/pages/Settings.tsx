import { AppBar, ScrollableTabBar } from '@commons';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { COLORS } from '@src/constants';
import { AccountNavigatorParams } from '@src/types';
import { Box } from 'native-base';
import React, { useRef } from 'react';
import { StyleSheet } from 'react-native';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import SettingAccount from '../components/SettingAccount';
import SettingCompany from '../components/SettingCompany';
import SettingFormCategories from '../components/SettingFormCategories';
import SettingNotification from '../components/SettingNotification';
import SettingProfile from '../components/SettingProfile';
import SettingSubscription from '../components/SettingSubscription';
import SettingStamping from '../components/SettingStamping';
import SettingPreferences from '../components/SettingPreferences';

type Props = NativeStackScreenProps<AccountNavigatorParams, 'Settings'>;

const Settings: React.FC<Props> = ({}) => {
  const tabBarRef = useRef<any>(null);

  return (
    <Box flex={1} bg={COLORS.neutrals.white}>
      <AppBar
        goBack
        barStyle={'dark-content'}
        title="Settings"
        noRight
        background={COLORS.primary[1]}
        titleProps={{ color: 'white' }}
      />

      <ScrollableTabView
        renderTabBar={() => (
          <ScrollableTabBar
            activeTextColor="#0695D7"
            inactiveTextColor={COLORS.neutrals.gray70}
            containerStyle={styles.tabContainer}
            ref={tabBarRef}
            // setDisabled={tabla}
          />
        )}
      >
        <SettingAccount tabLabel="Account" />
        {/* <SettingProfile tabLabel="Profile" /> */}
        <SettingStamping tabLabel="Digital Stamp" />
        <SettingPreferences tabLabel="Preferences" />
        {/* <SettingNotification tabLabel="Notifications"/> */}
        {/* <SettingFormCategories tabLabel="Form Categories" /> */}
        {/* <SettingSubscription tabLabel="Subscription" /> */}
        {/* <SettingCompany tabLabel="Company" /> */}
      </ScrollableTabView>
    </Box>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    backgroundColor: '#FFFFFF'
  }
});
export default Settings;
