import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { AccountNavigatorParams } from '@types';
import ActivityLog from './pages/ActivityLog';
import Settings from './pages/Settings';
import FormCategories from './pages/FormCategories';

const AccountStack = createNativeStackNavigator<AccountNavigatorParams>();

const AccountNavigator: React.FC<any> = () => {
  return (
    <AccountStack.Navigator initialRouteName="Account" screenOptions={{ headerShown: false }}>
      <AccountStack.Screen name="ActivityLog" component={ActivityLog} />
      <AccountStack.Screen name="Settings" component={Settings} />
      <AccountStack.Screen name="FormCategories" component={FormCategories} />
    </AccountStack.Navigator>
  );
};

export default AccountNavigator;
