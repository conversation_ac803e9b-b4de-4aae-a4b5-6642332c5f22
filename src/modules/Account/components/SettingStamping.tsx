import { Gql } from '@src/api';
import { Content } from '@src/commons';
import { FormikProps } from 'formik';
import { Avatar, Box, HStack, Image, ScrollView, Text, VStack } from 'native-base';
import React, { useRef } from 'react';
import _ from 'lodash';
import { Alert, FlatList, Linking, PermissionsAndroid, Platform, TouchableOpacity, View } from 'react-native';
import RNFetchBlob from 'rn-fetch-blob';
import RNFS from 'react-native-fs';
import Share from 'react-native-share';
import { pushError, pushSuccess } from '@src/configs';
import { useSelector } from '@src/store';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootNavigatorParams } from '@src/types';
import { CameraRoll } from '@react-native-camera-roll/camera-roll';

const SettingStamping: React.FC<any> = () => {
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const formRef = useRef<FormikProps<null>>(null);
  const { data } = Gql.useGetUserMeQuery({});
  const project = useSelector(state => state.project);

  const handleDownload = (stamp: any, name: any) => {
    // Request write storage permission (only required for Android)
    // if (Platform.OS === 'android') {
    //   PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE)
    //     .then(granted => {
    //       if (granted === PermissionsAndroid.RESULTS.GRANTED) {
    //         // Permission granted, proceed with the download
    //         downloadImage(stamp, name);
    //       } else {
    //         // Permission denied, show an alert
    //         Alert.alert('Permission Denied', 'Storage permission is required to download the image.');
    //       }
    //     })
    //     .catch(error => );
    // } else {
    //   // For iOS, directly initiate the download
    //   downloadImage(stamp, name);
    // }
    downloadImage(stamp, name);
  };

  const downloadImage = (imageUrl: any, name: any) => {
    const { dirs } = RNFetchBlob.fs;
    const filePath = `${dirs.DownloadDir}/${name}.png`; // Replace 'image.jpg' with your desired file name and extension

    RNFetchBlob.config({
      fileCache: true,
      addAndroidDownloads: {
        useDownloadManager: true,
        notification: true,
        path: filePath,
        mime: 'image/png',
        description: 'Image Download'
      }
    })
      .fetch('GET', imageUrl)
      .then(response => {
        if (Platform.OS === 'ios') {
          CameraRoll.save(response.path(), { type: 'photo', album: 'BINA' }) // Change 'MyApp' to your desired album name
            .then(() => {
              Alert.alert('Download Complete', `Image saved to Photos.`);
            })
            .catch((error: any) => {
              Alert.alert('Download Failed', 'Failed to save the image to Photos.');
            });
        } else {
          Alert.alert('Download Complete', `Image saved to ${filePath}`);
        }
      })
      .catch(error => {
        Alert.alert('Download Failed', 'Failed to download the image.');
      });
  };

  // const downloadImage = async (imageUrl: any, imageName: any) => {
  //   const dir = `${Platform.OS === 'ios' ? RNFetchBlob.fs.dirs.DownloadDir : RNFetchBlob.fs.dirs.DCIMDir}` + imageName;
  //   await RNFetchBlob.config({
  //     fileCache: true,
  //     addAndroidDownloads: {
  //       path: dir,
  //       mediaScannable: true,
  //       useDownloadManager: true,
  //       notification: true,
  //       title: imageName,
  //       mime: 'image/png'
  //     },
  //     path: dir + '/' + imageName + '.png',
  //     appendExt: 'png'
  //   })
  //     .fetch('GET', imageUrl)
  //     .then(res => {
  //       try {
  //         if (Platform.OS === 'ios') {
  //           Share.open({
  //             title: imageName + '.png',
  //             message: imageName,
  //             url: res.path(),
  //             filename: imageName,
  //             showAppsToView: true
  //           })
  //             .then(res => {
  //               if (res.success) pushSuccess('Successfully downloaded.');
  //             })
  //             .catch(err => {
  //               err && //             });
  //         }
  //       } catch (e) {
  //         pushError(e);
  //       }
  //     });
  // };

  return (
    <>
      <Box flex={1}>
        <Content pt={5}>
          <VStack>
            <HStack justifyContent={'space-between'} alignItems={'center'}>
              <Text>{(data?.getUserMe?.company?.name ?? 'Loading').toUpperCase()}</Text>
            </HStack>
            <Text fontSize={16} color={'black'} mt={0} mb={10}>
              Digital Stamps
            </Text>
          </VStack>
          <HStack>
            {project.projectUserRole === 'ProjectOwner' || project.projectUserRole === 'CloudCoordinator' ? (
              <View style={{ justifyContent: 'center' }}>
                <TouchableOpacity
                  onPress={() => {
                    Alert.alert('Download', 'Are you sure you want to download the stamp?', [
                      {
                        text: 'Cancel',
                        style: 'cancel'
                      },
                      {
                        text: 'Download',
                        style: 'default',
                        onPress: () => {
                          handleDownload(data?.getUserMe?.company?.stampUrl as string, 'Company_Stamp');
                        }
                      }
                    ]);
                  }}
                >
                  <Image
                    source={{ uri: data?.getUserMe?.company?.stampUrl as string }} // Replace with the URL of your static image
                    alt="Company Stamp"
                    style={{
                      aspectRatio: 1, // Locks the aspect ratio to 1:1 (square)
                      width: 150
                    }}
                    mb={5}
                    mr={5}
                  />
                </TouchableOpacity>
                <Text fontSize={16} color={'gray.400'} mt={0} mb={10}>
                  Company Stamp
                </Text>
              </View>
            ) : null}

            <View>
              <TouchableOpacity
                onPress={() => {
                  Alert.alert('Download', 'Are you sure you want to download the stamp?', [
                    {
                      text: 'Cancel',
                      style: 'cancel'
                    },
                    {
                      text: 'Download',
                      style: 'default',
                      onPress: () => {
                        handleDownload(data?.getUserMe?.stampUrl as string, 'Personal_Stamp');
                      }
                    }
                  ]);
                }}
              >
                <Image
                  source={{ uri: data?.getUserMe.stampUrl as string }} // Replace with the URL of your static image
                  alt="Personal Stamp"
                  style={{
                    aspectRatio: 1, // Locks the aspect ratio to 1:1 (square)
                    width: 150
                  }}
                  mb={5}
                />
              </TouchableOpacity>
              <Text fontSize={16} color={'gray.400'} mt={0} mb={10}>
                Personal Stamp
              </Text>
            </View>
          </HStack>
          <Text color={'gray.400'}>
            Note: Uploading stamp is only available in web version. Please visit app.bina.cloud
          </Text>
        </Content>
      </Box>
    </>
  );
};

export default SettingStamping;
