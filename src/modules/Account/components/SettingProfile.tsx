import { Gql } from '@src/api';
import { Content } from '@src/commons';
import { pushError, pushSuccess } from '@src/configs';
import apolloClient from '@src/lib/apollo';
import { Field, Formik, FormikProps } from 'formik';
import { Box, Button, ScrollView } from 'native-base';
import { AvatarPicker, PhoneInput, SelectInput, TextInput } from '@inputs';
import React, { useEffect, useRef, useState } from 'react';
import _ from 'lodash';

const SettingProfile: React.FC<any> = props => {
  const formRef = useRef<FormikProps<FormValues>>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true);
    try {
      const { name, phoneNo, companyName, position, reportTo, avatar, timezoneId } = values;

      const tz = parseInt(timezoneId);
      await apolloClient.mutate<Gql.UpdateUserMeMutation>({
        mutation: Gql.UpdateUserMeDocument,
        variables: {
          input: {
            name,
            phoneNo,
            companyOrigin: companyName,
            position,
            reportTo,
            timezoneId: tz,
            ...(_.isString(avatar) ? {} : { avatar })
          }
        }
      });
      pushSuccess('Profile update successfully');
    } catch (e) {
      pushError(e);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get initial values
  const { data, loading } = Gql.useGetUserMeQuery({});

  const [sortedTimezone, setSortedTimezone] = useState<any>([]);

  // Get timezone
  const { data: timezoneData, loading: timezoneLoading } = Gql.useTimezonesQuery({
    variables: {
      sorting: {
        direction: Gql.SortDirection.Asc,
        field: Gql.TimezoneSortFields.Value
      }
    }
  });

  useEffect(() => {
    if (!timezoneLoading) {
      const timezones = timezoneData?.timezones.nodes ?? [];
      const kl = timezones.find(tz => tz.name === 'Kuala Lumpur');

      if (kl) {
        const klIndex = timezones.indexOf(kl);
        timezones.splice(klIndex, 1);
        timezones.unshift(kl);
      }

      setSortedTimezone([...timezones]);
    }
  }, [timezoneData]);

  if (loading) return null;

  const initialValues = {
    avatar: data?.getUserMe?.avatar ?? '',
    name: data?.getUserMe?.name ?? '',
    phoneNo: data?.getUserMe?.phoneNo?.replace('+60', '') ?? '',
    companyName: data?.getUserMe?.companyOrigin ?? '',
    position: data?.getUserMe?.position ?? '',
    reportTo: data?.getUserMe?.reportTo ?? '',
    timezoneId: data?.getUserMe?.timezone?.id ?? ''
  };

  return (
    <>
      <ScrollView keyboardShouldPersistTaps="handled">
        <Box bg="#FFFFFF" height={'full'} mb={40}>
          <Formik onSubmit={onSubmit} initialValues={initialValues} innerRef={formRef}>
            {() => {
              return (
                <Box flex={1} height={600}>
                  <Content pt={5}>
                    <Field autoFocus name="avatar" label="Profile Photo" component={AvatarPicker} />
                    <Field autoFocus name="name" label="Full name" component={TextInput} />
                    <Field autoFocus name="phoneNo" label="Phone number" component={PhoneInput} />
                    <Field autoFocus name="companyName" label="Company Name" component={TextInput} />
                    {/* <Field autoFocus name="reportTo" label="Report To" component={TextInput} /> */}
                    <Field autoFocus name="position" label="Position" component={TextInput} />
                    <Field
                      autoFocus
                      name="timezoneId"
                      label="Timezone"
                      options={_.map(sortedTimezone, o => ({ label: o.name, value: o.id }))}
                      timezone={true}
                      component={SelectInput}
                    />

                    <Button
                      variant="primary"
                      isLoading={isSubmitting}
                      mt={4}
                      onPress={() => formRef.current?.handleSubmit()}
                    >
                      Save
                    </Button>
                  </Content>
                </Box>
              );
            }}
          </Formik>
        </Box>
      </ScrollView>
    </>
  );
};

interface FormValues {
  avatar: string;
  name: string;
  phoneNo: string;
  companyName: string;
  position: string;
  reportTo: string;
  timezoneId: string;
}

export default SettingProfile;
