import { Gql } from '@src/api';
import { Box, Divider, Flex, ScrollView, Text } from 'native-base';
import React from 'react';
import _ from 'lodash';
import { COLORS } from '@src/constants';
import { StyleSheet } from 'react-native';
import moment from 'moment';

const SettingSubscription: React.FC<any> = props => {
  const user = Gql.useGetUserMeQuery();

  const isSubscriptionActive = (companySubscriptions: any, returnDate: boolean = false) => {
    const activeSubscription = companySubscriptions?.find((subscription: any) =>
      moment(subscription.subscriptionEndDate).isAfter(moment())
    );
    return returnDate ? activeSubscription : !!activeSubscription;
  };

  const subscription = isSubscriptionActive(user?.data?.getUserMe?.company?.companySubscriptions?.nodes, true);
  const monthlyCost =
    (subscription?.subscriptionPackage?.amount || 0) / (subscription?.subscriptionPackage?.availableDuration || 0);

  return (
    <>
      <ScrollView>
        <Box style={styles.subscriptionBox}>
          <Flex flexDirection="column">
            <Text mb={4} fontWeight={600} fontSize={16}>
              Subscription
            </Text>
            <Box px={2}>
              <Text mb={1} style={styles.subscriptionText}>
                Your subscription is{' '}
                <Text bold style={{ color: COLORS.semantics.success }}>
                  active
                </Text>
              </Text>
              <Divider />
              <Text style={styles.subscriptionText} mt={2}>
                Current plan:{' '}
                <Text bold>
                  {subscription?.subscriptionPackage?.title} ({subscription?.subscriptionPackage?.availableDuration}{' '}
                  months) - RM{monthlyCost}/mo
                </Text>
              </Text>
              <Text style={styles.subscriptionText}>
                Payment method: <Text bold>Bank Transfer</Text>
              </Text>
              <Text style={styles.subscriptionText}>
                Next billing date: <Text bold>{moment(subscription?.subscriptionEndDate).format('DD MMM YYYY')}</Text>
              </Text>

              {/* <Button variant="primary" style={{ width: 311, height: 48, borderRadius: 8, marginTop: 20 }}>
                View payment history
              </Button>
              <Button
                style={{
                  width: 311,
                  height: 48,
                  borderRadius: 8,
                  marginTop: 10,
                  borderColor: '#E8E8E8',
                  backgroundColor: 'white',
                  borderWidth: 1
                }}
              >
                <Text style={{ color: COLORS.semantics.danger }}>Cancel plan</Text>
              </Button> */}
            </Box>
          </Flex>
        </Box>
      </ScrollView>
    </>
  );
};

const styles = StyleSheet.create({
  subscriptionBox: {
    height: 310,
    width: 338,
    padding: 12,
    borderRadius: 16,
    backgroundColor: '#FFFFFF',
    marginBottom: 12
  },
  subscriptionText: {
    fontWeight: '400'
  }
});

export default SettingSubscription;
