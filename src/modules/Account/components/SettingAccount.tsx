import { Content, Icon } from '@commons';
import { pushError, pushSuccess, pushMessaage, navigate } from '@configs';
import { Gql } from '@src/api';
import { UserAuthService } from '@src/api/rest';
import { COLORS, LINKS } from '@src/constants';
import { TextInput } from '@src/inputs';
import { AuthActions } from '@src/slice/auth.slice';
import { useDispatch } from '@src/store';
import { ErrorMessage, Field, Formik, FormikProps } from 'formik';
import { Box, Button, Divider, Flex, HStack, Pressable, ScrollView, Switch, Text, VStack } from 'native-base';
import React, { useRef, useState } from 'react';
import { Linking, StyleSheet, Alert, Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';

const SettingAccount: React.FC<any> = props => {
  const [initialValues] = useState<FormValues>({ currentPassword: '', newPassword: '', confirmPassword: '' });
  const formRef = useRef<FormikProps<FormValues>>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [currentPasswordShow, setCurrentPasswordShow] = useState(false);
  const [newPasswordShow, setNewPasswordShow] = useState(false);
  const [confirmPasswordShow, setConfirmPasswordShow] = useState(false);
  const dispatch = useDispatch();

  const { data: me, refetch } = Gql.useGetUserMeQuery({});

  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true);
    const oldPassword = values.currentPassword;
    const newPassword = values.newPassword;

    try {
      await UserAuthService.changePassword({ body: { oldPassword, newPassword } });
      pushSuccess('Password changed successfully');
    } catch (e: any) {
      pushError(e?.response?.data?.message || e?.message);
    }
    setIsSubmitting(false);
  };

  const [updateUserMe] = Gql.useUpdateUserMeMutation({
    onCompleted: () => {
      refetch();
      pushSuccess('Updated account successfully');
    },
    onError: (err: any) => {
      pushError(err.message);
    }
  });

  const [deleteUser] = Gql.useDeleteOneUserMutation({
    onCompleted: () => {
      pushSuccess('Deleted account successfully');
      dispatch(AuthActions.logout());
    },
    onError: (err: any) => {
      pushError(err.message);
    }
  });

  const onDelete = () => {
    Alert.alert('Delete Account', 'Are you sure you want to delete this account?', [
      {
        text: 'Cancel',
        onPress: () => {},
        style: 'cancel'
      },
      {
        text: 'OK',
        onPress: () => {
          const id = '' + me?.getUserMe?.id;
          deleteUser({ variables: { id } });
        }
      }
    ]);
  };

  const openUrl = async (url: string) => {
    try {
      await Linking.openURL(url);
    } catch (error) {
      pushError(error);
    }
  };

  return (
    <>
      <ScrollView keyboardShouldPersistTaps="handled">
        <VStack>
          <Box py={2}>
            <Flex flexDirection="column">
              {/* <Box style={styles.subscriptionBox}>
              <Flex flexDirection="column">
                <Text mb={4} fontWeight={600} fontSize={16}>
                  Subscription
                </Text>
                <Text mb={1} style={styles.subscriptionText}>
                  Your subscription is{' '}
                  <Text bold style={{ color: COLORS.semantics.success }}>
                    active
                  </Text>
                </Text>
                <Divider />
                <Text style={styles.subscriptionText} mt={2}>
                  Current plan: <Text bold> Bina Yearly Plan (12 months) - RM350/mo</Text>
                </Text>
                <Text style={styles.subscriptionText}>
                  Payment method: <Text bold>VISA •••• 1234</Text>
                </Text>
                <Text style={styles.subscriptionText}>
                  Next billing date: <Text bold>12 May 2022</Text>
                </Text>

                <Button variant="primary" style={{ width: 311, height: 48, borderRadius: 8, marginTop: 10 }}>
                  View payment history
                </Button>
                <Button
                  style={{
                    width: 311,
                    height: 48,
                    borderRadius: 8,
                    marginTop: 10,
                    borderColor: '#E8E8E8',
                    backgroundColor: 'white',
                    borderWidth: 1
                  }}
                >
                  <Text style={{ color: COLORS.semantics.danger }}>Cancel plan</Text>
                </Button>
              </Flex>
            </Box> */}
              <Box p={5}>
                <HStack alignItems="center" justifyContent="space-between">
                  <VStack>
                    <Text fontWeight={600} mb={2}>
                      2-Factor Authentication
                    </Text>
                    <Text color={'#4E616D'} fontSize={14}>
                      Enable email verification for login security.
                    </Text>
                  </VStack>

                  {me?.getUserMe?.enableTwoFA !== undefined && (
                    <Switch
                      value={me?.getUserMe?.enableTwoFA ?? false}
                      onToggle={async val => {
                        await updateUserMe({
                          variables: {
                            input: {
                              enableTwoFA: val
                            }
                          }
                        });
                      }}
                    />
                  )}
                </HStack>
              </Box>

              <Box style={styles.securityBox}>
                <Formik
                  onSubmit={onSubmit}
                  initialValues={initialValues}
                  innerRef={formRef}
                  validate={values => {
                    const errors: Partial<FormValues> = {};
                    if (!values.currentPassword) {
                      errors.currentPassword = 'Required';
                    }
                    if (!values.newPassword) {
                      errors.newPassword = 'Required';
                    }
                    if (!values.confirmPassword) {
                      errors.confirmPassword = 'Required';
                    }
                    if (values.newPassword !== values.confirmPassword) {
                      errors.confirmPassword = 'Password does not match';
                    }
                    return errors;
                  }}
                >
                  {() => {
                    return (
                      <Box flex={1}>
                        <Content>
                          <Text mb={4} fontWeight={600} fontSize={16}>
                            Security
                          </Text>
                          <Field
                            name="currentPassword"
                            label="Enter your current password"
                            component={TextInput}
                            secureTextEntry={currentPasswordShow ? false : true}
                            InputRightElement={
                              <Pressable onPress={() => setCurrentPasswordShow(!currentPasswordShow)}>
                                <Icon
                                  name={currentPasswordShow ? 'password-visible' : 'password-hidden'}
                                  style={styles.passwordIcons}
                                />
                              </Pressable>
                            }
                          />
                          <Field
                            name="newPassword"
                            label="Enter your new password"
                            component={TextInput}
                            secureTextEntry={newPasswordShow ? false : true}
                            InputRightElement={
                              <Pressable onPress={() => setNewPasswordShow(!newPasswordShow)}>
                                <Icon
                                  name={newPasswordShow ? 'password-visible' : 'password-hidden'}
                                  style={styles.passwordIcons}
                                />
                              </Pressable>
                            }
                          />

                          <Field
                            name="confirmPassword"
                            label="Re-enter your new password"
                            component={TextInput}
                            secureTextEntry={confirmPasswordShow ? false : true}
                            InputRightElement={
                              <Pressable onPress={() => setConfirmPasswordShow(!confirmPasswordShow)}>
                                <Icon
                                  name={confirmPasswordShow ? 'password-visible' : 'password-hidden'}
                                  style={styles.passwordIcons}
                                />
                              </Pressable>
                            }
                          />

                          <Button
                            isLoading={isSubmitting}
                            isDisabled={isSubmitting}
                            variant="primary"
                            onPress={() => {
                              formRef.current?.handleSubmit();
                            }}
                          >
                            Confirm password change
                          </Button>
                        </Content>
                      </Box>
                    );
                  }}
                </Formik>
              </Box>

              <Box style={styles.termsBox}>
                <Text mb={4} fontWeight={600} fontSize={16} pl={2}>
                  BINA CLOUDTECH SDN BHD
                </Text>
                <VStack space={4} pl={2}>
                  {Platform.OS === 'ios' && (
                    <Text pl={1}>
                      iOS Version: {DeviceInfo.getVersion()} ({DeviceInfo.getBuildNumber()})
                    </Text>
                  )}
                  {/* Display build version for android */}
                  {Platform.OS === 'android' && (
                    <Text pl={1}>
                      Android Version: {DeviceInfo.getVersion()} ({DeviceInfo.getBuildNumber()})
                    </Text>
                  )}
                  <Text style={styles.binaText} onPress={() => openUrl(LINKS.TermsAndCondition)}>
                    {' '}
                    Terms and conditions
                  </Text>
                  <Text style={styles.binaText} onPress={() => openUrl(LINKS.PrivacyAndPolicy)}>
                    {' '}
                    Privacy Policy
                  </Text>
                  {/* <Text style={styles.binaDelete} onPress={() => pushMessaage('Only Admin Can Delete', 'error', 'Account deletion can be done only by company admin.')}> Delete Account</Text> */}
                  <Text style={styles.binaDelete} onPress={() => onDelete()}>
                    {' '}
                    Delete Account
                  </Text>
                  {/* <Text style={styles.binaText}> Outlet and Branch</Text>
                <Text style={styles.binaText}> Frequently Asked Question</Text> */}
                  {/* <Text
                style={styles.binaText}
                  onPress={() => {
                    onDeleteAccount();
                  }}
                >
                  Delete Account
                </Text> */}
                </VStack>
              </Box>
            </Flex>
          </Box>
        </VStack>
      </ScrollView>
    </>
  );
};

interface FormValues {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

const styles = StyleSheet.create({
  subscriptionBox: {
    height: 310,
    width: 338,
    padding: 12,
    borderRadius: 16,
    backgroundColor: '#FFFFFF',
    marginBottom: 12
  },
  subscriptionText: {
    fontWeight: '400'
  },

  securityBox: {
    // height: 420,
    // width: 335,
    paddingTop: 12,
    borderRadius: 16,
    backgroundColor: '#FFFFFF',
    marginBottom: 12
  },
  passwordIcons: {
    marginRight: '4%'
  },
  termsBox: {
    // height: 250,
    // width: 335,
    padding: 12,
    borderRadius: 16,
    backgroundColor: '#FFFFFF',
    marginBottom: 30
  },
  binaText: {
    fontWeight: '600',
    color: '#2A64F9'
  },
  binaDelete: {
    fontWeight: '600',
    color: '#F92A64'
  }
});

export default SettingAccount;
