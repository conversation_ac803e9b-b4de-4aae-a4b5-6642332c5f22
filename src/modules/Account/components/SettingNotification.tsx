import { Gql } from '@src/api';
import moment from 'moment';
import { Ava<PERSON>, Box, Divider, FlatList, HStack, Text, VStack } from 'native-base';
import React from 'react';
import { Linking, RefreshControl, StyleSheet, TouchableOpacity } from 'react-native';

const SettingNotifications: React.FC<any> = props => {
  const { data, fetchMore, loading, refetch } = Gql.useGetNotificationsQuery({
    variables: {
      paging: {
        limit: 20,
        offset: 0
      },
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.NotificationTransactionSortFields.CreatedAt
        }
      ]
    },
    fetchPolicy: 'cache-and-network'
  });

  const notifications = data?.notificationTransactions?.nodes ?? [];
  const notificationLog = notifications.map((notification: any) => {
    return {
      id: notification.id,
      avatar: notification.actor.avatar,
      name: notification.actor.name,
      createdAt: moment(notification.createdAt).format('D MMM YYYY, h:mma'),
      actionName: notification.actionName,
      actionType: notification.actionType,
      deepLink: notification.mobileDeeplink
    };
  });

  const onLoaded = () => {
    if (!data?.notificationTransactions.pageInfo.hasNextPage) return;
    fetchMore({
      variables: {
        paging: {
          offset: data?.notificationTransactions.nodes.length,
          limit: 20
        }
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;
        const result = Object.assign({}, prev, {
          notificationTransactions: {
            ...fetchMoreResult.notificationTransactions,
            nodes: [...prev.notificationTransactions.nodes, ...fetchMoreResult.notificationTransactions.nodes]
          }
        });
        return result;
      }
    });
  };

  return (
    <>
      <Box>
        <FlatList
          contentContainerStyle={{ paddingHorizontal: 8, paddingTop: 8 }}
          data={notificationLog}
          onEndReachedThreshold={0.3}
          onEndReached={onLoaded}
          keyExtractor={item => item.id}
          refreshControl={<RefreshControl refreshing={loading} onRefresh={refetch} />}
          renderItem={({ item }: any) => (
            <Box style={styles.box}>
              <TouchableOpacity
                onPress={() => {
                  Linking.openURL(item?.deepLink);
                }}
              >
                <HStack space={3}>
                  <Avatar
                    size="32px"
                    source={{
                      uri: item.avatar
                    }}
                  />
                  <VStack width={'85%'}>
                    <Text color="neutrals.gray70">{item.createdAt}</Text>

                    <Box>
                      <Text>
                        <Text bold>
                          {item.name} {''}
                        </Text>

                        <Text>{item.actionType}:</Text>
                        <Text color="#2A64F9" numberOfLines={1} ellipsizeMode="tail">
                          {''} {item.actionName}
                        </Text>
                      </Text>
                    </Box>
                    <Divider mt={2} />
                  </VStack>
                </HStack>
              </TouchableOpacity>
            </Box>
          )}
        />
      </Box>
    </>
  );
};

const styles = StyleSheet.create({
  box: {
    padding: 2,
    backgroundColor: '#FFFFFF',
    marginBottom: 10,
    borderRadius: 12
  }
});

export default SettingNotifications;
