import { Gql } from '@src/api';
import { Content } from '@src/commons';
import { pushError, pushSuccess } from '@src/configs';
import apolloClient from '@src/lib/apollo';
import { Field, Formik, FormikProps } from 'formik';
import { Avatar, Box, Button, HStack, ScrollView, Stack, Text, VStack } from 'native-base';
import React, { useEffect, useRef, useState } from 'react';
import _ from 'lodash';
import { FlatList, TextBase, TextInputBase } from 'react-native';

const SettingCompany: React.FC<any> = () => {
  const formRef = useRef<FormikProps<null>>(null);
  const { data } = Gql.useGetUserMeQuery({});

  const { data: companyData } = Gql.useGetCompanyUsersQuery({
    fetchPolicy: 'no-cache'
  });

  return (
    <>
      <ScrollView keyboardShouldPersistTaps="handled">
        <Box flex={1} height={600}>
          <Content pt={5}>
            <VStack>
              <HStack justifyContent={'space-between'} alignItems={'center'}>
                <Text>{(data?.getUserMe?.company?.name ?? 'Loading').toUpperCase()}</Text>
              </HStack>
              <Text fontSize={10} color={'gray.400'} mt={4} mb={2}>
                Company Members
              </Text>
              <FlatList
                keyboardShouldPersistTaps="handled"
                data={companyData?.getCompanyUsers}
                keyExtractor={(item: any) => (item as any).id}
                renderItem={({ item }: any) => (
                  <HStack space={2} alignItems="center" mb={4}>
                    <Avatar
                      size="20px"
                      source={{
                        uri: item?.avatar ?? ''
                      }}
                    />
                    <Text>{item?.name}</Text>
                  </HStack>
                )}
              ></FlatList>
            </VStack>
          </Content>
        </Box>
      </ScrollView>
    </>
  );
};
export default SettingCompany;
