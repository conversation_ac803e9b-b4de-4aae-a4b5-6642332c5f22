import { Content } from '@src/commons';
import { Box, HStack, Switch, Text, VStack } from 'native-base';
import React, { useState } from 'react';
import _ from 'lodash';
import { useSelector } from '@src/store';
import useUpdateProject from '@src/mutation/project/useToggleDrawingSyncProject';
import NetInfo from '@react-native-community/netinfo';
import useSyncWithDownload from '@src/hooks/useSyncWithDownload';

const SettingPreferences: React.FC<any> = () => {
  const { mutateAsync: updateDrawingSync } = useUpdateProject();
  const isConnected = NetInfo.useNetInfo().isConnected;
  const project = useSelector(state => state.project);
  const { syncAndDownload } = useSyncWithDownload();

  const onToggleChange = async (isEnabled: any) => {
    setToggle(isEnabled);
    await updateDrawingSync({ projectId: parseInt(project.projectId ?? '0'), drawing_sync: isEnabled });
    if (isConnected) {
      syncAndDownload({
        syncMutateOptions: { dispatchStatus: true },
        offlineDownloadOptions: { id: '', dispatchStatus: true },
        showSyncModal: true
      });
    }
  };

  // States for toggle switches
  const [emailNotifications, setEmailNotifications] = useState(false);
  const [pushNotifications, setPushNotifications] = useState(false);
  const [appNotifications, setAppNotifications] = useState(true);
  const [toggle, setToggle] = useState(false);

  return (
    <>
      <Box flex={1} px={2} py={6} backgroundColor={'blueGray.100'}>
        <Content>
          <VStack space={4}>
            <Text fontSize="md" fontWeight="medium">
              PREFERENCES
            </Text>
            <Box bgColor={'white'} p="2" borderRadius="lg">
              <VStack space={3}>
                <Text fontSize="md" fontWeight={'bold'}>
                  Notifications
                </Text>
                <HStack justifyContent="space-between" alignItems="center">
                  <VStack>
                    <Text fontSize={'md'} fontWeight={'bold'}>
                      Email
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                      Receive new updates in your email inbox
                    </Text>
                  </VStack>
                  <Switch isChecked={emailNotifications} onToggle={() => setEmailNotifications(!emailNotifications)} />
                </HStack>
                <HStack justifyContent="space-between" alignItems="center">
                  <VStack>
                    <Text fontSize={'md'} fontWeight={'bold'}>
                      Push
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                      Get notified even when the app is not open
                    </Text>
                  </VStack>
                  <Switch isChecked={pushNotifications} onToggle={() => setPushNotifications(!pushNotifications)} />
                </HStack>
                <HStack justifyContent="space-between" alignItems="center">
                  <VStack>
                    <Text fontSize={'md'} fontWeight={'bold'}>
                      In App
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                      Enable notifications in the app to stay updated
                    </Text>
                  </VStack>
                  <Switch isChecked={appNotifications} onToggle={() => setAppNotifications(!appNotifications)} />
                </HStack>
              </VStack>
            </Box>
            <Box bgColor={'white'} p="2" borderRadius="lg">
              <VStack space={3}>
                <Text fontSize="md" fontWeight={'bold'}>
                  Offline Mode
                </Text>
                <HStack justifyContent="space-between" alignItems="center">
                  <VStack>
                    <Text fontSize={'md'}>Save all drawings offline</Text>
                    <Text fontSize="xs" color="gray.500">
                      May increase device storage usage
                    </Text>
                  </VStack>
                  <Switch isChecked={toggle} onToggle={value => onToggleChange(value)} />
                </HStack>
              </VStack>
            </Box>
          </VStack>
        </Content>
      </Box>
    </>
  );
};

export default SettingPreferences;
