import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Gql } from '@src/api';
import { Footer } from '@src/commons';
import { RootNavigatorParams } from '@src/types';
import moment from 'moment';
import { Avatar, Box, Button, FlatList, HStack, Skeleton, Stack, Text, VStack } from 'native-base';
import React, { useEffect } from 'react';
import { InteractionManager, StyleSheet, TouchableOpacity } from 'react-native';

const SettingFormCategories: React.FC<any> = props => {
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getFormCategories();
    });
  }, []);

  const [getFormCategories, { data, refetch, fetchMore, loading }] = Gql.useFormCategoriesLazyQuery({
    variables: {
      sorting: [
        {
          direction: Gql.SortDirection.Desc,
          field: Gql.FormCategorySortFields.CreatedAt
        }
      ],
      paging: {
        limit: 20,
        offset: 0
      }
    },
    fetchPolicy: 'cache-and-network'
  });

  const formCategoriesData = data?.formCategories?.nodes.map((category: any) => {
    return {
      id: category.id,
      name: category.name,
      updatedAt: moment(category.updatedAt).format('DD MMM YYYY, hh:mma'),
      avatar: category.owner.avatar,
      fullName: category.owner.name
    };
  });

  const onLoaded = () => {
    if (!data?.formCategories.pageInfo.hasNextPage) return;
    fetchMore({
      variables: {
        paging: {
          offset: data?.formCategories.nodes.length,
          limit: 20
        }
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;
        const result = Object.assign({}, prev, {
          formCategories: {
            ...fetchMoreResult.formCategories,
            nodes: [...prev.formCategories.nodes, ...fetchMoreResult.formCategories.nodes]
          }
        });
        return result;
      }
    });
  };

  return (
    <Box mb={70} mt={4}>
      {loading ? (
        <SkeletonComponent />
      ) : (
        <FlatList
          contentContainerStyle={{ paddingHorizontal: 20, paddingTop: 20 }}
          data={formCategoriesData}
          keyExtractor={item => item.id}
          onEndReachedThreshold={0.3}
          onEndReached={onLoaded}
          renderItem={({ item }: any) => (
            <Box style={styles.box}>
              <TouchableOpacity
                onPress={() => {
                  navigation.push('AccountNav', { screen: 'FormCategories', params: { id: item.id ?? '', refetch } });
                }}
              >
                <VStack space={1}>
                  <Text>{item.name}</Text>
                  <Text style={{ color: '#969696', fontWeight: '400', marginBottom: 4 }}>
                    Last updated: {item.updatedAt}
                  </Text>
                </VStack>

                <HStack space={2} alignItems="center">
                  <Avatar
                    size="20px"
                    source={{
                      uri: item.avatar
                    }}
                  />
                  <Text color="neutrals.gray90" style={{ marginLeft: 4 }}>
                    {item.fullName}
                  </Text>
                </HStack>
              </TouchableOpacity>
            </Box>
          )}
        />
      )}

      <Footer>
        <Button
          variant="primary"
          onPress={() => navigation.push('AccountNav', { screen: 'FormCategories', params: { refetch } })}
        >
          Add a category
        </Button>
      </Footer>
    </Box>
  );
};

const SkeletonComponent: React.FC<any> = () => {
  const SkeletonItem = () => {
    return (
      <VStack mb={5}>
        <Skeleton style={{ height: 90, padding: 14, borderRadius: 12 }} />
        <VStack space={1}></VStack>
      </VStack>
    );
  };
  return (
    <Stack p={5}>
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
      <SkeletonItem />
    </Stack>
  );
};

const styles = StyleSheet.create({
  box: {
    backgroundColor: '#FFFFFF',
    marginBottom: 20,
    padding: 14,
    borderRadius: 12
  }
});

export default SettingFormCategories;
