import { <PERSON><PERSON>, <PERSON><PERSON>, Modal } from '@commons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Gql } from '@src/api';
import { pushError } from '@src/configs';
import { projectActions } from '@src/slice/project.slice';
import { useDispatch } from '@src/store';
import { RootNavigatorParams } from '@src/types';
import { Box, Divider, FlatList, Flex, HStack, Spinner, Text } from 'native-base';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { ModalProps, Platform, StyleSheet, TouchableOpacity } from 'react-native';

interface Props {
  user: any;
}

export interface ModalRef extends ModalProps {
  pushModal: (v?: any) => void;
}

const CompanyAppBarModal = forwardRef<ModalRef, Props>((props, ref) => {
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const modalRef = useRef<any>(null);

  useImperativeHandle(ref, () => ({
    pushModal: () => {
      modalRef?.current?.pushModal();
    }
  }));

  const [selectedCompanyId, setSelectedCompanyId] = useState('');
  const dispatch = useDispatch();
  const [currentSelectedCompany, setCurrentSelectedCompany] = useState<string | null>(null);

  const companiesData = props?.user?.companies?.edges.map((comp: any) => {
    return {
      id: comp.node.id,
      title: comp.node.name,
      avatar: comp.node.avatar
    };
  });

  useEffect(() => {
    setSelectedCompanyId(props?.user?.company?.id);
  }, [props]);

  const [switchCompany, { loading }] = Gql.useSwitchCompanyMutation({
    onCompleted: () => {
      dispatch(projectActions.setSettingModal(false));
      setTimeout(() => {
        navigation.push('ProjectsNav', { screen: 'AllProjects' });
      }, 300);
    }
  });

  return (
    <Modal ref={modalRef} {...props}>
      <Flex direction="row" style={style.container} width="100%">
        <Text style={style.text}>Switch Company</Text>
      </Flex>
      <Divider />
      <Box height={Platform.OS === 'ios' ? 500 : 300}>
        <FlatList
          keyboardShouldPersistTaps="handled"
          data={companiesData}
          keyExtractor={item => (item as any).id}
          renderItem={({ item }: any) => (
            <TouchableOpacity
              disabled={loading}
              key={item.id}
              onPress={async () => {
                try {
                  if (loading) return;
                  setCurrentSelectedCompany(item.id);
                  if (selectedCompanyId !== item.id) {
                    await switchCompany({
                      variables: {
                        companyId: parseInt(item.id)
                      }
                    });
                  }
                } catch (e) {
                  pushError(e);
                } finally {
                  modalRef.current.closeModal();
                }
              }}
            >
              <Box px="5" py="5">
                <HStack alignItems={'center'}>
                  <Text pr={2}>{item.title ?? 'Loading company...'}</Text>
                  {selectedCompanyId == item.id ? (
                    <Icon name="tick" />
                  ) : currentSelectedCompany == item.id ? (
                    <Spinner size="sm" />
                  ) : null}
                </HStack>
              </Box>
            </TouchableOpacity>
          )}
        />
      </Box>
    </Modal>
  );
});

const style = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    height: 48,
    alignItems: 'center',
    justifyContent: 'center'
  },
  text: {
    textAlign: 'center'
  }
});

CompanyAppBarModal.displayName = 'CompanyAppBarModal';
export default CompanyAppBarModal;
