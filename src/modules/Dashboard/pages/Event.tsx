import { AppBar, Content, Footer } from '@commons';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { pushError, pushSuccess } from '@src/configs';
import { COLORS } from '@src/constants';
import { TextInput, TimePicker } from '@src/inputs';
import { CalendarPicker } from '@src/inputs/CalendarPicker';
import { DashboardNavigatorParams } from '@src/types';
import { Field, Formik, FormikProps } from 'formik';
import _ from 'lodash';
import apolloClient from '@src/lib/apollo';
import moment, { Moment } from 'moment';
import { Box, Button, Flex, HStack, Switch, Text, VStack } from 'native-base';
import React, { useEffect, useRef, useState } from 'react';
import { InteractionManager } from 'react-native';

type Props = NativeStackScreenProps<DashboardNavigatorParams, 'Event'>;

const Event: React.FC<Props> = ({ navigation, route }) => {
  const formRef = useRef<FormikProps<FormValues>>(null);
  const [isAllDay, setIsAllDay] = useState(false);
  const refetch = _.get(route, 'params.refetch', null);
  const id = route?.params?.id as string;
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getEvent();
    });
    getEvent();
  }, []);

  const [getEvent, { data, loading }] = Gql.useGetEventLazyQuery({
    variables: { id }
  });

  useEffect(() => {
    setIsAllDay(!!data?.event?.isAllDay);
  }, [data?.event?.isAllDay]);

  if (loading) return null;

  const initialValues = {
    title: data?.event?.title ?? '',
    startAt: moment(data?.event?.startAt).format('DD MMMM YYYY') ?? moment(),
    endAt: moment(data?.event?.endAt).format('DD MMMM YYYY') ?? moment(),
    startTime: moment(data?.event?.startTime).format('YYYY-MM-DD hh:mm:ss') ?? moment(),
    endTime: moment(data?.event?.endTime).format('YYYY-MM-DD hh:mm:ss') ?? moment(),
    isAllDay: data?.event?.isAllDay
  };

  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true);
    const { title, startAt, endAt, startTime, endTime } = values;
    try {
      if (id) {
        await apolloClient.mutate<Gql.UpdateEventMutation>({
          mutation: Gql.UpdateEventDocument,
          variables: {
            input: {
              id: id,
              update: {
                title: title,
                startAt: startAt?.toISOString(),
                endAt: endAt?.toISOString?.() ?? startAt?.toISOString(),
                startTime: startTime?.toISOString?.(),
                endTime: endTime?.toISOString?.()
              }
            }
          }
        });
        pushSuccess('Event updated');
        navigation.goBack();
        refetch();
      } else {
        await apolloClient.mutate<Gql.CreateEventMutation>({
          mutation: Gql.CreateEventDocument,
          variables: {
            input: {
              title: title,
              startAt: startAt?.toISOString(),
              endAt: endAt?.toISOString?.() ?? startAt?.toISOString(),
              startTime: startTime?.toISOString?.(),
              endTime: endTime?.toISOString?.(),
              isAllDay: isAllDay
            }
          }
        });
      }
      navigation.goBack();
      pushSuccess('Event added successfully');
      refetch();
    } catch (e) {
      pushError(e);
    } finally {
      setIsSubmitting(false);
    }
  };

  const onDelete = async (id: string) => {
    if (id) {
      try {
        await apolloClient.mutate<Gql.DeleteOneEventMutation>({
          mutation: Gql.DeleteOneEventDocument,
          variables: {
            id: id
          }
        });
        navigation.goBack();
        pushSuccess('Delete event successfully');
        refetch();
      } catch (e) {
        pushError(e);
      }
    }
  };

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <AppBar goBack barStyle={'dark-content'} title={id ? 'Edit event' : 'Add event'} noRight />
      <Formik onSubmit={onSubmit} initialValues={initialValues as any} innerRef={formRef}>
        {() => {
          return (
            <Box flex={1} bg="#FFFFFF">
              <Content pt={5}>
                <Field autoFocus name="title" label="Event title" component={TextInput} />
                <Flex direction="row" justifyContent="space-between" alignItems="center" mt={4} mb={6}>
                  <Field
                    autoFocus
                    name="isAllDay"
                    component={() => (
                      <>
                        <Text>All day</Text>
                        <Switch
                          size="sm"
                          value={isAllDay}
                          onToggle={() => {
                            setIsAllDay(isAllDay => !isAllDay);
                          }}
                        />
                      </>
                    )}
                  />
                </Flex>
                {isAllDay ? (
                  <VStack space={2} mb={3}>
                    <Field autoFocus name="startAt" label="Start Date" component={CalendarPicker} />
                    <Field autoFocus name="endAt" label="End Date" component={CalendarPicker} />
                  </VStack>
                ) : (
                  <Field autoFocus name="startAt" label="Date" component={CalendarPicker} />
                )}

                {isAllDay ? (
                  <></>
                ) : (
                  <HStack space={3} mt={4}>
                    <Box flex={1} width={190}>
                      <Field autoFocus name="startTime" label="Start Time" component={TimePicker} />
                    </Box>

                    <Box flex={1} width={133}>
                      <Field autoFocus name="endTime" label="End Time" component={TimePicker} />
                    </Box>
                  </HStack>
                )}
              </Content>
              <Footer>
                {id ? (
                  <Flex direction="row" justifyContent="space-between">
                    <Button
                      width={116}
                      variant="delete"
                      isLoading={isSubmitting}
                      onPress={() => {
                        onDelete(id);
                      }}
                    >
                      Delete
                    </Button>

                    <Button
                      width={203}
                      variant="primary"
                      isLoading={isSubmitting}
                      onPress={() => {
                        formRef.current?.handleSubmit();
                      }}
                    >
                      Save
                    </Button>
                  </Flex>
                ) : (
                  <Button
                    variant="primary"
                    isLoading={isSubmitting}
                    onPress={() => {
                      formRef.current?.handleSubmit();
                    }}
                  >
                    Save
                  </Button>
                )}
              </Footer>
            </Box>
          );
        }}
      </Formik>
    </Box>
  );
};

interface FormValues {
  title: string;
  startAt?: Moment;
  endAt?: Moment;
  startTime: Moment;
  endTime: Moment;
  isAllDay: boolean;
}

export default Event;
