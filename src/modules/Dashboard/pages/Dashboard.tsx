import { MoreHeaderBar } from '@commons';
import { ScrollableTabBar } from '@src/commons';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { COLORS } from '@src/constants';
import { DashboardNavigatorParams } from '@src/types';
import { Box, ScrollView, Text } from 'native-base';
import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import DashboardCalendar from '../components/DashboardCalendar';
import DashboardWeather from '../components/DashboardWeather';
import { useSelector } from '@src/store';
import DashboardSiteDiary from '../components/DashboardSiteDiary';
import Dashboard<PERSON>ieChart from '../components/DashboardPieChart';
import { useFocusEffect } from '@react-navigation/native';
import { InteractionManager, Platform } from 'react-native';
import { Alert, StyleSheet, TouchableOpacity } from 'react-native';
import DashboardSCurveFinance from '../components/DashboardSCurveFinance';
import DashboardSCurvePhysical from '../components/DashboardSCurvePhysical';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import _ from 'lodash';
import { Gql } from '@src/api';
import { ProjectAccess } from '@src/constants/subscription';
import { isAllowed } from '@src/lib/helper';
import DashboardMemo from '../components/DashboardMemo';
import { useGetProject } from '@src/queries/project/useGetProject';
import { useTaskSummary } from '@src/queries/tasks/useTasksSummary';
import { useGetTaskGroups } from '@src/queries/task-gorup/useGetTaskGroups';
import { useGetWorkspaceGroupSummary } from '@src/queries/workspace/useGetWorkspaceGroupSummary';
import useDebouncedSearch from '@src/hooks/useDebounceSearch';
import { useGetWorkspaceGroup } from '@src/queries/workspace/useGetWorkspaceGroups';

type Props = NativeStackScreenProps<DashboardNavigatorParams, 'Dashboard'>;

const Dashboard: React.FC<Props> = ({ navigation, route }) => {
  const project = useSelector(state => state.project);
  const [workspaceGroupName, setWorkspaceGroupName] = useState<null | string>(null);

  const [currentTab, setCurrentTab] = useState(0);
  const tabBarRef = useRef<any>(null);
  const pageIndex = Number(_.get(route, 'params.pageIndex', '0'));
  const scrollViewRef = useRef<any>(null);
  const redirect = _.get(route, 'params.redirect', null);

  const [selectedTaskGroupId, setSelectedTaskGroupId] = useState<number | undefined>(undefined);
  const [selectedWorkspaceGroupId, setSelectedWorkspaceGroupId] = useState<number | undefined>(undefined);

  const { data } = useGetProject(project.projectId);
  const { data: taskSummary, isLoading: loadingTaskSummary } = useTaskSummary(
    Number(project.projectId),
    selectedTaskGroupId
  );
  const { data: workSpaceGroup, isLoading: loadingWorkspaceSummary } = useGetWorkspaceGroupSummary(
    Number(project.projectId),
    selectedWorkspaceGroupId
  );

  const { debouncedValue: taskNameGroup, setInputValue: setTaskNameGroup } = useDebouncedSearch();
  const { data: taskGroups } = useGetTaskGroups(taskNameGroup, true);

  const { debouncedValue: workspaceValue, setInputValue: setWorkspaceValue } = useDebouncedSearch();
  const { data: workspaceGroups } = useGetWorkspaceGroup(workspaceValue, true);

  const sanitizedTaskGroups = useMemo(() => {
    if (!taskGroups) return [];
    return taskGroups?.pages.map(page => page.items).flat();
  }, [taskGroups]);

  const sanitizedWorkspaceGroups = useMemo(() => {
    if (!workspaceGroups) return [];
    return workspaceGroups?.pages.map(page => page.items).flat();
  }, [workspaceGroups]);

  useFocusEffect(
    React.useCallback(() => {
      InteractionManager.runAfterInteractions(async () => {
        isAllowed(companySubscriptions, ProjectAccess.TASK);
        isAllowed(companySubscriptions, ProjectAccess.WORKSPACE_DOCUMENT);
      });
    }, [])
  );

  const companySubscriptions = Gql.useCompanySubscriptionsQuery({
    variables: {
      sorting: {
        direction: Gql.SortDirection.Desc,
        field: Gql.CompanySubscriptionSortFields.SubscriptionEndDate
      },
      paging: {
        limit: 1
      }
    }
  });

  const redirectHandler = useCallback(() => {
    if (!redirect) return;

    if (redirect === 'site-diary' || redirect === 'calendar') {
      scrollViewRef?.current?.scrollToEnd({ animated: true });
    }
  }, [redirect]);

  useEffect(() => {
    redirectHandler();
  }, [redirectHandler]);

  return (
    <Box flex={1} bg={COLORS.neutrals.white}>
      <MoreHeaderBar goBack barStyle={'dark-content'} title="Dashboard" />

      <ScrollView keyboardShouldPersistTaps="handled" ref={scrollViewRef}>
        {/* PROJECT TITLE, CLICK TO VIEW FULL */}
        <TouchableOpacity
          onPress={() => {
            Alert.alert('Project', project.selectedProjectTitle ?? '', [{ text: 'Dismiss' }]);
          }}
        >
          <Text fontSize={12} px={4} my={4} variant={'bodyReg'} ellipsizeMode="tail" numberOfLines={2}>
            {project?.selectedProjectTitle}
          </Text>
        </TouchableOpacity>

        {/* S-CURVE TABS */}
        {isAllowed(companySubscriptions, ProjectAccess.DASHBOARD) && (
          <ScrollableTabView
            style={Platform.OS === 'android' ? { height: currentTab === 0 ? 330 : 400, position: 'relative' } : {}}
            key={pageIndex}
            initialPage={pageIndex}
            renderTabBar={() => (
              <ScrollableTabBar
                activeTextColor="#0695D7"
                inactiveTextColor={COLORS.neutrals.gray70}
                containerStyle={styles.tabContainer}
                ref={tabBarRef}
                onChange={(tab: number) => setCurrentTab(tab)}
              />
            )}
          >
            {/* @ts-ignore */}
            <DashboardSCurvePhysical tabLabel="Physical" fileUrlProgressJson={data?.fileUrlProgressJson} />
            {/* @ts-ignore */}
            <DashboardSCurveFinance
              tabLabel="Financial"
              fileUrlProgressFinanceJson={data?.fileUrlProgressFinanceJson}
            />
          </ScrollableTabView>
        )}

        {isAllowed(companySubscriptions, ProjectAccess.TASK) && (
          <DashboardPieChart
            type="TaskGroupName"
            data={taskSummary}
            options={sanitizedTaskGroups}
            label="TASK"
            total={sanitizedTaskGroups?.length || 0}
            onSearch={val => setTaskNameGroup(val)}
            loading={loadingTaskSummary}
            onPress={(val: number) => setSelectedTaskGroupId(val)}
          />
        )}
        {isAllowed(companySubscriptions, ProjectAccess.WORKSPACE_DOCUMENT) && (
          <DashboardPieChart
            type="workspaceGroupName"
            data={workSpaceGroup}
            options={sanitizedWorkspaceGroups}
            label="WORKSPACE"
            total={sanitizedWorkspaceGroups?.length || 0}
            onSearch={val => setWorkspaceValue(val)}
            loading={loadingWorkspaceSummary}
            onPress={(val: number) => setSelectedWorkspaceGroupId(val)}
          />
        )}
        <DashboardCalendar />
        <DashboardWeather />
        {isAllowed(companySubscriptions, ProjectAccess.WORKSPACE_DOCUMENT) && <DashboardSiteDiary />}
        <DashboardMemo navigation={navigation} />
      </ScrollView>
    </Box>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    backgroundColor: '#FFFFFF'
  }
});

export default Dashboard;
