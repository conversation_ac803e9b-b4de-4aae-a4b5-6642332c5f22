// fileHelpers.ts
import RNFS from 'react-native-fs';

export const setupMemoDirectory = async (): Promise<void> => {
  const memoDir = `${RNFS.DocumentDirectoryPath}/memo`;
  const exists = await RNFS.exists(memoDir);
  if (!exists) {
    await RNFS.mkdir(memoDir);
  }
};

export const generateFileName = (taskId: string, extension: string = 'pdf'): string => {
  const timestamp = new Date().toISOString().replace(/[:.-]/g, '');
  return `${taskId}-${timestamp}.${extension}`;
};

export const downloadFile = async (remoteUrl: string, taskId: string): Promise<string | null> => {
  try {
    await setupMemoDirectory();
    const fileName = generateFileName(taskId);
    const filePath = `${RNFS.DocumentDirectoryPath}/memo/${fileName}`;

    const response = await RNFS.downloadFile({
      fromUrl: remoteUrl,
      toFile: filePath
    }).promise;

    if (response.statusCode === 200) {
      return filePath;
    } else {
      return null;
    }
  } catch (error) {
    return null; // Ensure this is intentional and handled properly in your application.
  }
};
