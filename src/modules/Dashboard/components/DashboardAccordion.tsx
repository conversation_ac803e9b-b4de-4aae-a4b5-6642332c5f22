import { <PERSON>, Divider, Flex, HStack, Text, VStack } from 'native-base';
import React, { useCallback, useEffect, useState } from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import { Icon } from '@src/commons';
import DeviceInfo from 'react-native-device-info';
import { Gql } from '@src/api';
import { COLORS } from '@src/constants/colors';

type Props = {
  title: string;
  totalCount: number;
  isParent?: boolean;
  item: Gql.WorkspaceGroup;
  onPressChild?: (item: any) => void;
  onLongPressChild?: () => void;
  viewOnly?: boolean;
  handleThreeDots?: (item: any) => void;
  filteredValue?: string | null;
};

const DashboardAccordion = (props: Props) => {
  const [isOpen, setIsOpen] = useState(false);

  const isFixturesGroup =
    props?.title === 'Ungroup Documents' || props?.title === 'Site Diary' || props?.title === 'Ungroup Tasks';

  const toggleAccordion = () => {
    isFixturesGroup ? props?.onPressChild?.(props.item) : setIsOpen(!isOpen);
  };

  const appendAccordion = useCallback(() => {
    if (!props?.filteredValue) return;

    if (props?.filteredValue?.trim() !== '') {
      setIsOpen(true);
    }
  }, [props?.filteredValue]);

  useEffect(() => {
    appendAccordion();
  }, [appendAccordion]);

  return (
    <TouchableOpacity onPress={toggleAccordion} style={{ width: '100%' }}>
      <VStack mx={3}>
        <Box style={styles.box}>
          <HStack alignItems="center">
            <Box mr={2}>
              {isFixturesGroup ? (
                <Icon name="group" />
              ) : props?.isParent ? (
                <Icon name={isOpen ? 'arrow-down' : 'arrow-right'} />
              ) : null}
            </Box>
            <HStack height={'full'} mr={2}>
              <Text numberOfLines={1} ellipsizeMode="tail" w={DeviceInfo.getDeviceType() == 'Tablet' ? '90%' : '83%'}>
                {props?.title !== 'undefined (undefined)' ? `${props?.title}` : '...'}
              </Text>
            </HStack>
          </HStack>
        </Box>
        {isOpen
          ? props?.item?.children?.map?.((child: any, index: number) => {
              return (
                <TouchableOpacity onPress={() => props?.onPressChild?.(child)} style={{ width: '100%' }} key={index}>
                  <VStack>
                    <Box style={styles.box}>
                      <HStack alignItems="center">
                        <Box width={'100%'} ml={8}>
                          <Text
                            fontWeight={600}
                            numberOfLines={1}
                            ellipsizeMode="tail"
                            w={DeviceInfo.getDeviceType() == 'Tablet' ? '90%' : '83%'}
                          >
                            {child?.name || child?.title}
                          </Text>
                        </Box>
                      </HStack>
                    </Box>
                  </VStack>
                </TouchableOpacity>
              );
            })
          : null}
      </VStack>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  box: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden'
  },
  addButton: {
    position: 'absolute',
    bottom: 10,
    alignSelf: 'flex-end',
    right: '7%',
    shadowColor: 'rgba(0, 0, 0, 0.5)',
    shadowOpacity: 1,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 6 },
    backgroundColor: '#FFFFFF',
    borderRadius: 9999
  },
  threeDots: {
    width: '10%',
    position: 'absolute',
    right: 0
  }
});

export default DashboardAccordion;
