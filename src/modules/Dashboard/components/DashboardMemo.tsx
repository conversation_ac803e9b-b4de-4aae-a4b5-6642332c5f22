import * as Gql from '@src/api/graphql';
import { Modal } from '@src/commons';
import { getFileIcon } from '@src/commons/FileIcon';
import { pushError, pushSuccess } from '@src/configs';
import { useGetUnreceivedMemoTask } from '@src/queries/tasks/useGetUnrecievedMemoTask';
import { Box, Button, Divider, FlatList, HStack, Spinner, Text } from 'native-base';
import React, { useEffect, useMemo, useState } from 'react';
import { Alert, Image, Platform, Pressable, StyleSheet, TouchableOpacity } from 'react-native';
import RNFS from 'react-native-fs';
import PdfThumbnail from 'react-native-pdf-thumbnail';
import Carousel from 'react-native-snap-carousel';
import { downloadFile } from '../helpers/memoHelpers';
import useUpdateTask from '@src/mutation/task/useUpdateTask';
import queryClient from '@src/configs/reactQuerySetup';

type Props = {
  navigation: any;
  onSaved?: () => void;
};
const DashboardMemo: React.FC<Props> = ({ ...props }) => {
  const [thumbnail, setThumbnail] = React.useState<string[]>([]);
  const [index, setIndex] = useState(0);

  const modalRef = React.useRef<any>(null);
  const carouselRef = React.useRef<any>(null);

  const { data: memoTask, isLoading } = useGetUnreceivedMemoTask();
  const { mutateAsync: updateTask } = useUpdateTask();

  const getUnreceived = useMemo(() => memoTask?.filter((item: any) => !item?.isMemoReceive), [memoTask]);
  const getReceived = useMemo(() => memoTask?.filter((item: any) => item?.isMemoReceive), [memoTask]);
  const [getTasksMemoUrl, { data: tasksMemoUrl }] = Gql.useGetTasksMemoUrlLazyQuery();

  const generateThumbnail = async (url: string) => {
    try {
      if (!url) {
        return null;
      }

      const page = 0;
      if (Platform.OS === 'android') {
        const { uri } = await PdfThumbnail.generate(url, page);
        return uri;
      } else if (Platform.OS === 'ios') {
        const { uri } = await PdfThumbnail.generate(url, page);
        return uri;
      }
    } catch (error) {
      return null;
    }
  };

  useEffect(() => {
    const processTasks = async () => {
      const itemsToProcess = (getUnreceived ?? []).filter(item => item.memoUrl);
      const remoteIds = itemsToProcess.map(item => String(item.remoteId));

      if (remoteIds.length > 0) {
        try {
          const tasks = await getTasksMemoUrl({
            variables: {
              filter: {
                id: {
                  in: remoteIds
                }
              }
            }
          });

          const tasksByUrl = new Map(tasks?.data?.tasks?.nodes.map(node => [String(node.id), node.memoUrl]));

          for (const item of itemsToProcess) {
            const url = tasksByUrl.get(String(item.remoteId));
            let filePath = item.localMemoUrl;

            if (!filePath && url) {
              // Only download if there's no local file already
              filePath = await downloadFile(url, item.id);
              if (filePath) {
                await updateTask({
                  id: item.id,
                  newTask: {
                    localMemoUrl: filePath
                  }
                });
              }
            }

            if (filePath) {
              const thumbnailPath = await generateThumbnail(filePath);
              //@ts-ignore
              setThumbnail(prev => [...prev, thumbnailPath]);
            }
          }
        } catch (error) {}
      }
    };

    processTasks();
  }, [getUnreceived]);

  //receive Memo mutation
  // const [receiveMemo, { loading: updating }] = Gql.useReceiveMemoMutation({
  //   onError: e => {
  //     pushError(e);
  //   },
  //   onCompleted: async () => {
  //     modalRef?.current?.pushModal();
  //     await setThumbnail([]);
  //     // refetch();
  //     pushSuccess('Receive memo successfully');
  //   }
  // });

  const onReceiveMemo = () => {
    Alert.alert('Receive Memo', 'Are you sure you want to receive this memo?', [
      {
        text: 'Cancel',
        style: 'cancel'
      },
      {
        text: 'Receive',
        onPress: () => {
          if (getUnreceived)
            updateTask({
              id: getUnreceived[index]?.id,
              newTask: {
                isMemoReceive: true
              }
            })
              .then(async () => {
                modalRef?.current?.pushModal();
                pushSuccess('Receive memo successfully');

                setThumbnail(prev => prev.filter((_, i) => i !== index));
              })
              .catch(e => {
                pushError('Failed to receive memo');
              });
        }
      }
    ]);
  };

  const loading = isLoading;

  if (!thumbnail) return null;
  return (
    <Box>
      <Text color={'#585757'} mb={3} alignSelf={'center'} bold>
        MEMO
        {/* {`${index + 1} of ${thumbnail.length}`} */}
      </Text>
      <Box style={styles.container} alignItems={'center'} paddingX={5} paddingY={3} mb={4} alignSelf={'center'}>
        {thumbnail.length === 0 && (
          <Box>
            <Text>No Memo</Text>
          </Box>
        )}
        {loading ? (
          <Spinner />
        ) : (
          <Carousel
            data={thumbnail || []}
            itemWidth={100}
            vertical={false}
            style={{ marginTop: 30 }}
            ref={carouselRef}
            layout="stack"
            layoutCardOffset={1}
            sliderWidth={300}
            renderItem={({ item }: { item: any }) => {
              return (
                <Box justifyItems={'center'} alignItems={'center'}>
                  <Pressable onPress={() => modalRef?.current?.pushModal()}>
                    <Image source={{ uri: item }} style={{ width: 150, height: 200 }} />
                    {/* <FastImage
                    source={{
                      uri: item ?? '',
                      cache: FastImage.cacheControl.immutable,
                      priority: FastImage.priority.normal
                    }}
                    style={{ width: '100%', height: 250, marginTop: 30 }}
                    resizeMode="center"
                  /> */}
                  </Pressable>
                </Box>
              );
            }}
            onSnapToItem={setIndex}
          />
        )}
        {getReceived && getReceived?.length > 0 && (
          <>
            <Text mt={8}>Received Memo</Text>
            <FlatList
              height={200}
              maxHeight={200}
              width={'full'}
              overflowY={'auto'}
              data={getReceived || []}
              keyExtractor={item => item.id}
              nestedScrollEnabled={true}
              renderItem={({ item, index }: any) => {
                return (
                  <Box key={index}>
                    <TouchableOpacity
                      onPress={() => {
                        props.navigation.navigate('TasksNav', {
                          screen: 'EditTask',
                          params: { id: item?.id }
                        });
                      }}
                    >
                      <Box style={styles.box}>
                        <Divider w="full" my={4} mx={5} />
                        <HStack>
                          <Box pl={8}>{getFileIcon('pdf')}</Box>
                          <Text ml={2} fontSize={14} numberOfLines={1} ellipsizeMode="tail">
                            {item.title}
                          </Text>
                        </HStack>
                      </Box>
                    </TouchableOpacity>
                  </Box>
                );
              }}
            />
          </>
        )}
      </Box>

      <Modal ref={modalRef}>
        <Box alignSelf={'center'}>
          <Button
            isLoading={loading}
            variant="ghost"
            _text={{ color: 'black' }}
            onPress={() => {
              if (props?.navigation && getUnreceived) {
                props.navigation.navigate('TasksNav', {
                  screen: 'EditTask',
                  params: { id: getUnreceived[index]?.id }
                });
              }
            }}
          >
            <Text>View</Text>
          </Button>
          <Button
            isLoading={loading}
            variant="ghost"
            _text={{ color: 'black' }}
            onPress={() => {
              onReceiveMemo();
            }}
          >
            <Text>Receive</Text>
          </Button>
        </Box>
      </Modal>
    </Box>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '90%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 0.2, // Adjust the shadow intensity (0.0 - 1.0)
    shadowRadius: 2, // Adjust the spread of the shadow
    borderRadius: 12,
    backgroundColor: '#fff', // You may need to specify a background color for the shadow to be visible
    elevation: 9 // For Android, use elevation instead of shadow properties
  },
  box: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
    bordercolor: '#00000'
  }
});

export default DashboardMemo;
