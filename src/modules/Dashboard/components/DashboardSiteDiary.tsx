import { Gql } from '@src/api';
import { useDispatch, useSelector } from '@src/store';
import _ from 'lodash';
import { Box, Button, Card, Divider, FlatList, HStack, ScrollView, Text, VStack, View } from 'native-base';
import React, { useEffect, useMemo, useState } from 'react';
import PdfThumbnail from 'react-native-pdf-thumbnail';
import { Image, Platform, StyleSheet, TouchableOpacity } from 'react-native';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootNavigatorParams } from '@src/types';
import { getFileIcon } from '@src/commons/FileIcon';
import DeviceInfo from 'react-native-device-info';
import moment from 'moment';
import { Icon } from '@src/commons';
import RNFS from 'react-native-fs';
import { useGetSiteDiary } from '@src/queries/workspace/useGetSiteDiary';
import { downloadFile } from '../helpers/memoHelpers';
import useUpdateWorkspaceDocument from '@src/mutation/workspace/useUpdateWorkspaceDocument';

type Props = {};

const DashboardSiteDiary = (props: Props) => {
  const navigation = useNavigation<StackNavigationProp<RootNavigatorParams>>();
  const project = useSelector(state => state.project);
  const [thumbnail, setThumbnail] = useState<any>(null);
  const [isExpand, setIsExpand] = useState<boolean>(false);
  const dispatch = useDispatch();

  const [getProjectDocument, { data }] = Gql.useProjectDocumentLazyQuery();
  const { data: diaryData } = useGetSiteDiary();
  const { mutateAsync } = useUpdateWorkspaceDocument();

  const downloadFileAndConvertToContentUri = async (httpsUrl: any) => {
    try {
      // Create a unique file path in the app's cache directory
      const filePath = `${RNFS.CachesDirectoryPath}/${Math.random().toString(36).substring(7)}.pdf`;

      // Download the file from the HTTPS URL
      const response = await RNFS.downloadFile({
        fromUrl: httpsUrl,
        toFile: filePath
      }).promise;

      if (response.statusCode === 200) {
        // Get the content URI for the downloaded file
        const contentUri = `file://${filePath}`;
        return contentUri;
      } else {
        return null;
      }
    } catch (error) {
      return null;
    }
  };

  const recentSiteDiary: any = useMemo(() => {
    if (!diaryData || diaryData.length === 0) return null;

    const diary: any = diaryData[0];
    const isRecent = moment(diary.submittedAt).isAfter(moment().subtract(20, 'hours'));
    const isNotRejected = diary.status !== 'Rejected';

    return isRecent && isNotRejected ? diary : null;
  }, [diaryData]);

  async function generateThumbnail(url: any) {
    if (!url) return;
    const page = 0;
    if (Platform.OS === 'android') {
      const { uri } = await PdfThumbnail.generate(url, page);
      return uri;
    } else if (Platform.OS === 'ios') {
      const { uri } = await PdfThumbnail.generate(url, page);
      return uri;
    }
  }

  useEffect(() => {
    const fetchDocumentAndGenerateThumbnail = async () => {
      if (recentSiteDiary) {
        try {
          if (!recentSiteDiary.localFileUrl) {
            const document = await getProjectDocument({
              variables: {
                id: recentSiteDiary.remoteId?.toString()
              }
            });

            const fileUrl = document?.data?.projectDocument?.fileUrl;

            if (fileUrl) {
              const filePath = await downloadFile(fileUrl, recentSiteDiary.id);

              if (filePath) {
                await mutateAsync({
                  id: recentSiteDiary.id,
                  localFileUrl: filePath
                });
              }

              const uri = await generateThumbnail(fileUrl);
              setThumbnail(uri);
            } else {
            }
          } else {
            const fileUrl = recentSiteDiary.localFileUrl;

            if (fileUrl) {
              const uri = await generateThumbnail(fileUrl);
              setThumbnail(uri);
            } else {
            }
          }
        } catch (error) {}
      }
    };

    fetchDocumentAndGenerateThumbnail();
  }, [recentSiteDiary, getProjectDocument]);

  return (
    <Box my={5}>
      <Text color={'#585757'} mb={3} alignSelf={'center'} bold>
        SITE DIARY
      </Text>
      <Box style={styles.container} alignSelf={'center'} bg="#EEEEEE">
        {recentSiteDiary ? (
          <>
            <VStack alignItems={'center'} width={'full'} mt={3}>
              <Text>Today's Site Diary</Text>
            </VStack>
            <TouchableOpacity
              onPress={() => {
                dispatch(documentWorkspaceActions.setDocumentId(recentSiteDiary.id as any));
                navigation.navigate('DigitalFormNav', {
                  screen: 'DocumentDetail',
                  params: {
                    refetch: () => {}
                  }
                });
              }}
            >
              <Card mt={5} borderWidth={1} borderColor={'#EEE'} borderRadius={8} alignItems={'center'}>
                {thumbnail ? <Image source={{ uri: thumbnail }} style={{ width: 150, height: 200 }}></Image> : null}
              </Card>
              <Box style={styles.box} my={4}>
                <HStack
                  alignSelf={'center'}
                  style={{ alignItems: 'center', right: DeviceInfo.getDeviceType() == 'Tablet' ? 0 : 25 }}
                >
                  <Box style={{ width: DeviceInfo.getDeviceType() == 'Tablet' ? 75 : '25%', alignItems: 'center' }}>
                    {getFileIcon(recentSiteDiary?.type as any)}
                  </Box>
                  <VStack style={{ width: DeviceInfo.getDeviceType() == 'Tablet' ? '80%' : '62%' }}>
                    <Text fontSize={14} numberOfLines={1} ellipsizeMode="tail">
                      {recentSiteDiary?.name}
                    </Text>
                    <HStack space={2} alignItems="center"></HStack>
                  </VStack>
                </HStack>
              </Box>
            </TouchableOpacity>
          </>
        ) : (
          <VStack alignItems={'center'} width={'full'}>
            <Text>Today's site diary has not been uploaded yet</Text>
          </VStack>
        )}

        <VStack alignItems={'center'} width={'full'} mb={4}>
          <Text>PREVIOUS SITE DIARY</Text>
        </VStack>
        <Button
          _pressed={{
            bg: 'transparent',
            opacity: 0.8
          }}
          bg="transparent"
          onPress={() => setIsExpand(!isExpand)}
        >
          <Icon name={!isExpand ? 'chevron-down-primary' : 'chevron-up-primary'} />
        </Button>
        <Box>
          {isExpand ? (
            <FlatList
              height={200}
              maxHeight={200}
              overflowY={'auto'}
              data={diaryData?.slice(1)}
              keyExtractor={item => item.id}
              nestedScrollEnabled={true}
              renderItem={({ item, index }: any) => (
                <Box flexDirection="row" key={index}>
                  <TouchableOpacity
                    onPress={() => {
                      if (item.type === 'pdf') {
                        dispatch(documentWorkspaceActions.setDocumentId(item.id));
                        navigation.navigate('DigitalFormNav', {
                          screen: 'DocumentDetail',
                          params: {
                            refetch: () => {}
                          }
                        });
                      }
                    }}
                  >
                    <Box style={styles.box}>
                      <Divider width={250} my={4} mx={10} />
                      <HStack>
                        <Box
                          pl={8}
                          // style={{ width: DeviceInfo.getDeviceType() == 'Tablet' ? 75 : '25%', alignItems: 'center' }}
                        >
                          {getFileIcon(item.type)}
                        </Box>
                        {/* <VStack   style={{ width: DeviceInfo.getDeviceType() == 'Tablet' ? '80%' : '75%' }}> */}
                        <Text mr={100} fontSize={14} numberOfLines={1} ellipsizeMode="tail">
                          {item.name}
                        </Text>
                        {/* <HStack space={2} alignItems="center"></HStack> */}
                        {/* </VStack> */}
                      </HStack>
                    </Box>
                  </TouchableOpacity>
                </Box>
              )}
            />
          ) : null}
        </Box>
      </Box>
    </Box>
  );
};
const styles = StyleSheet.create({
  box: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
    bordercolor: '#00000'
  },
  addButton: {
    position: 'absolute',
    bottom: 10,
    alignSelf: 'flex-end',
    right: 30
  },
  container: {
    width: '90%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 0.2, // Adjust the shadow intensity (0.0 - 1.0)
    shadowRadius: 2, // Adjust the spread of the shadow
    borderRadius: 12,
    backgroundColor: '#fff', // You may need to specify a background color for the shadow to be visible
    elevation: 9 // For Android, use elevation instead of shadow properties
  }
});
export default DashboardSiteDiary;
