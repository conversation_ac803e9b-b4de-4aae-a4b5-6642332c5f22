import { Icon } from '@src/commons';
import { COLORS } from '@src/constants/colors';

import _ from 'lodash';
import { Box, FlatList, HStack, Input, Spinner, Text, View } from 'native-base';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { Animated, Pressable, StyleSheet, TouchableOpacity } from 'react-native';
import { VictoryLegend, VictoryPie } from 'victory-native';
import DashboardAccordion from './DashboardAccordion';
import ProjectGroupModel from '@src/database/model/project-group.model';
import WorkspaceGroupModel from '@src/database/model/workspace-group.model';

type Props = {
  label: string;
  total: number;
  options: any;
  data: any;
  // type also send as name in form
  type: 'TaskGroupName' | 'workspaceGroupName';
  loading: boolean;
  onSearch: (val: string) => void;
  onPress: (val: number) => void;
};
const DashboardPieChart: React.FC<Props> = ({ label, total = 0, options, data, type, onSearch, loading, onPress }) => {
  const [filteredValue, setFilteredValue] = useState<string>('');

  function generateLegendAndColorScale(getType: string) {
    let legendItems = [];
    let colorScale = [];

    if (getType === 'TaskGroupName') {
      const dataPoints = [
        { name: 'Open', count: data?.countOfOpenTasks, color: '#E00D0D' },
        { name: 'In Progress', count: data?.countOfInProgressTasks, color: '#2B87E3' },
        { name: 'Hold', count: data?.countOfHoldTasks, color: '#EBA10F' },
        { name: 'Closed', count: data?.countOfCompletedTasks, color: '#0CA85D' }
      ];

      for (const point of dataPoints) {
        if (point?.count > 0) {
          legendItems?.push({ name: point?.name, symbol: { fill: point?.color } });
          colorScale?.push(point.color);
        }
      }
    } else {
      const dataPoints = [
        { name: 'Submitted', count: data?.submittedWorkspaceTasks ?? 0, color: '#2B87E3' },
        { name: 'In Review', count: data?.inReviewWorkspaceTasks ?? 0, color: '#EBA10F' },
        { name: 'Pending', count: data?.pendingWorkspaceTasks ?? 0, color: '#74caec' },
        { name: 'In Progress', count: data?.inProgressWorkspaceTasks ?? 0, color: '#9ce255' },
        { name: 'Approved', count: data?.approvedWorkspaceTasks ?? 0, color: '#0CA85D' },
        { name: 'Rejected', count: data?.rejectedWorkspaceTasks ?? 0, color: '#E00D0D' }
      ];

      for (const point of dataPoints) {
        if (point.count > 0) {
          legendItems.push({ name: point.name, symbol: { fill: point.color } });
          colorScale.push(point.color);
        }
      }
    }

    return { legend: legendItems, colorScale };
  }

  // Change this to the desired type
  const { legend, colorScale } = generateLegendAndColorScale(type);

  const structuredData =
    type === 'TaskGroupName'
      ? [
          { y: data?.countOfOpenTasks, x: data?.countOfOpenTasks },
          { y: data?.countOfInProgressTasks, x: data?.countOfInProgressTasks },
          { y: data?.countOfHoldTasks, x: data?.countOfHoldTasks },
          { y: data?.countOfCompletedTasks, x: data?.countOfCompletedTasks }
        ]
      : [
          { y: data?.submittedWorkspaceTasks, x: data?.submittedWorkspaceTasks },
          { y: data?.inReviewWorkspaceTasks, x: data?.inReviewWorkspaceTasks },
          { y: data?.pendingWorkspaceTasks, x: data?.pendingWorkspaceTasks },
          { y: data?.inProgressWorkspaceTasks, x: data?.inProgressWorkspaceTasks },
          { y: data?.approvedWorkspaceTasks, x: data?.approvedWorkspaceTasks },
          { y: data?.rejectedWorkspaceTasks, x: data?.rejectedWorkspaceTasks }
        ];
  // get total count of data
  const totalCount = structuredData.reduce((acc: number, item: any) => {
    // Add a check to ensure item.y is a valid number before adding to the accumulator
    return acc + (typeof item?.y === 'number' ? item.y : 0);
  }, 0);

  const isZero = useMemo(() => structuredData.every((item: any) => item?.y === 0), [structuredData]);

  const handlePress = useCallback((item: any) => {
    setFilteredValue((item as ProjectGroupModel)?.name || item?.title);
    onPress(item?.remoteId);
  }, []);

  return (
    <Box my={5}>
      <Box>
        <Text color={'#585757'} mb={3} alignSelf={'center'} bold>
          {label}
        </Text>
      </Box>

      <Box style={styles.container} alignSelf={'center'}>
        <HStack px={2} mt={3}>
          <Input
            flexGrow={1}
            backgroundColor={COLORS.neutrals.white}
            placeholder="Search group"
            borderRadius="8"
            fontSize="14"
            InputRightElement={
              <Pressable
                style={{ marginRight: 10 }}
                onPress={() => {
                  setFilteredValue('');
                  onPress(null);
                }}
              >
                <Icon fill={'black'} name="cancel" height={10} width={10} />
              </Pressable>
            }
            value={filteredValue ?? ''}
            onChangeText={async value => {
              await setFilteredValue(value);

              setTimeout(() => {
                onSearch(value);
              }, 50);
            }}
          />
        </HStack>

        {loading && <Spinner />}

        {!_.isEmpty(filteredValue) ? (
          <>
            <FlatList
              keyboardShouldPersistTaps="handled"
              zIndex={-2}
              data={options}
              keyExtractor={(item: any) => item.id}
              renderItem={({ item }: any) => {
                const totalCount = item?.totalCount || 0;
                return (
                  <>
                    <DashboardAccordion
                      totalCount={totalCount}
                      item={item}
                      title={type === 'workspaceGroupName' ? item?.name : item?.title}
                      isParent={!item?.projectGroupId || !item?.workspaceGroupId}
                      onPressChild={handlePress}
                      filteredValue={filteredValue ?? ''}
                    />
                  </>
                );
              }}
            />
          </>
        ) : null}

        <HStack w={10} h={230} justifyContent={'space-between'} px={5}>
          <VictoryLegend x={180} y={60} orientation="vertical" data={legend} />
          {/* if zero data send grey chart */}
          {isZero ? (
            <VictoryPie
              colorScale={['#808080']}
              data={[{ y: 1 }]}
              width={250}
              height={250}
              innerRadius={40}
              style={{
                parent: { position: 'relative', left: -50 },
                labels: {
                  fill: 'black',
                  fontSize: 15
                }
              }}
            />
          ) : (
            <VictoryPie
              colorScale={colorScale}
              data={structuredData?.filter(item => item.x !== 0 && item.y !== 0)}
              width={250}
              height={250}
              innerRadius={40}
              style={{
                parent: { position: 'relative', left: -50 },
                labels: {
                  fill: 'black',
                  fontSize: 12,
                  padding: 8
                }
              }}
            />
          )}
          <View style={{ position: 'absolute', top: 100, left: 75 }}>
            <Text bold>Total </Text>
            <Text alignSelf={'center'} bold>
              {isZero ? 0 : totalCount}
            </Text>
          </View>
        </HStack>
      </Box>
    </Box>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '90%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1
    },

    shadowOpacity: 0.2, // Adjust the shadow intensity (0.0 - 1.0)
    shadowRadius: 2, // Adjust the spread of the shadow
    borderRadius: 12,
    backgroundColor: '#fff', // You may need to specify a background color for the shadow to be visible
    elevation: 9 // For Android, use elevation instead of shadow properties
  },
  flatList: {
    borderWidth: 1,
    borderColor: COLORS.neutrals.gray40,
    padding: 12
  }
});

export default DashboardPieChart;
