import { <PERSON>, Card, HStack, Spinner, Text, View, VStack } from 'native-base';
import { useSelector } from '@src/store';
import moment from 'moment';
import axios from 'axios';
import React, { useEffect, useState } from 'react';
import { Image, StyleSheet } from 'react-native';
import Icon from '@src/commons/Icon';
import { Gql } from '@src/api';
import _ from 'lodash';

const DashboardWeather: React.FC<any> = props => {
  const project = useSelector(state => state.project);
  const [weatherData, setWeatherData] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(false);

  const { data: projectData, loading: projectLoading } = Gql.useGetProjectQuery({
    variables: { id: _.toString(project.projectId) }
  });

  useEffect(() => {
    if (projectData?.project?.metTownId) getMETWeather();
  }, [projectData?.project?.id]);

  function getMETWeather() {
    setLoading(true);
    const URL = 'https://api.met.gov.my/v2.1/data';

    const param = {
      datasetid: 'FORECAST',
      datacategoryid: 'GENERAL',
      locationid: projectData?.project?.metTownId,
      start_date: moment().format('YYYY-MM-DD'),
      end_date: moment().format('YYYY-MM-DD')
    };

    axios
      .get(URL, { params: param, headers: { Authorization: 'METToken 3f8f42b5cf0a0e5db2dec513ef78ea171eb4af2e' } })
      .then(response => {
        setWeatherData(response.data.results);
        setLoading(false);
      })
      .catch(err => {
        setLoading(false);
      });
  }

  const getFullDate = (date: Date) => {
    const month = date.toLocaleString('default', { month: 'long' });
    const year = date.getFullYear();
    const day = date.getDate();
    const suffix = (day: number) => {
      if (day > 3 && day < 21) return 'th';
      switch (day % 10) {
        case 1:
          return 'st';
        case 2:
          return 'nd';
        case 3:
          return 'rd';
        default:
          return 'th';
      }
    };
    return `${day}${suffix(day)} ${month} ${year}`;
  };

  return (
    <Box my={5}>
      <VStack alignItems={'center'} width={'full'} mb={2}>
        <Text color={'#585757'} mb={3} alignSelf={'center'} bold>
          WEATHER
        </Text>
        <Text fontSize={12} fontWeight={1}>
          {getFullDate(new Date())}
        </Text>
      </VStack>
      <Box style={styles.container} paddingX={5} paddingY={3} alignSelf={'center'}>
        {(loading || projectLoading) && <Spinner size={'sm'} />}

        {!loading && !projectLoading && weatherData.length == 0 && (
          <View my={2}>
            <Text variant={'bodyReg'} fontSize={12}>
              No weather data available
            </Text>
          </View>
        )}

        {!loading && !projectLoading && weatherData.length > 0 && (
          <View my={2}>
            <Box>
              <HStack>
                <VStack width={'1/4'} alignItems={'center'} my={'auto'}>
                  <Image source={require('@assets/weather.png')}></Image>
                </VStack>
                <VStack width={'3/4'}>
                  <Text variant={'bodyReg'} fontSize={20}>
                    {
                      (weatherData?.find((data: any) => {
                        return data?.datatype === 'FSIGW';
                      })).value
                    }
                  </Text>
                  <HStack>
                    <Text variant={'bodyReg'} fontSize={11}>
                      High:{' '}
                      {
                        (weatherData?.find((data: any) => {
                          return data?.datatype === 'FMAXT';
                        })).value
                      }
                      &#8451;{' '}
                    </Text>
                    <Text variant={'bodyReg'} fontSize={11}>
                      Low:{' '}
                      {
                        (weatherData?.find((data: any) => {
                          return data?.datatype === 'FMINT';
                        })).value
                      }
                      &#8451;{' '}
                    </Text>
                  </HStack>
                  <Text variant={'bodyReg'} fontSize={11}>
                    {
                      (weatherData?.find((data: any) => {
                        return data?.datatype === 'FSIGW';
                      })).locationname
                    }
                    ,
                    {
                      (weatherData?.find((data: any) => {
                        return data?.datatype === 'FSIGW';
                      })).locationrootname
                    }
                  </Text>
                </VStack>
              </HStack>
              <HStack mt={6} alignItems={'center'}>
                <View width={'1/3'} alignItems={'center'}>
                  <HStack>
                    <Text fontSize={11} variant={'bodyReg'}>
                      Morning:{' '}
                    </Text>
                    <Text fontSize={11} variant={'bodyReg'}>
                      <Icon
                        name={
                          (weatherData?.find((data: any) => {
                            return data?.datatype === 'FGM';
                          })).value === 'Rain'
                            ? 'rainy'
                            : (weatherData?.find((data: any) => {
                                  return data?.datatype === 'FGM';
                                })).value === 'Thunderstorms'
                              ? 'thunderstorm'
                              : 'sunny'
                        }
                        width={24}
                        height={24}
                      />
                    </Text>
                  </HStack>
                </View>
                <View width={'1/3'} alignItems={'center'}>
                  <HStack>
                    <Text fontSize={11} variant={'bodyReg'}>
                      Afternoon:{' '}
                    </Text>
                    <Text fontSize={11} variant={'bodyReg'}>
                      <Icon
                        name={
                          (weatherData?.find((data: any) => {
                            return data?.datatype === 'FGA';
                          })).value === 'Rain'
                            ? 'rainy'
                            : (weatherData?.find((data: any) => {
                                  return data?.datatype === 'FGA';
                                })).value === 'Thunderstorms'
                              ? 'thunderstorm'
                              : 'sunny'
                        }
                        width={24}
                        height={24}
                      />
                    </Text>
                  </HStack>
                </View>
                <View width={'1/3'} alignItems={'center'}>
                  <HStack>
                    <Text fontSize={11} variant={'bodyReg'}>
                      Night:{' '}
                    </Text>
                    <Text fontSize={11} variant={'bodyReg'}>
                      <Icon
                        name={
                          (weatherData?.find((data: any) => {
                            return data?.datatype === 'FGN';
                          })).value === 'Rain'
                            ? 'rainy'
                            : (weatherData?.find((data: any) => {
                                  return data?.datatype === 'FGN';
                                })).value === 'Thunderstorms'
                              ? 'thunderstorm'
                              : 'night'
                        }
                        width={24}
                        height={24}
                      />
                    </Text>
                  </HStack>
                </View>
              </HStack>
            </Box>
          </View>
        )}
      </Box>
      <HStack paddingX={5} paddingY={1}>
        <Text fontSize={10} color={'gray.400'} fontStyle={'italic'}>
          Powered by MET Malaysia
        </Text>
      </HStack>
    </Box>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '90%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 0.2, // Adjust the shadow intensity (0.0 - 1.0)
    shadowRadius: 2, // Adjust the spread of the shadow
    borderRadius: 12,
    backgroundColor: '#fff', // You may need to specify a background color for the shadow to be visible
    elevation: 9 // For Android, use elevation instead of shadow properties
  }
});

export default DashboardWeather;
