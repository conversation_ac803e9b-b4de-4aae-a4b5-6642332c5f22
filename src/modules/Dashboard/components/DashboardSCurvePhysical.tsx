import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Box, HStack, Spinner, Text, View, VStack } from 'native-base';
import { StyleSheet, TouchableOpacity } from 'react-native';
import { COLORS } from '@src/constants/colors';
import moment from 'moment';
import { Icon } from '@src/commons';

type Props = {
  fileUrlProgressJson?: string;
  tabLabel?: string;
};

const DashboardSCurvePhysical: React.FC<Props> = (props: Props) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [physicalData, setPhysicalData] = useState<any[]>([]);
  const [plannedData, setPlannedData] = useState<any[]>([]);
  const [monthIndex, setMonthIndex] = useState<number>(0);

  useEffect(() => {
    try {
      const jsonData = JSON.parse(props.fileUrlProgressJson || '[]');
      const parsedData = JSON.parse(jsonData);
      setPhysicalData(parsedData);
      const planned = parsedData.filter((item: any) => item.category === 'PLANNED' && item.year !== 'Invalid date');
      setPlannedData(planned);
      const currentMonthYear = moment().format('MMM-YY');
      const initialMonthIndex = planned.findIndex((item: any) => item.year === currentMonthYear);
      setMonthIndex(initialMonthIndex >= 0 ? initialMonthIndex : 0);
    } catch (error) {}
    setLoading(false);
  }, [props.fileUrlProgressJson]);

  const getProgress = useCallback(
    (type: string) => {
      if (!physicalData.length) return '0.00';

      const planned = physicalData.filter((item: any) => item.category === 'PLANNED' && item.year !== 'Invalid date');
      const actual = physicalData.filter((item: any) => item.category === 'ACTUAL' && item.year !== 'Invalid date');

      if (!planned.length) return '0.00';
      if (!actual.length) return '0.00';

      const plannedEntry = planned[monthIndex];
      const actualEntry = actual.find((item: any) => item.year === plannedEntry.year);

      const plannedValue = plannedEntry?.value || 0;
      const actualValue = actualEntry?.value || 0;

      switch (type) {
        case 'planned':
          return plannedValue.toFixed(2);
        case 'actual':
          return actualValue.toFixed(2);
        case 'progress':
          const progress = !actualEntry?.value || !plannedEntry?.value ? 0.0 : actualEntry.value - plannedEntry.value;
          return progress.toFixed(2);
        case 'days':
          return (actualEntry?.progressDays ?? 0).toString();
        case 'month':
          return moment(plannedEntry?.year, 'MMM-YY').format('MMMM YYYY');
        default:
          return '0.00';
      }
    },
    [physicalData, monthIndex]
  );

  const plannedPercentage = useMemo(() => getProgress('planned'), [getProgress]);
  const actualPercentage = useMemo(() => getProgress('actual'), [getProgress]);
  const progressValue = useMemo(() => getProgress('progress'), [getProgress]);
  const progressDays = useMemo(() => getProgress('days'), [getProgress]);
  const progressMonth = useMemo(() => getProgress('month'), [getProgress]);

  if (loading) {
    return <Spinner size="sm" mb={10} />;
  }

  if (!plannedData.length) {
    return <Text>No data available</Text>;
  }

  return (
    <VStack alignItems={'center'} my={4} mx={4}>
      <HStack>
        <TouchableOpacity
          style={{ paddingHorizontal: 8, marginRight: 20 }}
          onPress={() => setMonthIndex(Math.max(0, monthIndex - 1))}
        >
          <Icon name={'chevron-left'} fill={COLORS.neutrals.gray90} />
        </TouchableOpacity>

        <Text textAlign={'center'} minWidth={40} mb={5}>
          {progressMonth}
        </Text>

        <TouchableOpacity
          style={{ paddingHorizontal: 8, marginLeft: 20 }}
          onPress={() => setMonthIndex(Math.min(plannedData.length - 1, monthIndex + 1))}
        >
          <Icon name={'chevron-right'} fill={COLORS.neutrals.gray90} />
        </TouchableOpacity>
      </HStack>

      <View style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', width: '100%' }}>
        <Box style={{ ...styles.container }} mr={2} px={2} py={1} borderWidth={'1px'} borderColor={'#EEE'}>
          <Text mb={4}>Planned</Text>
          <Text style={styles.value}>{plannedPercentage}%</Text>
        </Box>

        <Box style={{ ...styles.container }} ml={2} px={2} py={1} borderWidth={'1px'} borderColor={'#EEE'}>
          <Text mb={4}>Actual</Text>
          <Text style={styles.value}>{actualPercentage}%</Text>
        </Box>
      </View>

      <Box
        mt={4}
        px={2}
        py={1}
        borderWidth={'1px'}
        borderColor={'#EEE'}
        style={{
          width: '50%',
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 1
          },
          shadowOpacity: 0.2,
          shadowRadius: 2,
          borderRadius: 12,
          backgroundColor: '#fff',
          elevation: 9
        }}
      >
        <Text style={{ color: progressValue.startsWith('-') ? '#dc2626' : '#008B0E', ...styles.progress }}>
          {progressValue}%
        </Text>
        <Text style={{ textAlign: 'center', color: progressValue.startsWith('-') ? '#dc2626' : '#16a34a' }}>
          {progressDays} days
        </Text>
        <Text mt={0} mb={3} variant={'bodyReg'} style={{ textAlign: 'center' }}>
          {Number(progressValue) > 0 ? 'Ahead' : Number(progressValue) === 0 ? 'No progress' : 'Delay'}
        </Text>
      </Box>
    </VStack>
  );
};

const styles = StyleSheet.create({
  value: {
    color: '#0695D7',
    alignSelf: 'center',
    lineHeight: 38,
    fontSize: 38
  },
  progress: {
    lineHeight: 40,
    fontSize: 40,
    marginTop: 14,
    textAlign: 'center'
  },
  container: {
    flex: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    borderRadius: 12,
    backgroundColor: '#fff',
    elevation: 9
  }
});

export default DashboardSCurvePhysical;
