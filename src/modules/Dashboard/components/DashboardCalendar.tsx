import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, HStack, Heading, Text, VStack } from 'native-base';
import React, { useCallback, useMemo, useState } from 'react';
import CalendarImage from '@assets/calendar.png';
import FastImage from 'react-native-fast-image';
import { InteractionManager, StyleSheet } from 'react-native';
import { Icon } from '@src/commons';
import moment from 'moment';
import { useFocusEffect } from '@react-navigation/native';
import { Gql } from '@src/api';
import { useSelector } from '@src/store';
import { pushError } from '@src/configs';
import _ from 'lodash';
import { useGetEvents } from '@src/queries/events/useGetEvents';

type Props = {};

const DashboardCalendar = (props: Props) => {
  const [isExpand, setIsExpand] = useState<boolean>(false);
  const project = useSelector(state => state.project);
  const auth = useSelector(state => state.auth);

  const { data: events, fetchNextPage, hasNextPage, isLoading } = useGetEvents();

  const sanitizedEvents = useMemo(() => {
    if (!events) return [];
    return events.pages.flatMap(page => page.MergedEvents);
  }, [events]);

  const handleEndReached = useCallback(() => {
    if (hasNextPage && !isLoading) {
      fetchNextPage();
    }
  }, [hasNextPage, isLoading, fetchNextPage]);

  // useFocusEffect(
  //   useCallback(() => {
  //     InteractionManager.runAfterInteractions(() => {
  //       try {
  //         getEvents({
  //           variables: {
  //             filter: {
  //               projectId: {
  //                 eq: project?.projectId
  //               },
  //               startAt: {
  //                 between: {
  //                   lower: moment().startOf('day')?.toISOString(),
  //                   upper: moment().add(1000, 'years').toISOString()
  //                 }
  //               },
  //               userId: {
  //                 eq: auth?.user?.id
  //               }
  //             },
  //             sorting: {
  //               field: Gql.EventSortFields.StartAt,
  //               direction: Gql.SortDirection.Asc
  //             },
  //             paging: {
  //               offset: 0,
  //               limit: 9999
  //             }
  //           },
  //           fetchPolicy: 'cache-and-network'
  //         });
  //         getTasks({
  //           variables: {
  //             filter: {
  //               assignees: {
  //                 userId: {
  //                   eq: auth?.user?.id
  //                 }
  //               },
  //               dueDate: {
  //                 between: {
  //                   lower: moment('2021-01-01').toISOString(),
  //                   upper: moment().add(1000, 'years').toISOString()
  //                 }
  //               },
  //               status: {
  //                 neq: Gql.TaskStatusType.Completed
  //               }
  //             },
  //             sorting: {
  //               field: Gql.TaskSortFields.DueDate,
  //               direction: Gql.SortDirection.Asc
  //             },
  //             paging: {
  //               offset: 0,
  //               limit: 9999
  //             }
  //           }
  //         });
  //       } catch (error: any) {
  //         pushError(error?.message);
  //       }
  //     });
  //   }, [])
  // );

  // const onLoadedMore = () => {
  //   if (data?.getEventsByAssignee?.pageInfo?.hasNextPage) {
  //     fetchMore({
  //       variables: {
  //         paging: {
  //           offset: data?.getEventsByAssignee.nodes.length,
  //           limit: 10
  //         }
  //       },
  //       updateQuery: (prev, { fetchMoreResult }) => {
  //         if (!fetchMoreResult) return prev;
  //         const result = Object.assign({}, prev, {
  //           events: {
  //             ...fetchMoreResult.getEventsByAssignee,
  //             nodes: [...prev?.getEventsByAssignee?.nodes, ...fetchMoreResult?.getEventsByAssignee?.nodes]
  //           }
  //         });
  //         return result;
  //       }
  //     });
  //   }
  //   if (taskData?.tasks?.pageInfo?.hasNextPage) {
  //     fetchMoreTask({
  //       variables: {
  //         paging: {
  //           offset: taskData?.tasks.nodes.length,
  //           limit: 10
  //         }
  //       },
  //       updateQuery: (prev, { fetchMoreResult }) => {
  //         if (!fetchMoreResult) return prev;
  //         const result = Object.assign({}, prev, {
  //           tasks: {
  //             ...fetchMoreResult.tasks,
  //             nodes: [...prev.tasks.nodes, ...fetchMoreResult.tasks.nodes]
  //           }
  //         });
  //         return result;
  //       }
  //     });
  //   }
  // };

  const sanitizingData = useCallback(() => {
    const events = sanitizedEvents.map((event: any) => {
      const startMoment = event?.startAt ? moment(event?.startAt) : null;
      const endMoment = moment(event?.endAt ?? event?.dueDate);

      const formattedStartAt = startMoment?.format('MMM D, YYYY');
      const formattedEndAt = endMoment?.format('MMM D, YYYY');
      const startTime = startMoment?.format('hh:mm A');
      const endTime = endMoment?.format('hh:mm A');

      const isSingleDayEvent = startMoment && startMoment.isSame(endMoment, 'day');
      const dateRange =
        isSingleDayEvent === null || isSingleDayEvent
          ? false
          : `${startMoment?.format('D')}-${endMoment.format('D MMM, YYYY')}`;

      return {
        ...event,
        formattedStartAt,
        formattedEndAt,
        startTime,
        endTime,
        dateRange,
        startMoment
      };
    });

    return events;
  }, [sanitizedEvents]);

  const sanitizedData = useMemo(() => sanitizingData(), [sanitizedEvents]);

  const findCloseEvent = useCallback(() => {
    return sanitizedData?.[0];
  }, [sanitizedData]);

  const closeEvent = useMemo(() => findCloseEvent(), [findCloseEvent]);

  const datas = useMemo(() => sanitizedData?.filter(event => event !== closeEvent), [closeEvent, sanitizedData]);

  const eventList = (data: any, index: number) => (
    <Box key={index}>
      <Divider />
      <VStack style={{ gap: 3 }} paddingY={3}>
        <HStack justifyContent={'space-between'} alignItems={'center'}>
          {/* <Text color={'#707070'}>{!data?.dateRange ? data?.formattedStartAt : data?.dateRange}</Text> */}
          <HStack alignContent={'center'}>
            <Text color={'#707070'}>
              {data?.dueDate ? data?.formattedEndAt : !data?.dateRange ? data?.formattedStartAt : data?.dateRange}
              {'  '}
            </Text>
            <Box paddingTop={1}>
              <Icon
                name={data?.startMoment ? 'task-folder' : 'task-checkmark-undone'}
                width={data?.startMoment ? 20 : 18}
              />
            </Box>
          </HStack>
          <Text color={'#707070'} fontWeight={'semibold'}>
            {!data?.isAllDay ? data?.startTime : ''}
          </Text>
        </HStack>
        <HStack justifyContent={'space-between'}>
          <Text color={'#707070'} numberOfLines={1} ellipsizeMode="tail" w={'2/3'}>
            {data?.title}
          </Text>
          <Text color={'#707070'} fontWeight={'semibold'}>
            {!data?.isAllDay ? data?.endTime : 'All Day'}
          </Text>
        </HStack>
      </VStack>
    </Box>
  );

  return (
    <Box my={5}>
      <Text color={'#585757'} mb={3} alignSelf={'center'} bold>
        CALENDAR
      </Text>
      <Box style={styles.container} paddingX={5} paddingY={3} alignSelf={'center'} bg="#EEEEEE">
        <HStack justifyContent={'space-between'}>
          <Box width={'1/5'}>
            <FastImage
              source={CalendarImage}
              style={{ width: 50, height: 50 }}
              resizeMode={FastImage.resizeMode.contain}
            />
          </Box>
          <VStack width={'4/5'} style={{ gap: 3 }}>
            <Text fontSize={15} color={'#707070'} bold>
              Upcoming Event
            </Text>
            <Text fontWeight={'semibold'} color={'#707070'}>
              {closeEvent?.dueDate ? closeEvent?.formattedEndAt : closeEvent?.formattedStartAt}
            </Text>
            <HStack justifyContent={'space-between'}>
              <Text color={'#707070'} numberOfLines={1} ellipsizeMode="tail" w={'2/3'}>
                {closeEvent?.title}
              </Text>
              <VStack>
                {!closeEvent?.dueDate && (
                  <Text fontWeight={'semibold'} color={'#707070'}>
                    {closeEvent?.startTime}
                  </Text>
                )}
                <Text fontWeight={'semibold'} color={'#707070'}>
                  {closeEvent?.endTime}
                </Text>
              </VStack>
            </HStack>
          </VStack>
        </HStack>
        {datas?.length > 0 && (
          <>
            <HStack justifyContent={'center'} mt={3}>
              <Button
                _pressed={{
                  bg: 'transparent',
                  opacity: 0.8
                }}
                bg="transparent"
                onPress={() => setIsExpand(!isExpand)}
              >
                <Icon name={!isExpand ? 'chevron-down-primary' : 'chevron-up-primary'} />
              </Button>
            </HStack>
            {isExpand ? (
              <FlatList
                keyboardShouldPersistTaps="handled"
                height={250}
                data={datas}
                onEndReachedThreshold={0.1}
                onEndReached={handleEndReached}
                renderItem={({ item, index }) => eventList(item, index)}
                nestedScrollEnabled={true}
              />
            ) : null}
          </>
        )}
      </Box>
    </Box>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '90%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 0.2, // Adjust the shadow intensity (0.0 - 1.0)
    shadowRadius: 2, // Adjust the spread of the shadow
    borderRadius: 12,
    backgroundColor: '#fff', // You may need to specify a background color for the shadow to be visible
    elevation: 9 // For Android, use elevation instead of shadow properties
  }
});

export default DashboardCalendar;
