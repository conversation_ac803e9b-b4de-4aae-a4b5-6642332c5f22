import React, { useCallback, useEffect, useState } from 'react';
import { useSelector } from '@src/store';
import { Box, HStack, Spinner, Text, View, VStack } from 'native-base';
import { StyleSheet, TouchableOpacity } from 'react-native';
import { COLORS } from '@src/constants/colors';
import moment from 'moment';
import { Icon } from '@src/commons';

type Props = {
  fileUrlProgressFinanceJson?: string;
  tabLabel?: string;
};

const DashboardSCurveFinance: React.FC<Props> = props => {
  const project = useSelector(state => state.project);
  const [loading, setLoading] = useState<boolean>(true);
  const [financeData, setFinanceData] = useState<any>([]);
  const [monthIndex, setMonthIndex] = useState<number>(0);

  useEffect(() => {
    setLoading(true);
    try {
      const jsonData = JSON.parse(props.fileUrlProgressFinanceJson || '[]');
      const parsedData = JSON.parse(jsonData);
      setFinanceData(parsedData);
      const filteredPlanned = parsedData.filter(
        (item: any) => item.category === 'PLANNED ( x RM1000 )' && item.year !== 'Invalid date'
      );
      const currentMonthYear = moment().format('MMM-YY');
      const initialMonthIndex = filteredPlanned.findIndex((item: any) => item.year === currentMonthYear);
      setMonthIndex(initialMonthIndex >= 0 ? initialMonthIndex : 0);
    } catch (error) {}
    setLoading(false);
  }, [props.fileUrlProgressFinanceJson]);

  const getProgress = useCallback(
    (type: string) => {
      if (!financeData.length) return '0.00';

      const filteredPlanned = financeData.filter(
        (item: { category: string; year: string }) =>
          item.category === 'PLANNED ( x RM1000 )' && item.year !== 'Invalid date'
      );
      const filteredActual = financeData.filter(
        (item: { category: string; value: null; year: string }) =>
          item.category === 'ACTUAL ( x RM1000 )' && item.value !== null && item.year !== 'Invalid date'
      );

      const planned = filteredPlanned[monthIndex] || {};
      const actual = filteredActual.find((item: { year: any }) => item.year === planned.year) || {};

      switch (type) {
        case 'jadual':
          return planned.value ?? 0;
        case 'sebenar':
          return actual.value ?? 0;
        case 'progress':
          return actual?.value - planned?.value >= 0 ? actual?.value - planned?.value : 0;
        case 'bulan':
          return moment(planned.year, 'MMM-YY').format('MMMM YYYY');
        case 'plannedClaim':
          return planned.plannedClaim ?? 0;
        case 'currentClaim':
          return actual.currentClaim ?? 0;
        case 'days':
          return actual.progressDays ?? 0;
        default:
          return '0.00';
      }
    },
    [financeData, monthIndex]
  );

  if (loading) {
    return <Spinner size="sm" mb={10} />;
  }

  return (
    <VStack alignItems={'center'} my={4} mx={4}>
      <HStack>
        <TouchableOpacity
          style={{ paddingHorizontal: 8, marginRight: 20 }}
          onPress={() => setMonthIndex(Math.max(0, monthIndex - 1))}
        >
          <Icon name={'chevron-left'} fill={COLORS.neutrals.gray90} />
        </TouchableOpacity>
        <Text textAlign={'center'} minWidth={40} mb={5}>
          {getProgress('bulan')}
        </Text>
        <TouchableOpacity
          style={{ paddingHorizontal: 8, marginLeft: 20 }}
          onPress={() =>
            setMonthIndex(
              Math.min(
                financeData.filter((item: any) => item.category === 'PLANNED ( x RM1000 )').length - 1,
                monthIndex + 1
              )
            )
          }
        >
          <Icon name={'chevron-right'} fill={COLORS.neutrals.gray90} />
        </TouchableOpacity>
      </HStack>
      {/* Additional UI components to display the financial data */}

      <View style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', width: '100%' }}>
        <Box style={{ flex: 1, ...styles.container }} mr={2} px={2} py={1} borderWidth={'1px'} borderColor={'#EEE'}>
          <View>
            <Text mb={4}>Planned - Cumulative</Text>
            <Text style={styles.value}>{`RM ${(getProgress('jadual') * 1000)
              ?.toFixed(2)
              ?.replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`}</Text>
          </View>
        </Box>
        <Box style={{ flex: 1, ...styles.container }} ml={2} px={2} py={1} borderWidth={'1px'} borderColor={'#EEE'}>
          <View>
            <Text mb={4}>Actual - Cumulative</Text>
            <Text style={styles.value}>
              {'RM' + (getProgress('sebenar') * 1000)?.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
            </Text>
          </View>
        </Box>
      </View>

      <View
        style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', width: '100%', marginTop: 10 }}
      >
        <Box style={{ flex: 1, ...styles.container }} mr={2} px={2} py={1} borderWidth={'1px'} borderColor={'#EEE'}>
          <View>
            <Text mb={4}>Planned - This Month</Text>
            <Text style={styles.value}>
              {'RM' + (+getProgress('plannedClaim') * 1000)?.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
            </Text>
          </View>
        </Box>
        <Box style={{ flex: 1, ...styles.container }} ml={2} px={2} py={1} borderWidth={'1px'} borderColor={'#EEE'}>
          <View>
            <Text mb={4}>Actual - This Month</Text>
            <Text style={styles.value}>
              {'RM' + (+getProgress('currentClaim') * 1000)?.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
            </Text>
          </View>
        </Box>
      </View>

      <View style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', width: '100%' }}>
        <Box style={{ flex: 1, ...styles.container }} mt={4} px={2} py={1} borderWidth={'1px'} borderColor={'#EEE'}>
          <View>
            <Text
              style={{
                ...styles.progress,
                color: getProgress('progress') > 0 ? '#16a34a' : getProgress('progress') < 0 ? '#dc2626' : ''
              }}
            >
              {'RM' + (+getProgress('progress') * 1000)?.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
            </Text>
            <Text
              mt={0}
              mb={1}
              variant={'bodyReg'}
              style={{
                textAlign: 'center',
                color: getProgress('progress') > 0 ? '#16a34a' : getProgress('progress') < 0 ? '#dc2626' : ''
              }}
            >
              ({getProgress('days') ?? 0} days)
            </Text>
            <Text mt={0} mb={3} variant={'bodyReg'} style={{ textAlign: 'center' }}>
              {getProgress('progress') > 0 ? 'Ahead' : getProgress('progress') == 0 ? 'No progress' : 'Delay'}
            </Text>
          </View>
        </Box>
      </View>
    </VStack>
  );
};

const styles = StyleSheet.create({
  value: {
    color: '#0695D7',
    alignSelf: 'center'
    // lineHeight: 38,
    // fontSize: 38
  },
  progress: {
    color: '#008B0E',
    // lineHeight: 40,
    // fontSize: 40,
    marginTop: 14,
    textAlign: 'center'
  },
  container: {
    width: '90%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    borderRadius: 12,
    backgroundColor: '#fff',
    elevation: 9
  }
});

export default DashboardSCurveFinance;
