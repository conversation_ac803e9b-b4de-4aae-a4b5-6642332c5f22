import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { DashboardNavigatorParams } from '@src/types';
import React from 'react';
import Dashboard from './pages/Dashboard';
import Event from './pages/Event';

const DashboardStack = createNativeStackNavigator<DashboardNavigatorParams>();

const DashboardNavigator: React.FC<any> = () => {
  return (
    <DashboardStack.Navigator initialRouteName="Dashboard" screenOptions={{ headerShown: false }}>
      <DashboardStack.Screen name="Dashboard" component={Dashboard} />
      <DashboardStack.Screen name="Event" component={Event} />
    </DashboardStack.Navigator>
  );
};

export default DashboardNavigator;
