import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { VideoNavigatorParams } from '@src/types';
import React from 'react';
import VideoViewer from './pages/Video';

const VideoStack = createNativeStackNavigator<VideoNavigatorParams>();

const VideoNavigator: React.FC<any> = () => {
  return (
    <VideoStack.Navigator initialRouteName="VideoViewer" screenOptions={{ headerShown: false }}>
      <VideoStack.Screen name="VideoViewer" component={VideoViewer} />
    </VideoStack.Navigator>
  );
};

export default VideoNavigator;
