import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Alert, Platform } from 'react-native';
import { AppBar } from '@src/commons';
import { COLORS } from '@src/constants';
import { RootNavigatorParams, VideoNavigatorParams } from '@src/types';
import { Box } from 'native-base';
import React from 'react';
import Video from 'react-native-video';

type Props = CompositeScreenProps<
  BottomTabScreenProps<VideoNavigatorParams, 'VideoViewer'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const VideoViewer: React.FC<Props> = ({ route, navigation }) => {
  const name = route.params.name;
  const uri = route.params.fileUrl;
  const videoHeight = Platform.OS === 'android' ? '95%' : '89%';

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <AppBar
        goBack
        onGoBack={() => {
          navigation?.goBack?.();
          if (typeof route.params?.onPressBack === 'function') {
            return route.params?.onPressBack?.();
          }
        }}
        barStyle={'dark-content'}
        title={name}
        onPressTitle={() => {
          Alert.alert('File Name', name ?? '', [{ text: 'Dismiss' }]);
        }}
        noRight
      />
      <Video
        source={{ uri: uri }}
        controls={true}
        style={{ height: videoHeight }}
        resizeMode={'contain'}
        fullscreen={true}
      ></Video>
    </Box>
  );
};

export default VideoViewer;
