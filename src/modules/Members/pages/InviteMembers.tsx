import { Footer } from '@commons';
import { pushError, pushSuccess } from '@configs';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ProjectInvitationApiService } from '@src/api/rest';
import { AppBar, Content } from '@src/commons';
import { COLORS } from '@src/constants';
import { SelectInput, TextInput } from '@src/inputs';
import { MemberNavigatorParams, RootNavigatorParams } from '@src/types';
import { Field, FieldArray, Formik, FormikProps } from 'formik';
import _ from 'lodash';
import { Box, Button, HStack } from 'native-base';
import React, { useRef, useState } from 'react';
import { StyleSheet } from 'react-native';

type Props = CompositeScreenProps<
  BottomTabScreenProps<MemberNavigatorParams, 'InviteMembers'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const InviteMembers: React.FC<Props> = ({ navigation }) => {
  const formRef = useRef<FormikProps<any>>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const onInvite = async (values: any) => {
    const data = values?.inviteMembers.filter((inviteMember: any) => {
      return inviteMember.email;
    });

    if (_.isEmpty(data)) {
      return pushError('Please invite at least one user');
    }
    setIsSubmitting(true);
    try {
      await ProjectInvitationApiService.createInvitations({
        body: {
          projectInvitations: data
        }
      });
      pushSuccess('Invitation sent successfully');
    } catch (e) {
      pushError(e);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box flex={1} bg={COLORS.neutrals.white}>
      <AppBar goBack barStyle={'dark-content'} title="Invite members" noRight />
      <Formik onSubmit={onInvite} initialValues={{ inviteMembers: [{ email: '', role: '' }] }} innerRef={formRef}>
        {({ values }) => {
          return (
            <Box flex={1}>
              <Content pt={5}>
                <Box>
                  <FieldArray
                    name="inviteMembers"
                    render={arrayHelpers => (
                      <Box>
                        {_.map(values.inviteMembers, (o, i) => {
                          return (
                            <HStack space={3} key={i}>
                              <Box flex={1} width={190}>
                                <Field
                                  name={`inviteMembers.${i}.email`}
                                  label="Email"
                                  // val={o.email}
                                  component={TextInput}
                                />
                              </Box>

                              <Box flex={1} width={133}>
                                <Field
                                  name={`inviteMembers.${i}.role`}
                                  label="Role"
                                  // val={o.role}
                                  options={_.map(ROLE, o => ({ label: o, value: o }))}
                                  component={SelectInput}
                                />
                              </Box>
                            </HStack>
                          );
                        })}

                        <Button
                          variant="light"
                          mb={10}
                          onPress={() => {
                            arrayHelpers.push({
                              email: '',
                              role: ''
                            });
                          }}
                        >
                          Add more
                        </Button>
                      </Box>
                    )}
                  />
                </Box>
              </Content>
              <Footer>
                <Button
                  variant="primary"
                  isLoading={isSubmitting}
                  onPress={() => {
                    formRef.current?.handleSubmit();
                  }}
                >
                  Send invites
                </Button>
              </Footer>
            </Box>
          );
        }}
      </Formik>
    </Box>
  );
};

const ROLE = ['CanView', 'CanEdit', 'CloudCoordinator'];

const styles = StyleSheet.create({});

export default InviteMembers;
