import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { AppBar } from '@src/commons';
import { COLORS, DIMENS } from '@src/constants';
import { MemberNavigatorParams, RootNavigatorParams } from '@src/types';
import { Box, Button, Divider, FlatList, Flex, HStack, Skeleton, Stack, Text, VStack } from 'native-base';
import React, { useEffect } from 'react';
import { InteractionManager, RefreshControl, StyleSheet, TouchableOpacity } from 'react-native';

type Props = CompositeScreenProps<
  BottomTabScreenProps<MemberNavigatorParams, 'Members'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const Members: React.FC<Props> = ({ navigation }) => {
  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getEditor();
      getViewer();
    });
  }, []);

  const [getEditor, { data, refetch, loading }] = Gql.useProjectUsersLazyQuery({
    variables: {
      filter: {
        role: {
          eq:
            Gql.ProjectUserRoleType.ProjectOwner ||
            Gql.ProjectUserRoleType.CloudCoordinator ||
            Gql.ProjectUserRoleType.CanEdit
        }
      }
    }
  });

  const [getViewer, { data: viewer, refetch: refetchViewer }] = Gql.useProjectUsersLazyQuery({
    variables: {
      filter: {
        role: {
          eq: Gql.ProjectUserRoleType.CanView
        }
      }
    }
  });

  const editorsData = data?.projectUsers.nodes.map((editor: any) => {
    return {
      id: editor.id,
      name: editor.user.name,
      role: editor.role,
      email: editor.user.email,
      phoneNo: editor.user.phoneNo
    };
  });

  const viewerData = viewer?.projectUsers.nodes.map((viewer: any) => {
    return {
      id: viewer.id,
      name: viewer.user.name,
      role: viewer.role,
      email: viewer.user.email,
      phoneNo: viewer.user.phoneNo
    };
  });

  return (
    <Box flex={1} bg={COLORS.primary.light}>
      <AppBar goBack barStyle={'dark-content'} title="Members" noRight />
      <Flex direction="row" style={styles.container}>
        <Button
          style={{ backgroundColor: COLORS.primary[1] }}
          onPress={() => navigation.navigate('InviteMembers' as never)}
        >
          Invite members
        </Button>
      </Flex>

      {loading ? (
        <SkeletonComponent />
      ) : (
        <>
          {/* Editors  */}
          <Box px={7} py={5}>
            <Flex flexDirection="row" justifyContent="space-between" alignItems="center">
              <Text style={styles.titleText}>Editors</Text>
            </Flex>

            <FlatList
              keyboardShouldPersistTaps="handled"
              // contentContainerStyle={{ paddingHorizontal: 20, paddingTop: 20 }}
              data={editorsData}
              keyExtractor={item => item.id}
              refreshControl={<RefreshControl refreshing={loading} onRefresh={refetch} />}
              renderItem={({ item }) => (
                <TouchableOpacity
                  onPress={() => {
                    navigation.push('MembersNav', { screen: 'MembersDetails', params: { id: item.id, refetch } });
                  }}
                >
                  <Box style={styles.box}>
                    <VStack space={2}>
                      <Text>{item.name}</Text>
                      <HStack space={250}>
                        <Text style={styles.allUserText}>{item.role}</Text>
                      </HStack>
                      <HStack>
                        <Text style={styles.allUserText}>{item.email}</Text>
                        <Divider orientation="vertical" mx={3} />
                        <Text style={styles.allUserText}>{item.phoneNo}</Text>
                      </HStack>
                    </VStack>
                  </Box>
                </TouchableOpacity>
              )}
            />
          </Box>
          {/* Viewer  */}
          <Box px={7} py={5}>
            <Flex flexDirection="row" justifyContent="space-between" alignItems="center">
              <Text style={styles.titleText}>Viewers</Text>
            </Flex>

            <FlatList
              // contentContainerStyle={{ paddingHorizontal: 20, paddingTop: 20 }}
              data={viewerData}
              keyExtractor={item => item.id}
              renderItem={({ item }: any) => (
                <TouchableOpacity
                  onPress={() => {
                    navigation.push('MembersNav', { screen: 'MembersDetails', params: { id: item.id, refetchViewer } });
                  }}
                >
                  <Box style={styles.box}>
                    <VStack space={2}>
                      <Text>{item.name}</Text>
                      <HStack space={250}>
                        <Text style={styles.allUserText}>{item.role}</Text>
                      </HStack>
                      <HStack>
                        <Text style={styles.allUserText}>{item.email}</Text>
                        <Divider orientation="vertical" mx={3} />
                        <Text style={styles.allUserText}>{item.phoneNo}</Text>
                      </HStack>
                    </VStack>
                  </Box>
                </TouchableOpacity>
              )}
            />
          </Box>
        </>
      )}
    </Box>
  );
};

const SkeletonComponent: React.FC<any> = () => {
  const SkeletonItem = () => {
    return (
      <VStack mb={5}>
        <Skeleton style={{ width: DIMENS.screenWidth / 1.5, height: 22 }} mb={2} />
        <Skeleton style={{ height: 90, padding: 14, borderRadius: 12 }} mb={2} />
        <Skeleton style={{ height: 90, padding: 14, borderRadius: 12 }} mb={2} />
        <Skeleton style={{ height: 90, padding: 14, borderRadius: 12 }} mb={2} />
        <Skeleton style={{ height: 90, padding: 14, borderRadius: 12 }} mb={5} />
        <Skeleton style={{ width: DIMENS.screenWidth / 1.5, height: 22 }} mb={2} />
        <Skeleton style={{ height: 90, padding: 14, borderRadius: 12 }} mb={2} />
        <Skeleton style={{ height: 90, padding: 14, borderRadius: 12 }} mb={2} />
        <Skeleton style={{ height: 90, padding: 14, borderRadius: 12 }} mb={2} />
        <Skeleton style={{ height: 90, padding: 14, borderRadius: 12 }} mb={2} />
        <VStack space={1}></VStack>
      </VStack>
    );
  };
  return (
    <Stack p={5}>
      <SkeletonItem />
      <SkeletonItem />
    </Stack>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    height: 48,
    justifyContent: 'flex-end',
    padding: 6
  },
  titleText: {
    fontWeight: '600',
    fontSize: 16,
    marginBottom: 10
  },
  box: {
    backgroundColor: '#FFFFFF',
    marginBottom: 20,
    padding: 14,
    borderRadius: 12
  },
  allUserText: {
    fontWeight: '400',
    color: COLORS.neutrals.gray90
  }
});

export default Members;
