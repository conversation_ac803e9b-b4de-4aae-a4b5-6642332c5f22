import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Gql } from '@src/api';
import { AppBar, Content, Footer } from '@src/commons';
import { pushError, pushSuccess } from '@src/configs';
import { COLORS } from '@src/constants';
import { AvatarPicker, PhoneInput, SelectInput, TextInput } from '@src/inputs';
import apolloClient from '@src/lib/apollo';
import { MemberNavigatorParams, RootNavigatorParams } from '@src/types';
import { Field, Formik, FormikProps } from 'formik';
import _ from 'lodash';
import moment from 'moment';
import { Avatar, Box, Button, Divider, Flex, HStack, Text } from 'native-base';
import React, { useEffect, useRef, useState } from 'react';
import { InteractionManager, StyleSheet } from 'react-native';

type Props = CompositeScreenProps<
  BottomTabScreenProps<MemberNavigatorParams, 'MembersDetails'>,
  NativeStackScreenProps<RootNavigatorParams>
>;

const MembersDetails: React.FC<Props> = ({ navigation, route }) => {
  const formRef = useRef<FormikProps<FormValues>>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const memberId = route.params.id;
  const refetch = _.get(route, 'params.refetch', null);

  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true);
    try {
      const { role } = values;
      await apolloClient.mutate<Gql.UpdateOneProjectUserMutation>({
        mutation: Gql.UpdateOneProjectUserDocument,
        variables: {
          input: {
            id: _.toString(memberId),
            update: {
              role
            }
          }
        }
      });
      pushSuccess('Member details updated successfully');
      navigation.goBack();
      refetch();
    } catch (e) {
      pushError(e);
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getMember();
    });
  }, []);

  // Get initial values
  const [getMember, { data, loading }] = Gql.useProjectUserLazyQuery({
    variables: { id: memberId }
  });
  if (loading) return null;

  const initialValues = {
    avatar: data?.projectUser?.user?.avatar ?? '',
    name: data?.projectUser?.user?.name ?? '',
    phoneNo: data?.projectUser?.user?.phoneNo?.replace('+60', '') ?? '',
    email: data?.projectUser?.user?.email ?? '',
    role: data?.projectUser?.role ?? ''
  };

  return (
    <Box flex={1} bg={COLORS.neutrals.white}>
      <AppBar goBack barStyle={'dark-content'} title="Member details" noRight />
      <Formik onSubmit={onSubmit} initialValues={initialValues} innerRef={formRef}>
        {() => {
          return (
            <Box flex={1} height={600}>
              <Content pt={5}>
                <Field autoFocus name="avatar" label="Profile Photo" component={AvatarPicker} />
                <Field autoFocus name="name" label="Name" component={TextInput} />
                <Field autoFocus name="email" label="Email address" component={TextInput} />
                <Field autoFocus name="phoneNo" label="Phone number" component={PhoneInput} />
                <Field
                  autoFocus
                  name="role"
                  label="Role"
                  options={_.map(ROLE, o => ({ label: o, value: o }))}
                  component={SelectInput}
                />
                <Box>
                  <Text style={{ color: COLORS.neutrals.gray90, fontWeight: '400', fontSize: 14 }}>Added By</Text>
                  <HStack alignItems="center" space={2} mt={2} mb={4}>
                    <Avatar
                      size="32px"
                      source={{
                        uri: data?.projectUser?.owner.avatar ?? ''
                      }}
                    />
                    <Text>{data?.projectUser?.owner?.name ?? ''}</Text>
                  </HStack>
                  <Divider />
                  <HStack mt={5} space={4}>
                    <Text style={{ color: COLORS.neutrals.gray90, fontWeight: '400', fontSize: 14 }}>
                      Last modified
                    </Text>
                    <Text style={{ color: COLORS.neutrals.gray90, fontWeight: '400', fontSize: 14 }}>
                      {moment(data?.projectUser?.updatedAt).format('DD MMM YYYY, hh:mma')}
                    </Text>
                  </HStack>
                  <HStack mt={5} space={8} mb={20}>
                    <Text style={{ color: COLORS.neutrals.gray90, fontWeight: '400', fontSize: 14 }}>Created on</Text>
                    <Text style={{ color: COLORS.neutrals.gray90, fontWeight: '400', fontSize: 14 }}>
                      {moment(data?.projectUser?.createdAt).format('DD MMM YYYY, hh:mma')}
                    </Text>
                  </HStack>
                </Box>
              </Content>
              <Footer>
                <Flex direction="row" justifyContent="space-between">
                  <Button
                    style={{}}
                    width={116}
                    variant="delete"
                    isLoading={isSubmitting}
                    onPress={() => {
                      formRef.current?.handleSubmit();
                    }}
                  >
                    Delete
                  </Button>

                  <Button
                    width={203}
                    isLoading={isSubmitting}
                    variant="primary"
                    onPress={() => {
                      formRef.current?.handleSubmit();
                    }}
                  >
                    Save
                  </Button>
                </Flex>
              </Footer>
            </Box>
          );
        }}
      </Formik>
    </Box>
  );
};

const ROLE = ['CanView', 'CanEdit', 'CloudCoordinator', 'ProjectOwner'];

interface FormValues {
  avatar: string;
  name: string;
  phoneNo: string;
  email: string;
  role: string;
}

export default MembersDetails;
