import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { MemberNavigatorParams } from '@src/types';
import Members from './pages/Members';
import InviteMembers from './pages/InviteMembers';
import MembersDetails from './pages/MembersDetails';

const MemberStack = createNativeStackNavigator<MemberNavigatorParams>();

const MemberNavigator: React.FC<any> = () => {
  return (
    <MemberStack.Navigator initialRouteName="Members" screenOptions={{ headerShown: false }}>
      <MemberStack.Screen name="Members" component={Members} />
      <MemberStack.Screen name="InviteMembers" component={InviteMembers} />
      <MemberStack.Screen name="MembersDetails" component={MembersDetails} />
    </MemberStack.Navigator>
  );
};

export default MemberNavigator;
