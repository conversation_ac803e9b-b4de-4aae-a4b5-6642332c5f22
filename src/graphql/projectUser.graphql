fragment ProjectUserFields on ProjectUser {
  addedBy
  deletedAt
  deletedBy
  userId
  id
  projectId
  role
  user {
    ...UpdateMeFields
  }
  owner {
    ...UpdateMeFields
  }
  createdBy
  updatedBy
  createdAt
  updatedAt
}

query projectUser($id: ID!) {
  projectUser(id: $id) {
    ...ProjectUserFields
  }
}

query projectUsers(
  $paging: OffsetPaging
  $filter: ProjectUserFilter
  $sorting: [ProjectUserSort!]
) {
  projectUsers(paging: $paging, filter: $filter, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...ProjectUserFields
    }
  }
}
mutation createOneProjectUser($input: CreateOneProjectUserInput!) {
  createOneProjectUser(input: $input) {
    id
  }
}

mutation updateOneProjectUser($input: UpdateOneProjectUserInput!) {
  updateOneProjectUser(input: $input) {
    id
  }
}

mutation deleteOneProjectUser($id: ID!) {
  deleteOneProjectUser(input: { id: $id }) {
    id
  }
}
