fragment ProjectFields on Project {
  client
  cnsConsultant
  companyId
  completionDate
  contractor
  deputySuperintendent
  description
  environmentConsultant
  managedBy
  mneConsultant
  qsConsultant
  contractValue
  refNo
  startDate
  status
  title
  metTownId
  fileUrlProgress
  fileUrlProgressFinance
  createdAt
  createdBy
  deletedAt
  deletedBy
  id
  updatedAt
  updatedBy
  userId
  owner {
    ...UpdateMeFields
  }
  projectUsers {
    totalCount
    nodes {
      ...ProjectUserFields
    }
  }
  tasks {
    totalCount
    nodes {
      ...TaskFields
    }
  }
  carousels {
    nodes {
      ...ProjectCarouselFields
    }
  }
  company {
    name
  }
}

fragment ProjectCarouselFields on ProjectCarousel {
  fileUrl
  createdAt
  createdBy
  deletedAt
  deletedBy
  id
  name
  projectId
  updatedAt
  updatedBy
}

query getProjects($filter: ProjectFilter, $paging: OffsetPaging, $sorting: [ProjectSort!]) {
  projects(filter: $filter, paging: $paging, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...ProjectFields
    }
  }
}

query getProject($id: ID!) {
  project(id: $id) {
    ...ProjectFields
  }
}

query Projects($filter: ProjectFilter, $paging: OffsetPaging, $sorting: [ProjectSort!]) {
  projects(filter: $filter, paging: $paging, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...ProjectFields
    }
  }
}

query Project($id: ID!) {
  project(id: $id) {
    ...ProjectFields
  }
}

query AllProjects($filter: ProjectFilter, $paging: OffsetPaging, $sorting: [ProjectSort!]) {
  projects(filter: $filter, paging: $paging, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      id
      title
      metTownId
      updatedAt
      carousels {
        nodes {
          ...ProjectCarouselFields
        }
      }
      projectUsers {
        totalCount
        nodes {
          ...ProjectUserFields
        }
      }
      company {
        name
      }
    }
  }
}

mutation createNewProject($input: CreateProjectInputDTO!) {
  createNewProject(input: $input) {
    id
  }
}

mutation updateProject($id: ID!, $input: UpdateProjectInputDTO!) {
  updateOneProject(input: { id: $id, update: $input }) {
    id
  }
}

mutation deleteOneProject($id: ID!) {
  deleteOneProject(input: { id: $id }) {
    id
  }
}
