fragment DrawingLinkAttachmentFields on DrawingLinkAttachment {
  id
  fileUrl
  fileKey
  fileName
  drawingLinkId
}

query getDrawingLinkAttachments(
  $filter: DrawingLinkAttachmentFilter
  $paging: OffsetPaging
  $sorting: [DrawingLinkAttachmentSort!]
) {
  drawingLinkAttachments(filter: $filter, paging: $paging, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...DrawingLinkAttachmentFields
    }
  }
}

query getDrawingLinkAttachment($id: ID!) {
  drawingLinkAttachment(id: $id) {
    ...DrawingLinkAttachmentFields
  }
}

mutation createManyDrawingLinkAttachments($input: CreateManyDrawingLinkAttachmentsInput!) {
  createManyDrawingLinkAttachments(input: $input) {
    ...DrawingLinkAttachmentFields
  }
}

mutation deleteOneDrawingLinkAttachment($id: ID!) {
  deleteOneDrawingLinkAttachment(input: { id: $id }) {
    id
  }
}
