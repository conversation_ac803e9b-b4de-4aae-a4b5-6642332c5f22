fragment EmailFields on Email {
  id
  body
  reference
  subject
  deliveryStatus
  replyAt
  openedAt
  sentAt
  createdAt
  sender{
    id
    email
    name
    avatar
  }
  receivers{
    nodes{
      id
      email
      name
    }
  }
  parsedBody
  assets {
    name
    assetType
    assetKey
    url
  }
}

query getEmails($filter: EmailFilter, $paging: OffsetPaging, $sorting: [EmailSort!]) {
  emails(filter: $filter, paging: $paging, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...EmailFields
    }
  }
}

query getEmail($id: ID!) {
  email(id: $id) {
    ...EmailFields
  }
}

mutation createOneEmail($input: CreateOneEmailInput!) {
  createOneEmail(input: $input) {
    id
  }
}

# delete email
mutation deleteEmail($input: DeleteOneEmailInput!) {
  deleteOneEmail(input: $input) {
    id
  }
}
