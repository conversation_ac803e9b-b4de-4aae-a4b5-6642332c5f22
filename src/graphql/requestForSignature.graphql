fragment RequestForSignatureFields on RequestForSignature {
  id
  createdBy
  updatedBy
  createdAt
  updatedAt
  owner {
    ...UpdateMeFields
  }
  projectDocument {
    ...ProjectDocumentFields
  }
  projectDocumentId
  signBy {
    ...UpdateMeFields
  }
  signById
  status
}
query requestForSignature($id: ID!) {
  requestForSignature(id: $id) {
    ...RequestForSignatureFields
  }
}
query requestForSignatures(
  $paging: OffsetPaging
  $filter: RequestForSignatureFilter
  $sorting: [RequestForSignatureSort!]
) {
  requestForSignatures(paging: $paging, filter: $filter, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...RequestForSignatureFields
    }
  }
}
mutation createOneRequestForSignature($input: CreateOneRequestForSignatureInput!) {
  createOneRequestForSignature(input: $input) {
    id
  }
}

mutation updateOneRequestForSignature($input: UpdateOneRequestForSignatureInput!) {
  updateOneRequestForSignature(input: $input) {
    id
  }
}

mutation deleteOneRequestForSignature($id: ID!) {
  deleteOneRequestForSignature(input: { id: $id }) {
    id
  }
}

mutation approveFormOrUpdateStatus($input: ApproveFormOrUpdateStatusInputDTO!) {
  approveFormOrUpdateStatus(input: $input) {
    id
  }
}
