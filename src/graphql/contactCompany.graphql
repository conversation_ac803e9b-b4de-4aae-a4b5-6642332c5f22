fragment ContactCompanyFields on ContactCompany {
  id
  name
  createdBy
  updatedBy
  createdAt
  updatedAt
}
query contactCompany($id: ID!) {
  contactCompany(id: $id) {
    ...ContactCompanyFields
  }
}
query contactCompanies(
  $paging: OffsetPaging
  $filter: ContactCompanyFilter
  $sorting: [ContactCompanySort!]
) {
  contactCompanies(paging: $paging, filter: $filter, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...ContactCompanyFields
    }
  }
}
mutation createOneContactCompany($input: CreateOneContactCompanyInput!) {
  createOneContactCompany(input: $input) {
    id
  }
}

mutation updateOneContactCompany($input: UpdateOneContactCompanyInput!) {
  updateOneContactCompany(input: $input) {
    id
  }
}

mutation deleteOneContactCompany($id: ID!) {
  deleteOneContactCompany(input: { id: $id }) {
    id
  }
}
