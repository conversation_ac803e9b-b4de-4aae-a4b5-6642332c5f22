fragment WorkspaceAttachmentFields on WorkspaceAttachment {
  id
  fileUrl
  name
  type
  projectDocumentId
  createdBy
  updatedBy
  createdAt
  updatedAt
}
query workspaceAttachment($id: ID!) {
  workspaceAttachment(id: $id) {
    ...WorkspaceAttachmentFields
  }
}
query workspaceAttachments(
  $paging: OffsetPaging
  $filter: WorkspaceAttachmentFilter
  $sorting: [WorkspaceAttachmentSort!]
) {
  workspaceAttachments(paging: $paging, filter: $filter, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...WorkspaceAttachmentFields
    }
  }
}
mutation createOneWorkspaceAttachment($input: CreateOneWorkspaceAttachmentInput!) {
  createOneWorkspaceAttachment(input: $input) {
    id
  }
}

mutation createManyWorkspaceAttachments($input: CreateManyWorkspaceAttachmentsInput!) {
  createManyWorkspaceAttachments(input: $input) {
    ...WorkspaceAttachmentFields
  }
}

mutation updateOneWorkspaceAttachment($input: UpdateOneWorkspaceAttachmentInput!) {
  updateOneWorkspaceAttachment(input: $input) {
    id
  }
}

mutation deleteOneWorkspaceAttachment($id: ID!) {
  deleteOneWorkspaceAttachment(input: { id: $id }) {
    id
  }
}
