fragment ProjectDocumentFields on ProjectDocument {
  formCategory {
    ...FormCategoryFields
  }
  owner {
    ...UpdateMeFields
  }
  isBimPhoto
  requestForSignatures {
    signById
    ownerId
    status
    id
    assigneeNo
    createdAt
    signBy {
      name
      id
      avatar
    }
  }
  workspaceGroup {
    name
    id
    workspaceGroupId
    parent {
      id
      name
      workspaceGroupUsers {
        id
        userId
      }
    }
  }
  workspaceAttachments {
    ...WorkspaceAttachmentFields
  }
  workspacePhotos {
    ...WorkspacePhotoFields
  }
  workspaceCCs {
    ...WorkspaceCCFields
  }
  workspaceDocuments {
    id
    projectDocumentId
    name
    fileUrl
    type
    category
    documentId
  }
  id
  addedBy
  category
  fileSize
  fileUrl
  versionName
  allFormCode
  fileSystemType
  projectDocumentId
  projectId
  formCategoryId
  isDocsStored
  name
  workspaceGroupId
  xfdf
  description
  status
  type
  uploadLatitude
  uploadLongitude
  uploadAddress
  videoThumbnail
  submittedAt
  createdBy
  updatedBy
  createdAt
  updatedAt
  driveType
  autoDeskMetadata
  projectDocumentUsers {
    id
    type
  }
  currentUserId
  workflow
}

query projectDocument($id: ID!) {
  projectDocument(id: $id) {
    ...ProjectDocumentFields
  }
}

query getNextDoc($id: ID!) {
  projectDocument(id: $id) {
    id
    projectDocumentId
    drawingRevisions {
      fileName
      fileUrl
      versionName
      projectDocumentId
      xfdf
      id
      version
    }
  }
}

query getProjectDocuments($paging: OffsetPaging, $filter: ProjectDocumentFilter, $sorting: [ProjectDocumentSort!]) {
  getProjectDocuments(paging: $paging, filter: $filter, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    nodes {
      ...ProjectDocumentFields
    }
  }
}

query getProjectDocument($id: ID!) {
  getProjectDocument(id: $id) {
    ...ProjectDocumentFields
  }
}

query projectDocuments($paging: OffsetPaging, $filter: ProjectDocumentFilter, $sorting: [ProjectDocumentSort!]) {
  projectDocuments(paging: $paging, filter: $filter, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...ProjectDocumentFields
    }
  }
}

query getCorrespondenseIn {
  getCorrespondenceIn {
    ...ProjectDocumentFields
  }
}

query getCorrespondenseOut {
  getCorrespondenceOut {
    ...ProjectDocumentFields
  }
}

query getAllFormsDocuments($paging: OffsetPaging, $filter: ProjectDocumentFilter, $sorting: [ProjectDocumentSort!]) {
  projectDocuments(paging: $paging, filter: $filter, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      id
      name
      type
      allFormCode
      status
      category
      fileUrl
      groupCode
      isDocsStored
      workflow
      xfdf
      addedBy
      requestForSignatures {
        assigneeNo
        signBy {
          id
          avatar
        }
      }
      workspaceGroup {
        name
        code
        parent {
          code
          name
        }
      }
    }
  }
}

query getWorkspaceDocuments($paging: OffsetPaging, $filter: ProjectDocumentFilter, $sorting: [ProjectDocumentSort!]) {
  getWorkSpaceAllForm(paging: $paging, filter: $filter, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      id
      name
      type
      fileSystemType
      category
      addedBy
      fileSize
      createdAt
      updatedAt
      fileUrl
      driveType
      isDocsStored
      status
      allFormCode
      xfdf
      owner {
        id
      }
      groupCode
      requestForSignatures {
        signById
        ownerId
        status
        id
        createdAt
        signBy {
          name
          id
          avatar
        }
      }
      workspaceGroup {
        name
        id
        code
        parent {
          code
          workspaceGroupUsers {
            id
            userId
          }
        }
      }
      currentUserId
      workflow
      comments {
        id
      }
    }
  }
}

query getProjectDocumentsList($paging: OffsetPaging, $filter: ProjectDocumentFilter, $sorting: [ProjectDocumentSort!]) {
  getProjectDocuments(paging: $paging, filter: $filter, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    nodes {
      id
      name
      type
      updatedAt
      fileSize
      fileKey
      category
      fileUrl
      owner {
        id
        name
        avatar
      }
      projectDocumentUsers {
        id
        type
      }
      driveType
      projectDocumentId
      drawingRevisions {
        fileName
        id
        versionName
        version
        fileUrl
        projectDocumentId
        xfdf
      }
      project {
        id
        title
        carousels {
          nodes {
            ...ProjectCarouselFields
          }
        }
      }
      projectId
    }
  }
}

query getDrawingsPdfTron($input: GetDrawingsPdfTronInput!) {
  getDrawingsPdfTron(input: $input) {
    nextId
    previousId
  }
}

mutation createOneProjectDocument($input: CreateOneProjectDocumentInput!) {
  createOneProjectDocument(input: $input) {
    ...ProjectDocumentFields
  }
}
mutation createManyProjectDocuments($input: CreateManyProjectDocumentsInput!) {
  createManyProjectDocuments(input: $input) {
    ...ProjectDocumentFields
  }
}

mutation updateOneProjectDocument($input: UpdateOneProjectDocumentInput!) {
  updateOneProjectDocument(input: $input) {
    id
  }
}

mutation deleteOneProjectDocument($id: ID!) {
  deleteOneProjectDocument(input: { id: $id }) {
    id
  }
}

mutation deleteProjectDocuments($input: DeleteProjectDocumentInputDTO!) {
  deleteProjectDocuments(input: $input)
}

mutation duplicateEditedStandardForm($id: Float!, $xfdf: String!) {
  duplicateEditedStandardForm(input: { id: $id, xfdf: $xfdf }) {
    id
  }
}

mutation updateWorkspaceWorkflow($input: UpdateWorkspaceFlowInputDTO!) {
  updateWorkspaceWorkflow(input: $input) {
    workflow
  }
}

mutation approveOrRejectWorkspace($input: ApproveOrRejectWorkspaceInputDTO!) {
  approveOrRejectWorkspace(input: $input) {
    id
  }
}

mutation updateDynamicWorkspace($input: UpdateDynamicWorkspaceInputDTO!) {
  updateDynamicWorkspace(input: $input) {
    id
  }
}

mutation updateAmendToOwner($id: Float!) {
  amendToOwner(id: $id) {
    id
  }
}
