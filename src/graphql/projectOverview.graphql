fragment ProjectOverviewFields on ProjectOverview {
  createdAt
  createdBy
  deletedAt
  deletedBy
  id
  updatedAt
  updatedBy
  projectId
  key
  value
}

query projectOverviews($filter: ProjectOverviewFilter, $paging: OffsetPaging, $sorting: [ProjectOverviewSort!]) {
  projectOverviews(filter: $filter, paging: $paging, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...ProjectOverviewFields
    }
  }
}
