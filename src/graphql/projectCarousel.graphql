fragment ProjectCarouselFields on ProjectCarousel {
  fileUrl
  createdAt
  createdBy
  deletedAt
  deletedBy
  id
  name
  projectId
  updatedAt
  updatedBy
}

query getProjectCarousel($id: ID!) {
  projectCarousel(id: $id) {
    ...ProjectCarouselFields
  }
}

mutation createOneProjectCarousel($input: CreateProjectCarouselInputDTO!) {
  createOneProjectCarousel(input: { projectCarousel: $input }) {
    id
  }
}

mutation updateOneProjectCarousel($input: UpdateOneProjectCarouselInput!) {
  updateOneProjectCarousel(input: $input) {
    id
  }
}
