fragment ProjectGroupFields on ProjectGroup {
  id
  projectId
  title
  createdBy
  updatedBy
  createdAt
  updatedAt
  deletedAt
  deletedBy
  children {
    id
    title
    tasks {
      id
      status
    }
  }
}

query getProjectGroups(
  $filter: ProjectGroupWithChildrenFilter
  $paging: OffsetPaging
  $sorting: [ProjectGroupWithChildrenSort!]
) {
  getProjectGroups(filter: $filter, paging: $paging, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    nodes {
      totalCount
      openCount
      inProgressCount
      holdCount
      closedCount
      id
      projectId
      title
      createdBy
      updatedBy
      createdAt
      updatedAt
      children {
        id
        title
        totalCount
        openCount
        inProgressCount
        holdCount
        closedCount
        tasks {
          id
          status
        }
      }
    }
    totalCount
  }
}

mutation createOneProjectGroup($input: CreateOneProjectGroupInput!) {
  createOneProjectGroup(input: $input) {
    ...ProjectGroupFields
  }
}

mutation updateOneProjectGroup($input: UpdateOneProjectGroupInput!) {
  updateOneProjectGroup(input: $input) {
    ...ProjectGroupFields
  }
}
