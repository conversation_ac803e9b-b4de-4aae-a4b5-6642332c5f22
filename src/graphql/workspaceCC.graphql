fragment WorkspaceCCFields on WorkspaceCC {
  id
  ccId
  ccUser {
    ...UpdateMeFields
  }
  ccOwner {
    ...UpdateMeFields
  }
  ownerId
  projectDocumentId
  status
  createdBy
  updatedBy
  createdAt
  updatedAt
}
query workspaceCC($id: ID!) {
  workspaceCC(id: $id) {
    ...WorkspaceCCFields
  }
}
query workspaceCCS(
  $paging: OffsetPaging
  $filter: WorkspaceCCFilter
  $sorting: [WorkspaceCCSort!]
) {
  workspaceCCS(paging: $paging, filter: $filter, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...WorkspaceCCFields
    }
  }
}
mutation createOneWorkspaceCC($input: CreateOneWorkspaceCCInput!) {
  createOneWorkspaceCC(input: $input) {
    id
  }
}

mutation updateOneWorkspaceCC($input: UpdateOneWorkspaceCCInput!) {
  updateOneWorkspaceCC(input: $input) {
    id
  }
}

mutation deleteOneWorkspaceCC($id: ID!) {
  deleteOneWorkspaceCC(input: { id: $id }) {
    id
  }
}
