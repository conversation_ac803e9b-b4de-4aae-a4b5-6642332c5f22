fragment DocumentCommentFields on ProjectDocumentComment {
  createdAt
  createdBy
  deletedAt
  deletedBy
  id
  message
  projectDocument {
    name
  }
  projectDocumentId
  updatedAt
  updatedBy
  user {
    name
    avatar
  }
  userId
}

query getDocumentComments(
  $filter: ProjectDocumentCommentFilter
  $paging: OffsetPaging
  $sorting: [ProjectDocumentCommentSort!]
) {
  projectDocumentComments(filter: $filter, paging: $paging, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...DocumentCommentFields
    }
  }
}

mutation createProjectDocumentComment($input: CreateProjectDocumentCommentInputDTO!) {
  createProjectDocumentComment(input: $input){
    id
  }
}

mutation deleteProjectDocumentComment($input: DeleteOneProjectDocumentCommentInput!) {
  deleteOneProjectDocumentComment(input: $input){
    id
  }
}