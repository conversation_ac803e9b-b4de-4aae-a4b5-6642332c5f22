fragment ActivityLogFields on AuditLog {
  action
  content
  createdAt
  createdBy
  deletedAt
  deletedBy
  id
  module
  projects {
    id
    companyId
    title
  }
  projectId
  taskId
  resourceId
  updatedAt
  updatedBy
  user {
    id
    name
    avatar
  }
  userId
}

query getActivityLogs($filter: AuditLogFilter, $paging: OffsetPaging, $sorting: [AuditLogSort!]) {
  auditLogs(filter: $filter, paging: $paging, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...ActivityLogFields
    }
  }
}

mutation createOneAuditLog($input: CreateOneAuditLogInput!) {
  createOneAuditLog(input: $input) {
    id
  }
}

mutation createManyAuditLogs($input: CreateManyAuditLogsInput!) {
  createManyAuditLogs(input: $input) {
    id
  }
}
