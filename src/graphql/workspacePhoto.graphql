fragment WorkspacePhotoFields on WorkspacePhoto {
  id
  fileUrl
  name
  type
  projectDocumentId
  createdBy
  updatedBy
  createdAt
  updatedAt
}
query workspacePhoto($id: ID!) {
  workspacePhoto(id: $id) {
    ...WorkspacePhotoFields
  }
}
query workspacePhotos($paging: OffsetPaging, $filter: WorkspacePhotoFilter, $sorting: [WorkspacePhotoSort!]) {
  workspacePhotos(paging: $paging, filter: $filter, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...WorkspacePhotoFields
    }
  }
}
mutation createOneWorkspacePhoto($input: CreateOneWorkspacePhotoInput!) {
  createOneWorkspacePhoto(input: $input) {
    ...WorkspacePhotoFields
  }
}

mutation createManyWorkspacePhotos($input: CreateManyWorkspacePhotosInput!) {
  createManyWorkspacePhotos(input: $input) {
    ...WorkspacePhotoFields
  }
}

mutation updateOneWorkspacePhoto($input: UpdateOneWorkspacePhotoInput!) {
  updateOneWorkspacePhoto(input: $input) {
    id
  }
}

mutation deleteOneWorkspacePhoto($id: ID!) {
  deleteOneWorkspacePhoto(input: { id: $id }) {
    id
  }
}
