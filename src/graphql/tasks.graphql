fragment TaskFields on Task {
  createdAt
  createdBy
  deletedAt
  deletedBy
  description
  dueDate
  id
  projectId
  status
  ownerId
  taskCode
  title
  proposedStatus
  memoUrl
  previewMemoUrl
  isMemoReceive
  updatedAt
  updatedBy
  assignees {
    nodes {
      ...ProjectUserFields
    }
  }
  copies {
    nodes {
      ...ProjectUserFields
    }
  }
  attachments {
    nodes {
      ...TasksAttachmentFields
    }
  }
  medias {
    nodes {
      ...TasksMediaFields
    }
  }
  documents {
    nodes {
      ...ProjectDocumentFields
    }
  }
  owner {
    ...UpdateMeFields
  }
  group {
    ...ProjectGroupFields
  }
}

fragment TaskCommentFields on TaskComment {
  createdAt
  createdBy
  deletedAt
  deletedBy
  id
  userId
  message
  taskId
  updatedAt
  updatedBy
  task {
    projectId
  }
  user {
    name
    avatar
  }
}

fragment TasksAttachmentFields on TasksAttachment {
  fileUrl
  createdAt
  createdBy
  deletedAt
  deletedBy
  id
  name
  taskId
  updatedAt
  updatedBy
  type
}

fragment TasksMediaFields on TasksMedia {
  fileUrl
  createdAt
  createdBy
  deletedAt
  deletedBy
  id
  name
  taskId
  updatedAt
  updatedBy
  type
}

query getTasks($filter: TaskFilter, $paging: OffsetPaging, $sorting: [TaskSort!]) {
  tasks(filter: $filter, paging: $paging, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...TaskFields
    }
  }
}

query TaskComments($filter: TaskCommentFilter, $paging: OffsetPaging, $sorting: [TaskCommentSort!]) {
  taskComments(filter: $filter, paging: $paging, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...TaskCommentFields
    }
  }
}

query getTasksAttachments($filter: TasksAttachmentFilter, $paging: OffsetPaging, $sorting: [TasksAttachmentSort!]) {
  tasksAttachments(filter: $filter, paging: $paging, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...TasksAttachmentFields
    }
  }
}

query getTasksMedias($filter: TasksMediaFilter, $paging: OffsetPaging, $sorting: [TasksMediaSort!]) {
  tasksMedias(filter: $filter, paging: $paging, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...TasksMediaFields
    }
  }
}

query getTask($id: ID!) {
  task(id: $id) {
    createdBy
    description
    dueDate
    id
    projectId
    status
    ownerId
    taskCode
    memoUrl
    isMemoReceive
    title
    proposedStatus
    isUrgent
    previewMemoUrl
    updatedAt
    updatedBy
    assignees {
      nodes {
        id
        user {
          id
          name
          avatar
        }
      }
    }
    copies {
      nodes {
        id
        user {
          id
          name
          avatar
        }
      }
    }
    attachments {
      nodes {
        id
        name
        fileUrl
        type
      }
    }
    medias {
      nodes {
        id
        name
        fileUrl
        type
      }
    }
    documents {
      nodes {
        id
        name
        type
      }
    }
    owner {
      id
      name
      avatar
    }
    group {
      id
      title
    }
  }
}

query getTaskComment($id: ID!) {
  taskComment(id: $id) {
    ...TaskCommentFields
  }
}

query getTasksAttachment($id: ID!) {
  tasksAttachment(id: $id) {
    ...TasksAttachmentFields
  }
}

query getTasksMedia($id: ID!) {
  tasksMedia(id: $id) {
    ...TasksMediaFields
  }
}

query getUncompletedTasks($id: ID!) {
  task(id: $id) {
    ...TaskFields
  }
}

query getTasksAssignedToMe($id: ID!) {
  task(id: $id) {
    ...TaskFields
  }
}

query getUncompletedTasksAssignedToMe($id: ID!) {
  task(id: $id) {
    ...TaskFields
  }
}

query getTaskLists($filter: TaskFilter, $paging: OffsetPaging, $sorting: [TaskSort!]) {
  tasks(filter: $filter, paging: $paging, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      id
      title
      dueDate
      taskCode
      status
      description
      ownerId
      isUrgent
      memoUrl
      proposedStatus
      owner {
        id
        name
        avatar
      }
      assignees {
        nodes {
          id
          user {
            id
            name
            avatar
          }
        }
      }
    }
  }
}

query getTaskEventList($filter: TaskFilter, $paging: OffsetPaging, $sorting: [TaskSort!]) {
  tasks(filter: $filter, paging: $paging, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      id
      title
      dueDate
      assignees {
        nodes {
          id
          userId
        }
      }
    }
  }
}

query getTasksMemoUrl($filter: TaskFilter, $paging: OffsetPaging, $sorting: [TaskSort!]) {
  tasks(filter: $filter, paging: $paging, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      id
      title
      memoUrl
    }
  }
}

mutation createTask($input: CreateTaskInputDTO!) {
  createOneTask(input: { task: $input }) {
    id
  }
}
mutation createTaskComment($input: CreateTaskCommentInputDTO!) {
  createOneTaskComment(input: { taskComment: $input }) {
    id
  }
}

mutation createComment($input: CreateTaskCommentInputDTO!) {
  createComment(input: $input) {
    id
  }
}

mutation createTasksAttachment($input: CreateTasksAttachmentInputDTO!) {
  createOneTasksAttachment(input: { tasksAttachment: $input }) {
    id
  }
}

mutation createManyTasksAttachments($input: CreateManyTasksAttachmentsInput!) {
  createManyTasksAttachments(input: $input) {
    id
    name
    fileUrl
    type
  }
}

mutation addMediasToTask($input: AddMediasToTaskInput!) {
  addMediasToTask(input: $input) {
    id
  }
}

mutation updateOneTask($id: ID!, $input: UpdateTaskInputDTO!) {
  updateOneTask(input: { id: $id, update: $input }) {
    id
  }
}
mutation updateTaskComment($id: ID!, $input: UpdateTaskCommentInputDTO!) {
  updateOneTaskComment(input: { id: $id, update: $input }) {
    id
  }
}
mutation updateTasksAttachment($id: ID!, $input: UpdateTasksAttachmentInputDTO!) {
  updateOneTasksAttachment(input: { id: $id, update: $input }) {
    id
  }
}

mutation deleteOneTask($id: ID!) {
  deleteOneTask(input: { id: $id }) {
    id
  }
}
mutation deleteTaskComment($id: ID!) {
  deleteOneTaskComment(input: { id: $id }) {
    id
  }
}
mutation deleteTasksAttachment($id: ID!) {
  deleteOneTasksAttachment(input: { id: $id }) {
    id
  }
}

mutation removeDocumentsFromTask($id: ID!, $relationIds: [ID!]!) {
  removeDocumentsFromTask(input: { id: $id, relationIds: $relationIds }) {
    id
  }
}

mutation addAssigneesToTask($id: ID!, $userId: [ID!]!) {
  addAssigneesToTask(input: { id: $id, relationIds: $userId }) {
    id
  }
}

mutation removeAssigneesFromTask($id: ID!, $userId: [ID!]!) {
  removeAssigneesFromTask(input: { id: $id, relationIds: $userId }) {
    id
  }
}

mutation setAssigneesOnTask($id: ID!, $userId: [ID!]!) {
  setAssigneesOnTask(input: { id: $id, relationIds: $userId }) {
    id
  }
}

mutation assignTask($taskId: ID!, $projectUserIds: [ID!]!) {
  assignTask(input: { projectUserIds: $projectUserIds, taskId: $taskId }) {
    id
  }
}

mutation assignCcTask($taskId: ID!, $projectUserIds: [ID!]!) {
  assignCcTask(input: { projectUserIds: $projectUserIds, taskId: $taskId }) {
    id
  }
}

mutation deleteOneProjectGroup($id: ID!) {
  deleteOneProjectGroup(input: { id: $id }) {
    id
  }
}

mutation assignMediaTask($taskId: ID!, $medias: [CreateTasksMediaInputDTO!]!) {
  assignMediaTask(input: { medias: $medias, taskId: $taskId }) {
    id
    medias {
      nodes {
        id
        name
        fileUrl
        type
      }
    }
  }
}

mutation deleteOneTasksMedia($id: ID!) {
  deleteOneTasksMedia(input: { id: $id }) {
    id
  }
}

mutation deleteOneAttachment($id: Float!) {
  deleteOneAttachment(id: $id) {
    id
  }
}

mutation updateTaskStatus($input: UpdateTaskStatusInputDTO!) {
  updateOneTaskStatus(input: $input) {
    id
  }
}

mutation generateMemo($input: GenerateMemoInputDTO!) {
  generateMemo(input: $input) {
    id
    previewMemoUrl
  }
}

mutation receiveMemo($input: ReceiveMemoDTO!) {
  receiveMemo(input: $input) {
    id
  }
}