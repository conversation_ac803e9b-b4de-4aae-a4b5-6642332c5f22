fragment DrawingRevisionFields on DrawingRevision {
  id
  projectDocumentId
  fileUrl
  fileName
  versionName
  status
  version
  notes
  xfdf
}

query getDrawingRevisions($filter: DrawingRevisionFilter, $paging: OffsetPaging, $sorting: [DrawingRevisionSort!]) {
  drawingRevisions(filter: $filter, paging: $paging, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...DrawingRevisionFields
    }
  }
}

query getDrawingRevision($id: ID!) {
  drawingRevision(id: $id) {
    ...DrawingRevisionFields
  }
}

mutation createDrawingRevision($input: CreateOneDrawingRevisionInput!) {
  createOneDrawingRevision(input: $input) {
    id
  }
}

mutation updateOneDrawingRevision($input: UpdateOneDrawingRevisionInput!) {
  updateOneDrawingRevision(input: $input) {
    id
  }
}

mutation deleteOneDrawingRevision($input: DeleteOneDrawingRevisionInput!) {
  deleteOneDrawingRevision(input: $input) {
    id
  }
}
