fragment FormCategoryFields on FormCategory {
  id
  name
  userId
  owner {
    name
    avatar
  }
  createdBy
  updatedBy
  createdAt
  updatedAt
}

query formCategory($id: ID!) {
  formCategory(id: $id) {
    ...FormCategoryFields
  }
}

query formCategories($paging: OffsetPaging, $filter: FormCategoryFilter, $sorting: [FormCategorySort!]) {
  formCategories(paging: $paging, filter: $filter, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...FormCategoryFields
    }
  }
}

mutation createOneFormCategory($input: CreateOneFormCategoryInput!) {
  createOneFormCategory(input: $input) {
    id
  }
}

mutation updateOneFormCategory($input: UpdateOneFormCategoryInput!) {
  updateOneFormCategory(input: $input) {
    id
  }
}

mutation deleteOneFormCategory($id: ID!) {
  deleteOneFormCategory(input: { id: $id }) {
    id
  }
}
