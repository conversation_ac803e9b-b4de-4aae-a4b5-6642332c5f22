fragment ProjectInvitationFields on ProjectInvitation {
  id
  companyId
  email
  expireAt
  invitationRef
  isAccepted
  role
  projectId
  projectTitle
  createdBy
  updatedBy
  createdAt
  updatedAt
}

query projectInvitation($id: ID!) {
  projectInvitation(id: $id) {
    ...ProjectInvitationFields
  }
}

query projectInvitations(
  $paging: OffsetPaging
  $filter: ProjectInvitationFilter
  $sorting: [ProjectInvitationSort!]
) {
  projectInvitations(paging: $paging, filter: $filter, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...ProjectInvitationFields
    }
  }
}

mutation createOneProjectInvitation($input: CreateOneProjectInvitationInput!) {
  createOneProjectInvitation(input: $input) {
    id
  }
}

mutation updateOneProjectInvitation($input: UpdateOneProjectInvitationInput!) {
  updateOneProjectInvitation(input: $input) {
    id
  }
}

mutation deleteOneProjectInvitation($id: ID!) {
  deleteOneProjectInvitation(input: { id: $id }) {
    id
  }
}
