fragment MobileVersionFields on MobileVersion {
  createdAt
  createdBy
  deletedAt
  deletedBy
  id
  updatedAt
  updatedBy
  platformName
  buildCode
  versionCode
}

query getMobileVersions($filter: MobileVersionFilter, $paging: OffsetPaging, $sorting: [MobileVersionSort!]) {
  mobileVersions(filter: $filter, paging: $paging, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...MobileVersionFields
    }
  }
}
