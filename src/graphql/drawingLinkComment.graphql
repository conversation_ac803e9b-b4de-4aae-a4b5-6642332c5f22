fragment DrawingLinkCommentFields on Drawing<PERSON>inkComment {
  id
  message
  drawingLinkId
  user {
    avatar
    name
  }
}

query getDrawingLinkComments(
  $filter: DrawingLinkCommentFilter
  $paging: OffsetPaging
  $sorting: [DrawingLinkCommentSort!]
) {
  drawingLinkComments(filter: $filter, paging: $paging, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...DrawingLinkCommentFields
    }
  }
}

query getDrawingLinkComment($id: ID!) {
  drawingLinkComment(id: $id) {
    ...DrawingLinkCommentFields
  }
}

mutation createOneDrawingLinkComment($input: CreateOneDrawingLinkCommentInput!) {
  createOneDrawingLinkComment(input: $input) {
    id
  }
}

mutation deleteDrawingLinkComment($input: DeleteOneDrawingLinkCommentInput!) {
  deleteOneDrawingLinkComment(input: $input) {
    id
  }
}
