fragment UpdateMeFields on User {
  appleId
  avatar
  company {
    name
    ownerId
    logoUrl
    companySubscriptions {
      nodes {
        subscriptionEndDate
        subscriptionPackage {
          title
          amount
          availableDuration
        }
      }
    }
    stampUrl
    id
  }
  companies {
    edges {
      node {
        ...CompanyFields
      }
    }
  }
  timezone {
    id
    name
    value
  }
  companyId
  companyOrigin
  createdAt
  createdBy
  deletedAt
  deletedBy
  email
  facebookId
  googleId
  id
  isEmailVerified
  isFirstTimeLogin
  isReadChangeLog
  isReadChangeLogMobile
  lastLogin
  name
  phoneNo
  position
  reportTo
  stampUrl
  signUrl
  type
  enableTwoFA
  updatedAt
  updatedBy
  timezone {
    id
    name
    value
  }
}

query getUserMe {
  getUserMe {
    ...UpdateMeFields
  }
}

mutation updateUserMe($input: UpdateUserInputDTO!) {
  updateUserMe(input: $input) {
    email
  }
}

mutation updateMyPassword($input: UpdateUserPasswordDTO!) {
  updateMyPassword(input: $input) {
    email
  }
}

mutation deleteOneUser($id: ID!) {
  deleteOneUser(input: { id: $id }) {
    id
  }
}

mutation generateOtp($input: GenerateOtpInput!) {
  generateOtp(input: $input)
}

mutation verifyOtp($input: VerifyOtpInput!) {
  verifyOtp(input: $input)
}
