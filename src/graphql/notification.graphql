fragment NotificationFields on NotificationTransaction {
  actionName
  actor {
    ...UpdateMeFields
  }
  actorId
  actionType
  createdAt
  createdBy
  deeplink
  deletedAt
  deletedBy
  id
  read
  thumbnail
  title
  type
  updatedAt
  updatedBy
  userId
  mobileDeeplink
}

query getNotifications(
  $filter: NotificationTransactionFilter
  $paging: OffsetPaging
  $sorting: [NotificationTransactionSort!]
) {
  notificationTransactions(filter: $filter, paging: $paging, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...NotificationFields
    }
  }
}

mutation UpdateNotificationTransaction($input: UpdateOneNotificationTransactionInput!) {
  updateOneNotificationTransaction(input: $input) {
    id
  }
}
