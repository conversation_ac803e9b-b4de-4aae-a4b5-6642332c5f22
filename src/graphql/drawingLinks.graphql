fragment DrawingLinksFields on DrawingLink {
  id
  annotationId
  name
  projectDocumentId
  description
  category
  drawingLinkAttachments {
    nodes {
      id
      fileUrl
      fileKey
      fileName
      drawingLinkId
    }
  }
  drawingLinkDocuments {
    name
    id
    category
    fileSize
    fileUrl
    fileSystemType
    projectDocumentId
    updatedAt
    type
  }
}

query getDrawingLinks($filter: DrawingLinkFilter, $paging: OffsetPaging, $sorting: [DrawingLinkSort!]) {
  drawingLinks(filter: $filter, paging: $paging, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...DrawingLinksFields
    }
  }
}

query getDrawingLinksByAnnotId($input: GetDrawingLinksInput!) {
  getDrawingLinksByAnnotId(input: $input) {
    ...DrawingLinksFields
  }
}

mutation updateDrawingLink($input: UpdateOneDrawingLinkInput!) {
  updateOneDrawingLink(input: $input) {
    id
  }
}