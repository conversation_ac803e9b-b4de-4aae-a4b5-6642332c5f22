fragment WorkspaceGroupFields on WorkspaceGroup {
  id
  name
  projectId
  createdBy
  updatedBy
  createdAt
  updatedAt
  submittedCount
  workspaceGroupUsers {
    id
    userId
    user {
      id
      name
      email
      avatar
    }
  }
  inReviewCount
  approvedCount
  rejectedCount
  totalCount
}

query workspaceGroup($id: ID!) {
  workspaceGroup(id: $id) {
    ...WorkspaceGroupFields
  }
}

query getWorkspaceGroups(
  $paging: OffsetPaging
  $filter: WorkspaceGroupWithChildrenFilter
  $sorting: [WorkspaceGroupWithChildrenSort!]
) {
  getWorkspaceGroups(paging: $paging, filter: $filter, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      id
      name
      projectId
      createdBy
      updatedBy
      createdAt
      updatedAt
      submittedCount
      inReviewCount
      approvedCount
      rejectedCount
      inProgressCount
      amendCount
      pendingCount
      totalCount
      workspaceGroupId
      code
      workspaceGroupUsers {
        id
        userId
        user {
          id
          name
          email
          avatar
        }
      }
      children {
        id
        # parent {
        #   workspaceGroupUsers {
        #     userId
        #   }
        # }
        name
        workspaceGroupId
        submittedCount
        inReviewCount
        approvedCount
        rejectedCount
        inProgressCount
        amendCount
        pendingCount
        totalCount
      }
    }
  }
}

mutation createOneWorkspaceGroup($input: CreateOneWorkspaceGroupInput!) {
  createOneWorkspaceGroup(input: $input) {
    id
  }
}

mutation updateOneWorkspaceGroup($input: UpdateOneWorkspaceGroupInput!) {
  updateOneWorkspaceGroup(input: $input) {
    id
  }
}

mutation deleteOneWorkspaceGroup($id: ID!) {
  deleteOneWorkspaceGroup(input: { id: $id }) {
    id
  }
}

mutation createWorkspaceGroup($input: CreateWorkspaceGroupInputDTO!) {
  createWorkspaceGroup(input: $input) {
    ...WorkspaceGroupFields
  }
}

mutation updateWorkspaceGroup($input: UpdateWorkspaceGroupInputDTO!) {
  updateWorkspaceGroup(input: $input) {
    id
  }
}
