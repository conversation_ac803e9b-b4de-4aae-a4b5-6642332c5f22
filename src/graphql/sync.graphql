mutation syncData($input: PushSyncDataInput!) {
  syncData(input: $input) {
    project_groups {
      created {
        ...ProjectGroupFields
        remoteId
        projectGroupId
      }
      updated {
        ...ProjectGroupFields
        remoteId
      }
      deleted
      failed {
        id
        type
      }
    }
    workspace_groups {
      created {
        ...WorkspaceGroupFields
        remoteId
        workspaceGroupId
      }
      updated {
        ...WorkspaceGroupFields
        remoteId
      }
      deleted
      failed {
        id
        type
      }
    }
    tasks {
      created {
        id
        remoteId
        taskCode
      }
      updated {
        id
        taskCode
        remoteId
      }
      deleted
      failed {
        id
        type
      }
    }
    tasks_attachments {
      created {
        ...TaskAttachmentFields
        remoteId
      }
      updated {
        ...TaskAttachmentFields
        remoteId
      }
      deleted
      failed {
        id
        type
      }
    }
    task_comments {
      created {
        createdAt
        createdBy
        deletedAt
        deletedBy
        id
        userId
        message
        taskId
        updatedAt
        updatedBy
        remoteId
      }

      deleted
      failed {
        id
        type
      }
    }
    tasks_medias {
      created {
        ...TasksMediaFields
        remoteId
      }
      updated {
        ...TasksMediaFields
        remoteId
      }
      deleted
      failed {
        id
        type
      }
    }
    project_documents {
      created {
        id
        remoteId
        projectDocumentId
        allFormCode
        groupCode
        server_created_at
        currentUserId
      }
      updated {
        id
        remoteId
        projectDocumentId
        allFormCode
        currentUserId
      }
      deleted
      failed {
        id
        type
      }
    }
    drawing_revisions {
      created {
        ...DrawingRevisionFields
        remoteId
      }
      updated {
        ...DrawingRevisionFields
        remoteId
      }
      deleted
      failed {
        id
        type
      }
    }
    projects {
      created {
        id
        remoteId
      }
      updated {
        id
        remoteId
      }
      deleted
      failed {
        id
        type
      }
    }
    project_carousels {
      created {
        id
        remoteId
      }
      updated {
        id
        remoteId
      }
      deleted
      failed {
        id
        type
      }
    }
    request_for_signatures {
      created {
        id
        remoteId
        status
        projectDocumentId
      }
      updated {
        id
        remoteId
        status
        documentStatus
        projectDocumentId
      }
      deleted
      failed {
        id
        type
      }
    }
    workspace_ccs {
      created {
        id
        remoteId
        projectDocumentId
      }
      updated {
        id
        remoteId
      }
      deleted
      failed {
        id
        type
      }
    }
    workspace_attachments {
      created {
        id
        remoteId
        fileUrl
        projectDocumentId
      }
      updated {
        id
        remoteId
      }
      deleted
      failed {
        id
        type
      }
    }
    workspace_photos {
      created {
        id
        remoteId
        projectDocumentId
      }
      updated {
        id
        remoteId
      }
      deleted
      failed {
        id
        type
      }
    }
    workspace_document {
      created {
        id
        remoteId
        projectDocumentId
      }
      updated {
        id
        remoteId
      }
      deleted
      failed {
        id
        type
      }
    }
    project_document_comments {
      created {
        id
        remoteId
        projectDocumentId
      }
      updated {
        id
        remoteId
      }
      deleted
      failed {
        id
        type
      }
    }
  }
}
