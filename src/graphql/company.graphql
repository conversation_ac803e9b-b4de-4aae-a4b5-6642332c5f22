fragment CompanyFields on Company {
  id
  logoUrl
  ownerId
  name
  stampUrl
  createdAt
  createdBy
  updatedAt
  updatedBy
}

query company($id: ID!) {
  company(id: $id) {
    ...CompanyFields
  }
}

query companies(
  $paging: OffsetPaging
  $filter: CompanyFilter
  $sorting: [CompanySort!]
) {
  companies(paging: $paging, filter: $filter, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...CompanyFields
    }
  }
}

mutation createOneCompany($input: CreateOneCompanyInput!) {
  createOneCompany(input: $input) {
    id
  }
}

mutation updateOneCompany($input: UpdateOneCompanyInput!) {
  updateOneCompany(input: $input) {
    id
  }
}

query getCompanyUsers {
  getCompanyUsers {
    id
    name
    email
    # logoUrl
  }
}
