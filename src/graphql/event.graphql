fragment EventFields on Event {
  createdAt
  createdBy
  deletedAt
  deletedBy
  endAt
  id
  startAt
  title
  updatedAt
  updatedBy
  userId
  startTime
  endTime
  isAllDay
}

query getEvent($id: ID!) {
  event(id: $id) {
    ...EventFields
  }
}

query getEvents($filter: EventFilter, $paging: OffsetPaging, $sorting: [EventSort!]) {
  events(filter: $filter, paging: $paging, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...EventFields
    }
  }
}

query getEventsByAssignee($filter: EventFilter, $paging: OffsetPaging, $sorting: [EventSort!]) {
  getEventsByAssignee(filter: $filter, paging: $paging, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    nodes {
      ...EventFields
    }
  }
}

mutation createEvent($input: CreateEventInputDTO!) {
  createOneEvent(input: { event: $input }) {
    id
  }
}

mutation updateEvent($input: UpdateOneEventInput!) {
  updateOneEvent(input: $input) {
    id
  }
}

mutation deleteOneEvent($id: ID!) {
  deleteOneEvent(input: { id: $id }) {
    id
  }
}
