fragment ContactFields on Contact {
  id
  email
  name
  phoneNo
  projectId
  createdBy
  updatedBy
  createdAt
  updatedAt
  contactCompany {
    ...ContactCompanyFields
  }
  owner {
    name
    avatar
  }
}
query contact($id: ID!) {
  contact(id: $id) {
    ...ContactFields
  }
}
query contacts($paging: OffsetPaging, $filter: ContactFilter, $sorting: [ContactSort!]) {
  contacts(paging: $paging, filter: $filter, sorting: $sorting) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    totalCount
    nodes {
      ...ContactFields
    }
  }
}
mutation createNewContact($input: CreateContactInputDTO!) {
  createNewContact(input: $input) {
    id
  }
}

mutation updateContact($input: UpdateOneContactDTO!) {
  updateContact(input: $input) {
    id
  }
}

mutation deleteOneContact($id: ID!) {
  deleteOneContact(input: { id: $id }) {
    id
  }
}
