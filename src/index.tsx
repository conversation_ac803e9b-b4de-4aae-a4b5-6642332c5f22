import React, { useEffect, useCallback } from 'react';
import { ApolloProvider } from '@apollo/client';
import { AppContext, removeFcmToken, removeRemoveFcmTokenFlag } from '@configs';
import { apolloClient } from '@lib/apollo';
import Route from './route';
import { useSelector } from './store';
import useRestoreConnection from './hooks/useRestoreConnection';
import { getLastPulledAt } from '@nozbe/watermelondb/sync/impl';
import database from './database/index.native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import useSyncWithDownload from '@src/hooks/useSyncWithDownload';
import { getLastSyncTimestamp } from '@src/utils/syncTimestamp';
import '@lib/axios';

interface AuthState {
  isAuthenticated: boolean;
}

interface SyncAndDownloadOptions {
  syncMutateOptions: { dispatchStatus: boolean };
  offlineDownloadOptions: { id: string; dispatchStatus: boolean };
  showSyncModal?: boolean;
  firstTimeSync?: boolean;
}

const useServerUpdates = (auth: AuthState, syncAndDownload: (options: SyncAndDownloadOptions) => Promise<void>) => {
  const hasInitialSyncRef = React.useRef(false);
  const isMountedRef = React.useRef(true);

  // Reset sync flag when auth changes
  useEffect(() => {
    return () => {
      hasInitialSyncRef.current = false;
    };
  }, [auth.isAuthenticated]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  return useCallback(async () => {
    if (!auth.isAuthenticated || hasInitialSyncRef.current || !isMountedRef.current) return;

    try {
      const lastPulledAt = await getLastPulledAt(database);
      if (lastPulledAt === null) {
        hasInitialSyncRef.current = true;
        await syncAndDownload({
          syncMutateOptions: { dispatchStatus: true },
          offlineDownloadOptions: { id: '', dispatchStatus: false },
          showSyncModal: true,
          firstTimeSync: true
        });
      }

      const removeFcmTokenNeeded = await AsyncStorage.getItem('removeFcmTokenOnNextConnection');
      if (removeFcmTokenNeeded === 'true') {
        await removeFcmToken();
        await removeRemoveFcmTokenFlag();
      }
    } catch (error) {
      if (isMountedRef.current) {
        hasInitialSyncRef.current = false;
      }
    }
  }, [auth.isAuthenticated, syncAndDownload]);
};

const useBackgroundTasks = (auth: AuthState, syncAndDownload: (options: SyncAndDownloadOptions) => Promise<void>) => {
  const syncDisabled = useSelector((state: any) => state.app.syncDisabled);
  const timerRef = React.useRef<NodeJS.Timeout | null>(null);
  const SYNC_INTERVAL = 5 * 60 * 1000; // 5 minutes

  // Function to schedule the next background sync
  const scheduleNextSync = React.useCallback(() => {
    // Clear any existing timer
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }

    // Set a new timer
    timerRef.current = setTimeout(async () => {
      try {
        // Skip background sync if sync is disabled
        if (syncDisabled) {
          scheduleNextSync(); // Reschedule for next interval
          return;
        }

        await syncAndDownload({
          syncMutateOptions: { dispatchStatus: false },
          offlineDownloadOptions: { id: '', dispatchStatus: false }
        });
      } catch (error) {
      } finally {
        // Schedule the next sync regardless of success/failure
        scheduleNextSync();
      }
    }, SYNC_INTERVAL);
  }, [syncAndDownload, syncDisabled]);

  // Listen for manual sync events
  useEffect(() => {
    const checkForManualSync = async () => {
      // Check every minute if a manual sync has occurred
      const checkInterval = setInterval(async () => {
        const lastSync = await getLastSyncTimestamp();
        if (lastSync !== null) {
          const now = Date.now();
          const timeSinceLastSync = now - lastSync;

          // If a manual sync happened recently (less than our interval)
          if (timeSinceLastSync < SYNC_INTERVAL) {
            // A manual sync was recently performed, reset the timer
            scheduleNextSync();
          }
        }
      }, 60 * 1000); // Check every minute

      return () => clearInterval(checkInterval);
    };

    checkForManualSync();
  }, [scheduleNextSync, SYNC_INTERVAL]);

  // Initialize the background sync when authenticated
  useEffect(() => {
    if (!auth.isAuthenticated) return;

    scheduleNextSync();

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [auth.isAuthenticated, scheduleNextSync]);
};

const App: React.FC = () => {
  const context = {};
  const auth = useSelector((state: { auth: AuthState }) => state.auth);
  const { syncAndDownload } = useSyncWithDownload();

  useRestoreConnection();
  const handleServerUpdates = useServerUpdates(auth, syncAndDownload);
  useBackgroundTasks(auth, syncAndDownload);

  useEffect(() => {
    handleServerUpdates();
  }, [handleServerUpdates]);

  return (
    <AppContext.Provider value={context}>
      <ApolloProvider client={apolloClient}>
        <Route />
      </ApolloProvider>
    </AppContext.Provider>
  );
};

export default App;
