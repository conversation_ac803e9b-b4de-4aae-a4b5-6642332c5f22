import TasksMediaModel from '@src/database/model/task-media.model';
import { useMutation, useQueryClient } from '@tanstack/react-query';

const useDeleteTaskMedia = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      try {
        const TaskMedia = TasksMediaModel;
        return await TaskMedia.deleteMedia(id);
      } catch (error) {
        throw new Error(`Error deleting task media: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
    onError: (error: Error) => {}
  });
};

export default useDeleteTaskMedia;
