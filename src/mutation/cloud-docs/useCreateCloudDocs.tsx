import { useMutation, useQueryClient } from '@tanstack/react-query';
import ProjectDocumentModel from '@src/database/model/project-document';
import { RawProjectDocument } from '@src/database/model/project-document/types';

const useCreateProjectDocument = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (newDocsArray: Partial<RawProjectDocument>[]) => {
      const createdProjectDocs = await ProjectDocumentModel.createProjectDocs(newDocsArray);
      if (!createdProjectDocs.length) {
        throw new Error('Docs creation failed');
      }
      return createdProjectDocs;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['cloudDocs'] });
      queryClient.invalidateQueries({ queryKey: ['twoDDrawing'] });
      queryClient.invalidateQueries({ queryKey: ['template'] });
      queryClient.invalidateQueries({ queryKey: ['workspaceDocuments'] });
    }
  });
};

export default useCreateProjectDocument;
