import ProjectDocumentModel from '@src/database/model/project-document';
import { useMutation, useQueryClient } from '@tanstack/react-query';

const useDeleteCloudDocs = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      try {
        await ProjectDocumentModel.deleteDocument(id);
      } catch (error) {
        throw new Error(`Error deleting docs: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['cloudDocs'] });
    },
    onError: (error: Error) => {}
  });
};

export default useDeleteCloudDocs;
