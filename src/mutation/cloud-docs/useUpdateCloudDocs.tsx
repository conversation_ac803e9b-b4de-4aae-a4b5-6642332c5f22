import { useMutation, useQueryClient } from '@tanstack/react-query';
import ProjectDocumentModel from '@src/database/model/project-document';

export const useUpdateCloudDocs = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (docs: UpdateProjectDocumentInput) => {
      const updateProjectDocs = await ProjectDocumentModel.updateProjectDocs(docs);
      if (!updateProjectDocs) {
        throw new Error('Docs renaming failed');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['cloudDocs'] });
      queryClient.invalidateQueries({ queryKey: ['template'] });
    }
  });
};
