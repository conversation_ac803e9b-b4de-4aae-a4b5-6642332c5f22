import { useMutation, useQueryClient } from '@tanstack/react-query';
import OfflineDownloadModel from '@src/database/model/offline-download.model';
import { store, useSelector } from '@src/store';
import { AppActions } from '@src/slice/app.slice';
import ProjectDocumentModel from '@src/database/model/project-document';

const useSaveProjectDocument = () => {
  const queryClient = useQueryClient();
  const project = useSelector(state => state.project);

  return useMutation({
    mutationFn: async ({ id, dispatchStatus }: any) => {
      try {
        if (dispatchStatus) {
          store.dispatch(AppActions.setSyncStatus('pending'));
        }
        const data = await OfflineDownloadModel.fetchAllOfflineDownloads();
        const ids: any = [id, ...data];

        for (const id of ids) {
          if (id === '') continue;
          const document: any = await ProjectDocumentModel.getProjectDocumentAndChildren(id);
          await OfflineDownloadModel.handleOfflineDownload(document, parseInt(project.projectId || '0'));
        }
        if (dispatchStatus) {
          store.dispatch(AppActions.setSyncStatus('success'));
        }
        return;
      } catch (error) {
        if (dispatchStatus) {
          store.dispatch(AppActions.setSyncStatus('failed'));
        }
      } finally {
        if (dispatchStatus) {
          setTimeout(() => {
            store.dispatch(AppActions.setSyncStatus('idle'));
            store.dispatch(AppActions.setSyncProgress(0));
          }, 2000);
        }
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries();
    },
    onError: error => {}
  });
};

export default useSaveProjectDocument;
