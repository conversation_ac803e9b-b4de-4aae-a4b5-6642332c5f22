import WorkspaceAttachmentModel from '@src/database/model/workspace-attachments.model';
import { useMutation, useQueryClient } from '@tanstack/react-query';

const useDeleteWorkspaceAttachment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      try {
        const workspacePhoto = WorkspaceAttachmentModel;
        return await workspacePhoto.deleteAttachment(id);
      } catch (error) {
        throw new Error(`Error deleting task media: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documentDetail'] });
    },
    onError: (error: Error) => {}
  });
};

export default useDeleteWorkspaceAttachment;
