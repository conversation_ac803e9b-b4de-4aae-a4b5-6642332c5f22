import WorkspacePhotoModel from '@src/database/model/workspace-photos.model';
import { useMutation, useQueryClient } from '@tanstack/react-query';

const useDeleteWorkspaceMedia = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      try {
        const workspacePhoto = WorkspacePhotoModel;
        return await workspacePhoto.deleteMedia(id);
      } catch (error) {
        throw new Error(`Error deleting task media: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documentDetail'] });
    },
    onError: (error: Error) => {}
  });
};

export default useDeleteWorkspaceMedia;
