import ProjectModel from '@src/database/model/project.model';
import { useSelector } from '@src/store';
import { useMutation, useQueryClient } from '@tanstack/react-query';

interface UpdateProjectInput {
  projectId: string;
  title: string;
  carousel: string;
}

const useUpdateProject = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (input: UpdateProjectInput) => {
      try {
        const Project = ProjectModel;
        return await Project.updateProject(input.projectId, input.title, input.carousel);
      } catch (error) {
        throw new Error(`Error creating project: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['project'] });
    },
    onError: (error: Error) => {}
  });
};

export default useUpdateProject;
