import ProjectModel from '@src/database/model/project.model';
import { useSelector } from '@src/store';
import { useMutation, useQueryClient } from '@tanstack/react-query';

interface CreateProjectInput {
  title: string;
  carousel: string;
}

const useCreateProject = () => {
  const queryClient = useQueryClient();
  const companyId = useSelector(state => state?.auth?.company?.id);
  const userId = useSelector(state => state?.auth?.user?.id);

  return useMutation({
    mutationFn: async (input: CreateProjectInput) => {
      try {
        const Project = ProjectModel;
        return await Project.createProject(
          input.title,
          input.carousel,
          parseInt(companyId ?? '0'),
          parseInt(userId ?? '0')
        );
      } catch (error) {
        throw new Error(`Error creating project: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['project'] });
    },
    onError: (error: Error) => {}
  });
};

export default useCreateProject;
