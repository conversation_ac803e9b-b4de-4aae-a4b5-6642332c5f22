import ProjectModel from '@src/database/model/project.model';
import { useMutation, useQueryClient } from '@tanstack/react-query';

interface UpdateProjectInput {
  projectId: number;
  drawing_sync: boolean;
}

const useUpdateProject = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (input: UpdateProjectInput) => {
      try {
        const Project = ProjectModel;
        return await Project.updateDrawingSyncProjects(input.projectId, input.drawing_sync);
      } catch (error) {
        throw new Error(`Error syncing drawings based on project: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['project'] });
    },
    onError: (error: Error) => {}
  });
};

export default useUpdateProject;
