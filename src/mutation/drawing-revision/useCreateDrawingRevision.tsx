import { useMutation, useQueryClient } from '@tanstack/react-query';
import DrawingRevisionModel from '@src/database/model/drawing-revision.model';
import database from '@src/database/index.native';

const useCreateDrawingRevision = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (newDrawingRevision: DrawingRevisionModel) => {
      const DrawingRevision = new DrawingRevisionModel(database.get('drawing_revisions'), {} as any);
      return await DrawingRevision.createOneDrawingRevision(newDrawingRevision);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['drawingRevision'] });
      queryClient.invalidateQueries({ queryKey: ['twoDDrawing'] });
    }
  });
};

export default useCreateDrawingRevision;
