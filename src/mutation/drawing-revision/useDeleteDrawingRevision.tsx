import DrawingRevisionModel from '@src/database/model/drawing-revision.model';
import { useMutation, useQueryClient } from '@tanstack/react-query';

const useDeleteDrawingRevision = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      try {
        await DrawingRevisionModel.deleteDrawingRevision(id);
      } catch (error) {
        throw new Error(`Error deleting drawing revision: ${error}`);
      }
    },
    onSuccess: () => {
      // Invalidate queries to refresh the UI
      return Promise.all([
        queryClient.invalidateQueries({ queryKey: ['drawingRevision'] }),
        queryClient.invalidateQueries({ queryKey: ['projectDocument'] }),
        queryClient.invalidateQueries({ queryKey: ['twoDDrawing'] })
      ]);
    },
    onError: (error: Error) => {
      }
  });
};

export default useDeleteDrawingRevision;
