import { useMutation, useQueryClient } from '@tanstack/react-query';
import DrawingRevisionModel from '@src/database/model/drawing-revision.model';
import database from '@src/database/index.native';

const useUpdateDrawingRevision = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ id, newDrawingRevision }: { id: string; newDrawingRevision: DrawingRevisionModel }) => {
      const DrawingRevision = new DrawingRevisionModel(database.get('drawing_revisions'), {} as any);
      return await DrawingRevision.updateDrawingRevision(id, newDrawingRevision);
    },
    onSuccess: () => {
      // Invalidate queries to refresh the UI
      return Promise.all([
        queryClient.invalidateQueries({ queryKey: ['drawingRevision'] }),
        queryClient.invalidateQueries({ queryKey: ['projectDocument'] }),
        queryClient.invalidateQueries({ queryKey: ['twoDDrawing'] })
      ]);
    }
  });
};

export default useUpdateDrawingRevision;
