import database from '@src/database/index.native';
import ProjectDocumentModel from '@src/database/model/project-document';
import { useMutation, useQueryClient } from '@tanstack/react-query';

const useCreateWorkspaceTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (newDocs: CreateProjectDocumentInput[]) => {
      try {
        await ProjectDocumentModel.createProjectDocs(newDocs);
      } catch (error) {
        throw new Error(`Error creating workspace docs: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['template'] });
      queryClient.invalidateQueries({ queryKey: ['workspaceDocuments'] });
    },
    onError: (error: Error) => {}
  });
};

export default useCreateWorkspaceTemplate;
