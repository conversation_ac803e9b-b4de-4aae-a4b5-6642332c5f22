import database from '@src/database/index.native';
import ProjectDocumentModel from '@src/database/model/project-document';
import { useMutation, useQueryClient } from '@tanstack/react-query';

const useDeleteWorkspaceTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      try {
        await ProjectDocumentModel.deleteDocument(id);
      } catch (error) {
        throw new Error(`Error deleting docs: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['template'] });
    },
    onError: (error: Error) => {}
  });
};

export default useDeleteWorkspaceTemplate;
