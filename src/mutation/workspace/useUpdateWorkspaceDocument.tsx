import ProjectDocumentModel from '@src/database/model/project-document';
import { useMutation, useQueryClient } from '@tanstack/react-query';

// Define a compatible interface that bridges the type differences
interface UpdateWorkspaceDocumentInputCommand {
  id: string;
  remoteId?: string | number | null;
  name?: string;
  description?: string;
  workspaceGroupId?: number;
  workflow?: string;
  status?: string;
  localFileUrl?: string;
  fileUrl?: string;
  xfdf?: string;
  fileKey?: string;
  assignees?: any[];
  ccs?: any[];
  attachments?: any[];
  media?: any[];
  linkedDocuments?: any[];
  isQueued?: boolean;
}

const useUpdateWorkspaceDocument = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateWorkspaceDocumentInputCommand) => {
      try {
        // Convert remoteId to string if it's a number
        const adaptedData = {
          ...data,
          remoteId: data.remoteId != null ? String(data.remoteId) : undefined
        };

        await ProjectDocumentModel.updateWorkspaceDocument(adaptedData);
      } catch (error) {
        throw new Error(`Error updating workspace document: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['template'] });
      queryClient.invalidateQueries({ queryKey: ['documentDetail'] });
      queryClient.invalidateQueries({ queryKey: ['workspaceDocuments'] });
      queryClient.invalidateQueries({ queryKey: ['twoDDrawing'] });
      queryClient.invalidateQueries({ queryKey: ['workspaceGroup'] });
    },
    onError: (error: Error) => {}
  });
};

export default useUpdateWorkspaceDocument;
