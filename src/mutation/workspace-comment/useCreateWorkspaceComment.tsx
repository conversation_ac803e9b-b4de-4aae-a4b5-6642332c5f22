import database from '@src/database/index.native';
import WorkspaceCommentModel from '@src/database/model/workspace-comment.model';
import { useMutation, useQueryClient } from '@tanstack/react-query';

const useCreateWorkspaceComment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (newComment: CreateCommentInput) => {
      const workspaceComment = new WorkspaceCommentModel(database.get('project_document_comments'), {} as any);
      await workspaceComment.createWorkspaceDocumentComment(newComment);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documentDetail'] });
      queryClient.invalidateQueries({ queryKey: ['workspaceDocuments'] });
    },
    onError: (error: Error) => {}
  });
};

export default useCreateWorkspaceComment;
