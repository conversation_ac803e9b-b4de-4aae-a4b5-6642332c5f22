import database from '@src/database/index.native';
import WorkspaceCommentModel from '@src/database/model/workspace-comment.model';
import { useMutation, useQueryClient } from '@tanstack/react-query';

const useDeleteWorkspaceComment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const workspaceComment = new WorkspaceCommentModel(database.get('project_document_comments'), {} as any);
      await workspaceComment.deleteWorkspaceDocumentComment(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documentDetail'] });
    },
    onError: (error: Error) => {}
  });
};

export default useDeleteWorkspaceComment;
