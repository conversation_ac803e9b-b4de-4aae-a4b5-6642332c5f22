import RequestForSignaturesModel from '@src/database/model/request-for-signatures.model';
import { useMutation, useQueryClient } from '@tanstack/react-query';

const useDeleteRequestForSignature = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (ids: string[]) => {
      try {
        return await RequestForSignaturesModel.deleteRequestForSignature(ids);
      } catch (error) {
        throw new Error(`Error deleting request for signature: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspaceDocuments'] });
    },
    onError: (error: Error) => {}
  });
};

export default useDeleteRequestForSignature;
