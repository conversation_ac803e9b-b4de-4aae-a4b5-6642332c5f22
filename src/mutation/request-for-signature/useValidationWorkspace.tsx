import ProjectUserModel from '@src/database/model/project-user.model';
import RequestForSignaturesModel from '@src/database/model/request-for-signatures.model';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export interface dynamicWorkspaceValidation {
  documentId: string;
  requestForSignatureId: string;
  type: 'Approve Close' | 'Approve Proceed' | 'Amend' | 'Reject' | 'Work In Progress' | 'In Review' | 'Resubmit';
  newRequestedSignatureId?: ProjectUserModel;
}

const useValidationWorkspace = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: dynamicWorkspaceValidation) => {
      try {
        await RequestForSignaturesModel.dynamicWorkspaceValidation(
          data.documentId,
          data.requestForSignatureId,
          data.type,
          data.newRequestedSignatureId
        );
      } catch (error) {
        throw new Error(`Error updating workspace document: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['template'] });
      queryClient.invalidateQueries({ queryKey: ['documentDetail'] });
      queryClient.invalidateQueries({ queryKey: ['workspaceDocuments'] });
    },
    onError: (error: Error) => {}
  });
};

export default useValidationWorkspace;
