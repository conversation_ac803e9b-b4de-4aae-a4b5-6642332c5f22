import ProjectUserModel from '@src/database/model/project-user.model';
import RequestForSignaturesModel from '@src/database/model/request-for-signatures.model';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export interface LinearWorkspaceValidation {
  documentId: string;
  requestForSignatureId: string;
  type: 'In Review' | 'Approve' | 'Reject';
  newRequestedSignatureId?: ProjectUserModel;
}

const useValidationLinear = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: LinearWorkspaceValidation) => {
      try {
        await RequestForSignaturesModel.LinearWorkspaceValidation(
          data.documentId,
          data.requestForSignatureId,
          data.type
          // data.newRequestedSignatureId
        );
      } catch (error) {
        throw new Error(`Error updating workspace document: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['template'] });
      queryClient.invalidateQueries({ queryKey: ['documentDetail'] });
      queryClient.invalidateQueries({ queryKey: ['workspaceDocuments'] });
    },
    onError: (error: Error) => {}
  });
};

export default useValidationLinear;
