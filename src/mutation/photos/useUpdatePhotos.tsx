import { useMutation, useQueryClient } from '@tanstack/react-query';
import ProjectDocumentModel from '@src/database/model/project-document';

export const useUpdatePhotos = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (docs: UpdateProjectDocumentInput) => {
      const updatePhotos = await ProjectDocumentModel.updateProjectDocs(docs);
      if (!updatePhotos) {
        throw new Error('Docs renaming failed');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['photos'] });
    }
  });
};
