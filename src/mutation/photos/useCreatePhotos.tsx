import database from '@src/database/index.native';
import ProjectDocumentModel from '@src/database/model/project-document';
import { useQueryClient, useMutation } from '@tanstack/react-query';

const useCreatePhotos = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (newPhotosArray: CreateProjectDocumentInput[]) => {
      const createdPhotos = await ProjectDocumentModel.createProjectDocs(newPhotosArray);
      if (!createdPhotos.length) {
        throw new Error('Photos creation failed');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['photos'] });
    }
  });
};

export default useCreatePhotos;
