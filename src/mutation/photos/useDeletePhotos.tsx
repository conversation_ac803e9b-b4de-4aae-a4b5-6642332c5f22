import ProjectDocumentModel from '@src/database/model/project-document';
import { useMutation, useQueryClient } from '@tanstack/react-query';

const useDeletePhotos = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      try {
        await ProjectDocumentModel.deleteDocument(id);
      } catch (error) {
        throw new Error(`Error deleting docs: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['photos'] });
    },
    onError: (error: Error) => {}
  });
};

export default useDeletePhotos;
