// src/hooks/useCreateTask.ts
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { CreateTaskInput } from 'task';
import database from '@src/database/index.native';
import TaskModel from '@src/database/model/task.model';
import TasksMediaModel from '@src/database/model/task-media.model';
import TasksAttachmentModel from '@src/database/model/task-attachment.model';

const useCreateTask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (newTask: CreateTaskInput) => {
      const Task = new TaskModel(database.get('tasks'), {} as any);
      const TaskMedia = new TasksMediaModel(database.get('tasks_medias'), {} as any);
      const TaskAttachment = new TasksAttachmentModel(database.get('tasks_attachments'), {} as any);

      const createdTask = await Task.createTask(newTask);
      if (!createdTask || !createdTask.id) {
        throw new Error('Task creation failed');
      }

      const localTaskId = createdTask.id;
      const currentChosenPhotos = newTask.medias?.map((photo: TasksMediaModel) => ({
        localTaskId,
        ...photo
      }));

      const attachments = newTask.attachments?.map((attachment: TasksAttachmentModel) => ({
        localTaskId,
        ...attachment
      }));

      await Promise.all([
        TaskMedia.createTaskMedia(currentChosenPhotos),
        TaskAttachment.createTaskAttachment(attachments)
      ]);

      return createdTask;
    },
    // Invalidate and refetch tasks when a new task is successfully created
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
    onError: (error: Error) => {}
  });
};

export default useCreateTask;
