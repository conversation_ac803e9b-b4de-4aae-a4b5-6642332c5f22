import database from '@src/database/index.native';
import TaskModel from '@src/database/model/task.model';
import { useMutation, useQueryClient } from '@tanstack/react-query';

const useDeleteTask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      try {
        const Task = new TaskModel(database.get('tasks'), {} as any);
        await Task.deleteTask(id);
      } catch (error) {
        throw new Error(`Error deleting task: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
    onError: (error: Error) => {}
  });
};

export default useDeleteTask;
