// src/hooks/useUpdateTask.ts
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { UpdateTaskInput } from 'task';
import database from '@src/database/index.native';
import TaskModel from '@src/database/model/task.model';
import TasksMediaModel from '@src/database/model/task-media.model';
import TasksAttachmentModel from '@src/database/model/task-attachment.model';
import { CreateTaskAttachmentInput } from 'task-attachment';
import { CreateTaskMediaInput } from 'task-media';
import { useSelector } from '@src/store';

interface TaskUpdateInput {
  id: string;
  newTask: UpdateTaskInput;
  newAttachment?: CreateTaskAttachmentInput[];
  newMedias?: CreateTaskMediaInput[];
}

const useUpdateTask = () => {
  const queryClient = useQueryClient();
  const updatedBy = useSelector(state => state.auth.user?.id);

  return useMutation({
    mutationFn: async ({ newTask, newAttachment, newMedias, id }: TaskUpdateInput) => {
      try {
        const Task = new TaskModel(database.get('tasks'), {} as any);
        const TaskMedia = new TasksMediaModel(database.get('tasks_medias'), {} as any);
        const TaskAttachment = new TasksAttachmentModel(database.get('tasks_attachments'), {} as any);

        await Task.updateTask(id, newTask, parseInt(updatedBy ?? '0'));

        const createNewAttachment =
          newAttachment && newAttachment.length > 0
            ? TaskAttachment.createTaskAttachment(newAttachment)
            : Promise.resolve();
        const createNewMedias =
          newMedias && newMedias.length > 0 ? TaskMedia.createTaskMedia(newMedias) : Promise.resolve();

        await Promise.all([createNewAttachment, createNewMedias]);
        return newTask;
      } catch (error) {
        throw new Error(`Error updating task: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['unreceivedMemoTask'] });
    },
    onError: (error: Error) => {}
  });
};

export default useUpdateTask;
