import { useMutation, useQueryClient } from '@tanstack/react-query';
import { sync } from '@src/database/sync';
import { store, useDispatch, useSelector } from '@src/store';
import { AppActions } from '@src/slice/app.slice';
import UserModel from '@src/database/model/user.model';
import { AuthActions } from '@src/slice/auth.slice';
import useSyncAllProjectCarousel from './offline-download/useSyncAllProjectCarousel';
import { projectActions, ProjectActions } from '@src/slice/project.slice';
import ProjectDocumentModel from '@src/database/model/project-document';
import { documentWorkspaceActions } from '@src/slice/document-workspace.slice';

// Constants for query keys
const QUERY_KEYS = [
  'tasks',
  'taskGroup',
  'cloudDocs',
  'project',
  'template',
  'twoDDrawing',
  'project_carousels',
  'workspaceDocuments',
  'documentDetail'
];

const useSyncMutation = () => {
  const queryClient = useQueryClient();
  const userData = useSelector(state => state.auth.user?.id);
  const dispatch = useDispatch();
  const { mutateAsync: syncAllProjectCarousel } = useSyncAllProjectCarousel();

  const setSyncStatus = (status: 'success' | 'failed' | 'idle' | 'pending') => {
    store.dispatch(AppActions.setSyncStatus(status));
  };

  const performSync = async (dispatchStatus?: boolean) => {
    try {
      if (dispatchStatus) setSyncStatus('pending');
      await sync(null);
      await syncAllProjectCarousel();
      const updatedUser = await UserModel.downloadStampAndSignUrl(userData || '');
      if (updatedUser) {
        dispatch(AuthActions.updateUser(updatedUser));
      }

      if (dispatchStatus) setSyncStatus('success');
    } catch (error) {
      if (dispatchStatus) {
        setSyncStatus('failed');
        setTimeout(() => setSyncStatus('idle'), 2000);
      }
      throw error;
    } finally {
      if (dispatchStatus) {
        setTimeout(() => setSyncStatus('idle'), 2000);
      }
    }
  };

  return useMutation({
    mutationFn: performSync,
    onSuccess: () => {
      QUERY_KEYS.forEach(queryKey => queryClient.invalidateQueries({ queryKey: [queryKey] }));
      // invalidate apollo cache after successful sync
      const project = store.getState().project;

      dispatch(ProjectActions.updateProjectId(project.projectId ?? '')),
        dispatch(projectActions.setCurrentProjectTitle(project.selectedProjectTitle ?? ''));
    },
    onError: error => {}
  });
};

export default useSyncMutation;
