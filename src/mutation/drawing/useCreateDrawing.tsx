import { useMutation, useQueryClient } from '@tanstack/react-query';
import ProjectDocumentModel from '@src/database/model/project-document';

const useCreateDrawing = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (newDocsArray: CreateProjectDocumentInput[]) => {
      const createdProjectDocs = await ProjectDocumentModel.createProjectDocs(newDocsArray);
      if (!createdProjectDocs.length) {
        throw new Error('Docs creation failed');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['twoDDrawing'] });
    }
  });
};

export default useCreateDrawing;
