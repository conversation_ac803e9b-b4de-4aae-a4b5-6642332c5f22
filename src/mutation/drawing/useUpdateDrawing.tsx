import { useMutation, useQueryClient } from '@tanstack/react-query';
import ProjectDocumentModel from '@src/database/model/project-document.model';
import database from '@src/database/index.native';

export const useUpdateCloudDocs = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (docs: UpdateProjectDocumentInput) => {
      const projectDocs = new ProjectDocumentModel(database.get('project_documents'), {} as any);
      const updateProjectDocs = await projectDocs.updateProjectDocs(docs);
      if (!updateProjectDocs) {
        throw new Error('Docs renaming failed');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['cloudDocs'] });
    }
  });
};
