import { useMutation, useQueryClient } from '@tanstack/react-query';
import OfflineDownloadModel from '@src/database/model/offline-download.model';
import { pushSuccess } from '@src/configs';
import { useSelector } from '@src/store';
import ProjectDocumentModel from '@src/database/model/project-document';

const useSyncAllDrawing = () => {
  const queryClient = useQueryClient();
  const project = useSelector(state => state.project);

  return useMutation({
    mutationFn: async () => {
      try {
        if (!project.projectId) return;
        const allDrawings = await ProjectDocumentModel.getAllDrawings(parseInt(project.projectId ?? ''));
        if (allDrawings?.length !== 0) await OfflineDownloadModel.queueOfflineDownloads(allDrawings);
        return allDrawings;
      } catch (error) {
        throw new Error(`Error queuing offline download: ${error}`);
      }
    },
    onSuccess: (data: any) => {
      if (data && data.length === 0) {
        return;
      } else {
        pushSuccess('Document is queued for offline viewing');
      }
      queryClient.invalidateQueries({ queryKey: ['offline'] });
    },
    onError: (error: Error) => {}
  });
};

export default useSyncAllDrawing;
