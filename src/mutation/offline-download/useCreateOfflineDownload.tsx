import { useMutation, useQueryClient } from '@tanstack/react-query';
import OfflineDownloadModel from '@src/database/model/offline-download.model';
import { pushSuccess } from '@src/configs';

const useCreateOfflineDownload = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string[]) => {
      try {
        await OfflineDownloadModel.queueOfflineDownloads(id);
      } catch (error) {
        throw new Error(`Error queuing offline download: ${error}`);
      }
    },
    onSuccess: () => {
      pushSuccess('Document is queued for offline viewing');
      queryClient.invalidateQueries({ queryKey: ['twoDDrawing'] });
      queryClient.invalidateQueries({ queryKey: ['offline'] });
      queryClient.invalidateQueries({ queryKey: ['workspaceDocuments'] });
    },
    onError: (error: Error) => {}
  });
};

export default useCreateOfflineDownload;
