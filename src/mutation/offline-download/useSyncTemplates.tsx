import BackgroundService from 'react-native-background-actions';
import ProjectModel from '@src/database/model/project.model';
import { pushSuccess, pushError } from '@src/configs'; // Remove setProjectId import
import apolloClient from '@src/lib/apollo'; // Ensure apolloClient is imported
import { Gql } from '@src/api';
import ProjectDocumentModel from '@src/database/model/project-document';

// Types
interface Template {
  id: string;
  fileUrl: string;
  // Add other properties as needed
}

interface LocalTemplate {
  _raw: {
    id: string;
    remoteId: string;
  };
  // Add other properties as needed
}

interface SyncResult {
  completedTemplates: number;
  totalSyncedTemplates: number;
}

interface BackgroundServiceOptions {
  taskName: string;
  taskTitle: string;
  taskDesc: string;
  taskIcon: {
    name: string;
    type: string;
  };
  color: string;
  progressBar: {
    max: number;
    value: number;
    indeterminate: boolean;
  };
}

// Constants
const BACKGROUND_SERVICE_OPTIONS: BackgroundServiceOptions = {
  taskName: 'SyncProjectTemplates',
  taskTitle: 'Syncing Project Templates',
  taskDesc: 'Preparing to sync...',
  taskIcon: {
    name: 'ic_launcher',
    type: 'mipmap'
  },
  color: '#4D818C',
  progressBar: {
    max: 100,
    value: 0,
    indeterminate: true
  }
};

const calculateTotalTemplates = async (projectIds: string[]): Promise<number> => {
  const totals = await Promise.all(
    projectIds.map(async projectId => {
      const projectTemplates = await ProjectDocumentModel.getTotalTemplatesForProject(projectId.toString());
      return projectTemplates;
    })
  );
  return totals.reduce((acc, curr) => acc + curr, 0);
};

const updateProgress = async (completed: number, total: number): Promise<void> => {
  const progress = Math.floor((completed / total) * 100);

  await BackgroundService.updateNotification({
    taskDesc: `Downloading templates for offline use ${progress}%`,
    progressBar: {
      max: 100,
      value: progress,
      indeterminate: false
    }
  });
};

const saveTemplate = async (template: Template, localTemplates: LocalTemplate[], projectId: string): Promise<void> => {
  const localTemplate = localTemplates.find(t => t?._raw?.remoteId?.toString() === template.id);

  if (localTemplate) {
    const updatedItem = {
      ...template,
      id: localTemplate._raw?.id,
      fileUrl: template.fileUrl,
      tableName: 'project_documents',
      projectId: parseInt(projectId),
      createdAt: new Date().toISOString()
    };

    await ProjectDocumentModel?.saveProjectDocument(updatedItem);
  }
};

// Core Sync Functions
const syncTemplatesForProject = async (
  projectId: string,
  totalTemplates: number,
  completedTemplates: number,
  totalSyncedTemplates: number
): Promise<SyncResult> => {
  const templates: any[] = await ProjectDocumentModel.getAllTemplates(parseInt(projectId));
  const templateIds: string[] = templates.map(template => template?._raw?.remoteId?.toString());

  if (templateIds.length === 0) {
    return { completedTemplates, totalSyncedTemplates };
  }

  try {
    const response = await apolloClient.query({
      query: Gql.ProjectDocumentsDocument,
      variables: {
        filter: {
          category: { eq: Gql.CategoryType.StandardForm },
          projectId: { eq: projectId },
          fileSystemType: { eq: Gql.FileSystemType.Document },
          id: { in: templateIds }
        },
        paging: { offset: 0, limit: 9999999 }
      },
      context: {
        projectId: projectId // Pass the specific projectId for the authLink
      }
    });

    if (response?.data?.projectDocuments?.nodes) {
      for (const template of response.data.projectDocuments.nodes) {
        if (!(await BackgroundService.isRunning())) {
          break;
        }

        try {
          await saveTemplate(template, templates, projectId);
          totalSyncedTemplates++;
        } catch (error) {
          continue;
        } finally {
          completedTemplates++;
          await updateProgress(completedTemplates, totalTemplates);
        }
      }
    }
  } catch (e) {}

  return { completedTemplates, totalSyncedTemplates };
};

const syncProjectTemplates = async (): Promise<void> => {
  let totalSyncedTemplates = 0;
  let completedTemplates = 0;

  try {
    const projectIds: string[] = await ProjectModel.getAllProjectIds();
    const totalTemplates: number = await calculateTotalTemplates(projectIds);

    await updateProgress(0, totalTemplates);

    for (const projectId of projectIds) {
      if (!(await BackgroundService.isRunning())) {
        break;
      }

      // await setProjectId(projectId.toString()); // Remove this line
      const result = await syncTemplatesForProject(projectId, totalTemplates, completedTemplates, totalSyncedTemplates);
      completedTemplates = result.completedTemplates;
      totalSyncedTemplates = result.totalSyncedTemplates;
    }

    await BackgroundService.updateNotification({
      taskTitle: 'Sync completed',
      taskDesc: `${totalSyncedTemplates} templates synced`,
      progressBar: {
        max: 100,
        value: 100,
        indeterminate: false
      }
    });
    pushSuccess('Templates synced successfully', 'Sync Completed');
  } catch (error) {
    await BackgroundService.updateNotification({ taskTitle: 'Sync failed', taskDesc: 'An error occurred during sync' });
  }
};

// Exported Functions
export const startBackgroundSync = async (): Promise<void> => {
  try {
    await BackgroundService.start(syncProjectTemplates, BACKGROUND_SERVICE_OPTIONS);
    pushSuccess('Background sync started. Files will stay up to date', 'Background Sync Started');
  } catch (e) {
    pushError('Failed to start template sync: ' + (e as Error).message);
  }
};

export const stopBackgroundSync = async (): Promise<void> => {
  await BackgroundService.stop();
  pushSuccess('Template sync stopped');
};

export const isSyncRunning = async (): Promise<boolean> => {
  return await BackgroundService.isRunning();
};
