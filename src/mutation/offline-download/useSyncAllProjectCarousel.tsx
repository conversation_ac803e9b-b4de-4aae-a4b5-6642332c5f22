import { pushSuccess } from '@src/configs';
import ProjectCarouselModel from '@src/database/model/project-carousel.model';
import { useMutation, useQueryClient } from '@tanstack/react-query';

const useSyncAllProjectCarousell = () => {
  // const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      try {
        await ProjectCarouselModel.syncProjectCarouselImages();
      } catch (error) {
        throw new Error(`Error syncing project carousel: ${error}`);
      }
    },
    onSuccess: (data: any) => {
      if (data && data.length === 0) {
        return;
      } else {
      }
      // queryClient.invalidateQueries({ queryKey: ['offline'] });
    },
    onError: (error: Error) => {}
  });
};

export default useSyncAllProjectCarousell;
