import { useMutation, useQueryClient } from '@tanstack/react-query';
import WorkspaceGroupModel from '@src/database/model/workspace-group.model';

const useCreateWorkspaceGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (workspaceGroup: CreateWorkspaceGroup) => {
      try {
        return await WorkspaceGroupModel.createWorkspaceGroup(
          workspaceGroup.name,
          workspaceGroup.projectId,
          workspaceGroup.workspaceGroupId,
          workspaceGroup.localGroupId,
          workspaceGroup.code
        );
      } catch (error) {
        throw new Error(`Error creating workspace group: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspaceGroup'] });
    },
    onError: (error: Error) => {}
  });
};

export default useCreateWorkspaceGroup;
