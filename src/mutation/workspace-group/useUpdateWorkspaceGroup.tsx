import { useMutation, useQueryClient } from '@tanstack/react-query';
import WorkspaceGroupModel from '@src/database/model/workspace-group.model';

const useUpdateWorkspaceGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (workspaceGroup: UpdateWorkspaceGroup) => {
      try {
        return await WorkspaceGroupModel.updateWorkspaceGroup(
          workspaceGroup.workspaceGroupId,
          workspaceGroup.name,
          workspaceGroup.code
        );
      } catch (error) {
        throw new Error(`Error update workspace group: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspaceGroup'] });
    },
    onError: (error: Error) => {}
  });
};

export default useUpdateWorkspaceGroup;
