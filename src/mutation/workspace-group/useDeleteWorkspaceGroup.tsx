import WorkspaceGroupModel from '@src/database/model/workspace-group.model';
import { useMutation, useQueryClient } from '@tanstack/react-query';

const useDeleteWorkspaceGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      try {
        const WorkspaceGroup = WorkspaceGroupModel;
        return await WorkspaceGroup.deleteWorkspaceGroup(id);
      } catch (error) {
        throw new Error(`Error deleting task group: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspaceGroup'] });
    },
    onError: (error: Error) => {}
  });
};

export default useDeleteWorkspaceGroup;
