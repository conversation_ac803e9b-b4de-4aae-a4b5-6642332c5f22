import { useInfiniteQuery, useQuery } from '@tanstack/react-query';
import ProjectDocumentModel from '@src/database/model/project-document.model';
import { useSelector } from '@src/store';
import WorkspaceGroupUserModel from '@src/database/model/workspace-group-users.model';

interface IUseGetWorkspaceGroupUser {
  workspaceGroupId: number | null;
}

export const useGetWorkspaceGroupUser = (data: IUseGetWorkspaceGroupUser) => {
  if (!data.workspaceGroupId) {
    return { data: null, fetchNextPage: () => {}, hasNextPage: false, isFetchingNextPage: false, isLoading: false };
  }

  return useQuery({
    queryKey: ['workspaceGroupUser', data.workspaceGroupId],
    queryFn: async () => {
      return await WorkspaceGroupUserModel.getWorkspaceGroupUser(data.workspaceGroupId);
    },
    throwOnError: true,
    enabled: !!data.workspaceGroupId
  });
};
