import WorkspaceCcModel from '@src/database/model/workspace_ccs.model';
import { useMutation, useQueryClient } from '@tanstack/react-query';

const useDeleteWorkspaceCc = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (ids: string[]) => {
      try {
        return await WorkspaceCcModel.deleteWorkspaceCc(ids);
      } catch (error) {
        throw new Error(`Error deleting workspace cc: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspaceDocuments'] });
    },
    onError: (error: Error) => {}
  });
};

export default useDeleteWorkspaceCc;
