import { useMutation, useQueryClient } from '@tanstack/react-query';
import ProjectGroupModel from '@src/database/model/project-group.model';

const useUpdateTaskGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (taskGroup: UpdateProjectGroup) => {
      try {
        return await ProjectGroupModel.updateProjectGroup(taskGroup.projectGroupId, taskGroup.title);
      } catch (error) {
        throw new Error(`Error update task group: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['taskGroup'] });
    },
    onError: (error: Error) => {}
  });
};

export default useUpdateTaskGroup;
