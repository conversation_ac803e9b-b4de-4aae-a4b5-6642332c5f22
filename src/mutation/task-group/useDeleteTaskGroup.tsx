import ProjectGroupModel from '@src/database/model/project-group.model';
import { useMutation, useQueryClient } from '@tanstack/react-query';

const useDeleteTaskGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      try {
        const TaskGroup = ProjectGroupModel;
        return await TaskGroup.deleteProjectGroup(id);
      } catch (error) {
        throw new Error(`Error deleting task group: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['taskGroup'] });
    },
    onError: (error: Error) => {}
  });
};

export default useDeleteTaskGroup;
