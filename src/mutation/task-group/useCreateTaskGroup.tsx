import { useMutation, useQueryClient } from '@tanstack/react-query';
import ProjectGroupModel from '@src/database/model/project-group.model';

const useCreateTaskGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (taskGroup: CreateProjectGroup) => {
      try {
        return await ProjectGroupModel.createTaskGroup(
          taskGroup.title,
          taskGroup.projectId,
          taskGroup.projectGroupId,
          taskGroup.localGroupId
        );
      } catch (error) {
        throw new Error(`Error creating task group: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['taskGroup'] });
    },
    onError: (error: Error) => {}
  });
};

export default useCreateTaskGroup;
