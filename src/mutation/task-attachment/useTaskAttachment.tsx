import TasksAttachmentModel from '@src/database/model/task-attachment.model';
import { useMutation, useQueryClient } from '@tanstack/react-query';

const useDeleteTaskAttachment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      try {
        const TaskMedia = TasksAttachmentModel;
        return await TaskMedia.deleteAttachment(id);
      } catch (error) {
        throw new Error(`Error deleting task media: ${error}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
    onError: (error: Error) => {}
  });
};

export default useDeleteTaskAttachment;
