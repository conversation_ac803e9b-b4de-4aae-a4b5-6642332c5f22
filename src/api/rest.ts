/** Generate by swagger-axios-codegen */
// @ts-nocheck
/* eslint-disable */

/** Generate by swagger-axios-codegen */
/* eslint-disable */
// @ts-nocheck
import axiosStatic, { AxiosInstance, AxiosRequestConfig } from 'axios';

export interface IRequestOptions extends AxiosRequestConfig {}

export interface IRequestConfig {
  method?: any;
  headers?: any;
  url?: any;
  data?: any;
  params?: any;
}

// Add options interface
export interface ServiceOptions {
  axios?: AxiosInstance;
}

// Add default options
export const serviceOptions: ServiceOptions = {};

// Instance selector
export function axios(configs: IRequestConfig, resolve: (p: any) => void, reject: (p: any) => void): Promise<any> {
  if (serviceOptions.axios) {
    return serviceOptions.axios
      .request(configs)
      .then(res => {
        resolve(res.data);
      })
      .catch(err => {
        reject(err);
      });
  } else {
    throw new Error('please inject yourself instance like axios  ');
  }
}

export function getConfigs(method: string, contentType: string, url: string, options: any): IRequestConfig {
  const configs: IRequestConfig = { ...options, method, url };
  configs.headers = {
    ...options.headers,
    'Content-Type': contentType
  };
  return configs;
}

export const basePath = '';

export interface IList<T> extends Array<T> {}
export interface List<T> extends Array<T> {}
export interface IDictionary<TValue> {
  [key: string]: TValue;
}
export interface Dictionary<TValue> extends IDictionary<TValue> {}

export interface IListResult<T> {
  items?: T[];
}

export class ListResultDto<T> implements IListResult<T> {
  items?: T[];
}

export interface IPagedResult<T> extends IListResult<T> {
  totalCount?: number;
  items?: T[];
}

export class PagedResultDto<T = any> implements IPagedResult<T> {
  totalCount?: number;
  items?: T[];
}

// customer definition
// empty

export class MetricsService {
  /**
   *
   */
  static metrics(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/oakajcuqnnfubla/metrics';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
}

export class ProjectDocumentApiService {
  /**
   *
   */
  static downloadZip(
    params: {
      /** requestBody */
      body?: DownloadZipInputDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/cloud-docs/project-document/download-zip';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static downloadBim(
    params: {
      /** requestBody */
      body?: DownloadZipInputDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/cloud-docs/project-document/download-bim';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static downloadBulkZip(
    params: {
      /** requestBody */
      body?: DownloadBulkZipInputDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/cloud-docs/project-document/download-bulk-zip';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static filterData(
    params: {
      /** requestBody */
      body?: FilterProjectsDocumentsDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/cloud-docs/project-document/filter-data';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static searchData(
    params: {
      /** requestBody */
      body?: SearchProjectsDocumentsDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/cloud-docs/project-document/search-data';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static bimStatus(
    params: {
      /** requestBody */
      body?: BIMStatusInputDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/cloud-docs/project-document/BIM-status';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static translateSvfToDwg(
    params: {
      /** requestBody */
      body?: TranslateSvfToDwgDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/cloud-docs/project-document/translate-svf-to-dwg';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static document(
    params: {
      /**  */
      id: string;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/cloud-docs/project-document/document';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);
      configs.params = { ID: params['id'] };

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static projectDocsSubGroup(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/cloud-docs/project-document/project-docs-sub-group';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static projectDocsSubGroupCode(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/cloud-docs/project-document/project-docs-sub-group-code';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static projectDocsUngroupFix(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/cloud-docs/project-document/project-docs-ungroup-fix';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static projectDocsWorkflowFix(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/cloud-docs/project-document/project-docs-workflow-fix';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static copyDrawingToDrawingRevision(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/cloud-docs/project-document/copy-drawing-to-drawing-revision';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
}

export class IntegrationApiService {
  /**
   *
   */
  static autodeskAuth(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/integration/autodesk-auth';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static autodeskViewer(
    params: {
      /**  */
      autodeskToken: string;
      /**  */
      urn: string;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/integration/autodesk-viewer';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);
      configs.params = { autodeskToken: params['autodeskToken'], urn: params['urn'] };

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
}

export class AdminAuthService {
  /**
   *
   */
  static signIn(
    params: {
      /** requestBody */
      body?: AdminLoginInputDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/auth/admin/sign-in';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static forgetPassword(
    params: {
      /** requestBody */
      body?: AdminForgotPasswordInputDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/auth/admin/forget-password';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static resetPassword(
    params: {
      /** requestBody */
      body?: AdminResetPasswordInputDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/auth/admin/reset-password';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static changePassword(
    params: {
      /** requestBody */
      body?: AdminChangePasswordInputDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/auth/admin/change-password';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static logout(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/auth/admin/logout';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = null;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static revokeAuthentication(
    params: {
      /** requestBody */
      body?: AdminRevokeTokenInputDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/auth/admin/revoke-authentication';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
}

export class UserAuthService {
  /**
   *
   */
  static signIn(
    params: {
      /** requestBody */
      body?: UserLoginInputDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/auth/user/sign-in';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static appleSignIn(
    params: {
      /** requestBody */
      body?: AppleTokenInputDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/auth/user/apple-signIn';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static facebookSignIn(
    params: {
      /** requestBody */
      body?: FacebookTokenInputDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/auth/user/facebook-signIn';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static googleSignIn(
    params: {
      /** requestBody */
      body?: GoogleTokenInputDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/auth/user/google-signIn';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static signUp(
    params: {
      /** requestBody */
      body?: UserSignUpInputDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/auth/user/sign-up';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static emailVerification(
    params: {
      /** requestBody */
      body?: UserOnboardingSignInInputDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/auth/user/email-verification';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static isFirstTimeSignIn(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/auth/user/is-first-time-sign-in';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static forgotPassword(
    params: {
      /** requestBody */
      body?: UserForgotPasswordInputDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/auth/user/forgot-password';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static resetPassword(
    params: {
      /** requestBody */
      body?: UserResetPasswordInputDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/auth/user/reset-password';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static changePassword(
    params: {
      /** requestBody */
      body?: UserChangePasswordInput;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/auth/user/change-password';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static logout(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/auth/user/logout';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = null;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static currentRole(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/auth/user/current-role';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static session(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/auth/user/session';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static isCompanyOwner(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/auth/user/is-company-owner';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static revokeAuthentication(
    params: {
      /** requestBody */
      body?: UserRevokeTokenInputDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/auth/user/revoke-authentication';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static removeFcmToken(
    params: {
      /** requestBody */
      body?: UserRemoveFcmTokenInputDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/auth/user/remove-fcm-token';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static changeSignatureToken(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/auth/user/change-signature-token';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
}

export class UserApiService {
  /**
   *
   */
  static changeIsReadChangeLog(
    params: {
      /** requestBody */
      body?: IsReadChangeLogDTO;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/users/change-isReadChangeLog';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
}

export class ContactImportApiService {
  /**
   *
   */
  static import(
    params: {
      /** requestBody */
      body?: ContactImportDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/contact/import';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
}

export class DashboardApiService {
  /**
   *
   */
  static overview(
    params: {
      /** requestBody */
      body?: OverviewDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/dashboard/overview';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static tasksEventsList(
    params: {
      /** requestBody */
      body?: GetCalenderMonthDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/dashboard/tasks-events-list';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static restructureTasksEventsList(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/dashboard/restructure-tasks-events-list';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
}

export class NotificationApiService {
  /**
   *
   */
  static getNotifications(
    params: {
      /** requestBody */
      body?: GetNotificationByIdDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/notifications/get-notifications';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static getNotificationUnseenCount(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/notifications/get-notification-unseen-count';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static markAsRead(
    params: {
      /** requestBody */
      body?: markNotificationAsReadDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/notifications/mark-as-read';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static setDeviceToken(
    params: {
      /** requestBody */
      body?: setDeviceTokenDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/notifications/set-device-token';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static removeDeviceToken(
    params: {
      /** requestBody */
      body?: setDeviceTokenDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/notifications/remove-device-token';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
}

export class ProjectGroupApiService {
  /**
   *
   */
  static create(
    params: {
      /** requestBody */
      body?: ProjectGroupInputDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/project-group/create';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static options(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/project-group/options';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static all(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/project-group/all';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static searchProjectGroups(
    params: {
      /** requestBody */
      body?: ProjectGroupSearchDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/project-group/searchProjectGroups';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static update(
    params: {
      /** requestBody */
      body?: ProjectGroupUpdateDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/project-group/update';

      const configs: IRequestConfig = getConfigs('patch', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static delete(
    params: {
      /** requestBody */
      body?: ProjectGroupDeleteDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/project-group/delete';

      const configs: IRequestConfig = getConfigs('delete', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static groupsDocumentsCount(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/project-group/groups-documents-count';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static getTotalDocuments(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/project-group/get-total-documents';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static taskGroupSubGroup(
    params: {
      /**  */
      subGroup: boolean;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/project-group/task-group-sub-group';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);
      configs.params = { subGroup: params['subGroup'] };

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
}

export class ProjectInvitationApiService {
  /**
   *
   */
  static createInvitations(
    params: {
      /** requestBody */
      body?: ProjectInvitationInputDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/projects/create-invitations';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static inviterAndTitle(
    params: {
      /** requestBody */
      body?: ProjectInvitationRef;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/projects/inviter-and-title';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static invitation(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/projects/invitation';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static updateMemberRole(
    params: {
      /** requestBody */
      body?: ProjectInvitationUpdateRoleInputDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/projects/update-member-role';

      const configs: IRequestConfig = getConfigs('patch', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
}

export class TasksApiService {
  /**
   *
   */
  static filterTasks(
    params: {
      /** requestBody */
      body?: TaskFilterDTO;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/tasks/filter-tasks';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static taskAuthority(
    params: {
      /** requestBody */
      body?: TaskAuthority;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/tasks/task-authority';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static tasksSubGroup(
    params: {
      /**  */
      subGroup: boolean;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/tasks/tasks-sub-group';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);
      configs.params = { subGroup: params['subGroup'] };

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
}

export class WorkspaceGroupApiService {
  /**
   *
   */
  static getTotalDocuments(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/workspaceGroup/overview/get-total-documents';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static groupsDocumentsCount(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/workspaceGroup/overview/groups-documents-count';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static workspaceSubGroup(
    params: {
      /**  */
      subGroup: boolean;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/workspaceGroup/overview/workspace-sub-group';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);
      configs.params = { subGroup: params['subGroup'] };

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static workspaceScript(
    params: {
      /**  */
      script: boolean;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/workspaceGroup/overview/workspace-script';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);
      configs.params = { script: params['script'] };

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
}

export class SystemService {
  /**
   *
   */
  static version(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/system/system/version';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static alive(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/system/system/alive';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static migration(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/system/system/migration';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static calculation(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/system/system/calculation';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static presignedUpload(
    params: {
      /**  */
      key: string;
      /**  */
      size: number;
      /**  */
      mimeType: string;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/system/presigned-upload';

      const configs: IRequestConfig = getConfigs('get', 'application/json', url, options);
      configs.params = { key: params['key'], size: params['size'], mimeType: params['mimeType'] };

      /** 适配ios13，get请求不允许带body */

      axios(configs, resolve, reject);
    });
  }
}

export class SyncApiService {
  /**
   *
   */
  static sync(
    params: {
      /**  */
      tableName: string;
      /** requestBody */
      body?: SyncInputDto;
    } = {} as any,
    options: IRequestOptions = {}
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/sync/sync/{tableName}';
      url = url.replace('{tableName}', params['tableName'] + '');

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = params.body;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
}

export class EmailApiService {
  /**
   *
   */
  static extractEmails(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/email/webhooks/extract-emails';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = null;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
  /**
   *
   */
  static captureStatus(options: IRequestOptions = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      let url = basePath + '/api/email/webhooks/capture-status';

      const configs: IRequestConfig = getConfigs('post', 'application/json', url, options);

      let data = null;

      configs.data = data;

      axios(configs, resolve, reject);
    });
  }
}

export interface DownloadZipInputDto {
  /**  */
  id: number;
}

export interface DownloadBulkZipInputDto {
  /**  */
  ids: string[];
}

export interface FilterProjectsDocumentsDto {
  /**  */
  name: object;

  /**  */
  category: string;

  /**  */
  projectDocumentId?: string;

  /**  */
  allFormCode?: number;

  /**  */
  status?: object;

  /**  */
  group?: object;

  /**  */
  assignedTo?: object;
}

export interface SearchProjectsDocumentsDto {
  /**  */
  name: object;

  /**  */
  category: string;

  /**  */
  projectDocumentId?: string;
}

export interface BIMStatusInputDto {
  /**  */
  urn: string;
}

export interface TranslateSvfToDwgDto {
  /**  */
  urn: string;

  /**  */
  config: string;

  /**  */
  haveReference?: boolean;
}

export interface AdminLoginInputDto {
  /**  */
  email: string;

  /**  */
  password: string;
}

export interface AdminForgotPasswordInputDto {
  /**  */
  email: string;
}

export interface AdminResetPasswordInputDto {
  /**  */
  resetToken: string;

  /**  */
  newPassword: string;
}

export interface AdminChangePasswordInputDto {
  /**  */
  oldPassword: string;

  /**  */
  newPassword: string;
}

export interface AdminRevokeTokenInputDto {
  /**  */
  refreshToken: string;
}

export interface UserLoginInputDto {
  /**  */
  email: string;

  /**  */
  password: string;

  /**  */
  rememberMe: boolean;

  /**  */
  clientDeviceInfo?: string;
}

export interface AppleTokenInputDto {
  /**  */
  appleToken: string;
}

export interface FacebookTokenInputDto {
  /**  */
  facebookToken: string;
}

export interface GoogleTokenInputDto {
  /**  */
  googleToken: string;
}

export interface UserSignUpInputDto {
  /**  */
  email: string;

  /**  */
  password: string;
}

export interface UserOnboardingSignInInputDto {
  /**  */
  signUpToken: string;
}

export interface UserForgotPasswordInputDto {
  /**  */
  email: string;
}

export interface UserResetPasswordInputDto {
  /**  */
  resetToken: string;

  /**  */
  newPassword: string;
}

export interface UserChangePasswordInput {
  /**  */
  oldPassword: string;

  /**  */
  newPassword: string;
}

export interface UserRevokeTokenInputDto {
  /**  */
  refreshToken: string;
}

export interface UserRemoveFcmTokenInputDto {
  /**  */
  fcmToken: string;
}

export interface IsReadChangeLogDTO {}

export interface ContactImportType {
  /**  */
  name: string;

  /**  */
  company: string;

  /**  */
  email?: string;

  /**  */
  phoneNo: string;
}

export interface ContactImportDto {
  /**  */
  contacts: ContactImportType[];
}

export interface OverviewDto {
  /**  */
  workspaceGroupId: string;

  /**  */
  TaskGroupId: string;
}

export interface GetCalenderMonthDto {
  /**  */
  month: string;
}

export interface GetNotificationByIdDto {
  /**  */
  page: number;
}

export interface markNotificationAsReadDto {
  /**  */
  messageId: string;
}

export interface setDeviceTokenDto {
  /**  */
  deviceToken: string;
}

export interface ProjectGroupInputDto {
  /**  */
  title: string;

  /**  */
  projectGroupId?: number;
}

export interface ProjectGroupSearchDto {
  /**  */
  title: string;
}

export interface ProjectGroupUpdateDto {
  /**  */
  title: string;

  /**  */
  id: number;
}

export interface ProjectGroupDeleteDto {
  /**  */
  id: number;
}

export interface ProjectInvitationType {
  /**  */
  email: string;

  /**  */
  role: EnumProjectInvitationTypeRole;
}

export interface ProjectInvitationInputDto {
  /**  */
  projectInvitations: ProjectInvitationType[];
}

export interface ProjectInvitationRef {
  /**  */
  invitationRef: string;
}

export interface ProjectInvitationUpdateRoleInputDto {
  /**  */
  userId: number;

  /**  */
  role: string;
}

export interface TaskFilterDTO {
  /**  */
  title: string;

  /**  */
  taskCode: number;

  /**  */
  status: object;

  /**  */
  dueDate: string;

  /**  */
  group: string;

  /**  */
  assignedTo: string;
}

export interface TaskAuthority {
  /**  */
  taskId: number;
}

export interface SyncInputDto {
  /**  */
  lastPulledAt: Date;

  /**  */
  lastPushedAt?: Date;

  /**  */
  module?: EnumSyncInputDtoModule;

  /**  */
  parentIds?: number[];

  /**  */
  offset?: number;

  /**  */
  limit?: number;
}
export enum EnumProjectInvitationTypeRole {
  'CanEdit' = 'CanEdit',
  'CanView' = 'CanView',
  'ProjectOwner' = 'ProjectOwner',
  'CloudCoordinator' = 'CloudCoordinator'
}
export enum EnumSyncInputDtoModule {
  'Project' = 'Project',
  'Task' = 'Task',
  'TaskComment' = 'TaskComment',
  'Photo' = 'Photo',
  'Drawing' = 'Drawing',
  'Workspace' = 'Workspace',
  'WorkspaceComment' = 'WorkspaceComment',
  'Calendar' = 'Calendar',
  'SCurve' = 'SCurve',
  'Overview' = 'Overview',
  'CloudDocument' = 'CloudDocument',
  'Schedule' = 'Schedule',
  'Member' = 'Member',
  'ProjectGroup' = 'ProjectGroup',
  'WorkspaceGroup' = 'WorkspaceGroup',
  'DocumentEditor' = 'DocumentEditor'
}
