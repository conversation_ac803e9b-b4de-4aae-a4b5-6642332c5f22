import { apiCaller } from '@lib/axios';
import { API_URL } from '@constants';

export const signUp = (data: SignUp) => apiCaller.post('/api/auth/customer-register', data);
export const requestOTP = (data: RequestOTP) => apiCaller.post('/api/auth/request-otp', data);
export const verifyOTP = (data: VerifyOTP) => apiCaller.post('/api/auth/verify-otp', data);
export const login = (data: Login) => apiCaller.post('/api/auth/customer-login', data);
export const smsLogin = (data: SMSLogin) => apiCaller.post('/api/auth/phone/request-login', data);
export const verifyLoginToken = (data: VerifyLoginToken) => apiCaller.post('/api/auth/phone/login', data);
export const resetPassword = (data: ResetPassword) => apiCaller.post('/api/auth/customer-reset-password', data);
export const revokeToken = (data: RevokeToken) => apiCaller.post('/api/auth/phone/refresh', data);
export const logout = (data: Logout) => apiCaller.post('/api/auth/customer-logout', data);

interface SignUp {
  email: string;
  countryCode: string | undefined;
  phone: string | undefined;
  name: string | undefined;
  password: string;
  verificationCode: any;
}

interface Login {
  countryCode: string;
  phone: string;
  password: string;
}

interface SMSLogin {
  phone: string;
  phoneCountryCode: string;
}

interface RevokeToken {
  refreshToken: string;
}

interface RequestOTP {
  // type: 'SignUp' | 'Login';
  countryCode: string;
  phone: string;
}

interface VerifyLoginToken {
  loginToken: string;
}

interface VerifyOTP {
  phoneCountryCode: string;
  phoneNumber: string;
  code: string;
}

interface ResetPassword {
  countryCode: string;
  phone: string;
  otp: string;
  newPassword: string;
  confirmPassword: string;
}

interface Logout {
  fcmToken: string | any;
}

export const revokeAuthenticate = `${API_URL}/api/auth/phone/refresh`;
