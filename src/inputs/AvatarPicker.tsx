import React, { useRef, useState, useEffect } from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import { Box, Text, HStack, Circle } from 'native-base';
import { Icon, Modal, ModalRef } from '@commons';
import { FieldProps } from 'formik';
import ImagePicker from 'react-native-image-crop-picker';
import { preventDoubleClick } from '@configs';
import { generateRNFile } from '@configs/utils';
import { COLORS } from '@constants';
import _ from 'lodash';
import FastImage from 'react-native-fast-image';

interface Props extends FieldProps {
  buttonType: string;
  label?: string;
}
interface Avatar {
  path: string;
}
const AvatarPicker: React.FC<Props> = props => {
  const modalRef = useRef<ModalRef>(null);
  const [avatar, setAvatar] = useState<Avatar>({ path: '' });
  const { field, form, buttonType } = props;
  const error =
    (_.get(form.touched, field.name) && _.get(form.errors, field.name)) ||
    (form.submitCount > 0 && _.get(form.errors, field.name));

  useEffect(() => {
    if (field.value && _.isString(field.value)) {
      setAvatar({ path: field.value });
    }
  }, [field.value]);

  const openLibrary = () => {
    ImagePicker.openPicker({
      width: 500,
      height: 500,
      cropping: true,
      forceJpg: true,
      includeBase64: true
    }).then(image => {
      setAvatar(image);
      const o = generateRNFile({ uri: image.path, name: 'avatar' });
      form.setFieldValue(field.name, o);
      modalRef.current?.pushModal();
    });
  };

  const openCamera = () => {
    ImagePicker.openCamera({
      width: 500,
      height: 500,
      cropping: true,
      forceJpg: true,
      includeBase64: true
    }).then(image => {
      setAvatar(image);
      const o = generateRNFile({ uri: image.path, name: 'avatar' });
      form.setFieldValue(field.name, o);
      modalRef.current?.pushModal();
    });
  };

  return (
    <HStack>
      <Box style={{ width: '50%' }} flexDir={'column'}>
        <TouchableOpacity onPress={() => modalRef.current?.pushModal()}>
          <Text color={COLORS.neutrals.gray90}>{props?.label ?? 'Profile Photo'}</Text>
          <Box style={props?.avatarContainer ?? styles.avatarContainer} mb={5}>
            <Box>
              {avatar.path ? (
                <Circle size="64px">
                  <FastImage
                    source={{ uri: avatar.path }}
                    style={styles.avatar}

                    // style={{ borderColor: '#667388', borderWidth: 1, marginBottom: 16 }}
                  />
                </Circle>
              ) : (
                <Circle style={[styles.avatar, error ? { borderColor: '#ED2939', borderWidth: 2 } : undefined]}>
                  <Icon name="camera" fill={COLORS.primary.light} />
                </Circle>
              )}
            </Box>
            <Text variant="buttonText14" color={error ? 'semantics.danger' : 'primary.1'} mb={2} ml={2} mt={5}>
              {buttonType === 'upload' && avatar.path == '' ? ' Upload photo' : ' Change photo'}
            </Text>
          </Box>
        </TouchableOpacity>
      </Box>
      <Modal ref={modalRef} type="bottom" swipeDirection="down">
        <ModalItem label="Take Photo" onPress={() => openCamera()} />
        <ModalItem label="Choose From Library" onPress={() => openLibrary()} />
        <ModalItem label="Cancel" onPress={() => modalRef.current?.pushModal()} />
      </Modal>
    </HStack>
  );
};

type ModalItemProps = { label: string; onPress: () => void };
const ModalItem: React.FC<ModalItemProps> = props => {
  return (
    <Box>
      <TouchableOpacity
        onPress={() => {
          preventDoubleClick(() => props.onPress());
        }}
      >
        <HStack justifyContent="center" alignItems="center" borderBottomWidth={1} borderColor="#E0E0E0" py={4}>
          <Text variant="body1" color="neutrals.black">
            {props.label}
          </Text>
        </HStack>
      </TouchableOpacity>
    </Box>
  );
};

const styles = StyleSheet.create({
  avatarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center'
  },
  avatar: {
    width: 64,
    height: 64,
    backgroundColor: COLORS.primary.light,
    borderRadius: 60,
    borderWidth: 1,
    borderColor: COLORS.primary.light,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 1,
    marginTop: 2
  }
});

export { AvatarPicker };
export default AvatarPicker;
