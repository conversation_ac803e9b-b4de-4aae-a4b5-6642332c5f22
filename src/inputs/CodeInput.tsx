import React, { useState, useEffect } from 'react';
import { StyleSheet, Platform } from 'react-native';
import { Text, HStack } from 'native-base';
import { CodeField, Cursor, useBlurOnFulfill, useClearByFocusCell } from 'react-native-confirmation-code-field';

type Props = { onChange?: (code: string) => void };
const CodeInput: React.FC<Props> = ({ onChange }) => {
  const CELL_COUNT = 6;
  const [value, setValue] = useState<string>('');
  const ref = useBlurOnFulfill({ value, cellCount: CELL_COUNT });
  const [props, getCellOnLayoutHandler] = useClearByFocusCell({
    value,
    setValue
  });

  useEffect(() => {
    setTimeout(() => {
      ref.current?.focus();
    }, 300);
  }, []);

  const onChangeText = (code: string) => {
    const trimNumber = code.replace(/[^0-9]/g, '');
    setValue(trimNumber);
    if (onChange) onChange(trimNumber);
  };

  // TODO: COPY OTP FROM SMS
  return (
    <CodeField
      ref={ref}
      {...props}
      value={value}
      onChangeText={onChangeText}
      cellCount={CELL_COUNT}
      rootStyle={styles.codeFieldRoot}
      keyboardType={Platform.OS === 'android' ? 'numeric' : 'number-pad'}
      textContentType="oneTimeCode"
      renderCell={({ index, symbol, isFocused }) => (
        <HStack style={[styles.cell, isFocused && styles.focusCell]} onLayout={getCellOnLayoutHandler(index)}>
          <Text fontFamily="Inter" fontWeight={600} fontSize={28}>
            {symbol || (isFocused ? <Cursor /> : null)}
          </Text>
        </HStack>
      )}
    />
  );
};

const styles = StyleSheet.create({
  codeFieldRoot: { marginBottom: 20 },
  cell: {
    width: 52,
    height: 52,
    borderWidth: 1,
    borderRadius: 8,
    borderColor: '#D4D6DB',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFF'
  },
  focusCell: {
    shadowColor: '#4e4e4e',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 2,
    backgroundColor: 'white'
  }
});

export { CodeInput };
export default CodeInput;
