import { FieldProps } from 'formik';
import { Box, IInputProps, Input, Text } from 'native-base';
import React, { useEffect, useImperativeHandle, useRef } from 'react';
import { KeyboardType, KeyboardTypeAndroid, KeyboardTypeIOS } from 'react-native';

import _ from 'lodash';

interface Props extends FieldProps, IInputProps {
  secureTextEntry?: boolean;
  disabled?: boolean;
  placeholder?: string;
  autoFocus?: boolean;
  label?: string;
  maxLength?: number;
  addOnLeft?: any;
  type?: 'username' | 'normal';
  paddingLeft?: number;
  direction?: 'vertical' | 'horizontal';
  suffix?: string;
  keyboardType?: KeyboardType | KeyboardTypeAndroid | KeyboardTypeIOS;
  widthBelt?: string;
  widthJoint?: string;
  InputRightElement?: any;
  autoCapitalize?: string;
}

const TextInput: React.FC<Props> = props => {
  const { field, form } = props;
  const inputRef = useRef(null);

  const error =
    (_.get(form.touched, field.name) && _.get(form.errors, field.name)) ||
    (form.submitCount > 0 && _.get(form.errors, field.name));

  const onChangeText = (value: string | undefined) => {
    form.setFieldValue(field.name, value);
  };

  return (
    <Box mb={3} color="red.400">
      {props.label && (
        <Text flex={1} variant="bodyReg" mb={2}>
          {props.label}
        </Text>
      )}
      <Input
        ref={inputRef}
        isDisabled={props.disabled}
        height={props?.height || 58}
        borderRadius={10}
        borderColor="#E6E6E6"
        borderWidth={1}
        backgroundColor="#FFFFFF"
        fontWeight="400"
        fontSize={16}
        color="#000000"
        placeholder={props.placeholder}
        paddingLeft={3}
        autoCapitalize="none"
        _focus={{ borderColor: 'neutrals.gray40', bg: '#FFF' }}
        flex={1}
        rounded={8}
        onChangeText={onChangeText}
        value={field.value}
        keyboardType={props.keyboardType}
        secureTextEntry={props.secureTextEntry}
        InputRightElement={props.InputRightElement}
      />
      {error && (
        <Text variant="bodyReg" color="semantics.danger" mt={1}>
          {`${error}`}
        </Text>
      )}
    </Box>
  );
};

export { TextInput };
export default TextInput;
