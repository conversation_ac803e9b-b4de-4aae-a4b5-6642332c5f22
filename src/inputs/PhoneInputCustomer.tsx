import React, { useRef, useEffect, useState } from 'react';
import { StyleSheet, Platform } from 'react-native';
import { FieldProps } from 'formik';
import { Box, HStack, Text, VStack } from 'native-base';
import { Icon } from '@commons';
import TextInputMask from 'react-native-text-input-mask';
import _ from 'lodash';
import { FONTS, COLORS } from '@constants';

interface Props extends FieldProps {
  placeholder?: string;
  autoFocus?: boolean;
  label?: string;
  disabled?: boolean;
}
const PhoneInputCustomer: React.FC<Props> = props => {
  const { field, form, placeholder, autoFocus = false, disabled } = props;
  const inputRef = useRef<any>(null);
  const [focus, setFocus] = useState<boolean>(autoFocus);
  const error =
    (_.get(form.touched, field.name) && _.get(form.errors, field.name)) ||
    (form.submitCount > 0 && _.get(form.errors, field.name));

  useEffect(() => {
    if (autoFocus) {
      setTimeout(() => {
        inputRef?.current?.focus();
      }, 300);
    }
  }, [autoFocus]);

  const onChange = (value: string | undefined) => {
    const phone = value || '';
    const removeZero = phone.slice(0, 10).replace(/^0+/, '');
    const trimNumber = removeZero.replace(/[^0-9]/g, '');
    form.setFieldValue(field.name, trimNumber);
  };

  return (
    <Box mb={3}>
      <VStack style={[styles.container]}>
        {props.label && (
          <Text color="neutrals.gray50" fontSize={12} fontWeight={500}>
            {props.label}
          </Text>
        )}
        <HStack mt={-2} mb={-2} alignItems="center">
          <HStack w="13%" alignItems="center">
            <Text fontSize={16}>+60</Text>
          </HStack>
          <Box flex={1}>
            <TextInputMask
              onFocus={() => setFocus(true)}
              onBlur={() => setFocus(false)}
              ref={inputRef}
              autoCapitalize="none"
              placeholderTextColor="#B3B7C1"
              autoCorrect={false}
              blurOnSubmit={false}
              value={field.value}
              // @ts-ignore
              onChangeText={(formatted, extracted) => {
                onChange(extracted);
              }}
              style={[styles.textStyle, focus && styles.focusedInput]}
              keyboardType={Platform.OS === 'android' ? 'numeric' : 'number-pad'}
              placeholder={placeholder}
              mask={'[00] [000] [00000]'}
            />
          </Box>
        </HStack>
        {disabled && <Box position="absolute" h="42px" w="100%" bg="transparent" />}
      </VStack>
      {/* {error && (
        <Text variant="body2" color="semantics.danger" mt={1}>
          {error}
        </Text>
      )} */}
    </Box>
  );
};

const styles = StyleSheet.create({
  container: {
    // height: 60,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderColor: COLORS.neutrals.gray25,
    borderWidth: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12
  },
  // inputWrapper: {
  //   height: 40,
  //   borderColor: '#D4D6DB',
  //   borderBottomWidth: 1,
  //   backgroundColor: '#FFFFFF'
  // },
  focusedInput: {
    // borderColor: COLORS.primary
  },
  textStyle: {
    fontFamily: FONTS.inter,
    fontSize: 16,
    color: COLORS.neutrals.black
    // paddingHorizontal: 16
  }
});

export { PhoneInputCustomer };
export default PhoneInputCustomer;
