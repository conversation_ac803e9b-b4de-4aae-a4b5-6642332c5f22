import React from 'react';
import { FieldProps } from 'formik';
import { Box, Text, Select } from 'native-base';
import _ from 'lodash';

interface Props extends FieldProps {
  options: Option[];
  placeholder?: string;
  label?: string;
}

interface Option {
  label: string;
  value: string;
}

const SelectInput: React.FC<Props> = props => {
  const { options = [], field, form } = props;
  const error =
    (_.get(form.touched, field.name) && _.get(form.errors, field.name)) ||
    (form.submitCount > 0 && _.get(form.errors, field.name));

  const onValueChange = (selectedItem: any) => {
    form.setFieldValue(field.name, selectedItem);
  };

  return (
    <Box style={{ marginBottom: 16 }}>
      {props.label && (
        <Text variant="bodyReg" mb={2}>
          {props.label}
        </Text>
      )}
      <Select
        height={57}
        borderRadius={10}
        borderColor="#E6E6E6"
        borderWidth={1}
        backgroundColor="#FFFFFF"
        fontWeight="400"
        fontSize={16}
        color="#000000"
        paddingLeft={3}
        selectedValue={field.value}
        onValueChange={onValueChange}
        placeholder={props.placeholder}
        accessibilityLabel={props.label}
        _selectedItem={{ bg: 'gray.300' }}
      >
        {_.map(options, o => (
          <Select.Item key={o.value} label={o.label} value={o.value} />
        ))}
      </Select>
      {error && (
        <Text variant="body2" color="semantics.danger" mt={1}>
          {error}
        </Text>
      )}
    </Box>
  );
};

export { SelectInput };
export default SelectInput;
