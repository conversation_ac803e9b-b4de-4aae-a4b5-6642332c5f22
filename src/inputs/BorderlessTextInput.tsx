import { FieldProps } from 'formik';
import { Box, IInputProps, Input, Text } from 'native-base';
import React from 'react';
import { KeyboardType, KeyboardTypeAndroid, KeyboardTypeIOS } from 'react-native';

import _ from 'lodash';

interface Props extends FieldProps, IInputProps {
  secureTextEntry?: boolean;
  fontSize?: number;
  fontWeight?: string;
  placeholder?: string;
  autoFocus?: boolean;
  label?: string;
  maxLength?: number;
  addOnLeft?: any;
  type?: 'text' | 'normal';
  paddingLeft?: number;
  direction?: 'vertical' | 'horizontal';
  suffix?: string;
  keyboardType?: KeyboardType | KeyboardTypeAndroid | KeyboardTypeIOS;
  widthBelt?: string;
  widthJoint?: string;
  InputRightElement?: any;
  autoCapitalize?: string;
  readonly?: boolean;
}

const BorderlessTextInput: React.FC<Props> = props => {
  const { field, form } = props;
  const error =
    (_.get(form.touched, field.name) && _.get(form.errors, field.name)) ||
    (form.submitCount > 0 && _.get(form.errors, field.name));

  const onChangeText = (value: string | undefined) => {
    form.setFieldValue(field.name, value);
  };

  return (
    <Box mb={3} color="red.400">
      {props.label && (
        <Text flex={1} variant="bodyReg" mb={2}>
          {props.label}
        </Text>
      )}
      <Input
        autoCapitalize="none"
        color="neutrals.black"
        borderColor="neutrals.gray40"
        variant="unstyled"
        fontSize={props.fontSize ? props.fontSize : 16}
        fontWeight={props.fontWeight}
        _focus={{ borderColor: 'neutrals.gray40', bg: '#FFF' }}
        onChangeText={onChangeText}
        placeholder={props.placeholder}
        value={field.value}
        keyboardType={props.keyboardType}
        secureTextEntry={props.secureTextEntry}
        InputRightElement={props.InputRightElement}
      />
      {error && (
        <Text variant="bodyReg" color="semantics.danger" mt={1}>
          {`${error}`}
        </Text>
      )}
    </Box>
  );
};

export { BorderlessTextInput };
export default BorderlessTextInput;
