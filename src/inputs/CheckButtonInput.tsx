import React from 'react';
import { StyleSheet } from 'react-native';
import { FieldProps } from 'formik';
import { Box, Button, Text } from 'native-base';
import _ from 'lodash';

interface Props extends FieldProps {
  options: Option[];
  max?: number;
  column?: number;
  onlyOne?: boolean;
  label?: string;
}

interface Option {
  label: string;
  value: string;
}

const CheckButtonInput: React.FC<Props> = props => {
  const { form, field, options = [], max = 1, column = 2, onlyOne = false, label } = props;
  const { setFieldValue } = form;

  const error =
    (_.get(form.touched, field.name) && _.get(form.errors, field.name)) ||
    (form.submitCount > 0 && _.get(form.errors, field.name));

  const onHandleChange = (value: string) => {
    try {
      if (onlyOne) {
        setFieldValue(field.name, value);
      } else {
        const fieldValue = _.get(field, 'value', []);
        if (fieldValue.length !== 0) {
          const hasValue = _.includes(fieldValue, value);
          if (hasValue) {
            const currentValue = [...fieldValue, value];
            setFieldValue(field.name, _.pull(currentValue, value));
          } else {
            setFieldValue(field.name, [...fieldValue, value]);
          }
        } else {
          setFieldValue(field.name, [value]);
        }
      }
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  };

  const checkSelected = (value: string) => {
    const selected = _.get(field, 'value', []);
    const checked = !!(selected.length !== 0 && _.includes(selected, value));
    return checked ? true : false;
  };

  const checkSelectedOne = (value: string) => {
    const selected = field.value;
    return selected === value ? true : false;
  };

  const isDisabled = (value: string) => {
    const checked = _.get(field, 'value', []);
    return checked.length >= max && checked.indexOf(value) === -1;
  };

  const spacings = ['left', 'right'];
  let num = 0;
  let space = 13;

  return (
    <Box overflow="hidden" style={{ marginBottom: 16 }}>
      {label && (
        <Text variant="body2" color="neutrals.gray2" mb={2}>
          {label}
        </Text>
      )}
      <Box flexDirection="row" flexWrap="wrap" flex={1}>
        {_.map(options, (item, index) => {
          if (num === 0) {
            num = 1;
          } else if (num === 1) {
            num = 0;
          }
          const align = spacings[num];

          return (
            <Box style={[styles.item, { width: `${Number(100 / column)}%` }]} key={index}>
              <Button
                _text={
                  onlyOne
                    ? checkSelectedOne(item.value)
                      ? styles.selectedFont
                      : undefined
                    : checkSelected(item.value)
                      ? styles.selectedFont
                      : undefined
                }
                onPress={() => onHandleChange(item.value)}
                variant="secondary"
                isDisabled={onlyOne ? false : isDisabled(item.value)}
                style={[
                  align === 'right' && { marginRight: space / 2 },
                  align === 'left' && { marginLeft: space / 2 },
                  { marginBottom: space },
                  onlyOne
                    ? checkSelectedOne(item.value)
                      ? styles.selected
                      : undefined
                    : checkSelected(item.value)
                      ? styles.selected
                      : undefined
                ]}
              >
                {item.label}
              </Button>
            </Box>
          );
        })}
      </Box>
      {error && (
        <Text variant="body2" color="semantics.danger" mt={1}>
          {error}
        </Text>
      )}
    </Box>
  );
};

const styles = StyleSheet.create({
  item: {},
  selected: {
    borderColor: '#609C66'
  },
  selectedFont: {
    color: '#609C66'
  }
});

export { CheckButtonInput };
export default CheckButtonInput;
