import React, { useRef, useEffect, useState } from 'react';
import { StyleSheet, Platform } from 'react-native';
import { FieldProps } from 'formik';
import { Box, HStack, Text } from 'native-base';
import { Icon } from '@commons';
import TextInputMask from 'react-native-text-input-mask';
import _ from 'lodash';
import { FONTS, COLORS } from '@constants';

interface Props extends FieldProps {
  placeholder?: string;
  autoFocus?: boolean;
  label?: string;
  disabled?: boolean;
  error: string | undefined;
}
const PhoneInput: React.FC<Props> = props => {
  const { field, form, placeholder, autoFocus = false, disabled } = props;
  const inputRef = useRef<any>(null);
  const [focus, setFocus] = useState<boolean>(autoFocus);
  const phoneNumberPattern = /^\+60\d{9,10}$/;
  const error =
    (_.get(form.touched, field.name) && _.get(form.errors, field.name)) ||
    (form.submitCount > 0 && _.get(form.errors, field.name));

  useEffect(() => {
    if (autoFocus) {
      setTimeout(() => {
        inputRef?.current?.focus();
      }, 300);
    }
  }, [autoFocus]);

  const onChange = (value: string | undefined) => {
    const phone = value || '';

    if (phone) {
      const removeZero = phone.slice(0, 10).replace(/^0+/, '');
      const trimNumber = removeZero.replace(/[^0-9]/g, '');
      const formattedNumber = `+60${trimNumber}`;
      // Check if the trimmed phone number matches the validation pattern
      if (phoneNumberPattern.test(formattedNumber)) {
        form.setFieldValue(field.name, trimNumber);
      } else {
        // Display an error message for invalid phone numbers
        form.setFieldError(field.name, 'Invalid phone number');
      }
    } else form.setFieldValue(field.name, '');
  };

  return (
    <Box mb={3}>
      {props.label && (
        <Text flex={1} variant="bodyReg" mb={2}>
          {props.label}
        </Text>
      )}
      <HStack space={3}>
        <HStack style={[styles.inputWrapper]} w="25%" alignItems="center">
          <Icon name="malaysia" width={24} height={24} />
          <Text ml={1} fontSize={14}>
            +60
          </Text>
        </HStack>
        <Box flex={1}>
          <TextInputMask
            onFocus={() => setFocus(true)}
            onBlur={() => setFocus(false)}
            ref={inputRef}
            autoCapitalize="none"
            placeholderTextColor={COLORS.neutrals.gray90}
            autoCorrect={false}
            blurOnSubmit={false}
            value={field.value}
            // @ts-ignore
            onChangeText={(formatted, extracted) => {
              onChange(extracted);
            }}
            style={[styles.inputWrapper, styles.textStyle]}
            keyboardType={Platform.OS === 'android' ? 'numeric' : 'number-pad'}
            placeholder={placeholder}
            mask={'[00] [000] [00000]'}
          />
        </Box>
        {disabled && <Box position="absolute" h="42px" w="100%" bg="transparent" />}
      </HStack>
      {props?.error && (
        <Text variant="body2" color="semantics.danger" mt={1}>
          {props?.error}
        </Text>
      )}
    </Box>
  );
};

const styles = StyleSheet.create({
  inputWrapper: {
    height: 48,
    borderColor: '#D4D6DB',
    borderRadius: 6,
    borderWidth: 1,
    padding: 7,
    backgroundColor: '#FFFFFF'
  },

  textStyle: {
    fontFamily: FONTS.inter,
    fontSize: 14,
    color: '#020403'
  }
});

export { PhoneInput };
