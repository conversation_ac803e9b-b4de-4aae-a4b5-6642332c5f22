import { Box, Input, Text } from 'native-base';
import React, { memo, useRef, useState } from 'react';
import { ActivityIndicator, Alert, KeyboardType, KeyboardTypeAndroid, KeyboardTypeIOS, Platform } from 'react-native';
import _ from 'lodash';
import TaskDescriptionModal from '@src/modules/Task/components/Tasks/TaskDescriptionModal';

interface Props {
  secureTextEntry?: boolean;
  fontSize?: number;
  fontWeight?: string;
  placeholder?: string;
  autoFocus?: boolean;
  label?: string;
  maxLength?: number;
  addOnLeft?: any;
  type?: 'text' | 'normal';
  paddingLeft?: number;
  direction?: 'vertical' | 'horizontal';
  suffix?: string;
  keyboardType?: KeyboardType | KeyboardTypeAndroid | KeyboardTypeIOS;
  widthBelt?: string;
  widthJoint?: string;
  InputRightElement?: any;
  autoCapitalize?: string;
  readonly?: boolean;
  textType?: string;
  isTaskOwner?: boolean;
  role?: boolean;
  loading?: boolean;
  value?: string;
  onChangeText?: (value: string) => void;
}

const BorderlessTextInput: React.FC<Props> = props => {
  const descriptionRef = useRef<any>(null);

  return (
    <Box mb={3} color="red.400">
      {props.label && (
        <Text flex={1} variant="bodyReg" mb={2}>
          {props.label}
        </Text>
      )}
      {props.loading ? (
        <ActivityIndicator size="small" color="#808080" />
      ) : (
        <Input
          autoCapitalize="none"
          color="neutrals.black"
          borderColor="neutrals.gray40"
          variant="unstyled"
          fontSize={props.fontSize ? props.fontSize : 16}
          fontWeight={props.fontWeight}
          _focus={{ borderColor: 'neutrals.gray40', bg: '#FFF' }}
          onChangeText={props.role ? props.onChangeText : undefined}
          placeholder={props.placeholder}
          value={props.value}
          keyboardType={props.keyboardType}
          secureTextEntry={props.secureTextEntry}
          InputRightElement={props.InputRightElement}
          isReadOnly={props.readonly}
          onPressOut={() => {
            if (props.textType === 'Description') {
              descriptionRef?.current?.pushModal();
            } else if (props.textType === 'Title') {
              if (!(props.role || props.isTaskOwner)) {
                Alert.alert(`${props?.value}`, '', [
                  {
                    text: 'Done',
                    style: 'default'
                  }
                ]);
                return;
              }
              if (Platform.OS === 'ios') {
                Alert.prompt('Edit Title', 'Input a new title', [
                  {
                    text: 'Cancel',
                    style: 'cancel'
                  },
                  {
                    text: 'OK',
                    onPress: name => {
                      props?.onChangeText?.(name as string);
                    }
                  }
                ]);
              }
              // else if (Platform.OS === 'android') {
              //   //   prompt('Edit Title', 'Input a new title', [
              //     {
              //       text: 'Cancel',
              //       style: 'cancel'
              //     },
              //     {
              //       text: 'OK',
              //       onPress: (name: any) => {
              //         onChangeText(name);
              //       }
              //     }
              //   ]);
              // }
            }
          }}
        />
      )}

      <TaskDescriptionModal
        ref={descriptionRef}
        value={props.value}
        onChange={(value: any) => props?.onChangeText?.(value)}
        textType={props.textType}
        role={props.role}
        isTaskOwner={props.isTaskOwner}
      />
    </Box>
  );
};

export { BorderlessTextInput };
export default memo(BorderlessTextInput);
