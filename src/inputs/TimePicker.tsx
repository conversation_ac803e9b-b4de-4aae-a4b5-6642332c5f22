import { Icon, Modal } from '@commons';
import { COLORS, FONTS } from '@constants';
import { FieldProps } from 'formik';
import moment from 'moment';
import { Box, Button, HStack, Text } from 'native-base';
import React, { useRef, useState } from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import DatePicker from 'react-native-date-picker';

interface Props extends FieldProps {
  placeholder?: string;
  label?: string;
  disabled?: boolean;
}

const TimePicker: React.FC<Props> = props => {
  const modalRef = useRef<any>(null);
  const [time, setTime] = useState(new Date());
  const { field, form, placeholder, disabled } = props;
  // const error =
  //   (_.get(form.touched, field.name) && _.get(form.errors, field.name)) ||
  //   (form.submitCount > 0 && _.get(form.errors, field.name));

  const onOk = () => {
    form.setFieldValue(field.name, time);
    modalRef.current.pushModal();
  };
  return (
    <Box mb={4}>
      {props.label && (
        <Text flex={1} variant="bodyReg" mb={2}>
          {props.label}
        </Text>
      )}

      <TouchableOpacity onPress={() => modalRef.current.pushModal()} activeOpacity={0.98} disabled={disabled}>
        <HStack
          style={[styles.container]}
          // flex={1}
          alignItems="center"
          justifyContent="space-between"
        >
          {field.value ? (
            <Box>
              <Text variant="caption" color="neutrals.gray50">
                {placeholder}
              </Text>
              <Text variant="body1">{moment(field.value).format('h:mm A')}</Text>
            </Box>
          ) : (
            <Text variant="body1" color="neutrals.gray50">
              {placeholder}
            </Text>
          )}
          <Icon name="clock" />
        </HStack>
      </TouchableOpacity>
      <Modal ref={modalRef}>
        <Box py={6} px={5} justifyContent="center">
          <Text variant="headline" mb={3} textAlign="center">
            Select a time
          </Text>
          <Box justifyContent="center">
            <DatePicker mode="time" date={time} onDateChange={setTime} style={{ alignSelf: 'center' }} />
          </Box>
        </Box>
        <HStack px={5}>
          <Button flex={1} mr={3} variant="light" height={54} onPress={() => modalRef?.current?.pushModal()}>
            Cancel
          </Button>
          <Button flex={1} variant="primary" onPress={() => onOk()}>
            Ok
          </Button>
        </HStack>
      </Modal>
      {/* {error && (
        <Text variant="body2" color="semantics.danger" mt={1}>
          {error}
        </Text>
      )} */}
    </Box>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 60,
    borderColor: COLORS.neutrals.gray40,
    borderWidth: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    paddingHorizontal: 16
  },
  title: {
    color: COLORS.primary[1],
    fontFamily: FONTS.interBold,
    fontSize: 18
  },
  text: {
    fontFamily: FONTS.inter,
    fontSize: 16
  },
  selectedDate: {
    backgroundColor: COLORS.primary[1]
  },
  selectedDateText: {
    color: '#FFF'
  }
});

export { TimePicker };
