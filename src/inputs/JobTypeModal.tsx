// import React, { useEffect, useRef, useState } from 'react';
// import { StyleSheet, KeyboardType, TouchableOpacity, KeyboardTypeIOS } from 'react-native';
// import { FieldProps } from 'formik';
// import { Box, Text, Input, HStack, Button } from 'native-base';
// import _ from 'lodash';
// import { FONTS, COLORS } from '@constants';
// import { Icon, Modal, NativeTouchable } from '@commons';

// interface Props extends FieldProps {
//   placeholder?: string;
//   label?: string;
//   arrayHelpers: any;
// }

// const JobTypeModal: React.FC<Props> = props => {
//   const modalRef = useRef<any>(null);
//   const { field, form, arrayHelpers } = props;

//   const jobType = [
//     {
//       label: 'Partial Replacement',
//       value: 'Partial Replacement'
//     },
//     {
//       label: 'Resplice',
//       value: 'Resplice'
//     }
//   ];

//   const onChange = (val: any) => {
//     form.setFieldValue(field.name, val);
//   };

//   return (
//     <Box mb={4}>
//       <Button variant="light" mt={2} onPress={() => modalRef.current.pushModal()}>
//         Add another
//       </Button>
//       <Modal ref={modalRef}>
//         <Box py={1} style={{ borderBottomWidth: StyleSheet.hairlineWidth, borderBottomColor: '#E8E8E8' }}>
//           <Text variant="headline" mb={3} textAlign="center">
//             Type of Job
//           </Text>
//         </Box>
//         <Box py={2} px={5}>
//           <Box py={2}>
//             {_.map(jobType, (o, i) => {
//               const isSelected = field.value === o.value;
//               return (
//                 <NativeTouchable key={`${i}`} onPress={() => onChange(o.value)}>
//                   <HStack px={5} py={3} justifyContent="space-between" alignItems="center">
//                     <Text style={isSelected && { color: '#14A07F' }}>{o.label}</Text>
//                     {isSelected && <Icon name="check" fill="#14A07F" width={18} height={18} />}
//                   </HStack>
//                 </NativeTouchable>
//               );
//             })}
//           </Box>
//         </Box>
//         <Box px={8}>
//           <Button
//             variant="primary"
//             onPress={() => {
//               form.values.jobTypeInModal === 'Partial Replacement'
//                 ? arrayHelpers.push({ beltLength: null, noOfJoints: null })
//                 : arrayHelpers.push({ jointType: '', noOfJoints: '' });
//               modalRef.current.pushModal();
//             }}
//           >
//             Continue
//           </Button>
//         </Box>
//       </Modal>
//     </Box>
//   );
// };

// const styles = StyleSheet.create({
//   container: {
//     height: 60,
//     borderColor: COLORS.neutrals.gray25,
//     borderWidth: 1,
//     backgroundColor: '#FFFFFF',
//     borderRadius: 12,
//     paddingHorizontal: 16
//   },
//   title: {
//     color: COLORS.primary[1],
//     fontFamily: FONTS.interBold,
//     fontSize: 18
//   },
//   text: {
//     fontFamily: FONTS.inter,
//     fontSize: 16
//   },
//   selectedDate: {
//     backgroundColor: COLORS.primary[1]
//   },
//   selectedDateText: {
//     color: '#FFF'
//   }
// });

// export { JobTypeModal };
