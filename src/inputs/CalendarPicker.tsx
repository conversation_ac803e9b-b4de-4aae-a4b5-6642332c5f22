import { Icon, Modal } from '@commons';
import { COLORS, FONTS } from '@constants';
import moment from 'moment';
import { Box, HStack, Text } from 'native-base';
import React, { useEffect, useRef } from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import RNCalendarPicker from 'react-native-calendar-picker';

interface Props {
  placeholder?: string;
  label?: string;
  isTaskOwner?: boolean;
  role?: boolean;
  loading?: boolean;
  date?: string;
  setSelectedDate: (date: Date) => void;
}

const CalendarPicker: React.FC<Props> = props => {
  const modalRef = useRef<any>(null);
  const { date, setSelectedDate, placeholder } = props;

  // const error =
  //   (_.get(form.touched, field.name) && _.get(form.errors, field.name)) ||
  //   (form.submitCount > 0 && _.get(form.errors, field.name));

  const onDateChange = (date: any) => {
    setSelectedDate(date);
    modalRef.current.pushModal();
  };

  return (
    <Box>
      {/* {props.label && <Text color="neutrals.gray90">{props.label}</Text>} */}
      {props.label && (
        <Text flex={1} variant="bodyReg" mb={2}>
          {props.label}
        </Text>
      )}
      <TouchableOpacity
        onPress={() => {
          modalRef.current.pushModal();
        }}
        disabled={(!props.isTaskOwner && !props.role) || props.loading}
        activeOpacity={0.98}
      >
        <HStack style={[styles.container]} alignItems="baseline" justifyContent="flex-start">
          {date ? (
            <Box>
              <Text variant="caption" color="neutrals.gray50">
                {placeholder}
              </Text>
              <Text variant="body1" color={!(props.role || props.isTaskOwner) ? 'gray.400' : 'black'}>
                {moment(date).format('ddd, D MMM YYYY')}
              </Text>
            </Box>
          ) : props.label ? (
            <Box>
              <Text variant="caption" color="neutrals.gray50">
                {props.label}
              </Text>
              <Text variant="body1">{placeholder}</Text>
            </Box>
          ) : (
            <Text variant="body1" color="neutrals.gray50">
              {placeholder}
            </Text>
          )}
          {/* <Icon name="calendar" /> */}
        </HStack>
      </TouchableOpacity>

      <Modal ref={modalRef}>
        <Box py={6} px={5}>
          <Text variant="headline" mb={3} textAlign="center">
            Select a date
          </Text>
          <RNCalendarPicker
            onDateChange={onDateChange}
            allowRangeSelection={false}
            previousComponent={<Icon name="chevron-left" fill={COLORS.neutrals.gray90} height={24} width={24} />}
            nextComponent={<Icon name="chevron-right" fill={COLORS.neutrals.gray90} height={24} width={24} />}
            monthTitleStyle={styles.title}
            yearTitleStyle={styles.title}
            minDate={new Date()}
            weekdays={['S', 'M', 'T', 'W', 'T', 'F', 'S']}
            customDayHeaderStyles={customDayHeaderStylesCallback}
            // selectedDayColor={COLORS.primary}
            textStyle={styles.text}
            selectedDayStyle={styles.selectedDate}
            selectedDayTextStyle={styles.selectedDateText}
            selectedStartDate={new Date()}
          />
        </Box>
      </Modal>
      {/* {error && (
        <Text variant="body2" color="semantics.danger" mt={1}>
          {error}
        </Text>
      )} */}
    </Box>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 50,
    borderColor: COLORS.neutrals.gray40,
    borderWidth: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    paddingHorizontal: 16
  },
  title: {
    color: COLORS.primary[1],
    fontFamily: FONTS.interBold,
    fontSize: 18
  },
  text: {
    fontFamily: FONTS.inter,
    fontSize: 16
  },
  selectedDate: {
    backgroundColor: COLORS.primary[1]
  },
  selectedDateText: {
    color: '#FFF'
  }
});

const customDayHeaderStylesCallback = ({}) => {
  return {
    textStyle: {
      fontFamily: FONTS.inter,
      fontSize: 13
    }
  };
};

export { CalendarPicker };
