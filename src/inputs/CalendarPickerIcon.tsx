import { Icon, Modal } from '@commons';
import { COLORS, FONTS } from '@constants';
import { FieldProps } from 'formik';
import moment from 'moment';
import { Box, HStack, Text } from 'native-base';
import React, { useRef } from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import RNCalendarPicker from 'react-native-calendar-picker';

interface Props {
  selectedDate: any;
  setSelectedDate: any;
}

const CalendarPickerIcon: React.FC<Props> = props => {
  const modalRef = useRef<any>(null);
  const { selectedDate, setSelectedDate } = props;

  const onDateChange = (date: any) => {
    // form.setFieldValue(field.name, date);
    setSelectedDate(date);
    modalRef.current.pushModal();
  };

  return (
    <Box>
      <TouchableOpacity onPress={() => modalRef.current.pushModal()} activeOpacity={0.98}>
        <Icon name="calendar-grey" height={24} width={24} />
      </TouchableOpacity>

      <Modal ref={modalRef}>
        <Box pb={6} pt={3} px={5}>
          <Text
            fontSize={16}
            textAlign="right"
            onPress={() => {
              setSelectedDate(null);
              modalRef.current.pushModal();
            }}
            color={COLORS.primary[1]}
          >
            Clear
          </Text>
          <Text variant="headline" mb={3} textAlign="center">
            Select a date
          </Text>
          <RNCalendarPicker
            onDateChange={onDateChange}
            allowRangeSelection={false}
            previousComponent={<Icon name="chevron-left" fill={COLORS.neutrals.gray90} height={24} width={24} />}
            nextComponent={<Icon name="chevron-right" fill={COLORS.neutrals.gray90} height={24} width={24} />}
            monthTitleStyle={styles.title}
            yearTitleStyle={styles.title}
            maxDate={new Date()}
            weekdays={['S', 'M', 'T', 'W', 'T', 'F', 'S']}
            customDayHeaderStyles={customDayHeaderStylesCallback}
            // selectedDayColor={COLORS.primary}
            textStyle={styles.text}
            selectedDayStyle={styles.selectedDate}
            selectedDayTextStyle={styles.selectedDateText}
            selectedStartDate={selectedDate}
          />
        </Box>
      </Modal>
    </Box>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 60,
    borderColor: COLORS.neutrals.gray40,
    borderWidth: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    paddingHorizontal: 16
  },
  title: {
    color: COLORS.primary[1],
    fontFamily: FONTS.interBold,
    fontSize: 18
  },
  text: {
    fontFamily: FONTS.inter,
    fontSize: 16
  },
  selectedDate: {
    backgroundColor: COLORS.primary[1]
  },
  selectedDateText: {
    color: '#FFF'
  }
});

const customDayHeaderStylesCallback = ({}) => {
  return {
    textStyle: {
      fontFamily: FONTS.inter,
      fontSize: 13
    }
  };
};

export { CalendarPickerIcon };
