import React, { useRef } from 'react';
import { FieldProps } from 'formik';
import { Box, HStack, Text, Button } from 'native-base';
import _ from 'lodash';
import { Dimensions, StyleSheet } from 'react-native';
import { COLORS } from '@constants';
import { color } from 'native-base/lib/typescript/theme/styled-system';

interface Props extends FieldProps {
  options: Option[];
  placeholder?: string;
  label?: string;
}

interface Option {
  label: string;
  value: string;
}

// TODO: need custom
const RadioButtonInput: React.FC<Props> = props => {
  const { options = [], field, form } = props;
  const error =
    (_.get(form.touched, field.name) && _.get(form.errors, field.name)) ||
    (form.submitCount > 0 && _.get(form.errors, field.name));
  // const error =
  //   (_.get(form.touched, field.name) && _.get(form.errors, field.name)) ||
  //   (form.submitCount > 0 && _.get(form.errors, field.name));

  const onChange = (val: any) => {
    form.setFieldValue(field.name, val);
  };

  return (
    <Box>
      <HStack mb={3} alignItems="center">
        {props.label && (
          <Text flex={1} variant="body1">
            {props.label}
          </Text>
        )}
        {_.map(options, (o, i) => {
          const isSelected = o.value === field.value;
          return (
            <Button
              key={`${i}`}
              px={6}
              mx={1}
              onPress={() => onChange(o.value)}
              rounded={12}
              variant={isSelected ? 'primary' : 'outlineGray'}
              borderColor={isSelected ? 'primary' : 'neutrals.gray10'}
              _text={{
                color: isSelected ? '#FFF' : 'neutrals.gray25'
              }}
            >
              {o.label}
            </Button>
          );
        })}
      </HStack>
      {error && (
        <Text
          variant="body2"
          color="semantics.danger"
          mb={2}
          style={{ flex: 1, flexDirection: 'row', textAlign: 'right' }}
        >
          {`${error}`}
        </Text>
      )}
    </Box>
  );
};

export { RadioButtonInput };
