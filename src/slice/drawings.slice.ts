import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface DrawingState {
  isDrawingThreeOptionModalOpen: boolean;
  isRenameDrawingsModalOpen: boolean;
  isPhotosDetailsModalOpen: boolean;
  isPhotoOptionModalOpen: boolean;
  selectedDocumentId?: string;
  filteredValue?: string;
  files: {};
  data: any;
  linkedDocuments: any;
}

const initialState: DrawingState = {
  isDrawingThreeOptionModalOpen: false,
  isRenameDrawingsModalOpen: false,
  isPhotosDetailsModalOpen: false,
  isPhotoOptionModalOpen: false,
  selectedDocumentId: undefined,
  filteredValue: '',
  data: null,
  files: {},
  linkedDocuments: []
};

const slice = createSlice({
  name: 'document-workspace',
  initialState,
  reducers: {
    resetAll: (state: DrawingState) => {
      state.isDrawingThreeOptionModalOpen = false;
      state.isRenameDrawingsModalOpen = false;
      state.isPhotosDetailsModalOpen = false;
      state.selectedDocumentId = undefined;
    },
    closeAll: (state: DrawingState) => {
      state.isDrawingThreeOptionModalOpen = false;
      state.isRenameDrawingsModalOpen = false;
      state.isPhotosDetailsModalOpen = false;
      state.isPhotoOptionModalOpen = false;
    },
    openDrawingThreeOptionModal: (state: DrawingState, action: PayloadAction<string>) => {
      state.isDrawingThreeOptionModalOpen = true;
      state.data = action.payload;
    },
    closeDrawingThreeOptionModal: (state: DrawingState) => {
      state.isDrawingThreeOptionModalOpen = false;
    },
    openRenameDrawingsModal: (state: DrawingState) => {
      // state.isDrawingThreeOptionModalOpen = false;
      state.isRenameDrawingsModalOpen = true;
    },
    closeRenameDrawingsModal: (state: DrawingState) => {
      state.isRenameDrawingsModalOpen = false;
      // state.selectedDocumentId = action.payload;
    },
    openPhotoDetailModal: (state: DrawingState, action: PayloadAction<any>) => {
      state.isDrawingThreeOptionModalOpen = true;
      state.isPhotosDetailsModalOpen = true;
      state.files = { ...state.files, ...action.payload };
    },
    setIsPhotosDetailsModalOpen: (state: DrawingState, action: PayloadAction<boolean>) => {
      state.isPhotosDetailsModalOpen = action.payload;
      state.isPhotoOptionModalOpen = false;
    },
    setFilterDrawingRevision: (state: DrawingState, action: PayloadAction<any>) => {
      state.filteredValue = action.payload;
    },
    setLinkedDocuments: (state: DrawingState, action: PayloadAction<any>) => {
      state.linkedDocuments = action.payload;
    }
  }
});

export const { reducer: drawingsReducer, actions: drawingsActions } = slice;
