import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { NotificationApiService } from '@src/api/rest';
import { pushError } from '@src/configs';
import { AppThunk } from '@src/store';
import moment from 'moment';

interface notification {
  id: string;
  avatar: string;
  name: string;
  createdAt: string;
  actionName: string;
  actionType: string;
  deepLink: string;
  read: boolean;
}

interface notifications {
  notifications: notification[];
  totalCount: number;
  unreadCount: number;
  page: number;
}

const initialState: notifications = {
  notifications: [],
  totalCount: 0,
  unreadCount: 0,
  page: 0
};
const slice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    setNotifications(state, action: PayloadAction<notification[]>) {
      state.notifications = [...(state.notifications ?? []), ...action.payload];
    },
    setTotalCount(state, action: PayloadAction<number>) {
      state.totalCount = action.payload;
    },
    setUnreadCount(state, action: PayloadAction<number>) {
      state.unreadCount = action.payload;
    },
    setPage(state, action: PayloadAction<number>) {
      state.page = action.payload;
    }
  }
});

const getNotifications =
  (page: number): AppThunk =>
  async (dispatch): Promise<void> => {
    try {
      await NotificationApiService.getNotifications({ body: { page } }).then(res => {
        const { data } = res;
        const notifications: notification[] = data.map((item: any) => {
          return {
            id: item?._id,
            avatar: item?.payload?.user?.avatar,
            name: item?.payload?.user?.title,
            createdAt: moment(item?.createdAt).format('D MMM YYYY, h:mma'),
            actionName: typeof item?.payload?.body === 'object' ? '' : item?.payload?.body,
            actionType: item?.payload?.head,
            deepLink: item?.payload?.link?.mobile,
            read: item?.read
          };
        });

        dispatch(slice.actions.setTotalCount(res.totalCount));
        dispatch(slice.actions.setNotifications(notifications));
      });
    } catch (e) {
      pushError(e);
      return Promise.reject(e);
    }
  };

const getUnreadCount =
  (): AppThunk =>
  async (dispatch): Promise<void> => {
    try {
      await NotificationApiService.getNotificationUnseenCount().then(res => {
        const { data } = res;
        dispatch(slice.actions.setUnreadCount(data?.count));
      });
    } catch (e) {
      // pushError(e);
      return Promise.reject(e);
    }
  };

export const { reducer: notificationReducer } = slice;
export const notificationActions = { getNotifications, getUnreadCount, ...slice.actions };
