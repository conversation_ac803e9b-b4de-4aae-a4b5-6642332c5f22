import { createSlice, PayloadAction } from '@reduxjs/toolkit';
interface PhotosState {
  isPhotosAddModalOpen: boolean;
  isPhotosDescModalOpen: boolean;
  isCrateFolderModalOpen: boolean;
  isPhotosDetailsModalOpen: boolean;
  isPhotoOptionModalOpen: boolean;
  isPhotoRenameModalOpen: boolean;
  selectedDocumentId?: string;
  files: '';
  type?: string;
  data?: any;
}

const initialState: PhotosState = {
  isPhotosAddModalOpen: false,
  isCrateFolderModalOpen: false,
  isPhotosDetailsModalOpen: false,
  isPhotoOptionModalOpen: false,
  isPhotoRenameModalOpen: false,
  selectedDocumentId: undefined,
  files: '',
  type: undefined,
  data: undefined,
  isPhotosDescModalOpen: false
};

const slice = createSlice({
  name: 'document-workspace',
  initialState,
  reducers: {
    resetAll: (state: PhotosState) => {
      state.isPhotosAddModalOpen = false;
      state.isCrateFolderModalOpen = false;
      state.isPhotosDetailsModalOpen = false;
      state.selectedDocumentId = undefined;
      state.isPhotoOptionModalOpen = false;
      state.isPhotosDescModalOpen = false;
      state.isPhotoRenameModalOpen = false;
      state.files = '';
      state.type = undefined;
    },
    closeAll: (state: PhotosState) => {
      state.isPhotosAddModalOpen = false;
      state.isCrateFolderModalOpen = false;
      state.isPhotosDetailsModalOpen = false;
      state.isPhotoRenameModalOpen = false;
      state.isPhotoOptionModalOpen = false;
      state.isPhotosDescModalOpen = false;
    },
    openPhotosAddModal: (state: PhotosState) => {
      state.isPhotosAddModalOpen = true;
    },
    openPhotosDescModal: (state: PhotosState) => {
      state.isPhotosDescModalOpen = true;
    },
    openCreateFolderModal: (state: PhotosState) => {
      // state.isPhotosAddModalOpen = false;
      state.isCrateFolderModalOpen = true;
    },
    closePhotosAddModal: (state: PhotosState) => {
      state.isPhotosAddModalOpen = false;
    },
    openPhotosOptionModal: (state: PhotosState, action: PayloadAction<any>) => {
      state.isPhotoOptionModalOpen = true;
      state.selectedDocumentId = action.payload.id;
      state.files = action.payload.fileUrl ?? '';
      state.type = action.payload.type;
      state.data = action.payload.data;
    },
    openPhotoDetailModal: (state: PhotosState, action: PayloadAction<any>) => {
      state.isPhotosAddModalOpen = true;
      state.isPhotosDetailsModalOpen = true;
      state.files = action.payload;
    },
    setIsPhotosDetailsModalOpen: (state: PhotosState, action: PayloadAction<boolean>) => {
      state.isPhotosDetailsModalOpen = action.payload;
      state.isPhotoOptionModalOpen = false;
    },
    setIsPhotosRenameModalOpen: (state: PhotosState, action: PayloadAction<boolean>) => {
      state.isPhotoRenameModalOpen = true;
      // state.isPhotoOptionModalOpen = false;
    },
    setIsPhotosDescModalOpen: (state: PhotosState, action: PayloadAction<boolean>) => {
      state.isPhotosDescModalOpen = true;
      // state.isPhotoOptionModalOpen = false;
    }
  }
});

export const { reducer: photosReducer, actions: photosActions } = slice;
