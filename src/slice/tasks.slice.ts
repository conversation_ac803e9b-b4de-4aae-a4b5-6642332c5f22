import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Gql } from '@src/api';
import { TaskStatusType } from '@src/api/graphql';
import { UserDetail } from 'project-user';

interface Task {
  id: string;
  title: string;
  description?: string;
  status: TaskStatusType | undefined;
  dueDate?: Date;
  assignee: UserDetail[];
  cc?: UserDetail[];
  group: {
    id: string;
    title: string;
    remoteId: string;
  };
  createdBy: {
    id: string;
    name: string;
    avatar: string;
  };

  linkedDocument?: any[];
  attachment?: any[];
  medias?: any[];
  comments?: any[];
}
interface TaskState {
  isTaskOptionModalOpen: boolean;
  isTaskDetailModalOpen: boolean;
  isTaskFilterModalOpen: boolean;

  modalType?: string;

  isTaskAddModalOpen: boolean;

  addedAssignee: string[];
  addedCC: string[];
  status?: TaskStatusType | null;
  selectedGroup?: {
    id: number | null;
    name: string | null;
  };
  addGroup?: {
    id: number | null;
    name: string | null;
  };
  attachment: any[];
  photos: any[];
  isPhotosDetailsModalOpen: boolean;
  isPhotoOptionModalOpen: boolean;
  selectedDocumentId?: string;
  groupId?: number;
  taskId?: string;
  taskOwnerId?: string;
  isFiltering: boolean;
  linkedDocument: any[];
  linkedTo: any[];

  filter: Filter;
  filterItems: Filter;

  setInitialState: boolean;

  isFetchingComments: boolean | null;

  task?: Task;
  isUrgent?: boolean;
}

export interface Filter {
  id: string | undefined;
  hideCompleted: boolean;
  assignToMe: boolean;
  status?: TaskStatusType[];
  assignee?: string[];
  group?: { id: string; title: string };
  cc?: string[];
  date?: string;
}

const defaultFilter: Filter = {
  id: undefined,
  hideCompleted: false,
  assignToMe: false,
  status: undefined,
  assignee: undefined,
  group: undefined,
  cc: undefined,
  date: undefined
};

const defaultTask: Task = {
  id: '',
  title: '',
  description: '',
  status: undefined,
  dueDate: new Date(),
  assignee: [],
  cc: [],
  group: {
    id: '',
    title: '',
    remoteId: ''
  },
  linkedDocument: [],
  attachment: [],
  medias: [],
  comments: [],
  createdBy: {
    id: '',
    name: '',
    avatar: ''
  }
};

const initialState: TaskState = {
  isTaskOptionModalOpen: false,
  isTaskDetailModalOpen: false,
  isTaskFilterModalOpen: false,

  modalType: undefined,

  isTaskAddModalOpen: false,

  addedAssignee: [],
  addedCC: [],
  selectedGroup: {
    id: null,
    name: null
  },
  addGroup: {
    id: null,
    name: null
  },
  attachment: [],
  status: undefined,
  photos: [],
  isPhotosDetailsModalOpen: false,
  isPhotoOptionModalOpen: false,
  selectedDocumentId: undefined,
  groupId: undefined,
  taskId: undefined,
  isFiltering: false,
  linkedDocument: [],
  linkedTo: [],

  filter: defaultFilter,
  filterItems: defaultFilter,

  isFetchingComments: null,

  setInitialState: true
};

const slice = createSlice({
  name: 'tasks',
  initialState,
  reducers: {
    resetAll: (state: TaskState) => {
      state.isTaskOptionModalOpen = false;
      state.isTaskDetailModalOpen = false;
      state.isPhotosDetailsModalOpen = false;
      state.isTaskFilterModalOpen = false;
      state.selectedDocumentId = undefined;
      state.modalType = undefined;
      state.isTaskAddModalOpen = false;
      state.addedAssignee = [];
      state.addedCC = [];
      state.selectedGroup = {
        id: null,
        name: null
      };
      state.photos = [];
      state.attachment = [];
      state.taskId = undefined;
      state.status = undefined;
      state.linkedDocument = [];
      state.linkedTo = [];
      state.task = defaultTask;
    },
    closeAll: (state: TaskState) => {
      state.isTaskOptionModalOpen = false;
      state.isTaskDetailModalOpen = false;
      state.isPhotosDetailsModalOpen = false;
      state.isTaskFilterModalOpen = false;
      state.isPhotoOptionModalOpen = false;
      state.isTaskAddModalOpen = false;
      state.addedAssignee = [];
      state.addedCC = [];
      state.selectedGroup = {
        id: null,
        name: null
      };
      state.photos = [];
      state.attachment = [];
      state.taskId = undefined;
      state.status = undefined;
      state.linkedDocument = [];
      state.linkedTo = [];
      state.task = defaultTask;
    },
    clearFilter: (state: TaskState) => {
      state.filter = defaultFilter;
      state.filterItems = defaultFilter;
    },
    OpenTaskAddModal: (state: TaskState) => {
      state.isTaskAddModalOpen = true;
    },
    OpenTaskFilterModal: (state: TaskState) => {
      state.isTaskFilterModalOpen = true;
    },
    toggleTaskModal: (state: TaskState) => {
      state.isTaskDetailModalOpen = !state.isTaskDetailModalOpen;
    },
    OpenTaskOptionModal: (state: TaskState, action: PayloadAction<any>) => {
      state.isTaskOptionModalOpen = true;
      state.taskId = action.payload.taskId;
      state.taskOwnerId = action.payload.ownerId;
      // state.isUrgent = action.payload;
    },
    OpenTaskDetailModal: (state: TaskState, action: PayloadAction<string>) => {
      state.taskId = action.payload;
      state.isTaskDetailModalOpen = true;
    },
    AddAssignee: (state: TaskState, action: PayloadAction<string>) => {
      state.addedAssignee = [...action.payload];
    },
    AddCC: (state: TaskState, action: PayloadAction<string>) => {
      state.addedCC = [...action.payload];
    },
    AddGroup: (state: TaskState, action: PayloadAction<any>) => {
      state.selectedGroup = { ...action.payload };
    },
    SetPhotos: (state: TaskState, action: PayloadAction<string[]>) => {
      state.photos = [...action.payload];
    },
    SetAttachment: (state: TaskState, action: PayloadAction<any>) => {
      state.attachment = [...action.payload];
    },
    SetStatus: (state: TaskState, action: PayloadAction<TaskStatusType>) => {
      state.status = action.payload;
    },
    toggleFilter: (state: TaskState, action: PayloadAction<boolean>) => {
      state.isFiltering = action.payload;
    },
    setFilter: (state: TaskState, action: PayloadAction<Filter>) => {
      state.filter = action.payload;
    },
    setFilterItems: (state: TaskState, action: PayloadAction<Filter>) => {
      state.filterItems = action.payload;
    },
    setLinkedDocument: (state: TaskState, action: PayloadAction<any>) => {
      state.linkedDocument = action.payload;
    },
    setLinkedTo: (state: TaskState, action: PayloadAction<any>) => {
      state.linkedTo = action.payload;
    },
    setGroup: (state: TaskState, action: PayloadAction<any>) => {
      state.addGroup = { ...action.payload };
    },
    setInitialState: (state: TaskState, action: PayloadAction<boolean>) => {
      state.setInitialState = action.payload;
    },
    initialState: (state: TaskState, action: PayloadAction<Task>) => {
      state.task = action.payload;
    },
    setIsFetchingComments: (state: TaskState, action: PayloadAction<boolean>) => {
      state.isFetchingComments = action.payload;
    }
  }
});

export const { reducer: taskReducer, actions: taskActions } = slice;
