import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Gql } from '@src/api';
import { ProjectUserRoleType } from '@src/api/graphql';
import { UserAuthService } from '@src/api/rest';
import { setMetTownId, setProjectId } from '@src/configs';
import apolloClient from '@src/lib/apollo';
import ProjectUserModel from '@src/database/model/project-user.model';
import { AppThunk } from '@src/store';

interface ProjectState {
  projectId?: string;
  selectedProjectTitle?: string;
  selectedProjectImage?: string;
  projectUserRole?: Gql.ProjectUserRoleType;
  isSettingModalOpen?: boolean;
  recentProjects?: Gql.Project[];
}

const initialState: ProjectState = {
  projectId: undefined,
  selectedProjectTitle: undefined,
  selectedProjectImage: undefined,
  projectUserRole: undefined,
  isSettingModalOpen: false,
  recentProjects: []
};

const slice = createSlice({
  name: 'project',
  initialState,
  reducers: {
    initialize(state: ProjectState, action: PayloadAction<ProjectState>): void {
      if (action.payload) {
        // state.projectId = action.payload.projectId;
        state.projectUserRole = action.payload.projectUserRole;
      }
    },
    setCurrentProjectId(state: ProjectState, action: PayloadAction<string>): void {
      state.projectId = action.payload;
    },
    setCurrentProjectTitle(state: ProjectState, action: PayloadAction<string>): void {
      state.selectedProjectTitle = action.payload;
    },
    setCurrentProjectImage(state: ProjectState, action: PayloadAction<string>): void {
      state.selectedProjectImage = action.payload;
    },
    setSettingModal(state: ProjectState, action: PayloadAction<boolean>): void {
      state.isSettingModalOpen = action.payload;
    },
    setRecentProjects(state: ProjectState, action: PayloadAction<Gql.Project[]>): void {
      state.recentProjects = action.payload;
    }
  }
});

const initialize =
  (userId: number, projectId: string): AppThunk =>
  async dispatch => {
    const role: any = await ProjectUserModel.getProjectUserRole(projectId, userId);
    return dispatch(slice.actions.initialize({ projectUserRole: role ?? undefined }));
  };

const updateProjectId =
  (projectId: string): AppThunk =>
  async (dispatch): Promise<void> => {
    await setProjectId(projectId);
    dispatch(slice.actions.setCurrentProjectId(projectId));
  };

export const { reducer: projectReducer, actions: projectActions } = slice;

export const ProjectActions = { initialize, updateProjectId };
