import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Gql } from '@src/api';
import { NotificationApiService, UserAuthService } from '@src/api/rest';
import {
  getFcmToken,
  getRefreshToken,
  getUserInfo,
  pushError,
  removeFcmToken,
  removeJwtToken,
  removeRefreshToken,
  removeUserInfo,
  setJwtToken,
  setRemoveFcmTokenFlag,
  storeFcmToken,
  storeRefreshToken,
  storeUserInfo
} from '@src/configs';
import apolloClient from '@src/lib/apollo';
import { requestDeviceToken } from '@src/lib/axios';
import { AppThunk } from '@src/store';
import { ProjectActions } from './project.slice';
import { AppActions } from './app.slice';
import database from '@src/database/index.native';
import NetInfo from '@react-native-community/netinfo';

interface AuthState {
  user: Gql.User | null;
  isAuthenticated: boolean;
  isInitialized: boolean;
  company: Gql.Company | null;
  requestForLoggedOut: boolean;
}

interface UserAuthState {
  id: string;
  email: string;
}

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isInitialized: false,
  company: null,
  requestForLoggedOut: false
};

const slice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    initialize(state: AuthState, action: PayloadAction<Gql.User | undefined>): void {
      if (action.payload) {
        state.user = action.payload;
        state.isAuthenticated = !!action.payload;
      }
      state.isInitialized = true;
    },
    setAuthenticated(state: AuthState, action: PayloadAction<Gql.User>): void {
      state.user = action.payload;
      state.isAuthenticated = true;
    },
    setBeforeOnboardingAuthenticated(state: AuthState, action: PayloadAction<Gql.User>): void {
      state.user = action.payload;
    },
    setAfterOnboardingAuthenticated(state: AuthState, action: PayloadAction<Gql.User>): void {
      state.user = action.payload;
      state.isAuthenticated = true;
    },
    // setCurrentProjectId(state: AuthState, action: PayloadAction<string>): void {
    //   state.projectId = action.payload;
    // },
    logout(state: AuthState): void {
      state.isAuthenticated = false;
      state.requestForLoggedOut = false;
    },
    setCompany(state: AuthState, action: PayloadAction<Gql.Company>): void {
      state.company = action.payload;
    },
    setRequestForLoggedOut(state: AuthState, action: PayloadAction<boolean>): void {
      state.requestForLoggedOut = action.payload;
    }
    // setAuthentication(state: AuthState): void {
    //   state.isAuthenticated = true;
    // }
  }
});

// Check whether token is still valid
// const initialize = (): AppThunk => async dispatch => {
//   try {

//     const isConnected = await NetInfo.fetch();

//     const res = await apolloClient.query<Gql.GetUserMeQuery>({
//       query: Gql.GetUserMeDocument
//     });
//     dispatch(slice.actions.initialize(res.data.getUserMe as Gql.User));
//     dispatch(ProjectActions.initialize());
//   } catch (error) {
//     dispatch(slice.actions.initialize());
//   }
// };

const initialize = (): AppThunk => async dispatch => {
  try {
    // Try to get user data from AsyncStorage first
    const userData = await getUserInfo();
    if (userData) {
      dispatch(slice.actions.initialize(userData as Gql.User));
    } else {
      // If no user data in storage, check network status
      const networkState = await NetInfo.fetch();
      const refreshToken = await getRefreshToken();

      if (networkState.isConnected && refreshToken) {
        // If connected, fetch user data from the server
        const res = await apolloClient.query<Gql.GetUserMeQuery>({
          query: Gql.GetUserMeDocument
        });

        if (res.data.getUserMe) {
          // Store the new user data in AsyncStorage and initialize the app
          await storeUserInfo(res.data.getUserMe as Gql.User);
          dispatch(slice.actions.initialize(res.data.getUserMe as Gql.User));
        } else {
          dispatch(AuthActions.logout());
        }
      } else {
        dispatch(slice.actions.initialize());
        dispatch(AuthActions.logout());
      }
    }
  } catch (error) {
    dispatch(slice.actions.initialize());
    dispatch(AuthActions.logout());
  }
};

const login =
  (inputDto: { email: string; password: string; rememberMe: boolean }): AppThunk =>
  async (dispatch): Promise<any> => {
    try {
      const tokens = await UserAuthService.signIn({ body: inputDto });

      const fcmToken = await requestDeviceToken();
      await storeRefreshToken(tokens.refreshToken);
      await setJwtToken(tokens.accessToken);
      await storeFcmToken(fcmToken ?? '');
      await NotificationApiService.setDeviceToken({
        body: {
          deviceToken: fcmToken ?? ''
        }
      });

      const { data } = await apolloClient.query<Gql.GetUserMeQuery>({
        query: Gql.GetUserMeDocument
      });

      // store user inside the async storage
      await storeUserInfo(data?.getUserMe as Gql.User);

      dispatch(slice.actions.setAuthenticated(data?.getUserMe as Gql.User));
    } catch (e) {
      pushError(e);
      return Promise.reject(e);
    }
  };

const updateUser =
  (user: any): AppThunk =>
  async (dispatch): Promise<any> => {
    try {
      dispatch(slice.actions.setAuthenticated(user as Gql.User));
    } catch (e) {
      pushError(e);
      return Promise.reject(e);
    }
  };

const loginSocial =
  (inputDto: { accessToken: string; refreshToken: string; accessTokenExpiry: number }): AppThunk =>
  async (dispatch): Promise<void> => {
    try {
      const fcmToken = await requestDeviceToken();
      await storeRefreshToken(inputDto.refreshToken);
      await setJwtToken(inputDto.accessToken);
      await storeFcmToken(fcmToken ?? '');
      await NotificationApiService.setDeviceToken({
        body: {
          deviceToken: fcmToken ?? ''
        }
      });
      const { data } = await apolloClient.query<Gql.GetUserMeQuery>({
        query: Gql.GetUserMeDocument
      });
      dispatch(slice.actions.setAuthenticated(data?.getUserMe as Gql.User));
    } catch (e) {
      pushError(e);
      return Promise.reject(e);
    }
  };

const loginBeforeOnboarding =
  (inputDto: { signUpToken: string }): AppThunk =>
  async (dispatch): Promise<void> => {
    const tokens = await UserAuthService.emailVerification({ body: inputDto });
    await storeRefreshToken(tokens.refreshToken);
    await setJwtToken(tokens.accessToken);
    const { data } = await apolloClient.query<Gql.GetUserMeQuery>({
      query: Gql.GetUserMeDocument
    });
    dispatch(slice.actions.setBeforeOnboardingAuthenticated(data.getUserMe as Gql.User));
  };

const loginAfterOnboarding =
  (): AppThunk =>
  async (dispatch): Promise<void> => {
    const { data } = await apolloClient.query<Gql.GetUserMeQuery>({
      query: Gql.GetUserMeDocument
    });
    dispatch(slice.actions.setAfterOnboardingAuthenticated(data.getUserMe as Gql.User));
  };

// Add Watermelon DB code
const logout = (): AppThunk => async dispatch => {
  const isConnected = await NetInfo.fetch().then(state => state.isConnected);
  dispatch(slice.actions.setRequestForLoggedOut(true));

  // Teardown WatermelonDB
  await database.write(async () => {
    await database.unsafeResetDatabase();
  });

  // Remove tokens and user info locally regardless of connection status.
  await removeUserInfo();
  await removeJwtToken();
  await removeRefreshToken();

  if (isConnected) {
    try {
      await removeFcmToken();
    } catch (error) {}
  } else {
    await setRemoveFcmTokenFlag();
  }

  dispatch(slice.actions.logout());
};

const verifyOtp =
  (inputDto: { otp?: string; email: string; tokens: any }): AppThunk =>
  async (dispatch): Promise<any> => {
    try {
      if (inputDto.otp) {
        const { data } = await apolloClient.mutate<Gql.VerifyOtpMutation>({
          variables: {
            input: {
              otp: inputDto.otp,
              email: inputDto.email
            }
          },
          mutation: Gql.VerifyOtpDocument
        });
      }

      const fcmToken = await requestDeviceToken();
      await storeRefreshToken(inputDto.tokens.refreshToken);
      await setJwtToken(inputDto.tokens.accessToken);
      await storeFcmToken(fcmToken ?? '');
      await NotificationApiService.setDeviceToken({
        body: {
          deviceToken: fcmToken ?? ''
        }
      });

      const { data } = await apolloClient.query<Gql.GetUserMeQuery>({
        query: Gql.GetUserMeDocument
      });

      dispatch(slice.actions.setAuthenticated(data?.getUserMe as Gql.User));
    } catch (e) {
      pushError(e);
      return Promise.reject(e);
    }
  };

export const { reducer: authReducer } = slice;
export const AuthActions = {
  initialize,
  login,
  loginSocial,
  loginBeforeOnboarding,
  loginAfterOnboarding,
  logout,
  verifyOtp,
  updateUser,
  setCompany: slice.actions.setCompany,
  setRequestForLoggedOut: slice.actions.setRequestForLoggedOut
};
