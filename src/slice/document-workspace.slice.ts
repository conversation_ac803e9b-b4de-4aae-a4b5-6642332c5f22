import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Gql } from '@src/api';
import ProjectDocumentModel from '@src/database/model/project-document.model';
import ProjectUserModel from '@src/database/model/project-user.model';

type RequestApprovalDocument = {
  selectedAssigneeIds?: string[];
  selectedCcs?: string[];
};

export type DocumentDetails = {
  id: string;
  selectedAssigneeIds?: string[];
  selectedCcs?: string[];
  group?: { id: string; name: string; remoteId: number };
  status?: Gql.ProjectDocumentStatus;
  attachment?: Gql.WorkspaceAttachment[];
  photos?: string[];
  linkedDocuments?: Gql.WorkspaceDocument[];
  description?: string;
  name?: string;
  remoteId?: number;
  createdBy?: Partial<ProjectUserModel>;
  document?: Partial<ProjectDocumentModel>;
  currentAssigneeId?: string;
};

type DocumentWorkspaceState = {
  isDocumentOptionModalOpen: boolean;
  isRequestApprovalModalOpen: boolean;
  isDocumentDetailsModalOpen: boolean;
  isDocumentFilterModalOpen: boolean;
  selectedDocumentId?: string;
  RequestApprovalDocument?: RequestApprovalDocument;
  DocumentDetails?: DocumentDetails;
  driveType: Gql.ProjectDocumentStatus | '';
  setInitialState?: boolean;
  filter: Filter;
  filterItems: Filter;
  isFetchingComments?: boolean | null;
};

export interface Filter {
  id: string | undefined;
  assignToMe: boolean;
  status?: Gql.ProjectDocumentStatus[];
  assigneeId?: string[];
  assigneeName?: string[];
  groupId?: string;
  groupName?: string;
  allFormCode?: number | undefined;
  parentCode?: string | undefined;
  groupCode?: string | undefined;
}

const defaultFilter: Filter = {
  id: undefined,
  assignToMe: false,
  status: undefined,
  assigneeId: undefined,
  groupId: undefined,
  allFormCode: undefined,
  groupName: undefined,
  assigneeName: undefined
};

const initialState: DocumentWorkspaceState = {
  isDocumentOptionModalOpen: false,
  isRequestApprovalModalOpen: false,
  isDocumentDetailsModalOpen: false,
  isDocumentFilterModalOpen: false,
  selectedDocumentId: undefined,
  RequestApprovalDocument: undefined,
  DocumentDetails: undefined,
  driveType: '',
  setInitialState: true,
  filter: defaultFilter,
  filterItems: defaultFilter,
  isFetchingComments: null
};

const slice = createSlice({
  name: 'document-workspace',
  initialState,
  reducers: {
    resetAll: state => {
      state.isDocumentOptionModalOpen = false;
      state.isRequestApprovalModalOpen = false;
      state.isDocumentDetailsModalOpen = false;
      state.isDocumentFilterModalOpen = false;
      state.selectedDocumentId = undefined;
      state.RequestApprovalDocument = undefined;
      state.DocumentDetails = undefined;
    },
    closeAll: state => {
      state.isDocumentOptionModalOpen = false;
      state.isRequestApprovalModalOpen = false;
      state.isDocumentDetailsModalOpen = false;
      state.isDocumentFilterModalOpen = false;
    },
    openDocumentOptionModal: (state, action: PayloadAction<string>) => {
      state.isDocumentOptionModalOpen = true;
      state.selectedDocumentId = action.payload;
    },
    openRequestApprovalModal: state => {
      state.isRequestApprovalModalOpen = true;
    },
    openRequestApprovalDocumentModal: (state, action: PayloadAction<RequestApprovalDocument>) => {
      state.isDocumentOptionModalOpen = true;
      state.isRequestApprovalModalOpen = true;
      state.RequestApprovalDocument = { ...state.RequestApprovalDocument, ...action.payload };
    },
    openDocumentDetailsModal: (state, action: PayloadAction<DocumentDetails>) => {
      // state.isDocumentOptionModalOpen = true;
      // state.isDocumentDetailsModalOpen = true;
      state.DocumentDetails = { ...state.DocumentDetails, ...action.payload };
    },
    setIsDocumentDetailsModalOpen: (state, action: PayloadAction<boolean>) => {
      state.isDocumentDetailsModalOpen = action.payload;
    },
    setDocumentId: (state, action: PayloadAction<string>) => {
      state.selectedDocumentId = action.payload;
      state.isDocumentDetailsModalOpen = true;
    },
    openDocumentFilterModal: state => {
      state.isDocumentFilterModalOpen = true;
    },
    setDriveType: (state, action: PayloadAction<Gql.ProjectDocumentStatus>) => {
      state.driveType = action.payload;
    },
    setDocumentDetails: (state, action: PayloadAction<DocumentDetails>) => {
      state.DocumentDetails = action.payload;
    },
    clearDocumentDetails: state => {
      state.DocumentDetails = undefined;
    },
    ShouldSetInitialState: (state, action: PayloadAction<boolean>) => {
      state.setInitialState = action.payload;
    },
    setFilter: (state, action: PayloadAction<Filter>) => {
      state.filter = action.payload;
    },
    setFilterItems: (state, action: PayloadAction<Filter>) => {
      state.filterItems = action.payload;
    },
    clearFilter: state => {
      state.filter = defaultFilter;
      state.filterItems = defaultFilter;
    },
    setIsFetchingComments: (state, action: PayloadAction<boolean | null>) => {
      state.isFetchingComments = action.payload;
    }
  }
});

export const { reducer: documentWorkspaceReducer, actions: documentWorkspaceActions } = slice;
