import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { AppThunk } from '@src/store';
import { Gql } from '@src/api';

interface CorrespondenceState {
  openModal?: boolean;
  filter: Filter;
}

interface Filter {
  sentByMe?: boolean;
  sentToMe?: boolean;
  status?: any;
  date?: string;
}

const initialState: CorrespondenceState = {
  filter: {
    sentByMe: false,
    sentToMe: false,
    status: undefined,
    date: undefined
  },
  openModal: false
};

const slice = createSlice({
  name: 'correspondence',
  initialState,
  reducers: {
    initialize(state: CorrespondenceState, action: PayloadAction<CorrespondenceState>): void {
      state.filter = action.payload.filter;
    },
    setOpenModalFilter(state: CorrespondenceState, action: PayloadAction<boolean>): void {
      state.openModal = action.payload;
    },
    clearFilters(state: CorrespondenceState): void {
      state.filter = { sentByMe: false, sentToMe: false, status: undefined, date: undefined };
    },
    setFilter(state: CorrespondenceState, action: PayloadAction<Filter>): void {
      state.filter = action.payload;
    }
  }
});

const initialize =
  (offlineMode = false): AppThunk =>
  async dispatch => {};

export const { reducer: correspondenceReducer, actions: correspondenceActions } = slice;
