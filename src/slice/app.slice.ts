import { PayloadAction, createSlice } from '@reduxjs/toolkit';

interface AppState {
  OFFLINE_MODE?: boolean;
  syncStatus: 'idle' | 'pending' | 'success' | 'failed' | 'upload';
  syncProgress?: number;
  retryCount?: number;
  showSyncModal?: boolean;
  syncDisabled?: boolean;
}

const initialState: AppState = {
  OFFLINE_MODE: false,
  syncStatus: 'idle',
  syncProgress: 0,
  retryCount: 0,
  showSyncModal: false,
  syncDisabled: false
};
const slice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    setOfflineMode(state: AppState, action: PayloadAction<boolean>): void {
      state.OFFLINE_MODE = action.payload;
    },
    setSyncStatus(state: AppState, action: PayloadAction<'idle' | 'pending' | 'success' | 'failed' | 'upload'>): void {
      state.syncStatus = action.payload;
    },
    setSyncProgress(state: AppState, action: PayloadAction<number>): void {
      state.syncProgress = action.payload;
    },
    setShowSyncModal(state: AppState, action: PayloadAction<boolean>): void {
      state.showSyncModal = action.payload;
    },
    setSyncDisabled(state: AppState, action: PayloadAction<boolean>): void {
      state.syncDisabled = action.payload;
    }
  }
});

export const { reducer: appReducer } = slice;
export const AppActions = slice.actions;
